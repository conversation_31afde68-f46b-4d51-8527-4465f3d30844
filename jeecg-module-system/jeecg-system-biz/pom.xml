<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.jeecgframework.boot</groupId>
        <artifactId>jeecg-module-system</artifactId>
        <version>3.7.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jeecg-system-biz</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-sdk-java</artifactId>
            <version>1.6.0</version>
        </dependency>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-system-local-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>hibernate-re</artifactId>
        </dependency>

        <!-- 企业微信/钉钉 api -->
        <dependency>
            <groupId>org.jeecgframework</groupId>
            <artifactId>jeewx-api</artifactId>
        </dependency>
        <!-- 积木报表 -->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-dashboard-spring-boot-starter</artifactId>
        </dependency>
        <!--	<dependency>
                <groupId>org.jeecgframework.jimureport</groupId>
                <artifactId>jimureport-drag</artifactId>
                <version>2.0.1</version>
            </dependency>-->
        <!-- 积木报表 mongo redis 支持包
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-nosql-starter</artifactId>
        </dependency>-->


    </dependencies>

</project>
