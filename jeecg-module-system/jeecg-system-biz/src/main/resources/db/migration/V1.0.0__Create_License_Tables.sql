-- 系统授权管理表
CREATE TABLE IF NOT EXISTS `sys_license` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `license_code` varchar(500) DEFAULT NULL COMMENT '授权码',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `hardware_fingerprint` varchar(100) DEFAULT NULL COMMENT '硬件指纹',
  `license_type` int(1) DEFAULT NULL COMMENT '授权类型：1-试用版，2-正式版，3-永久版',
  `start_time` datetime DEFAULT NULL COMMENT '授权开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '授权结束时间',
  `max_users` int(11) DEFAULT NULL COMMENT '最大用户数',
  `authorized_modules` text COMMENT '授权功能模块（JSON格式）',
  `status` int(1) DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  `signature` varchar(500) DEFAULT NULL COMMENT '加密签名',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_license_code` (`license_code`),
  KEY `idx_hardware_fingerprint` (`hardware_fingerprint`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统授权管理';

-- 授权日志表
CREATE TABLE IF NOT EXISTS `sys_license_log` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `license_id` varchar(32) DEFAULT NULL COMMENT '授权ID',
  `operation_type` varchar(50) DEFAULT NULL COMMENT '操作类型：INSTALL-安装，VERIFY-验证，UNINSTALL-卸载',
  `operation_result` varchar(20) DEFAULT NULL COMMENT '操作结果：SUCCESS-成功，FAILED-失败',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `create_by` varchar(32) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_license_id` (`license_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权操作日志';

-- 插入默认权限数据
INSERT IGNORE INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES
('license_management', '2e42e3835c2b44ec9f7bc26c146ee531', '授权管理', '/system/license', 'system/license/LicenseManagement', 'LicenseManagement', NULL, 1, NULL, '1', 5.00, 0, 'safety-certificate', 1, 1, 0, 0, 0, '系统授权管理', '1', 0, 0, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00', 0),
('license_install', 'license_management', '安装授权', NULL, NULL, NULL, NULL, 2, 'sys:license:install', '1', 1.00, 0, NULL, 0, 1, 0, 0, 0, '安装授权文件', '1', 0, 0, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00', 0),
('license_uninstall', 'license_management', '卸载授权', NULL, NULL, NULL, NULL, 2, 'sys:license:uninstall', '1', 2.00, 0, NULL, 0, 1, 0, 0, 0, '卸载授权文件', '1', 0, 0, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00', 0),
('license_generate', 'license_management', '生成授权', NULL, NULL, NULL, NULL, 2, 'sys:license:generate', '1', 3.00, 0, NULL, 0, 1, 0, 0, 0, '生成授权文件', '1', 0, 0, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00', 0);

-- 插入字典数据
INSERT IGNORE INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES
('license_type_dict', '授权类型', 'license_type', '系统授权类型字典', 0, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00', 0),
('valid_status_dict', '有效状态', 'valid_status', '有效状态字典', 0, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00', 0);

INSERT IGNORE INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('license_type_1', 'license_type_dict', '试用版', '1', '试用版授权', 1, 1, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00'),
('license_type_2', 'license_type_dict', '正式版', '2', '正式版授权', 2, 1, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00'),
('license_type_3', 'license_type_dict', '永久版', '3', '永久版授权', 3, 1, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00'),
('valid_status_1', 'valid_status_dict', '有效', '1', '状态有效', 1, 1, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00'),
('valid_status_0', 'valid_status_dict', '无效', '0', '状态无效', 2, 1, 'admin', '2024-01-01 00:00:00', 'admin', '2024-01-01 00:00:00');
