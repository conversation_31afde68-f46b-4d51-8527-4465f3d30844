package org.jeecg.modules.bpmn.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.identity.Group;
import org.jeecg.common.message.SysRoleEvent;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CamundaGroupSynchronizationService {

    @Autowired
    private IdentityService identityService;

    @Autowired
    private ISysRoleService sysRoleService;

    @EventListener
    public void handleSysRoleEvent(SysRoleEvent event) {
        if(true)
        {
            return;
        }
        //System.out.println("接收到SysRoleEvent: " + event.getMessage());
        // 处理消息逻辑
        String message = event.getMessage();
        if (StringUtils.equals(message, "add")) {
            SysRole sysRole = (SysRole) event.getData();
            Group camundaGroup = identityService.createGroupQuery().groupId(sysRole.getRoleCode()).singleResult();
            if (camundaGroup == null) {
                camundaGroup = identityService.newGroup(sysRole.getRoleCode());
            }
            camundaGroup.setId(sysRole.getRoleCode());
            camundaGroup.setName(sysRole.getRoleName());
            camundaGroup.setType(sysRole.getRoleCode());
            identityService.saveGroup(camundaGroup);
        } else if (StringUtils.equals(message, "delete")) {
            SysRole sysRole = (SysRole) event.getData();
            identityService.deleteGroup(sysRole.getRoleCode());
            // 删除用户角色关联
            identityService.createGroupQuery().groupMember(sysRole.getRoleCode()).list().forEach(group -> {
                identityService.deleteMembership(null, group.getId());
            });
        } else if (StringUtils.equals(message, "deleteBatch")) {
            List<SysRole> roles = (List<SysRole>) event.getData();
            for (SysRole role : roles) {
                identityService.deleteGroup(role.getRoleCode());
                // 删除用户角色关联
                identityService.createGroupQuery().groupMember(role.getRoleCode()).list().forEach(group -> {
                    identityService.deleteMembership(null, group.getId());
                });
            }
        }
    }

    public void syncGroups() {
        if(true)
        {
            return;
        }
        List<SysRole> sysRoles = sysRoleService.list();

        for (SysRole sysRole : sysRoles) {
            Group camundaGroup = identityService.createGroupQuery().groupId(sysRole.getRoleCode()).singleResult();

            if (camundaGroup == null) {
                // 创建新组
                camundaGroup = identityService.newGroup(sysRole.getRoleCode());
            }

            camundaGroup.setId(sysRole.getRoleCode());
            camundaGroup.setName(sysRole.getRoleName());
            camundaGroup.setType(sysRole.getRoleCode());

            identityService.saveGroup(camundaGroup);
        }
    }
}
