package org.jeecg.modules.bpmn.service.impl;

import org.jeecg.modules.bpmn.entity.ProcessDefinition;
import org.jeecg.modules.bpmn.mapper.ProcessDefinitionMapper;
import org.jeecg.modules.bpmn.service.IProcessDefinitionService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 流程定义
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
@Service
public class ProcessDefinitionServiceImpl extends ServiceImpl<ProcessDefinitionMapper, ProcessDefinition> implements IProcessDefinitionService {

}
