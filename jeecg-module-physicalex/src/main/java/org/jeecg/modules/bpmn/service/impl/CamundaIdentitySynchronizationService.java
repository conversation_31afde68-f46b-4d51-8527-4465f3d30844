package org.jeecg.modules.bpmn.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CamundaIdentitySynchronizationService {

    @Autowired
    private CamundaUserSynchronizationService userSynchronizationService;

    @Autowired
    private CamundaGroupSynchronizationService groupSynchronizationService;

    @Autowired
    private CamundaMembershipSynchronizationService membershipSynchronizationService;

    public void syncAll() {
        groupSynchronizationService.syncGroups();
        userSynchronizationService.syncUsers();
        membershipSynchronizationService.syncMemberships();
    }
}
