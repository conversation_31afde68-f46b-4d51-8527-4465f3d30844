package org.jeecg.modules.bpmn.controller;

import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.camunda.bpm.engine.repository.DeploymentBuilder;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.bpmn.entity.ProcessDefinition;
import org.jeecg.modules.bpmn.service.IProcessDefinitionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 流程定义
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
@Api(tags="流程定义")
@RestController
@RequestMapping("/bpmn/processDefinition")
@Slf4j
public class ProcessDefinitionController extends JeecgController<ProcessDefinition, IProcessDefinitionService> {
	@Autowired
	private IProcessDefinitionService processDefinitionService;

	 @Autowired
	 private RepositoryService repositoryService;
	
	/**
	 * 分页列表查询
	 *
	 * @param processDefinition
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "流程定义-分页列表查询")
	@ApiOperation(value="流程定义-分页列表查询", notes="流程定义-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ProcessDefinition>> queryPageList(ProcessDefinition processDefinition,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ProcessDefinition> queryWrapper = QueryGenerator.initQueryWrapper(processDefinition, req.getParameterMap());
		Page<ProcessDefinition> page = new Page<ProcessDefinition>(pageNo, pageSize);
		IPage<ProcessDefinition> pageList = processDefinitionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param processDefinition
	 * @return
	 */
	@AutoLog(value = "流程定义-添加")
	@ApiOperation(value="流程定义-添加", notes="流程定义-添加")
	@RequiresPermissions("bpmn:process_definition:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ProcessDefinition processDefinition) {
		processDefinitionService.save(processDefinition);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param processDefinition
	 * @return
	 */
	@AutoLog(value = "流程定义-编辑")
	@ApiOperation(value="流程定义-编辑", notes="流程定义-编辑")
	@RequiresPermissions("bpmn:process_definition:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ProcessDefinition processDefinition) {
		processDefinitionService.updateById(processDefinition);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "流程定义-通过id删除")
	@ApiOperation(value="流程定义-通过id删除", notes="流程定义-通过id删除")
	@RequiresPermissions("bpmn:process_definition:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		processDefinitionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "流程定义-批量删除")
	@ApiOperation(value="流程定义-批量删除", notes="流程定义-批量删除")
	@RequiresPermissions("bpmn:process_definition:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.processDefinitionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "流程定义-通过id查询")
	@ApiOperation(value="流程定义-通过id查询", notes="流程定义-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProcessDefinition> queryById(@RequestParam(name="id",required=true) String id) {
		ProcessDefinition processDefinition = processDefinitionService.getById(id);
		if(processDefinition==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(processDefinition);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param processDefinition
    */
    @RequiresPermissions("bpmn:process_definition:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProcessDefinition processDefinition) {
        return super.exportXls(request, processDefinition, ProcessDefinition.class, "流程定义");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("bpmn:process_definition:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProcessDefinition.class);
    }





	 // 上传并保存流程定义
	 @PostMapping("/upload")
	 public String uploadProcessDefinition(@RequestParam("file") MultipartFile file) throws IOException {
		 byte[] bytes = file.getBytes();
		 String fileName = file.getOriginalFilename();

		 // Create a new ProcessDefinition entity
		 ProcessDefinition processDefinition = new ProcessDefinition();
		 processDefinition.setName(fileName);
		 processDefinition.setResourceName(fileName);
		 processDefinition.setBpmnBytes(bytes);

		 // Save the process definition
		 processDefinitionService.save(processDefinition);


		 return "Process definition saved with ID: " + processDefinition.getId();
	 }

	 // 部署流程定义到 Camunda 引擎
	 @PostMapping("/deploy/{id}")
	 public String deployProcessDefinition(@PathVariable Long id) {
		 ProcessDefinition entity = processDefinitionService.getById(id);
		 if (entity == null) {
			 return "Process definition not found";
		 }

		 ByteArrayInputStream inputStream = new ByteArrayInputStream(entity.getBpmnBytes());
		 DeploymentBuilder deploymentBuilder = repositoryService.createDeployment()
				 .name(entity.getName())
				 .addInputStream(entity.getResourceName(), inputStream);
		 deploymentBuilder.deploy();

		 return "Process definition deployed";
	 }

}
