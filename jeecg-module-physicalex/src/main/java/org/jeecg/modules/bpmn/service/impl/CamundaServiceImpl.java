package org.jeecg.modules.bpmn.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.identity.Group;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.bpmn.entity.HighLineDTO;
import org.jeecg.modules.bpmn.entity.HistoricalUserTask;
import org.jeecg.modules.bpmn.entity.ProcessInstanceDto;
import org.jeecg.modules.bpmn.entity.TaskDto;
import org.jeecg.modules.bpmn.service.CamundaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CamundaServiceImpl implements CamundaService {

    @Autowired
    private TaskService taskService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private RepositoryService repositoryService;

    // 获取任务列表，并包含流程变量
    @Override
    public List<TaskDto> getTasks(String assignee) {
        List<Task> tasks = taskService.createTaskQuery().initializeFormKeys().taskAssignee(assignee).orderByTaskCreateTime().desc().list();
        return tasks.stream().map(this::convertToProcessInstanceDto).collect(Collectors.toList());
    }

    @Override
    public Page<TaskDto> getCandidateGroupTasks(String assignee, Page<TaskDto> page) {
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        int offset = (current - 1) * size;

        // Get user groups
        List<String> userGroups = identityService.createGroupQuery().groupMember(assignee).list().stream().map(Group::getId).collect(Collectors.toList());
        // Query tasks with pagination
        List<Task> candidateGroupTasks = taskService.createTaskQuery().initializeFormKeys().taskCandidateGroupIn(userGroups).listPage(offset, size);
        List<TaskDto> taskDtos = candidateGroupTasks.stream().map(this::convertToProcessInstanceDto).collect(Collectors.toList());

        // Get total count
        long total = taskService.createTaskQuery().taskCandidateGroupIn(userGroups).count();

        // Set total and records in the page object
        page.setTotal(total);
        page.setRecords(taskDtos);
        return page;
    }

    @Override
    public Page<TaskDto> getAllUnclaimedTasks(String assignee, Page<TaskDto> page) {
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        int offset = (current - 1) * size;

        List<Task> unclaimedTasks = taskService.createTaskQuery().initializeFormKeys().taskCandidateUser(assignee).taskCandidateGroupIn(identityService.createGroupQuery().groupMember(assignee).list().stream().map(Group::getId).collect(Collectors.toList())).listPage(offset, size);

        List<TaskDto> taskDtos = unclaimedTasks.stream().map(this::convertToProcessInstanceDto).collect(Collectors.toList());

        long total = taskService.createTaskQuery().taskCandidateUser(assignee).count();

        page.setTotal(total);
        page.setRecords(taskDtos);
        return page;
    }

    // 获取待认领任务
    @Override
    public List<TaskDto> getAllUnclaimedTasks(String assignee) {
        // Get user groups
        List<String> userGroups = identityService.createGroupQuery().groupMember(assignee).list().stream().map(Group::getId).collect(Collectors.toList());

        // Query tasks for candidate user
        List<Task> candidateUserTasks = taskService.createTaskQuery().initializeFormKeys().taskCandidateUser(assignee).orderByTaskCreateTime().desc().list();

        // Query tasks for candidate groups
        List<Task> candidateGroupTasks = taskService.createTaskQuery().initializeFormKeys().taskCandidateGroupIn(userGroups).orderByTaskCreateTime().desc().list();

        // Combine the results and remove duplicates
        List<Task> combinedTasks = Stream.concat(candidateUserTasks.stream(), candidateGroupTasks.stream()).distinct().sorted(Comparator.comparing(Task::getCreateTime).reversed()).collect(Collectors.toList());

        // Filter out already claimed tasks (only unclaimed)
        return combinedTasks.stream().filter(task -> task.getAssignee() == null).map(this::convertToProcessInstanceDto).collect(Collectors.toList());
    }

    // 完成任务，并传递流程变量
    @Override
    public void completeTask(String taskId, Map<String, Object> variables) {
        if (variables != null && !variables.isEmpty()) {
            taskService.complete(taskId, variables);
        } else {
            taskService.complete(taskId);
        }
    }

    @Override
    public void claimTask(String taskId, String assignee) {
        taskService.claim(taskId, assignee);
    }

    // 获取流程实例列表
    @Override
    public List<ProcessInstanceDto> getProcessInstances(String businessKey) {
        List<ProcessInstance> instances = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(businessKey).list();
        return instances.stream().map(this::convertToProcessInstanceDto).collect(Collectors.toList());
    }

    // 获取已办任务列表，并支持分页
    @Override
    public Page<TaskDto> getCompletedTasks(String assignee, Page<TaskDto> page) {
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        int offset = (current - 1) * size;

        List<HistoricTaskInstance> completedTasks = historyService.createHistoricTaskInstanceQuery().taskAssignee(assignee).finished().orderByHistoricTaskInstanceEndTime().desc().listPage(offset, size);
        List<TaskDto> taskDtos = completedTasks.stream().map(this::convertHistoricToDto).collect(Collectors.toList());

        // 获取总数
        long total = historyService.createHistoricTaskInstanceQuery().taskAssignee(assignee).finished().count();

        page.setTotal(total);
        page.setRecords(taskDtos);
        return page;
    }

    @Override
    public List<HistoricalUserTask> getHistoricalUserTasks(String processInstanceId) {
        List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByHistoricTaskInstanceEndTime().desc().list();
        return historicTaskInstances.stream().map(historicTaskInstance -> {
            HistoricalUserTask historicalUserTask = new HistoricalUserTask();
            historicalUserTask.setId(historicTaskInstance.getId());
            historicalUserTask.setName(historicTaskInstance.getName());
            historicalUserTask.setAssignee(historicTaskInstance.getAssignee());
            historicalUserTask.setTaskDefinitionKey(historicTaskInstance.getTaskDefinitionKey());
            historicalUserTask.setStartTime(historicTaskInstance.getStartTime());
            historicalUserTask.setEndTime(historicTaskInstance.getEndTime());
            historicalUserTask.setVariables(historyService.createHistoricVariableInstanceQuery().taskIdIn(historicTaskInstance.getId()).list().stream().collect(Collectors.toMap(HistoricVariableInstance::getName, HistoricVariableInstance::getValue)));
            return historicalUserTask;
        }).collect(Collectors.toList());
    }

    @Override
    public ProcessInstanceDto startProcessInstance(String processDefinitionKey, String businessKey, Map<String, Object> variables) {
        ProcessInstance instance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, variables);
        return convertToProcessInstanceDto(instance);
    }

    @Override
    public HighLineDTO getHighlightNode(String processInsId) {
        HistoricProcessInstance hisProIns = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInsId).singleResult();
        //System.out.println(hisProIns.getProcessDefinitionName()+" "+hisProIns.getProcessDefinitionKey());
        //===================已完成节点
        List<HistoricActivityInstance> finished = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInsId).finished().orderByHistoricActivityInstanceStartTime().asc().list();
        Set<String> highPoint = new HashSet<>();
        finished.forEach(t -> highPoint.add(t.getActivityId()));

        //=================待完成节点
        List<HistoricActivityInstance> unfinished = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInsId).unfinished().list();
        Set<String> waitingToDo = new HashSet<>();
        unfinished.forEach(t -> waitingToDo.add(t.getActivityId()));

        //=================iDo 我执行过的
        Set<String> iDo = new HashSet<>(); //存放 高亮 我的办理节点
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<HistoricTaskInstance> taskInstanceList = historyService.createHistoricTaskInstanceQuery().taskAssignee(sysUser.getUsername()).finished().processInstanceId(processInsId).list();
        taskInstanceList.forEach(a -> iDo.add(a.getTaskDefinitionKey()));

        //===========高亮线
        Set<String> highLine2 = new HashSet<>(); //保存高亮的连线
        //获取流程定义的bpmn模型
        BpmnModelInstance bpmn = repositoryService.getBpmnModelInstance(hisProIns.getProcessDefinitionId());
        //已完成任务列表 可直接使用上面写过的
        List<HistoricActivityInstance> finishedList = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInsId).finished().orderByHistoricActivityInstanceStartTime().asc().list();
        int finishedNum = finishedList.size();
        //循环 已完成的节点
        for (int i = 0; i < finishedNum; i++) {
            HistoricActivityInstance finItem = finishedList.get(i);
            //根据 任务key 获取 bpmn元素
            ModelElementInstance domElement = bpmn.getModelElementById(finItem.getActivityId());
            //转换成 flowNode流程节点 才能获取到 输出线 和输入线
            FlowNode act = (FlowNode) domElement;
            Collection<SequenceFlow> outgoing = act.getOutgoing();
            //循环当前节点的 向下分支
            outgoing.forEach(v -> {
                String tarId = v.getTarget().getId();
                //已完成
                for (int j = 0; j < finishedNum; j++) {
                    //循环历史完成节点 和当前完成节点的向下分支比对
                    //如果当前完成任务 的结束时间 等于 下个任务的开始时间
                    HistoricActivityInstance setpFinish = finishedList.get(j);
                    String finxId = setpFinish.getActivityId();
                    if (tarId.equals(finxId)) {
                        if (finItem.getEndTime().equals(setpFinish.getStartTime())) {
                            highLine2.add(v.getId());
                        }
                    }
                }
                //待完成
                for (int j = 0; j < unfinished.size(); j++) {
                    //循环待节点 和当前完成节点的向下分支比对
                    HistoricActivityInstance setpUnFinish = unfinished.get(j);
                    String finxId = setpUnFinish.getActivityId();
                    if (tarId.equals(finxId)) {
                        if (finItem.getEndTime().equals(setpUnFinish.getStartTime())) {
                            highLine2.add(v.getId());
                        }
                    }
                }
            });
        }

        //返回结果
        HighLineDTO highLineDTO = new HighLineDTO();
        highLineDTO.setHighPoint(highPoint);
        highLineDTO.setHighLine(highLine2);
        highLineDTO.setWaitingToDo(waitingToDo);
        highLineDTO.setIDo(iDo);
        return highLineDTO;
    }

    @Override
    public ProcessInstanceDto getProcessInstanceByTaskId(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        ProcessInstance instance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        return convertToProcessInstanceDto(instance);
    }

    @Override
    public ProcessInstanceDto getProcessInstanceById(String processInstanceId) {
        ProcessInstance instance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        return convertToProcessInstanceDto(instance);
    }


    // 转换 Task 到 TaskDto，并包含流程变量
    private TaskDto convertToProcessInstanceDto(Task task) {
        //taskService.createTaskQuery().taskId(task.getId()).singleResult();

        TaskDto dto = new TaskDto();
        ;
        dto.setId(task.getId());
        dto.setName(task.getName());
        dto.setFormKey(task.getFormKey());
        dto.setAssignee(task.getAssignee());
        dto.setProcessInstanceId(task.getProcessInstanceId());
        dto.setProcessDefinitionId(task.getProcessDefinitionId());
        dto.setDescription(task.getDescription()); // Assign description
        dto.setPriority(task.getPriority());
        dto.setDueDate(task.getDueDate());
        dto.setCreateTime(task.getCreateTime());
        dto.setOwner(task.getOwner());
        dto.setDelegationState(task.getDelegationState() != null ? task.getDelegationState().toString() : null);

        // Get process variables
        Map<String, Object> variables = taskService.getVariables(task.getId());
        dto.setVariables(variables);

        return dto;
    }

    // 转换 ProcessInstance 到 ProcessInstanceDto
    private ProcessInstanceDto convertToProcessInstanceDto(ProcessInstance instance) {
        ProcessInstanceDto dto = new ProcessInstanceDto();
        dto.setId(instance.getId());
        dto.setBusinessKey(instance.getBusinessKey());
        dto.setProcessDefinitionId(instance.getProcessDefinitionId());
        //dto.setProcessDefinitionKey(instance.getProcessDefinitionId());
        dto.setTenantId(instance.getTenantId());
        dto.setSuspended(String.valueOf(instance.isSuspended()));
        return dto;
    }

    // 转换 HistoricTaskInstance 到 TaskDto，并包含流程变量
    private TaskDto convertHistoricToDto(HistoricTaskInstance historicTask) {
        TaskDto dto = new TaskDto();
        dto.setId(historicTask.getId());
        dto.setName(historicTask.getName());
        dto.setAssignee(historicTask.getAssignee());
        dto.setProcessInstanceId(historicTask.getProcessInstanceId());
        dto.setProcessDefinitionId(historicTask.getProcessDefinitionId());
        dto.setDescription(historicTask.getDescription()); // Assign description
        dto.setPriority(historicTask.getPriority());
        dto.setDueDate(historicTask.getDueDate());
        dto.setCreateTime(historicTask.getStartTime());
        dto.setOwner(historicTask.getOwner());

        // 获取流程变量（只能获取历史变量，如果需要获取更多信息，请根据需求调整）
        List<HistoricVariableInstance> variablesList = historyService.createHistoricVariableInstanceQuery().processInstanceId(historicTask.getProcessInstanceId()).list();
        if(variablesList != null && !variablesList.isEmpty()) {
            Map<String, Object> variables = new HashMap<>();
            for (HistoricVariableInstance variable : variablesList) {
                if(StringUtils.isNotBlank(variable.getName()))
                {
                    variables.put(variable.getName(), variable.getValue());
                }
            }
            dto.setVariables(variables);
        }

        return dto;
    }
}

