package org.jeecg.modules.bpmn.listener;

import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import java.util.Date;

public class ClaimTimeListener implements TaskListener {
    @Override
    public void notify(DelegateTask delegateTask) {
        if ("assignment".equals(delegateTask.getEventName())) {
            delegateTask.setVariable("claimTime", new Date());
        }
    }
}

