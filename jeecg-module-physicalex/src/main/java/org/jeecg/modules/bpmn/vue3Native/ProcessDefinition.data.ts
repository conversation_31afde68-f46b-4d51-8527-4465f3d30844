import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: "center",
    dataIndex: 'name'
  },
  {
    title: '流程Key',
    align: "center",
    dataIndex: 'processKey'
  },
  {
    title: '资源',
    align: "center",
    dataIndex: 'resourceName'
  },
  {
    title: '创建人账号',
    align: "center",
    dataIndex: 'createBy'
  },
  {
    title: '创建时间',
    align: "center",
    dataIndex: 'createTime'
  },
  {
    title: '更新人账号',
    align: "center",
    dataIndex: 'updateBy'
  },
  {
    title: '更新时间',
    align: "center",
    dataIndex: 'updateTime'
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '名称',order: 0,view: 'text', type: 'string',},
  processKey: {title: '流程Key',order: 1,view: 'text', type: 'string',},
  resourceName: {title: '资源',order: 2,view: 'text', type: 'string',},
  createBy: {title: '创建人账号',order: 3,view: 'text', type: 'string',},
  createTime: {title: '创建时间',order: 4,view: 'datetime', type: 'string',},
  updateBy: {title: '更新人账号',order: 5,view: 'text', type: 'string',},
  updateTime: {title: '更新时间',order: 6,view: 'datetime', type: 'string',},
};
