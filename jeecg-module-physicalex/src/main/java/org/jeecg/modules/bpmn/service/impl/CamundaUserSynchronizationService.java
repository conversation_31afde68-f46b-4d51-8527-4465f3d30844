package org.jeecg.modules.bpmn.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.identity.User;
import org.jeecg.common.message.SysUserEvent;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.mapper.XgSystemMapper;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CamundaUserSynchronizationService {

    @Autowired
    private IdentityService identityService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private XgSystemMapper xgSystemMapper;


    @EventListener
    public void handleSysUserEvent(SysUserEvent event) {
        if(true)
        {
            return;
        }
        //System.out.println("接收到SysUserRoleEvent: " + event.getMessage());
        // 处理消息逻辑
        String message = event.getMessage();
        if (message.equals("add")) {
            SysUser sysUser = (SysUser) event.getData();
            User camundaUser = identityService.createUserQuery().userId(sysUser.getUsername()).singleResult();

            if (camundaUser == null) {
                // 创建新用户
                camundaUser = identityService.newUser(sysUser.getUsername());
            }

            camundaUser.setFirstName(sysUser.getRealname());
            camundaUser.setLastName("");
            camundaUser.setEmail(sysUser.getEmail());
            // 设置默认密码或处理密码逻辑
            camundaUser.setPassword("123456");

            identityService.saveUser(camundaUser);
        } else if (message.equals("delete")) {
            List<SysUser> sysUsers = (List<SysUser>) event.getData();
            for (SysUser sysUser : sysUsers) {
                identityService.deleteUser(sysUser.getUsername());
                identityService.createGroupQuery().groupMember(sysUser.getUsername()).list().forEach(group -> {
                    identityService.deleteMembership(sysUser.getUsername(), group.getId());
                });
            }
        }
    }

    public void syncUsers() {
        if(true)
        {
            return;
        }
        List<SysUser> sysUsers = xgSystemMapper.listAllUser();
        for (SysUser sysUser : sysUsers) {
            if (StringUtils.equals(sysUser.getUsername(), "272327")) {
                System.out.println("272327");
            }
            User camundaUser = identityService.createUserQuery().userId(sysUser.getUsername()).singleResult();

            if (camundaUser == null) {
                // 创建新用户
                camundaUser = identityService.newUser(sysUser.getUsername());
            }

            camundaUser.setFirstName(sysUser.getRealname());
            camundaUser.setLastName("");
            camundaUser.setEmail(sysUser.getEmail());
            // 设置默认密码或处理密码逻辑
            camundaUser.setPassword("123456");

            identityService.saveUser(camundaUser);
        }
    }
}
