package org.jeecg.modules.bpmn.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.bpmn.entity.HighLineDTO;
import org.jeecg.modules.bpmn.entity.HistoricalUserTask;
import org.jeecg.modules.bpmn.entity.ProcessInstanceDto;
import org.jeecg.modules.bpmn.entity.TaskDto;

import java.util.List;
import java.util.Map;

public interface CamundaService {

    List<TaskDto> getTasks(String assignee);

    Page<TaskDto> getCandidateGroupTasks(String assignee, Page<TaskDto> page);

    void completeTask(String taskId, Map<String, Object> variables);

    void claimTask(String taskId, String assignee);

    List<ProcessInstanceDto> getProcessInstances(String businessKey);

    // 获取已办任务列表，支持分页
    Page<TaskDto> getCompletedTasks(String assignee, Page<TaskDto> page);

    Page<TaskDto> getAllUnclaimedTasks(String assignee, Page<TaskDto> page);

    List<TaskDto> getAllUnclaimedTasks(String assignee);

    List<HistoricalUserTask> getHistoricalUserTasks(String processInstanceId);
    // 启动流程实例
    ProcessInstanceDto startProcessInstance(String processDefinitionKey, String businessKey, Map<String, Object> variables);

    HighLineDTO getHighlightNode(String processInsId);

    ProcessInstanceDto getProcessInstanceByTaskId(String taskId);

    ProcessInstanceDto getProcessInstanceById(String processInstanceId);
}
