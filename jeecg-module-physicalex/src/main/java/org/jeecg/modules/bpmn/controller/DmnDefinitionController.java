package org.jeecg.modules.bpmn.controller;

import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.repository.DeploymentBuilder;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.bpmn.entity.DmnDefinition;
import org.jeecg.modules.bpmn.service.IDmnDefinitionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: DMN定义
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
@Api(tags="DMN定义")
@RestController
@RequestMapping("/bpmn/dmnDefinition")
@Slf4j
public class DmnDefinitionController extends JeecgController<DmnDefinition, IDmnDefinitionService> {
	@Autowired
	private IDmnDefinitionService dmnDefinitionService;
	 @Autowired
	 private RepositoryService repositoryService;


	 /**
	 * 分页列表查询
	 *
	 * @param dmnDefinition
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "DMN定义-分页列表查询")
	@ApiOperation(value="DMN定义-分页列表查询", notes="DMN定义-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<DmnDefinition>> queryPageList(DmnDefinition dmnDefinition,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<DmnDefinition> queryWrapper = QueryGenerator.initQueryWrapper(dmnDefinition, req.getParameterMap());
		Page<DmnDefinition> page = new Page<DmnDefinition>(pageNo, pageSize);
		IPage<DmnDefinition> pageList = dmnDefinitionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param dmnDefinition
	 * @return
	 */
	@AutoLog(value = "DMN定义-添加")
	@ApiOperation(value="DMN定义-添加", notes="DMN定义-添加")
	@RequiresPermissions("bpmn:dmn_definition:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody DmnDefinition dmnDefinition) {
		dmnDefinitionService.save(dmnDefinition);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param dmnDefinition
	 * @return
	 */
	@AutoLog(value = "DMN定义-编辑")
	@ApiOperation(value="DMN定义-编辑", notes="DMN定义-编辑")
	@RequiresPermissions("bpmn:dmn_definition:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody DmnDefinition dmnDefinition) {
		dmnDefinitionService.updateById(dmnDefinition);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "DMN定义-通过id删除")
	@ApiOperation(value="DMN定义-通过id删除", notes="DMN定义-通过id删除")
	@RequiresPermissions("bpmn:dmn_definition:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		dmnDefinitionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "DMN定义-批量删除")
	@ApiOperation(value="DMN定义-批量删除", notes="DMN定义-批量删除")
	@RequiresPermissions("bpmn:dmn_definition:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.dmnDefinitionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "DMN定义-通过id查询")
	@ApiOperation(value="DMN定义-通过id查询", notes="DMN定义-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<DmnDefinition> queryById(@RequestParam(name="id",required=true) String id) {
		DmnDefinition dmnDefinition = dmnDefinitionService.getById(id);
		if(dmnDefinition==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(dmnDefinition);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param dmnDefinition
    */
    @RequiresPermissions("bpmn:dmn_definition:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DmnDefinition dmnDefinition) {
        return super.exportXls(request, dmnDefinition, DmnDefinition.class, "DMN定义");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("bpmn:dmn_definition:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DmnDefinition.class);
    }

	 // 上传并保存 DMN 定义
	 @PostMapping("/upload")
	 public String uploadDmnDefinition(@RequestParam("file") MultipartFile file) throws IOException {
		 byte[] bytes = file.getBytes();
		 String fileName = file.getOriginalFilename();

		 // 保存到数据库
		 DmnDefinition entity = new DmnDefinition();
		 entity.setName(fileName);
		 entity.setResourceName(fileName);
		 entity.setDmnBytes(bytes);
		 entity.setDecisionKey("your_decision_key"); // 设置 decisionKey，根据需要设置

		 dmnDefinitionService.save(entity);

		 return "DMN definition saved with ID: " + entity.getId();
	 }

	 // 部署 DMN 定义到 Camunda 引擎
	 @PostMapping("/deploy/{id}")
	 public String deployDmnDefinition(@PathVariable Long id) {
		 DmnDefinition entity = dmnDefinitionService.getById(id);
		 if (entity == null) {
			 return "DMN definition not found";
		 }

		 ByteArrayInputStream inputStream = new ByteArrayInputStream(entity.getDmnBytes());
		 DeploymentBuilder deploymentBuilder = repositoryService.createDeployment()
				 .name(entity.getName())
				 .addInputStream(entity.getResourceName(), inputStream);
		 deploymentBuilder.deploy();

		 return "DMN definition deployed";
	 }

}
