package org.jeecg.modules.bpmn.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.IdentityService;
import org.jeecg.common.message.SysUserRoleEvent;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysRoleService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.IXgSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CamundaMembershipSynchronizationService {

    @Autowired
    private IdentityService identityService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private IXgSystemService xgSystemService;

    @EventListener
    public void handleSysUserRoleEvent(SysUserRoleEvent event) {
        //System.out.println("接收到SysUserRoleEvent: " + event.getMessage());
        // 处理消息逻辑
        if(true)
        {
            return;
        }

        String message = event.getMessage();
        if (StringUtils.equals(message, "edit")) {
            JSONObject data = (JSONObject) event.getData();
            List<String> roleIds = (List<String>) data.get("roleIds");
            String userId = data.getString("userId");

            SysUser sysUser = sysUserService.getById(userId);
            QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", roleIds);
            List<SysRole> sysRoles = sysRoleService.list(queryWrapper);

            //先删除用户的所有角色
            identityService.createGroupQuery().groupMember(sysUser.getUsername()).list().forEach(group -> {
                identityService.deleteMembership(sysUser.getUsername(), group.getId());
            });
            for (SysRole role : sysRoles) {
                //再添加用户的角色
                identityService.createMembership(sysUser.getUsername(), role.getRoleCode());
            }
        }
    }

    public void syncMemberships() {
        if(true)
        {
            return;
        }

        xgSystemService.queryUserRoleRelation().forEach(map -> {
            String username = map.get("username");
            String roleCode = map.get("role_code");
            if (StringUtils.isNotBlank(username) && StringUtils.isNotBlank(roleCode)) {
                // 检查用户是否已经在组中
                boolean alreadyMember = identityService.createUserQuery().userId(username).memberOfGroup(roleCode).count() > 0;
                if (!alreadyMember) {
                    identityService.createMembership(username, roleCode);
                }
            }
        });


      /*  List<SysUser> sysUsers = sysUserService.list();
        for (SysUser sysUser : sysUsers) {
            Set<String> roleIds = xgSystemService.queryUserRolesById(sysUser.getId());

            for (String roleId : roleIds) {
                // 检查用户是否已经在组中
                boolean alreadyMember = identityService.createUserQuery().userId(sysUser.getId()).memberOfGroup(roleId).count() > 0;
                if (!alreadyMember) {
                    identityService.createMembership(sysUser.getId(), roleId);
                }
            }
        }*/
    }
}

