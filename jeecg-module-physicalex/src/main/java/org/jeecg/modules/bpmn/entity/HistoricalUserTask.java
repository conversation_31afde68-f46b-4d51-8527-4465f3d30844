package org.jeecg.modules.bpmn.entity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class HistoricalUserTask {
    private String id;
    private String name;
    private String assignee;
    private String taskDefinitionKey;
    private Date startTime;
    private Date endTime;
    private Map<String, Object> variables;
}
