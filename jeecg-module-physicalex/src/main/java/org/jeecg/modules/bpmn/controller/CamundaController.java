package org.jeecg.modules.bpmn.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.bpmn.entity.HighLineDTO;
import org.jeecg.modules.bpmn.entity.HistoricalUserTask;
import org.jeecg.modules.bpmn.entity.ProcessInstanceDto;
import org.jeecg.modules.bpmn.entity.TaskDto;
import org.jeecg.modules.bpmn.service.CamundaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "Camunda API")
@RestController
@RequestMapping("/camunda")
public class CamundaController {

    @Autowired
    private CamundaService camundaService;

    @ApiOperation(value = "获取任务列表", notes = "根据分配人获取任务列表，并包含流程变量")
    @GetMapping("/tasks")
    public Result<?> getTasks(@RequestParam String assignee) {
        List<TaskDto> tasks = camundaService.getTasks(assignee);
        return Result.OK(tasks);
    }

    @ApiOperation(value = "获取待办任务列表", notes = "获取待办任务列表")
    @GetMapping("/unclaimed-tasks-assignee")
    public Result<?> getUnclaimedTasks(@RequestParam String assignee, @RequestParam(defaultValue = "1") int current, @RequestParam(defaultValue = "10") int size) {
        Page<TaskDto> page = new Page<>(current, size);
        Page<TaskDto> unclaimedTasks = camundaService.getAllUnclaimedTasks(assignee, page);
        return Result.OK(unclaimedTasks);
    }

    @ApiOperation(value = "获取待办任务列表", notes = "获取待办任务列表")
    @GetMapping("/unclaimed-tasks")
    public Result<?> getAllUnclaimedTasks(@RequestParam String assignee) {

        List<TaskDto> unclaimedTasks = camundaService.getAllUnclaimedTasks(assignee);
        return Result.OK(unclaimedTasks);
    }


    @ApiOperation(value = "获取已办任务列表", notes = "根据分配人获取已办任务列表，并包含流程变量，支持分页")
    @GetMapping("/completed-tasks")
    public Result<?> getCompletedTasks(@RequestParam String assignee, @RequestParam(defaultValue = "1") int current, @RequestParam(defaultValue = "10") int size) {
        Page<TaskDto> page = new Page<>(current, size);
        Page<TaskDto> completedTasks = camundaService.getCompletedTasks(assignee, page);
        return Result.OK(completedTasks);
    }


    @ApiOperation(value = "完成任务", notes = "根据任务ID完成任务，并可传递流程变量")
    @PostMapping("/tasks/{taskId}/complete")
    public Result<?> completeTask(@PathVariable String taskId, @RequestBody(required = false) Map<String, Object> variables) {
        camundaService.completeTask(taskId, variables);
        return Result.OK();
    }

    @ApiOperation(value = "认领任务", notes = "根据任务ID认领任务")
    @GetMapping("/tasks/{taskId}/claim")
    public Result<?> claimTask(@PathVariable String taskId, @RequestParam String assignee) {
        camundaService.claimTask(taskId, assignee);
        return Result.OK();
    }

    @ApiOperation(value = "获取流程实例列表", notes = "根据业务关键字获取流程实例列表")
    @GetMapping("/process-instances")
    public Result<?> getProcessInstances(@RequestParam String businessKey) {
        List<ProcessInstanceDto> instances = camundaService.getProcessInstances(businessKey);
        return Result.OK(instances);
    }

    @ApiOperation(value = "获取流程实例高亮", notes = "获取流程实例高亮")
    @GetMapping("/process-highlight-node")
    public Result<?> getHighlightANode(@RequestParam String processInsId) {
        HighLineDTO instancesDto = camundaService.getHighlightNode(processInsId);
        return Result.OK(instancesDto);
    }

    /**
     * 获取指定流程实例的历史用户任务，按完成时间倒序排列
     *
     * @param processInstanceId 流程实例ID
     * @return 历史用户任务列表
     */
    @GetMapping("/historical-user-tasks")
    public Result<?> getHistoricalUserTasks(@RequestParam String processInstanceId) {

        List<HistoricalUserTask> tasks = camundaService.getHistoricalUserTasks(processInstanceId);
        return Result.OK(tasks);
    }

    @GetMapping("/process-instance")
    public Result<?> getProcessInstance(@RequestParam String processInstanceId) {

        ProcessInstanceDto processInstanceDto = camundaService.getProcessInstanceById(processInstanceId);
        return Result.OK(processInstanceDto);
    }


    // 您可以根据需要添加更多的 API 端点
}

