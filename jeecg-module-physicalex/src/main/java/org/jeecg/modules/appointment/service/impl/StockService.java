package org.jeecg.modules.appointment.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * 扣库存
 *
 * <AUTHOR>
 */
@Service
public class StockService {
    Logger logger = LoggerFactory.getLogger(StockService.class);
    @Autowired
    private JdbcTemplate jdbcTemplate;
    /**
     * 不限库存
     */
    public static final long UNINITIALIZED_STOCK = -3L;

    /**
     * Redis 客户端
     */
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 执行扣库存的脚本
     */
    public static final String STOCK_LUA;

    static {
        /**
         *
         * @desc 扣减库存Lua脚本
         * 库存（stock）-1：表示不限库存
         * 库存（stock）0：表示没有库存
         * 库存（stock）大于0：表示剩余库存
         *
         * @params 库存key
         * @return
         * 		-3:库存未初始化
         * 		-2:库存不足
         * 		-1:不限库存
         * 		大于等于0:剩余库存（扣减之后剩余的库存）
         * 	    redis缓存的库存(value)是-1表示不限库存，直接返回1
         */
        StringBuilder sb = new StringBuilder();
        sb.append("if (redis.call('exists', KEYS[1]) == 1) then");
        sb.append("    local stock = tonumber(redis.call('get', KEYS[1]));");
        sb.append("    local num = tonumber(ARGV[1]);");
        sb.append("    if (stock == -1) then");
        sb.append("        return -1;");
        sb.append("    end;");
        sb.append("    if (stock >= num) then");
        sb.append("        return redis.call('incrby', KEYS[1], 0 - num);");
        sb.append("    end;");
        sb.append("    return -2;");
        sb.append("end;");
        sb.append("return -3;");
        STOCK_LUA = sb.toString();
    }

    /**
     * @param key    库存key
     * @param expire 库存有效时间,单位秒
     * @param num    扣减数量
     * @return -2:库存不足; -1:不限库存; 大于等于0:扣减库存之后的剩余库存
     */
    public long stock(String key, long expire, int num) {
        long stock = stock(key, num);
        // 初始化库存
        if (stock == UNINITIALIZED_STOCK) {

            // 双重验证，避免并发时重复回源到数据库
            stock = stock(key, num);
            if (stock == UNINITIALIZED_STOCK) {
                // 获取初始化库存
                final int initStock = getScheduleAvailableCount(key);
                // 将库存设置到redis
                redisTemplate.opsForValue().set(key, initStock, expire, TimeUnit.SECONDS);
                // 调一次扣库存的操作
                stock = stock(key, num);
            }
        }

        return stock;
    }

    public int initStock(String key) {

        // 获取初始化库存
        final int initStock = getScheduleAvailableCount(key);
        // 将库存设置到redis,key为scheduleId
        redisTemplate.opsForValue().set(key, initStock, 86400, TimeUnit.SECONDS);

        return initStock;
    }

    /**
     * 加库存(还原库存)
     *
     * @param key 库存key
     * @param num 库存数量
     * @return
     */
    public long addStock(String key, int num) {

        return addStock(key, null, num);
    }

    /**
     * 加库存
     *
     * @param key    库存key
     * @param expire 过期时间（秒）
     * @param num    库存数量
     * @return
     */
    public long addStock(String key, Long expire, int num) {
        boolean hasKey = redisTemplate.hasKey(key);
        // 判断key是否存在，存在就直接更新
        if (hasKey) {
            return redisTemplate.opsForValue().increment(key, num);
        }

        Assert.notNull(expire, "初始化库存失败，库存过期时间不能为null");
        // 获取到锁后再次判断一下是否有key
        hasKey = redisTemplate.hasKey(key);
        if (!hasKey) {
            // 初始化库存
            redisTemplate.opsForValue().set(key, num, expire, TimeUnit.SECONDS);
        }

        return num;
    }

    /**
     * 获取库存
     *
     * @param key 库存key
     * @return -1:不限库存; 大于等于0:剩余库存
     */
    public int getStock(String key) {

        boolean hasKey = redisTemplate.hasKey(key);
        // 判断key是否存在，存在就直接更新
        if (hasKey) {
            return (Integer) redisTemplate.opsForValue().get(key);
        } else {
            return initStock(key);
        }
    }

    /**
     * 扣库存
     *
     * @param key 库存key
     * @param num 扣减库存数量
     * @return 扣减之后剩余的库存【-3:库存未初始化; -2:库存不足; -1:不限库存; 大于等于0:扣减库存之后的剩余库存】
     */
    private Long stock(String key, int num) {
        // 指定 lua 脚本，并且指定返回值类型
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(STOCK_LUA,Long.class);
        // 参数一：redisScript，参数二：key列表，参数三：arg（可多个）

        return redisTemplate.execute(redisScript, Collections.singletonList(key),num);
    }

    public Integer getScheduleAvailableCount(String scheduleId) {

        Integer limitAmount = 0;
        try {
            limitAmount = jdbcTemplate.queryForObject("select limit_amount from appointment_schedule where id=?", Integer.class, scheduleId);
        } catch (Exception e) {
            limitAmount = 0;
        }

        Integer appontmentCount = jdbcTemplate.queryForObject("select count(1) from customer_order where schedule_id=? and status!='CANCELED' and status!='REFUNDED'", Integer.class, scheduleId);

        return limitAmount - appontmentCount >= 0 ? limitAmount - appontmentCount : 0;
    }

}
