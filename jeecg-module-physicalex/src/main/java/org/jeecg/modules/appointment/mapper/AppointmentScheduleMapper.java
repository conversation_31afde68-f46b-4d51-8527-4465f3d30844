package org.jeecg.modules.appointment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.appointment.entity.AppointmentSchedule;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * @Description: 预约排班
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
public interface AppointmentScheduleMapper extends BaseMapper<AppointmentSchedule> {

    List<AppointmentSchedule> getSchedule4Day(@Param("day") LocalDate day);

    List<AppointmentSchedule> getValidScheduleByTpl(@Param("tenantId") String tenantId, @Param("tplId") String tplId);

    List<AppointmentSchedule> getValidScheduleByTpls(@Param("tenantId") String tenantId, @Param("tplIds") List<String> tplIds);

    void updateValidScheduleByTpls(@Param("limitAmout") Integer limitAmount, @Param("enableFlag") String enableFlag,@Param("startTime") LocalTime startTime, @Param("endTime")LocalTime endTime, @Param("tplIds") List<String> tplIds);

    List<AppointmentSchedule> getValidScheduleBySetting(@Param("tenantId") String tenantId, @Param("settingId") String settingId);
}
