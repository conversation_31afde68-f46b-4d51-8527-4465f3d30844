package org.jeecg.modules.appointment.uid;


import org.jeecg.modules.appointment.uid.impl.CachedUidGenerator;
import org.jeecg.modules.appointment.uid.impl.DefaultUidGenerator;
import org.jeecg.modules.appointment.uid.worker.DisposableWorkerIdAssigner2;
import org.jeecg.modules.appointment.uid.worker.WorkerIdAssigner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration("idGeneratorConfig2")
public class IdGeneratorConfig {

    @Bean("disposableWorkerIdAssigner")
    public WorkerIdAssigner disposableWorkerIdAssigner()
    {
        return new DisposableWorkerIdAssigner2();
    }

    @Bean("uidGenerator")
    public CachedUidGenerator cachedUidGenerator() {
        CachedUidGenerator generator = new CachedUidGenerator();
        generator.setWorkerIdAssigner(this.disposableWorkerIdAssigner());
        return generator;
    }


    public DefaultUidGenerator defaultUidGenerator() {
        DefaultUidGenerator generator = new DefaultUidGenerator();
        generator.setWorkerIdAssigner(this.disposableWorkerIdAssigner());
        generator.setTimeBits(29);
        generator.setWorkerBits(21);
        generator.setSeqBits(13);
        generator.setEpochStr("epochStr");
        return generator;
    }

}
