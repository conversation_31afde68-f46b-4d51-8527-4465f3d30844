package org.jeecg.modules.appointment.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.appointment.service.IAppointmentScheduleService;
import org.jeecg.modules.appointment.service.ICompanyAppointmentService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoSendCompanyAppointNotifyJob implements Job {

    @Autowired
    private ICompanyAppointmentService companyAppointmentService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                companyAppointmentService.executeNotify();
            } catch (Exception e) {
                log.error("团检接洽业务的短信通知失败！", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("团检接洽业务的短信通知任务正在执行中!");
        }
    }
}