package org.jeecg.modules.appointment.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: appointment_unavailable_config
 * @Author: jeecg-boot
 * @Date:   2025-06-09
 * @Version: V1.0
 */
@Data
@TableName("appointment_unavailable_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="appointment_unavailable_config对象", description="appointment_unavailable_config")
public class AppointmentUnavailableConfig implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**不可约的日期*/
	@Excel(name = "不可约的日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "不可约的日期")
    private LocalDate unavailableDate;
	/**不可约开始时间*/
	@Excel(name = "不可约开始时间", width = 15)
    @ApiModelProperty(value = "不可约开始时间")
    private LocalTime startTime;
	/**不可约结束时间*/
	@Excel(name = "不可约结束时间", width = 15)
    @ApiModelProperty(value = "不可约结束时间")
    private LocalTime endTime;
	/**不可约原因*/
	@Excel(name = "不可约原因", width = 15)
    @ApiModelProperty(value = "不可约原因")
    private java.lang.String reason;
}
