package org.jeecg.modules.appointment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.appointment.entity.AppointmentSetting;
import org.jeecg.modules.appointment.mapper.AppointmentSettingMapper;
import org.jeecg.modules.appointment.mapper.ScheduleTplMapper;
import org.jeecg.modules.appointment.service.IAppointmentSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: 检测设置
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Service
public class AppointmentSettingServiceImpl extends ServiceImpl<AppointmentSettingMapper, AppointmentSetting> implements IAppointmentSettingService {

    @Autowired
    private AppointmentSettingMapper appointmentSettingMapper;
    @Autowired
    private ScheduleTplMapper scheduleTplMapper;

    @Override
    @Transactional
    public void delMain(String id) {
        scheduleTplMapper.deleteByMainId(id);
        appointmentSettingMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            scheduleTplMapper.deleteByMainId(id.toString());
            appointmentSettingMapper.deleteById(id);
        }
    }

}
