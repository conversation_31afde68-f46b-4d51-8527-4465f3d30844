package org.jeecg.modules.appointment.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: company_appointment
 * @Author: jeecg-boot
 * @Date: 2024-10-28
 * @Version: V1.0
 */
@Data
@TableName("company_appointment")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "company_appointment对象", description = "company_appointment")
public class CompanyAppointment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * customerId
     */
    @Excel(name = "customerId", width = 15)
    @ApiModelProperty(value = "customerId")
    private java.lang.String customerId;
    /**
     * openId
     */
    @Excel(name = "openId", width = 15)
    @ApiModelProperty(value = "openId")
    private java.lang.String openId;
    /**
     * 预约单位
     */
    @Excel(name = "预约单位", width = 15)
    @ApiModelProperty(value = "预约单位")
    private java.lang.String companyName;
    /**
     * 体检人数
     */
    @Excel(name = "体检人数", width = 15)
    @ApiModelProperty(value = "体检人数")
    private java.lang.Integer examCount;
    /**
     * 人均预算
     */
    @Excel(name = "人均预算", width = 15)
    @ApiModelProperty(value = "人均预算")
    private java.lang.String pepoleBudget;
    /**
     * 联系人
     */
    @Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    private java.lang.String contactName;
    /**
     * 联系电话
     */
    @Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private java.lang.String contactPhone;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
    /**
     * createBy
     */
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
    /**
     * updateBy
     */
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;
    /**
     * 通知状态
     */
    @ApiModelProperty(value = "通知状态")
    private String notifyStatus;
    /**
     * 处理状态
     */
    @ApiModelProperty(value = "处理状态")
    private String dealStatus;

    private String remark;
}
