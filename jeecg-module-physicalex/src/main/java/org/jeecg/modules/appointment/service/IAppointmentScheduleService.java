package org.jeecg.modules.appointment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.appointment.entity.AppointmentSchedule;
import org.jeecg.modules.appointment.exception.AppointmentException;
//import org.jeecg.modules.order.entity.DaySchedule;
import org.jeecg.modules.appointment.entity.DaySchedule;
import org.jeecg.modules.appointment.entity.ScheduleTpl;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * @Description: 预约排班
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
public interface IAppointmentScheduleService extends IService<AppointmentSchedule> {

    void generateSchedule(String tenantId,String settingId) throws AppointmentException;

    void saveScheduleByTpl(ScheduleTpl scheduleTpl) throws AppointmentException;

    void generateScheduleBySetting(String settingId);

    void updateSchedule(String tenantId) throws AppointmentException;

    void updateScheduleBySetting(String tenantId, String settingId);

    void updateValidScheduleByTpl(String tenantId, Integer limitAmount, String enableFlag, LocalTime startTime, LocalTime endTime, String... tplId);

    List<AppointmentSchedule> getValidScheduleBySetting(String tenantId, String settingId);

    List<AppointmentSchedule> getValidScheduleByTpl(String tenantId, String... tplId);

    List<DaySchedule> getAllSchedule(String tenantId);

    List<AppointmentSchedule> getDaySchedule(String tenantId, LocalDate day);

    List<AppointmentSchedule> getSchedule4Day(LocalDate date);

    Integer getScheduleAvailableCount(AppointmentSchedule schedule);

    Integer getScheduleAvailableCount(String scheduleId);

    void batchUpdateEnableFlag(List<String> idList, String enableFlag);
}
