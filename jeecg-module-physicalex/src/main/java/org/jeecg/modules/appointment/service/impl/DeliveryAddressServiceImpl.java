package org.jeecg.modules.appointment.service.impl;

import org.jeecg.modules.appointment.entity.DeliveryAddress;
import org.jeecg.modules.appointment.mapper.DeliveryAddressMapper;
import org.jeecg.modules.appointment.service.IDeliveryAddressService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: delivery_address
 * @Author: jeecg-boot
 * @Date:   2024-10-11
 * @Version: V1.0
 */
@Service
public class DeliveryAddressServiceImpl extends ServiceImpl<DeliveryAddressMapper, DeliveryAddress> implements IDeliveryAddressService {

}
