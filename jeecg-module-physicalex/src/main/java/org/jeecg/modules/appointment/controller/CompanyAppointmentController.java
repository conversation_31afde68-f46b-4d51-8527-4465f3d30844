package org.jeecg.modules.appointment.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.appointment.entity.CompanyAppointment;
import org.jeecg.modules.appointment.service.ICompanyAppointmentService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: company_appointment
 * @Author: jeecg-boot
 * @Date:   2024-10-28
 * @Version: V1.0
 */
@Api(tags="company_appointment")
@RestController
@RequestMapping("/appointment/companyAppointment")
@Slf4j
public class CompanyAppointmentController extends JeecgController<CompanyAppointment, ICompanyAppointmentService> {
	@Autowired
	private ICompanyAppointmentService companyAppointmentService;
	
	/**
	 * 分页列表查询
	 *
	 * @param companyAppointment
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "company_appointment-分页列表查询")
	@ApiOperation(value="company_appointment-分页列表查询", notes="company_appointment-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CompanyAppointment>> queryPageList(CompanyAppointment companyAppointment,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CompanyAppointment> queryWrapper = QueryGenerator.initQueryWrapper(companyAppointment, req.getParameterMap());
		Page<CompanyAppointment> page = new Page<CompanyAppointment>(pageNo, pageSize);
		IPage<CompanyAppointment> pageList = companyAppointmentService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param companyAppointment
	 * @return
	 */
	@AutoLog(value = "company_appointment-添加")
	@ApiOperation(value="company_appointment-添加", notes="company_appointment-添加")
//	@RequiresPermissions("wx.appointment:company_appointment:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CompanyAppointment companyAppointment) {
		companyAppointmentService.save(companyAppointment);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param companyAppointment
	 * @return
	 */
	@AutoLog(value = "company_appointment-编辑")
	@ApiOperation(value="company_appointment-编辑", notes="company_appointment-编辑")
//	@RequiresPermissions("wx.appointment:company_appointment:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CompanyAppointment companyAppointment) {
		companyAppointmentService.updateById(companyAppointment);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "company_appointment-通过id删除")
	@ApiOperation(value="company_appointment-通过id删除", notes="company_appointment-通过id删除")
//	@RequiresPermissions("wx.appointment:company_appointment:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		companyAppointmentService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "company_appointment-批量删除")
	@ApiOperation(value="company_appointment-批量删除", notes="company_appointment-批量删除")
//	@RequiresPermissions("wx.appointment:company_appointment:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.companyAppointmentService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "company_appointment-通过id查询")
	@ApiOperation(value="company_appointment-通过id查询", notes="company_appointment-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CompanyAppointment> queryById(@RequestParam(name="id",required=true) String id) {
		CompanyAppointment companyAppointment = companyAppointmentService.getById(id);
		if(companyAppointment==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(companyAppointment);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param companyAppointment
    */
//    @RequiresPermissions("wx.appointment:company_appointment:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CompanyAppointment companyAppointment) {
        return super.exportXls(request, companyAppointment, CompanyAppointment.class, "company_appointment");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
//    @RequiresPermissions("wx.appointment:company_appointment:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CompanyAppointment.class);
    }

}
