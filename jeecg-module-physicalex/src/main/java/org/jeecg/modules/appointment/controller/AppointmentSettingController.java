package org.jeecg.modules.appointment.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.appointment.entity.AppointmentSetting;
import org.jeecg.modules.appointment.entity.ScheduleTpl;
import org.jeecg.modules.appointment.exception.AppointmentException;
import org.jeecg.modules.appointment.service.IAppointmentScheduleService;
import org.jeecg.modules.appointment.service.IAppointmentSettingService;
import org.jeecg.modules.appointment.service.IScheduleTplService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 检测设置
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Api(tags = "检测设置")
@RestController
@RequestMapping("/appointment/appointmentSetting")
@Slf4j
public class AppointmentSettingController extends JeecgController<AppointmentSetting, IAppointmentSettingService> {
    @Autowired
    private IAppointmentSettingService appointmentSettingService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private IScheduleTplService scheduleTplService;
    @Autowired
    private IAppointmentScheduleService appointmentScheduleService;

    /**
     * 分页列表查询
     *
     * @param appointmentSetting
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "检测设置-分页列表查询")
    @ApiOperation(value = "检测设置-分页列表查询", notes = "检测设置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(AppointmentSetting appointmentSetting,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<AppointmentSetting> queryWrapper = QueryGenerator.initQueryWrapper(appointmentSetting, req.getParameterMap());
        Page<AppointmentSetting> page = new Page<AppointmentSetting>(pageNo, pageSize);
        IPage<AppointmentSetting> pageList = appointmentSettingService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /*@AutoLog(value = "检测设置-分页列表查询")
    @ApiOperation(value = "检测设置-分页列表查询", notes = "检测设置-分页列表查询")
    @GetMapping(value = "/enableBarcode")
    public Result<?> queryEnableBarcode() {
        String enableBarcode = "0";
        try {
            enableBarcode = jdbcTemplate.queryForObject("select enable_barcode from detection_setting where tenant_id=? and enable_flag=? limit 1", String.class, "1", "true");
        } catch (Exception e) {
            enableBarcode = "0";
        }
        return Result.OK(enableBarcode);
    }*/

    /**
     * 添加
     *
     * @param appointmentSetting
     * @return
     */
    @AutoLog(value = "检测设置-添加")
    @ApiOperation(value = "检测设置-添加", notes = "检测设置-添加")
    @PostMapping(value = "/add")
    @Transactional
    public Result<?> add(@RequestBody AppointmentSetting appointmentSetting) {
        if (StringUtils.equals(appointmentSetting.getAutoJobFlag(),"1")){
            appointmentSettingService.update(new LambdaUpdateWrapper<AppointmentSetting>().set(AppointmentSetting::getAutoJobFlag,"0"));
        }
        appointmentSettingService.save(appointmentSetting);
        try {
            scheduleTplService.generateScheduleTpl(appointmentSetting.getId());
        } catch (AppointmentException e) {
            log.error("生成检测排班模板异常", e);
        }

        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param appointmentSetting
     * @return
     */
    @AutoLog(value = "检测设置-编辑")
    @ApiOperation(value = "检测设置-编辑", notes = "检测设置-编辑")
    @PostMapping(value = "/edit")
    @Transactional
    public Result<?> edit(@RequestBody AppointmentSetting appointmentSetting) {
        if (StringUtils.equals(appointmentSetting.getAutoJobFlag(),"1")){
            appointmentSettingService.update(new LambdaUpdateWrapper<AppointmentSetting>().set(AppointmentSetting::getAutoJobFlag,"0"));
        }
        appointmentSettingService.updateById(appointmentSetting);
        try {
            scheduleTplService.updateScheduleTpl(appointmentSetting.getId());
            if (appointmentSetting.getUpdateSchedule()) {
                appointmentScheduleService.updateScheduleBySetting("1", appointmentSetting.getId());
            }
        } catch (AppointmentException e) {
            log.error("生成检测排班模板异常", e);
        }
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "检测设置-通过id删除")
    @ApiOperation(value = "检测设置-通过id删除", notes = "检测设置-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        appointmentSettingService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "检测设置-批量删除")
    @ApiOperation(value = "检测设置-批量删除", notes = "检测设置-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.appointmentSettingService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "检测设置-通过id查询")
    @ApiOperation(value = "检测设置-通过id查询", notes = "检测设置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        AppointmentSetting appointmentSetting = appointmentSettingService.getById(id);
        if (appointmentSetting == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(appointmentSetting);
    }





    /*--------------------------------子表处理-schedule_tpl-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    @AutoLog(value = "schedule_tpl-通过主表ID查询")
    @ApiOperation(value = "schedule_tpl-通过主表ID查询", notes = "schedule_tpl-通过主表ID查询")
    @GetMapping(value = "/listScheduleTplByMainId")
    public Result<?> listScheduleTplByMainId(ScheduleTpl scheduleTpl,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                             HttpServletRequest req) {
        QueryWrapper<ScheduleTpl> queryWrapper = QueryGenerator.initQueryWrapper(scheduleTpl, req.getParameterMap());
        Page<ScheduleTpl> page = new Page<ScheduleTpl>(pageNo, pageSize);
        IPage<ScheduleTpl> pageList = scheduleTplService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param scheduleTpl
     * @return
     */
    @AutoLog(value = "schedule_tpl-添加")
    @ApiOperation(value = "schedule_tpl-添加", notes = "schedule_tpl-添加")
    @PostMapping(value = "/addScheduleTpl")
    public Result<?> addScheduleTpl(@RequestBody ScheduleTpl scheduleTpl) {

        scheduleTplService.save(scheduleTpl);
        if(scheduleTpl.getUpdateSchedule()!=null&&scheduleTpl.getUpdateSchedule()){
            try {
                appointmentScheduleService.saveScheduleByTpl( scheduleTpl);
            } catch (AppointmentException e) {
               return Result.error(e.getMessage());
            }
        }
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param scheduleTpl
     * @return
     */
    @AutoLog(value = "schedule_tpl-编辑")
    @ApiOperation(value = "schedule_tpl-编辑", notes = "schedule_tpl-编辑")
    @PostMapping(value = "/editScheduleTpl")
    public Result<?> editScheduleTpl(@RequestBody ScheduleTpl scheduleTpl) {
        scheduleTplService.updateById(scheduleTpl);
        if (scheduleTpl.getUpdateSchedule()!=null&&scheduleTpl.getUpdateSchedule()) {
            appointmentScheduleService.updateValidScheduleByTpl("1", scheduleTpl.getLimitAmount(), scheduleTpl.getEnableFlag(), scheduleTpl.getStartTime(), scheduleTpl.getEndTime(), scheduleTpl.getId());
        }
        return Result.OK("编辑成功!");
    }

    @AutoLog(value = "预约排班模板-批量修改")
    @ApiOperation(value = "预约排班-批量修改", notes = "预约排班-批量修改")
    @PostMapping(value = "/alterLimit")
    public Result<?> alterLimit(@RequestBody JSONObject info) {
        Boolean updateSchedule = info.getBoolean("updateSchedule");
        Integer limitAmount = info.getInteger("limitAmount");
        String enableFlag = info.getString("enableFlag");
        JSONArray idsArr = info.getJSONArray("ids");
        List<Object[]> batchUpdateParam = new ArrayList<>();
        List<String> tplIds = new ArrayList<>();
        for (int i = 0; i < idsArr.size(); i++) {
            Object[] param = new Object[3];
            param[0] = limitAmount;
            param[1] = enableFlag;
            param[2] = idsArr.getString(i);
            batchUpdateParam.add(param);
            tplIds.add(idsArr.getString(i));
        }

        jdbcTemplate.batchUpdate("update schedule_tpl set limit_amount=?,enable_flag=? where id=?", batchUpdateParam);
        if (updateSchedule) {
            appointmentScheduleService.updateValidScheduleByTpl("1", limitAmount, enableFlag, null, null, tplIds.toArray(new String[0]));
        }

        return Result.OK("修改成功！");
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "schedule_tpl-通过id删除")
    @ApiOperation(value = "schedule_tpl-通过id删除", notes = "schedule_tpl-通过id删除")
    @DeleteMapping(value = "/deleteScheduleTpl")
    public Result<?> deleteScheduleTpl(@RequestParam(name = "id", required = true) String id) {
        scheduleTplService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "schedule_tpl-批量删除")
    @ApiOperation(value = "schedule_tpl-批量删除", notes = "schedule_tpl-批量删除")
    @DeleteMapping(value = "/deleteBatchScheduleTpl")
    public Result<?> deleteBatchScheduleTpl(@RequestParam(name = "ids", required = true) String ids) {
        this.scheduleTplService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

}
