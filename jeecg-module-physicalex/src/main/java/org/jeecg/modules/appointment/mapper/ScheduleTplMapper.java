package org.jeecg.modules.appointment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.appointment.entity.ScheduleTpl;

import java.util.List;

/**
 * @Description: 预约排班
 * @Author: jeecg-boot
 * @Date:   2022-01-19
 * @Version: V1.0
 */
public interface ScheduleTplMapper extends BaseMapper<ScheduleTpl> {

    public boolean deleteByMainId(@Param("mainId") String mainId);

    public List<ScheduleTpl> selectByMainId(@Param("mainId") String mainId);
}
