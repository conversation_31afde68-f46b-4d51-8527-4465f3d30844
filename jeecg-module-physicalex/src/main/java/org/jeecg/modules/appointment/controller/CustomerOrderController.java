package org.jeecg.modules.appointment.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.appointment.service.ICustomerOrderService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 体检预约订单
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
@Api(tags = "体检预约订单")
@RestController(value = "wxCustomerOrderController")
@RequestMapping("/order/customerOrder")
@Slf4j
public class CustomerOrderController extends JeecgController<CustomerOrder, ICustomerOrderService> {
    @Autowired
    private ICustomerOrderService customerOrderService;

    /**
     * 分页列表查询
     *
     * @param customerOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检预约订单-分页列表查询")
    @ApiOperation(value = "体检预约订单-分页列表查询", notes = "体检预约订单-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerOrder>> queryPageList(CustomerOrder customerOrder, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CustomerOrder> queryWrapper = QueryGenerator.initQueryWrapper(customerOrder, req.getParameterMap());
        Page<CustomerOrder> page = new Page<CustomerOrder>(pageNo, pageSize);
        IPage<CustomerOrder> pageList = customerOrderService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    //统计订单数量
    @ApiOperation(value = "统计订单数量", notes = "统计订单数量")
    @GetMapping(value = "/statOrderByAccountId")
    public Result<?> statOrderByAccountId(@RequestParam(name = "accountId", required = true) String accountId) {
        try {
            return Result.OK(customerOrderService.statOrderByAccountId(accountId));
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    //取消订单
    @AutoLog(value = "体检预约订单-取消订单")
    @ApiOperation(value = "体检预约订单-取消订单", notes = "体检预约订单-取消订单")
    @GetMapping(value = "/cancelOrder")
    public Result<?> cancelOrder(@RequestParam(name = "orderId", required = true) String orderId) {
        try {
            customerOrderService.cancelOrder(orderId);
            return Result.OK("取消成功！");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param customerOrder
     * @return
     */
    @AutoLog(value = "体检预约订单-添加")
    @ApiOperation(value = "体检预约订单-添加", notes = "体检预约订单-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody CustomerOrder customerOrder, HttpServletRequest request) {
        //新增的时候要传customerRegId
        try {
            CustomerReg customerReg = customerOrderService.addCustomerOrder(customerOrder, request);
            return Result.OK("添加成功！", customerReg);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    //退款
    @AutoLog(value = "体检预约订单-退款")
    @ApiOperation(value = "体检预约订单-退款", notes = "体检预约订单-退款")
    @GetMapping(value = "/refund")
    public Result<?> refund(@RequestParam(name = "orderId", required = true) String orderId, HttpServletRequest request) {
        try {
            customerOrderService.refundOrder(orderId, request.getRemoteAddr());
            return Result.OK("退款成功！");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param customerOrder
     * @return
     */
    @AutoLog(value = "体检预约订单-编辑")
    @ApiOperation(value = "体检预约订单-编辑", notes = "体检预约订单-编辑")
//	@RequiresPermissions("order:customer_order:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomerOrder customerOrder) {
        customerOrderService.updateById(customerOrder);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检预约订单-通过id删除")
    @ApiOperation(value = "体检预约订单-通过id删除", notes = "体检预约订单-通过id删除")
//	@RequiresPermissions("order:customer_order:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerOrderService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检预约订单-批量删除")
    @ApiOperation(value = "体检预约订单-批量删除", notes = "体检预约订单-批量删除")
//	@RequiresPermissions("order:customer_order:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerOrderService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检预约订单-通过id查询")
    @ApiOperation(value = "体检预约订单-通过id查询", notes = "体检预约订单-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerOrder> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerOrder customerOrder = customerOrderService.queryOrdersById(id);
        if (customerOrder == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerOrder);
    }

    /**
     * 根据状态查询订单列表
     *
     * @param state
     * @return
     */
    //@AutoLog(value = "体检预约订单-通过id查询")
    @ApiOperation(value = "体检预约订单-根据状态查询订单列表", notes = "体检预约订单-根据状态查询订单列表")
    @GetMapping(value = "/queryOrdersByState")
    public Result<?> queryOrdersByState(@RequestParam(name = "state") String state, @RequestParam(name = "openId") String openId) {
        List<CustomerOrder> customerOrders = customerOrderService.queryOrdersByState(state, openId);
        if (CollectionUtils.isEmpty(customerOrders)) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerOrders);
    }

    /**
     * 根据账号Id查询订单列表
     *
     * @return
     */
    //@AutoLog(value = "体检预约订单-通过id查询")
    @ApiOperation(value = "体检预约订单-根据账号Id查询订单列表", notes = "体检预约订单-根据账号Id查询订单列表")
    @GetMapping(value = "/queryOrdersByAccountId")
    public Result<?> queryOrdersByAccountId(String state, @RequestParam(name = "accountId",required = true) String accountId, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        Page<CustomerOrder> page = new Page<>(pageNo, pageSize);
        customerOrderService.pageOrderByAccountId(page, state, accountId);
        return Result.OK(page);
    }

    /**
     * 根据ID查询订单详情
     *
     * @return
     */
    //@AutoLog(value = "体检预约订单-通过id查询")
    @ApiOperation(value = "体检预约订单-根据ID查询订单详情", notes = "体检预约订单-根据ID查询订单详情")
    @GetMapping(value = "/orderDetail")
    public Result<?> queryOrderDetailById(@RequestParam(name = "id",required = true) String id) {
        CustomerOrder customerOrder = customerOrderService.queryOrdersById(id);
        if (customerOrder == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerOrder);
    }


    /**
     * 导出excel
     *
     * @param request
     * @param customerOrder
     */
    @RequiresPermissions("order:customer_order:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerOrder customerOrder) {
        return super.exportXls(request, customerOrder, CustomerOrder.class, "体检预约订单");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("order:customer_order:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerOrder.class);
    }

    /**
     * 通过id查询
     *
     * @param customerId
     * @return
     */
    //@AutoLog(value = "体检预约订单-通过id查询")
    @ApiOperation(value = "体检预约订单-通过customerId查询", notes = "体检预约订单-通过customerId查询")
    @GetMapping(value = "/queryByCustomerId")
    public Result<List<CustomerOrder>> queryById(@RequestParam(name = "id", required = true) String customerId, @RequestParam(name = "state") String state) {
        LambdaQueryWrapper<CustomerOrder> queryWrapper = new LambdaQueryWrapper<CustomerOrder>().eq(CustomerOrder::getCustomerId, customerId);
        if (StringUtils.isNotBlank(state)) {
            queryWrapper.eq(CustomerOrder::getStatus, state);
        }
        List<CustomerOrder> list = customerOrderService.list(queryWrapper);
        return Result.OK(list);
    }

}
