package org.jeecg.modules.appointment.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import feign.Param;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 体检预约订单
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
public interface CustomerOrderMapper extends BaseMapper<CustomerOrder> {
    Page<CustomerOrder> pageOrderByAccountId(Page<CustomerOrder> page, @Param("status") String status, @Param("accountId") String accountId);
}
