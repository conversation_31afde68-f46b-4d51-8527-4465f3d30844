package org.jeecg.modules.appointment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.appointment.entity.AppointmentSetting;
import org.jeecg.modules.appointment.entity.CompanyAppointment;
import org.jeecg.modules.appointment.entity.ScheduleTpl;
import org.jeecg.modules.appointment.service.IAppointmentScheduleService;
import org.jeecg.modules.appointment.service.IAppointmentSettingService;
import org.jeecg.modules.appointment.service.IScheduleTplService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@Api(tags = "预约模版")
@RestController
@RequestMapping("/appointment/scheduleTpl")
@Slf4j
public class ScheduleTplController extends JeecgController<ScheduleTpl, IScheduleTplService> {
    @Autowired
    private IScheduleTplService scheduleTplService;

    /**
     * 分页列表查询
     *
     * @param scheduleTpl
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "排版模版设置-分页列表查询")
    @ApiOperation(value = "排版模版设置-分页列表查询", notes = "排版模版设置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ScheduleTpl scheduleTpl,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ScheduleTpl> queryWrapper = QueryGenerator.initQueryWrapper(scheduleTpl, req.getParameterMap());
        Page<ScheduleTpl> page = new Page<ScheduleTpl>(pageNo, pageSize);
        IPage<ScheduleTpl> pageList = scheduleTplService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     *   添加
     *
     * @param scheduleTpl
     * @return
     */
    @AutoLog(value = "scheduleTpl-添加")
    @ApiOperation(value="scheduleTpl-添加", notes="scheduleTpl-添加")
//	@RequiresPermissions("wx.appointment:company_appointment:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ScheduleTpl scheduleTpl) {
        scheduleTplService.save(scheduleTpl);
        return Result.OK("添加成功！");
    }

    /**
     *  编辑
     *
     * @param scheduleTpl
     * @return
     */
    @AutoLog(value = "scheduleTpl-编辑")
    @ApiOperation(value="scheduleTpl-编辑", notes="scheduleTpl-编辑")
//	@RequiresPermissions("wx.appointment:company_appointment:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
    public Result<String> edit(@RequestBody ScheduleTpl scheduleTpl) {
        scheduleTplService.updateById(scheduleTpl);
        return Result.OK("编辑成功!");
    }

    /**
     *   通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "scheduleTpl-通过id删除")
    @ApiOperation(value="scheduleTpl-通过id删除", notes="scheduleTpl-通过id删除")
//	@RequiresPermissions("wx.appointment:company_appointment:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        scheduleTplService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     *  批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "scheduleTpl-批量删除")
    @ApiOperation(value="scheduleTpl-批量删除", notes="scheduleTpl-批量删除")
//	@RequiresPermissions("wx.appointment:company_appointment:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.scheduleTplService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "company_appointment-通过id查询")
    @ApiOperation(value="scheduleTpl-通过id查询", notes="scheduleTpl-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScheduleTpl> queryById(@RequestParam(name="id",required=true) String id) {
        ScheduleTpl scheduleTpl = scheduleTplService.getById(id);
        if(scheduleTpl==null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(scheduleTpl);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param scheduleTpl
     */
//    @RequiresPermissions("wx.appointment:company_appointment:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ScheduleTpl scheduleTpl) {
        return super.exportXls(request, scheduleTpl, ScheduleTpl.class, "schedule_tpl");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
//    @RequiresPermissions("wx.appointment:company_appointment:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ScheduleTpl.class);
    }
}
