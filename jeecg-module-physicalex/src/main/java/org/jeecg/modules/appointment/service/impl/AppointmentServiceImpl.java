package org.jeecg.modules.appointment.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.appointment.entity.Appointment;
import org.jeecg.modules.appointment.mapper.AppointmentMapper;
import org.jeecg.modules.appointment.service.IAppointmentService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Description: 预约
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class AppointmentServiceImpl extends ServiceImpl<AppointmentMapper, Appointment> implements IAppointmentService {
    /*@Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private AppointmentMapper appointmentMapper;
    @Autowired
    private IProfileService iProfileService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private PayService payService;
    @Autowired
    private IDetectionItemService detectionItemService;
    @Autowired
//    private IReqMasterService iReqMasterService;
    @Value("${biz.hospitalName}")
    private String hospitalName;

    @Override
    public void pageAppointment(Page<Appointment> page, String userId) {
        QueryWrapper<Appointment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("create_time");
        page(page, queryWrapper);
    }

    @Override
    public Appointment getRecentPaid(String userId) {
        QueryWrapper<Appointment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", "PAID");
        queryWrapper.orderByDesc("create_time");
        queryWrapper.last(" limit 1");

        return getOne(queryWrapper);
    }

    @Override
    public Appointment getByBarcode(String barcode, String tenantId) {

        QueryWrapper<Appointment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("barcode", barcode);
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.orderByDesc("create_time");

        List<Appointment> list = list(queryWrapper);

        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public void dealTimeoutAppointments() {
        List<Appointment> timeoutList = appointmentMapper.getTimeoutAppointments();

        for (Appointment appointment : timeoutList) {
            try {
                String status = appointment.getStatus();
                if (StringUtils.equals(status, "PAID")) {
                    payService.refund(appointment, "schedule-task", "过期未检测，系统自动退款");
                } else if (StringUtils.equals(status, "SUBMITTED")) {
                    jdbcTemplate.update("update appointment set status=? where id=?", "CANCELED", appointment.getId());
                }
            } catch (Exception e) {
                log.error("定时任务-过期预约处理异常，appointmentId：" + appointment.getId(), e);
            }
        }
    }

    @Override
    public Map<String, Long> statCurrDay(String tenantId) {

        return appointmentMapper.statCurrDay(tenantId);
    }

    @Override
    public Map<String, Long> stat(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return appointmentMapper.stat(tenantId, startTime, endTime);
    }

    @Override
    public List<Appointment> getUnreported(String tenantId, Integer hour, String id, String name, String
            appointTime, String barcode, String idcard) {
        return appointmentMapper.getUnreported(tenantId, hour, id, name, appointTime, barcode, idcard);
    }

    @Override
    public Page<Appointment> pageUnreported(Page<Appointment> page, String tenantId, Integer hour, String
            id, String name, String appointTime, String barcode, String idcard) {
        return appointmentMapper.pageUnreported(page, tenantId, hour, id, name, appointTime, barcode, idcard);
    }

    @Override
    public Long countUnreported(String tenantId, Integer hours) {
        return appointmentMapper.countUnreported(tenantId, hours);
    }

    @Override
    public boolean canFree(String detectionItemId, String profileId, String idcard, String name, String
            scheduleDate) {
        if (StringUtils.equals(hospitalName, "鄂托克旗人民医院")) {
            DetectionItem detectionItem = detectionItemService.getDetail(detectionItemId);
            List<String> limitProfile = detectionItem.getLimitProfile();
            *//*if (limitProfile.contains("特殊行业从业人员")) {
                return profileValidService.canFree(idcard, name, scheduleDate);
            } else {
                return true;
            }*//*

            return true;
        } else {
            return true;
        }
    }
*/

    @Override
    public void pageAppointment(Page<Appointment> page, String userId) {

    }

    @Override
    public Appointment getRecentPaid(String userId) {
        return null;
    }

    @Override
    public Appointment getByBarcode(String barcode, String tenantId) {
        return null;
    }

    @Override
    public void dealTimeoutAppointments() {

    }

    @Override
    public Map<String, Long> statCurrDay(String tenantId) {
        return null;
    }

    @Override
    public Map<String, Long> stat(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return null;
    }

    @Override
    public List<Appointment> getUnreported(String tenantId, Integer hour, String id, String name, String appointTime, String barcode, String idcard) {
        return null;
    }

    @Override
    public Page<Appointment> pageUnreported(Page<Appointment> page, String tenantId, Integer hour, String id, String name, String appointTime, String barcode, String idcard) {
        return null;
    }

    @Override
    public Long countUnreported(String tenantId, Integer hours) {
        return null;
    }

    @Override
    public boolean canFree(String detectionItemId, String profileId, String idcard, String name, String schedualDate) {
        return false;
    }
}
