package org.jeecg.modules.appointment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.appointment.entity.Appointment;
import org.jeecg.modules.appointment.service.IAppointmentService;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.core.MessageProperties;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: 预约
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Api(tags = "预约")
@RestController
@RequestMapping("/appointment/appointment")
@Slf4j
public class AppointmentController extends JeecgController<Appointment, IAppointmentService> {
    @Autowired
    private IAppointmentService appointmentService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${jeecg.path.upload}")
    private String upLoadPath;


    /**
     * 分页列表查询
     */
    @AutoLog(value = "预约-分页列表查询")
    @ApiOperation(value = "预约-分页列表查询", notes = "预约-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(Appointment appointment,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<Appointment> queryWrapper = QueryGenerator.initQueryWrapper(appointment, req.getParameterMap());
        Page<Appointment> page = new Page<Appointment>(pageNo, pageSize);
        IPage<Appointment> pageList = appointmentService.page(page, queryWrapper);
        for (Appointment a : pageList.getRecords()) {
            if (StringUtils.equals(a.getStatus(), "PAID")) {
//                DetectionItem detectionItem = detectionItemService.getById(a.getDetectionItemId());
//                a.setPrintTimesOnce(detectionItem != null ? detectionItem.getPrintTimesOnce() : 1);
            }
        }

        return Result.OK(pageList);
    }


    /**
     * 分页列表查询
     */
    @AutoLog(value = "预约-分页查询未出报告列表")
    @ApiOperation(value = "预约-分页查询未出报告列表", notes = "预约-分页查询未出报告列表")
    @GetMapping(value = "/noreportList")
    public Result<?> noreportList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, String id, Integer hour, String name, String appointTime, String barcode, String idcard) {
        Page<Appointment> page = new Page<Appointment>(pageNo, pageSize);
        hour = hour != null ? hour : 12;

        appointmentService.pageUnreported(page, "1", hour, id, name, appointTime, barcode, idcard);
        return Result.OK(page);
    }



    /**
     * 分页列表查询
     */
    @AutoLog(value = "预约-分页列表查询")
    @ApiOperation(value = "预约-分页列表查询", notes = "预约-分页列表查询")
    @GetMapping(value = "/list2")
    public Result<?> queryPageList2(Appointment appointment,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                    HttpServletRequest req) {
        QueryWrapper<Appointment> queryWrapper = new QueryWrapper<>();
        Page<Appointment> page = new Page<Appointment>(pageNo, pageSize);

        boolean needQuery = false;
        String id = req.getParameter("id");
        String name = req.getParameter("name");
        String idCard = req.getParameter("idCard");
        String phone = req.getParameter("phone");
        String barcode = req.getParameter("barcode");

        if (StringUtils.isNotBlank(id)) {
            needQuery = true;
            queryWrapper.eq("id", id);
        }
        if (StringUtils.isNotBlank(name)) {
            needQuery = true;
            queryWrapper.eq("name", name);
        }
        if (StringUtils.isNotBlank(idCard)) {
            needQuery = true;
            queryWrapper.eq("id_card", idCard);
        }
        if (StringUtils.isNotBlank(phone)) {
            needQuery = true;
            queryWrapper.eq("phone", phone);
        }
        if (StringUtils.isNotBlank(barcode)) {
            needQuery = true;
            queryWrapper.eq("barcode", barcode);
        }

        if (needQuery) {
            page = appointmentService.page(page, queryWrapper);
            for (Appointment a : page.getRecords()) {
                if (StringUtils.equals(a.getStatus(), "PAID")) {

                }


            }
        }

        return Result.OK(page);
    }

    /**
     * 添加
     *
     * @param appointment
     * @return
     */
    @AutoLog(value = "预约-添加")
    @ApiOperation(value = "预约-添加", notes = "预约-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Appointment appointment) {
        appointmentService.save(appointment);
        return Result.OK("添加成功！");
    }

    @AutoLog(value = "预约统计")
    @ApiOperation(value = "预约统计", notes = "预约统计")
    @GetMapping(value = "/stat-currday")
    public Result<?> statCurrDay(String tenantId) {
        Map<String, Long> stat = appointmentService.statCurrDay(tenantId);
        return Result.OK(stat);
    }

    @AutoLog(value = "预约统计")
    @ApiOperation(value = "预约统计", notes = "预约统计")
    @GetMapping(value = "/stat")
    public Result<?> stat(String tenantId, String scheduleDate_begin, String scheduleDate_end) {

        LocalDateTime start = null;
        LocalDateTime end = null;
        if (StringUtils.isNotBlank(scheduleDate_begin)) {
            start = LocalDate.parse(scheduleDate_begin, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
        }
        if (StringUtils.isNotBlank(scheduleDate_end)) {
            end = LocalDate.parse(scheduleDate_end, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atTime(LocalTime.MAX);
        }
        Map<String, Long> stat = appointmentService.stat(tenantId, start, end);
        Long unreportedCount = appointmentService.countUnreported(tenantId, 12);
        stat.put("unreported", unreportedCount);
        return Result.OK(stat);
    }

  /**
     * 分页列表查询
     */
    @AutoLog(value = "预约-分页列表查询")
    @ApiOperation(value = "预约-分页列表查询", notes = "预约-分页列表查询")
    @GetMapping(value = "/unreported")
    public Result<?> queryUnreportedList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, String name, String id, String appointTime, String barcode, String idcard) {
        Page<Appointment> page = new Page<Appointment>(pageNo, pageSize);
        IPage<Appointment> pageList = appointmentService.pageUnreported(page, "1", 12, id, name, appointTime, barcode, idcard);
        return Result.OK(pageList);
    }

    /**
     * 编辑
     *
     * @param appointment
     * @return
     */
    @AutoLog(value = "预约-编辑")
    @ApiOperation(value = "预约-编辑", notes = "预约-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody Appointment appointment) {
        appointmentService.updateById(appointment);
        return Result.OK("编辑成功!");
    }

    /*@AutoLog(value = "预约-通过id查询")
    @ApiOperation(value = "预约-通过id查询", notes = "预约-通过id查询")
    @GetMapping(value = "/send2Lis")
    public Result<?> send2Lis(@RequestParam(name = "id", required = true) String id, String barcode) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Appointment appointment = appointmentService.getById(id);
        if (appointment == null) {
            return Result.error("未找到对应数据");
        }



        Integer existReqCount = jdbcTemplate.queryForObject("select count(1) from req_master where inp_id=? or barcode=?", Integer.class, id, barcode);
        if (existReqCount > 0) {
            return Result.error("样本条码重复，请认真核对！");
        }

        try {
            appointment.setBarcode(barcode);
            ReqMaster reqMaster = reqMasterService.generateRequest(appointment);
            reqMasterService.sendRequest(reqMaster);
            UpdateWrapper<Appointment> appointmentUpdateWrapper = new UpdateWrapper<>();
            appointmentUpdateWrapper.set("check_time", LocalDateTime.now());
            appointmentUpdateWrapper.set("status", ConstantDict.APPOINTMENT_STATE_DETECTED);
            appointmentUpdateWrapper.set("barcode", barcode);
            appointmentUpdateWrapper.eq("id", id);
            appointmentService.update(appointmentUpdateWrapper);

            return Result.OK("条码发送成功！");
        } catch (AppointmentException e) {
            return Result.error(e.getMessage());
        }
    }*/

    /*@AutoLog(value = "预约-通过id查询")
    @ApiOperation(value = "预约-通过id查询", notes = "预约-通过id查询")
    @GetMapping(value = "/updateBarcode")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateBarcode(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "reqId", required = true) Long reqId, @RequestParam(name = "detectionItem", required = true) String detectionItem) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        String barcode = "";

        UpdateWrapper<ReqMaster> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("barcode", barcode);
        updateWrapper.eq("req_id", reqId);
        reqMasterService.update(updateWrapper);

        UpdateWrapper<Appointment> appointmentUpdateWrapper = new UpdateWrapper<>();
        appointmentUpdateWrapper.set("check_time", LocalDateTime.now());
        appointmentUpdateWrapper.set("status", "DETECTED");
        appointmentUpdateWrapper.set("barcode", barcode);
        appointmentUpdateWrapper.eq("id", id);
        appointmentService.update(appointmentUpdateWrapper);

        ReqMaster reqMaster = reqMasterService.getById(reqId);
        MessageProperties properties = new MessageProperties();
        properties.setContentType(MessageProperties.CONTENT_TYPE_JSON);
        properties.setContentEncoding("UTF8");
        Message message = new Message(JSONObject.toJSONBytes(reqMaster), properties);
        rabbitTemplate.send(CovidSystemMQConst.RABBITMQ_DIRECT_EXCHANGE, CovidSystemMQConst.RABBITMQ_QUEUE_SAMPLE, message);

        return Result.OK(barcode);
    }*/

   /**
     * 通过id删除
     */
    @AutoLog(value = "预约-通过id删除")
    @ApiOperation(value = "预约-通过id删除", notes = "预约-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        appointmentService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "预约-批量删除")
    @ApiOperation(value = "预约-批量删除", notes = "预约-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.appointmentService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "预约-通过id查询")
    @ApiOperation(value = "预约-通过id查询", notes = "预约-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        Appointment appointment = appointmentService.getById(id);
        if (appointment == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(appointment);
    }

    /**
     * 检查barcode是否重复
     */
    @AutoLog(value = "预约-检查barcode是否重复")
    @ApiOperation(value = "预约-检查barcode是否重复", notes = "预约-检查barcode是否重复")
    @GetMapping(value = "/checkBarcode")
    public Result<?> checkBarcode(@RequestParam(name = "barcode", required = true) String barcode) {
        Integer existBarcodeCount = jdbcTemplate.queryForObject("select count(1) from appointment where barcode=?", Integer.class, barcode);

        return Result.OK(existBarcodeCount == 0);
    }

    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Appointment appointment) {
        return super.exportXls(request, appointment, Appointment.class, "预约记录");
    }

    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportUnreportedUrl")
    public ModelAndView exportUnreportedUrl(String id, Integer hour, String name, String appointTime, String barcode, String idcard) {
        // Step.1 组装查询条件
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        hour = hour != null ? hour : 12;
        List<Appointment> exportList = appointmentService.getUnreported(null, hour, id, name, appointTime, barcode, id);
        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "超" + hour + "小时未出报告"); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.CLASS, Appointment.class);
        //update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
        ExportParams exportParams = new ExportParams("超" + hour + "小时未出报告", "导出人:" + sysUser.getRealname(), "超" + hour + "小时未出报告");
        exportParams.setImageBasePath(upLoadPath);
        //update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Appointment.class);
    }

}
