package org.jeecg.modules.appointment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.appointment.entity.AppointmentSetting;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: 检测设置
 * @Author: jeecg-boot
 * @Date:   2022-01-19
 * @Version: V1.0
 */
public interface IAppointmentSettingService extends IService<AppointmentSetting> {

    /**
     * 删除一对多
     */
    public void delMain (String id);

    /**
     * 批量删除一对多
     */
    public void delBatchMain (Collection<? extends Serializable> idList);

}
