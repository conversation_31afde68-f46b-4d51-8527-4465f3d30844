package org.jeecg.modules.appointment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.appointment.exception.AppointmentException;
import org.jeecg.modules.appointment.entity.ScheduleTpl;

import java.util.List;

/**
 * @Description: 预约排班
 * @Author: jeecg-boot
 * @Date:   2022-01-19
 * @Version: V1.0
 */
public interface IScheduleTplService extends IService<ScheduleTpl> {

    void generateScheduleTpl(String settingId) throws AppointmentException;

    void updateScheduleTpl(String settingId) throws AppointmentException;;

    List<ScheduleTpl> selectByMainId(String mainId);

}
