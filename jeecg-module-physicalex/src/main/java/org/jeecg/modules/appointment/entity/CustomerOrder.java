package org.jeecg.modules.appointment.entity;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 体检预约订单
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
@Data
@TableName("customer_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "customer_order对象", description = "体检预约订单")
public class CustomerOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 订单编号
     */
    @Excel(name = "订单编号", width = 15)
    @ApiModelProperty(value = "订单编号")
    private String orderNo;
    /**
     * 体检人id
     */
    @Excel(name = "体检人id", width = 15)
    @ApiModelProperty(value = "体检人id")
    private String customerId;
    /**
     * openId
     */
    @Excel(name = "体检人id", width = 15)
    @ApiModelProperty(value = "openId")
    private String openId;
    /**
     * 登记id
     */
    @Excel(name = "登记id", width = 15)
    @ApiModelProperty(value = "登记id")
    private String customerRegId;
    /**
     * 体检人姓名
     */
    @Excel(name = "体检人姓名", width = 15)
    @ApiModelProperty(value = "体检人姓名")
    private String customerName;
    /**
     * 体检单位
     */
    @Excel(name = "体检单位", width = 15)
    @ApiModelProperty(value = "体检单位")
    private String orgName;

    private String scheduleId;
    /**
     * 预约时间
     */
    @Excel(name = "预约时间", width = 15)
    @ApiModelProperty(value = "预约时间")
    private java.util.Date bookTime;
    /**
     * 应付金额
     */
    @Excel(name = "应付金额", width = 15)
    @ApiModelProperty(value = "应付金额")
    private java.math.BigDecimal payableAmount;
    /**
     * 实付金额
     */
    @Excel(name = "实付金额", width = 15)
    @ApiModelProperty(value = "实付金额")
    private java.math.BigDecimal actualAmount;
    /**
     * 支付方式
     */
    @Excel(name = "支付方式", width = 15)
    @ApiModelProperty(value = "支付方式")
    private String payMode;
    /**
     * 支付时间
     */
    @Excel(name = "支付时间", width = 15)
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date payTime;
    /**
     * 订单状态
     */
    @Excel(name = "订单状态", width = 15)
    @ApiModelProperty(value = "订单状态")
    private String status;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 套餐名称
     */
    @ApiModelProperty(value = "套餐名称")
    private String suitName;
    /**
     * 套餐id
     */
    @ApiModelProperty(value = "套餐id")
    private String suitId;

    private String companyRegId;

    private String accountId;

    private String examCategory;

    private String billId;

    private String cardNo;

    private String examNo;
    //短信通知
    private String notifyFlag;

    @TableField(exist = false)
    private String cardPwd;
    @TableField(exist = false)
    private List<CustomerRegItemGroup> customerRegItemGroups;
    @TableField(exist = false)
    private String suitPicture;
    @TableField(exist = false)
    private String companyRegName;
    @TableField(exist = false)
    private String companyName;
    @TableField(exist = false)
    private CustomerReg customerReg;

}
