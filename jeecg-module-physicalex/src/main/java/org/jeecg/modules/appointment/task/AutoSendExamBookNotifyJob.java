package org.jeecg.modules.appointment.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.appointment.service.ICustomerOrderService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoSendExamBookNotifyJob implements Job {

    @Autowired
    private ICustomerOrderService customerOrderService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                customerOrderService.sendExamBookNotify();
            } catch (Exception e) {
                log.error("预约到检通知发送异常", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("预约到检通知正在发送!");
        }
    }
}