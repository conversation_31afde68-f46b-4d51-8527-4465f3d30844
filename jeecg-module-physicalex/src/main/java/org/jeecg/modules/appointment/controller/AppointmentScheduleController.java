package org.jeecg.modules.appointment.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.appointment.entity.AppointmentSchedule;
import org.jeecg.modules.appointment.entity.DaySchedule;
import org.jeecg.modules.appointment.exception.AppointmentException;
import org.jeecg.modules.appointment.service.IAppointmentScheduleService;
import org.jeecg.modules.appointment.service.impl.StockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 预约排班
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Api(tags = "预约排班")
@RestController
@RequestMapping("/appointment/appointmentSchedule")
@Slf4j
public class AppointmentScheduleController extends JeecgController<AppointmentSchedule, IAppointmentScheduleService> {
    @Autowired
    private IAppointmentScheduleService appointmentScheduleService;
    @Autowired
    private StockService stockService;

    /**
     * 分页列表查询
     *
     * @param appointmentSchedule
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "预约排班-分页列表查询")
    @ApiOperation(value = "预约排班-分页列表查询", notes = "预约排班-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(AppointmentSchedule appointmentSchedule,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<AppointmentSchedule> queryWrapper = QueryGenerator.initQueryWrapper(appointmentSchedule, req.getParameterMap());
        queryWrapper.orderByAsc("start_time");
        queryWrapper.orderByAsc("own_date");
        Page<AppointmentSchedule> page = new Page<AppointmentSchedule>(pageNo, pageSize);
        IPage<AppointmentSchedule> pageList = appointmentScheduleService.page(page, queryWrapper);
        for(AppointmentSchedule schedule:pageList.getRecords())
        {
            Integer availaleAmount = stockService.getStock(schedule.getId());
            schedule.setAvailableAmount(availaleAmount);
            schedule.setAppointmentAmount(schedule.getLimitAmount()-availaleAmount);
        }
        return Result.OK(pageList);
    }
    @AutoLog(value = "预约排班-分页列表查询")
    @ApiOperation(value = "预约排班-分页列表查询", notes = "预约排班-分页列表查询")
    @GetMapping(value = "/queryAppointList")
    public Result<?> queryAppointList(AppointmentSchedule appointmentSchedule,
                                    HttpServletRequest req) {
        List<DaySchedule> allSchedule = appointmentScheduleService.getAllSchedule("0");
        return Result.OK(allSchedule);
    }
    /* 按照预约设置自动生成预约排班
     * @param appointmentSchedule
     * @return
     */
    @AutoLog(value = "预约排班-生成")
    @ApiOperation(value = "预约排班-生成", notes = "预约排班-生成")
    @GetMapping(value = "/generate")
    public Result<?> generate(@RequestParam("settingId")String settingId) {
        try {
            appointmentScheduleService.generateSchedule("1",settingId);
            return Result.OK("已生成！");
        }catch (AppointmentException e)
        {
            return Result.error("生成失败！",e.getMessage());
        }
    }

    @AutoLog(value = "预约排班-更新")
    @ApiOperation(value = "预约排班-更新", notes = "预约排班-更新")
    @GetMapping(value = "/update")
    public Result<?> update() {
        try {
            appointmentScheduleService.updateSchedule("1");
            return Result.OK("已更新！");
        }catch (AppointmentException e)
        {
            return Result.error("更新失败！",e.getMessage());
        }
    }

    @AutoLog(value = "预约排班-添加")
    @ApiOperation(value = "预约排班-添加", notes = "预约排班-添加")
    @PostMapping(value = "/alterLimit")
    public Result<?> alterLimit(@RequestBody JSONObject info) {
        Integer limitAmount = info.getInteger("limitAmount");
        String enableFlag = info.getString("enableFlag");
        JSONArray idsArr = info.getJSONArray("ids");
        for(int i=0;i<idsArr.size();i++)
        {
            String id = idsArr.getString(i);
            UpdateWrapper<AppointmentSchedule> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("limit_amount", limitAmount);
            updateWrapper.set("enable_flag", enableFlag);
            updateWrapper.eq("id", id);
            appointmentScheduleService.update(updateWrapper);
            stockService.initStock(id);
        }

        return Result.OK("修改成功！");
    }

    /**
     * 添加
     *
     * @param appointmentSchedule
     * @return
     */
    @AutoLog(value = "预约排班-添加")
    @ApiOperation(value = "预约排班-添加", notes = "预约排班-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody AppointmentSchedule appointmentSchedule) {
        appointmentScheduleService.save(appointmentSchedule);
        stockService.initStock(appointmentSchedule.getId());
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param appointmentSchedule
     * @return
     */
    @AutoLog(value = "预约排班-编辑")
    @ApiOperation(value = "预约排班-编辑", notes = "预约排班-编辑")
    @PostMapping(value = "/edit")
    public Result<?> edit(@RequestBody AppointmentSchedule appointmentSchedule) {
        appointmentScheduleService.updateById(appointmentSchedule);
        stockService.initStock(appointmentSchedule.getId());
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "预约排班-通过id删除")
    @ApiOperation(value = "预约排班-通过id删除", notes = "预约排班-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        appointmentScheduleService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "预约排班-批量删除")
    @ApiOperation(value = "预约排班-批量删除", notes = "预约排班-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.appointmentScheduleService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "预约排班-通过id查询")
    @ApiOperation(value = "预约排班-通过id查询", notes = "预约排班-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        AppointmentSchedule appointmentSchedule = appointmentScheduleService.getById(id);
        if (appointmentSchedule == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(appointmentSchedule);
    }
    /**
     * 批量更新启用状态
     * @return
     */
    @ApiOperation(value = "预约排班-批量更新启用状态", notes = "预约排班-批量更新启用状态")
    @PostMapping(value = "/batchUpdateEnableFlag")
    public Result<?> batchUpdateEnableFlag(@RequestBody JSONObject info) {
        List<String> idList = info.getJSONArray("ids").toJavaList(String.class);
        String enableFlag = info.getString("enableFlag");
        appointmentScheduleService.batchUpdateEnableFlag(idList, enableFlag);
        return Result.OK("操作成功");
    }

    /**
     * 导出excel
     *
     * @param request
     * @param appointmentSchedule
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AppointmentSchedule appointmentSchedule) {
        return super.exportXls(request, appointmentSchedule, AppointmentSchedule.class, "预约排班");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AppointmentSchedule.class);
    }

}
