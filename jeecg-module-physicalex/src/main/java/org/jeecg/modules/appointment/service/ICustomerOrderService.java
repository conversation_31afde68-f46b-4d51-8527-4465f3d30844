package org.jeecg.modules.appointment.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @Description: 体检预约订单
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
public interface ICustomerOrderService extends IService<CustomerOrder> {

    CustomerReg addCustomerOrder(CustomerOrder customerOrder, HttpServletRequest request) throws Exception;

    List<CustomerOrder> queryOrdersByState(String state, String openId);

    CustomerOrder queryOrdersById(String id);

    void pageOrderByAccountId(Page<CustomerOrder> page, String status, String accountId);

    Map<String, Integer> statOrderByAccountId(String accountId);

    void refundOrder(String orderId, String clientIp) throws Exception;

    void cancelOrder(String orderId) throws Exception;

    void sendExamBookNotify() throws Exception;
}
