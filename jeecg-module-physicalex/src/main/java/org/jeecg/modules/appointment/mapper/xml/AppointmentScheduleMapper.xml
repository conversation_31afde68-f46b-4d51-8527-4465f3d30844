<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.appointment.mapper.AppointmentScheduleMapper">

    <select id="getSchedule4Day" resultMap="BaseResultMap">
        select * from appointment_schedule where enable_flag='1' and own_date = #{day} order by start_time
    </select>

    <select id="getValidScheduleByTpl" resultMap="BaseResultMap">
        select * from appointment_schedule where own_date  <![CDATA[>=]]> CURRENT_DATE and enable_flag='1' and schedule_tpl_id=#{tplId}
    </select>

    <select id="getValidScheduleByTpls" resultMap="BaseResultMap">
        select * from appointment_schedule where own_date  <![CDATA[>=]]> CURRENT_DATE and enable_flag='1' and schedule_tpl_id in <foreach
            collection="tplIds" open="(" close=")" separator="," item="tplId"> #{tplId} </foreach>
    </select>

    <update id="updateValidScheduleByTpls">
        update appointment_schedule set limit_amount=#{limitAmout},enable_flag=#{enableFlag} <if test="startTime!=null">,start_time=#{startTime}</if> <if test="endTime!=null"> ,end_time=#{endTime}</if> where own_date  <![CDATA[>=]]> CURRENT_DATE  and  schedule_tpl_id in <foreach
            collection="tplIds" open="(" close=")" separator="," item="tplId"> #{tplId} </foreach>
    </update>

    <select id="getValidScheduleBySetting" resultMap="BaseResultMap">
        select * from appointment_schedule where own_date  <![CDATA[>=]]> CURRENT_DATE and enable_flag='1' and setting_id=#{settingId}
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.appointment.entity.AppointmentSchedule">
        <id column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="own_date" property="ownDate" />
        <result column="sys_org_code" property="sysOrgCode" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="status" property="status" />
        <result column="enable_flag" property="enableFlag" />
        <result column="tenant_id" property="tenantId" />
        <result column="limit_amount" property="limitAmount" />
    </resultMap>
</mapper>