<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.appointment.mapper.CustomerOrderMapper">

    <select id="pageOrderByAccountId" resultType="org.jeecg.modules.appointment.entity.CustomerOrder">
        select * from customer_order where account_id = #{accountId} <if test="status!=null">and status=#{status}</if> order
        by create_time desc
    </select>

</mapper>