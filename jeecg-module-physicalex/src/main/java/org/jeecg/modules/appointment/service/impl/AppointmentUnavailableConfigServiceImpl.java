package org.jeecg.modules.appointment.service.impl;

import org.jeecg.modules.appointment.entity.AppointmentUnavailableConfig;
import org.jeecg.modules.appointment.mapper.AppointmentUnavailableConfigMapper;
import org.jeecg.modules.appointment.service.IAppointmentUnavailableConfigService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: appointment_unavailable_config
 * @Author: jeecg-boot
 * @Date:   2025-06-09
 * @Version: V1.0
 */
@Service
public class AppointmentUnavailableConfigServiceImpl extends ServiceImpl<AppointmentUnavailableConfigMapper, AppointmentUnavailableConfig> implements IAppointmentUnavailableConfigService {

}
