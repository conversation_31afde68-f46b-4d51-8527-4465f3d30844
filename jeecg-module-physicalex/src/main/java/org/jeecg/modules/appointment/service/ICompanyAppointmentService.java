package org.jeecg.modules.appointment.service;

import org.jeecg.modules.appointment.entity.CompanyAppointment;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: company_appointment
 * @Author: jeecg-boot
 * @Date:   2024-10-28
 * @Version: V1.0
 */
public interface ICompanyAppointmentService extends IService<CompanyAppointment> {


    /**
     * 执行通知
     * @return CompanyAppointment
     */
    void executeNotify();

    /**
     * 更新处理状态
     * @param id
     * @return CompanyAppointment
     */
    void updateDealStatus(String id);
}
