package org.jeecg.modules.appointment.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.appointment.entity.Appointment;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Description: 预约
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
public interface IAppointmentService extends IService<Appointment> {

    void pageAppointment(Page<Appointment> page, String userId);

    Appointment getRecentPaid(String userId);

    Appointment getByBarcode(String barcode, String tenantId);

    void dealTimeoutAppointments();

    Map<String, Long> statCurrDay(String tenantId);

    Map<String, Long> stat(String tenantId, LocalDateTime startTime, LocalDateTime endTime);

    List<Appointment> getUnreported(String tenantId, Integer hour, String id, String name, String appointTime, String barcode, String idcard);

    Page<Appointment> pageUnreported(Page<Appointment> page, String tenantId, Integer hour, String id, String name, String appointTime, String barcode, String idcard);

    Long countUnreported(String tenantId, Integer hours);

    boolean canFree(String detectionItemId, String profileId, String idcard, String name, String schedualDate);
}
