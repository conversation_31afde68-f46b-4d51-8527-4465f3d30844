package org.jeecg.modules.appointment.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.MustacheUtil;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.appointment.mapper.CustomerOrderMapper;
import org.jeecg.modules.appointment.service.ICustomerOrderService;
import org.jeecg.modules.basicinfo.entity.AutoSmsSetting;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemSuit;
import org.jeecg.modules.basicinfo.entity.SuitGroup;
import org.jeecg.modules.basicinfo.service.*;
import org.jeecg.modules.fee.entity.CustomerRegBill;
import org.jeecg.modules.fee.service.ICustomerRegBillService;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICompanyRegService;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.mobile.utils.FileUrlUtils;
import org.jeecg.modules.sms.bo.SmsResult;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 体检预约订单
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
@Service
public class CustomerOrderServiceImpl extends ServiceImpl<CustomerOrderMapper, CustomerOrder> implements ICustomerOrderService {
    @Autowired
    private CustomerOrderMapper customerOrderMapper;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private ICustomerRegBillService customerRegBillService;
    @Autowired
    private SequenceGenerator sequenceGenerator;
    @Autowired
    private IItemGroupService itemGroupService;
    @Autowired
    private IItemSuitService itemSuitService;
    @Autowired
    private ISuitGroupService suitGroupService;
    @Autowired
    private ICompanyRegService companyRegService;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private IFeePayRecordService feePayRecordService;
    @Autowired
    private IAutoSmsSettingService autoSmsSettingService;
    @Autowired
    private ISmsRecordsService smsRecordsService;


    @Override
    public CustomerReg addCustomerOrder(CustomerOrder customerOrder, HttpServletRequest request) throws Exception {
        List<CustomerRegItemGroup> itemGroups = customerOrder.getCustomerRegItemGroups();
        if (CollectionUtils.isEmpty(itemGroups)) {
            throw new Exception("未选择检查项目，下单失败!");
        }
        CustomerReg customerReg = customerRegService.addCustomerReg4Order(customerOrder);
        customerOrder.setOrderNo(sequenceGenerator.getFormatedOrderNoBaseToday("customerOrderNo"));
        customerOrder.setStatus(ExConstants.ORDER_STATUS_待支付);
        customerOrder.setCustomerRegId(customerReg.getId());
        customerOrder.setExamNo(customerReg.getExamNo());
        this.save(customerOrder);
        //保存大项
        if (StringUtils.isNotBlank(customerOrder.getSuitId())) {
            List<SuitGroup> suitGroups = suitGroupService.list(new LambdaQueryWrapper<SuitGroup>().eq(SuitGroup::getSuitId, customerOrder.getSuitId()));
            Map<String, List<SuitGroup>> suitGroupMap = suitGroups.stream().collect(Collectors.groupingBy(i->i.getGroupId()+StringUtils.defaultIfBlank(i.getCheckPartCode(),"")));
            for (CustomerRegItemGroup group : itemGroups) {
                SuitGroup sg = CollectionUtils.isNotEmpty(suitGroupMap.get(group.getItemGroupId()+StringUtils.defaultIfBlank(group.getCheckPartCode(),""))) ? suitGroupMap.get(group.getItemGroupId()+StringUtils.defaultIfBlank(group.getCheckPartCode(),"")).get(0) : null;
                if (sg == null) {
                    throw new Exception("选择检查项目中存在无效检查项，下单失败");
                }
                group.setCustomerRegId(customerReg.getId());
                group.setIdCard(customerReg.getIdCard());
                group.setExamNo(customerReg.getExamNo());
                group.setCustomerId(customerReg.getCustomerId());
                group.setPayStatus(ExConstants.ORDER_STATUS_待支付);
                group.setItemSuitId(customerOrder.getSuitId());
                group.setItemSuitName(customerOrder.getSuitName());
                group.setAddMinusFlag(0);
                group.setPriceAfterDis(sg.getPriceAfterDis());
                group.setMinDiscountRate(sg.getMinDiscountRate());
                group.setDisRate(new BigDecimal(sg.getDisRate()));
                if (StringUtils.isBlank(group.getCheckStatus())) {
                    group.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                }
                group.setCreateTime(new Date());
                group.setUpdateTime(new Date());
                group.setCreateBy("移动端订单");
                group.setCreateName("移动端订单");
                group.setUpdateBy("移动端订单");
                group.setUpdateName("移动端订单");
//                group.setCheckPartCode(sg.getCheckPartCode());
//                group.setCheckPartName(sg.getCheckPartName());
//                group.setCheckPartId(sg.getCheckPartId());
            }
        } else {
            Set<String> itemGroupIds = itemGroups.stream().filter(i -> StringUtils.isNotBlank(i.getItemGroupId())).map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(itemGroupIds)) {
                throw new Exception("未选择检查项目，下单失败!");
            }
            Map<String, List<ItemGroup>> itemGroupMap = itemGroupService.listByIds(itemGroupIds).stream().collect(Collectors.groupingBy(ItemGroup::getId));

            for (CustomerRegItemGroup group : itemGroups) {
                ItemGroup ig = CollectionUtils.isNotEmpty(itemGroupMap.get(group.getItemGroupId())) ? itemGroupMap.get(group.getItemGroupId()).get(0) : null;
                Validate.notNull(ig, "选择检查项目中存在无效检查项，下单失败");
                group.setCustomerRegId(customerReg.getId());
                group.setIdCard(customerReg.getIdCard());
                group.setExamNo(customerReg.getExamNo());
                group.setCustomerId(customerReg.getCustomerId());
                group.setPayStatus(ExConstants.PAY_STATUS_WAIT);
                group.setItemSuitId(customerOrder.getSuitId());
                group.setItemSuitName(customerOrder.getSuitName());
                group.setAddMinusFlag(0);
                group.setPayerType(customerOrder.getExamCategory());
                group.setPriceAfterDis(ig.getPrice());
                group.setMinDiscountRate(ig.getMinDiscountRate());
                group.setDisRate(BigDecimal.ONE);
                if (StringUtils.isBlank(group.getCheckStatus())) {
                    group.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                }
                group.setCreateTime(new Date());
                group.setUpdateTime(new Date());
                group.setCreateBy("移动端订单");
                group.setCreateName("移动端订单");
                group.setUpdateBy("移动端订单");
                group.setUpdateName("移动端订单");
                group.setCheckPartCode(group.getCheckPartCode());
                group.setCheckPartName(group.getCheckPartName());
                group.setCheckPartId(group.getCheckPartId());
            }
        }
        customerRegItemGroupService.saveBatch(itemGroups);

        if (StringUtils.equals(customerOrder.getPayMode(), ExConstants.PAY_CHANNEL_体检卡) && StringUtils.isNotBlank(customerOrder.getCardNo()) && StringUtils.isNotBlank(customerOrder.getCardPwd())) {
            customerRegBillService.generateBillAndPayByCard(customerOrder, customerReg, itemGroups, request.getRemoteAddr());
            //更新订单状态
            customerOrder.setPayTime(new Date());
            customerOrder.setStatus(ExConstants.ORDER_STATUS_待登记);
            this.updateById(customerOrder);
        }

        return customerReg;
    }

    //state 0待支付  1已支付 2 已退款 3 已完成
    @Override
    public List<CustomerOrder> queryOrdersByState(String state, String openId) {
        LambdaQueryWrapper<CustomerOrder> queryWrapper = new LambdaQueryWrapper<CustomerOrder>().eq(CustomerOrder::getOpenId, openId);
        if (StringUtils.isNotBlank(state)) {
            queryWrapper.eq(CustomerOrder::getStatus, state);
           /* if (StringUtils.equals(state,ExConstants.ORDER_STATUS_PAID)){
                queryWrapper .eq(CustomerOrder::getStatus, ExConstants.ORDER_STATUS_SUBMITTED);
                List<CustomerOrder> orders = customerOrderMapper.selectList(queryWrapper);
                for (CustomerOrder order:orders){
                    String status = customerRegSummaryMapper.getStatusByReg(order.getCustomerRegId());
                    if (StringUtils.equals(status,"审核通过")){
                        orders.remove(order);
                    }
                }

            }else if (StringUtils.equals(state,ExConstants.ORDER_STATUS_REPORTED)){
                queryWrapper .eq(CustomerOrder::getStatus, ExConstants.ORDER_STATUS_SUBMITTED);
                List<CustomerOrder> orders = customerOrderMapper.selectList(queryWrapper);
                for (CustomerOrder order:orders){
                    String status = customerRegSummaryMapper.getStatusByReg(order.getCustomerRegId());
                    if (!StringUtils.equals(status,"审核通过")){
                        orders.remove(order);
                    }
                }
        }else{
                queryWrapper .eq(CustomerOrder::getStatus, state);
            }*/
        }
        queryWrapper.orderByDesc(CustomerOrder::getCreateTime);
        customerOrderMapper.selectList(queryWrapper);
        List<CustomerOrder> orders = customerOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(orders)) {
            List<String> suitIds = orders.stream().map(CustomerOrder::getSuitId).collect(Collectors.toList());
            Map<String, List<ItemSuit>> suitMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(suitIds)) {
                suitMap = itemSuitService.listByIds(suitIds).stream().collect(Collectors.groupingBy(ItemSuit::getId));
            }
            Map<String, List<CompanyReg>> companyRegMap = Maps.newHashMap();
            List<String> companyRegIds = orders.stream().map(CustomerOrder::getCompanyRegId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(companyRegIds)) {
                companyRegMap = companyRegService.listByIds(companyRegIds).stream().collect(Collectors.groupingBy(CompanyReg::getId));

            }
            //获取报告图片
            String openFileUrl = sysSettingService.getValueByCode("open_file_url");
            for (CustomerOrder o : orders) {
                ItemSuit itemSuit = CollectionUtils.isNotEmpty(suitMap.get(o.getSuitId())) ? suitMap.get(o.getSuitId()).get(0) : new ItemSuit();
                CompanyReg companyReg = CollectionUtils.isNotEmpty(companyRegMap.get(o.getCompanyRegId())) ? companyRegMap.get(o.getCompanyRegId()).get(0) : new CompanyReg();
                o.setSuitName(itemSuit.getName());
                o.setSuitPicture(FileUrlUtils.replaceUrl(itemSuit.getSuitPicture(), openFileUrl));
                o.setCompanyRegName(companyReg.getRegName());
                o.setCompanyName(companyReg.getCompanyName());
            }
        }
        return orders;
    }

    @Override
    public CustomerOrder queryOrdersById(String id) {
        CustomerOrder order = getById(id);
        if (!Objects.isNull(order)) {
            if (StringUtils.isNotBlank(order.getSuitId())) {
                String openFileUrl = sysSettingService.getValueByCode("open_file_url");
                ItemSuit itemSuit = itemSuitService.getById(order.getSuitId());
                if (Objects.nonNull(itemSuit)) {
                    order.setSuitName(itemSuit.getName());
                    order.setSuitPicture(FileUrlUtils.replaceUrl(itemSuit.getSuitPicture(), openFileUrl));
                }
            }
            if (StringUtils.isNotBlank(order.getCompanyRegId())) {
                CompanyReg companyReg = companyRegService.getById(order.getCustomerRegId());
                if (Objects.nonNull(companyReg)) {
                    order.setCompanyName(companyReg.getCompanyName());
                    order.setCompanyRegName(companyReg.getRegName());
                }
            }

            CustomerReg reg = customerRegService.getRegWithItemGroup(order.getCustomerRegId());
            if (Objects.nonNull(reg)) {
                order.setCustomerReg(reg);
            }

        }
        return order;
    }

    @Override
    public void pageOrderByAccountId(Page<CustomerOrder> page, String status, String accountId) {
        if (StringUtils.equals(status, "全部")) {
            status = null;
        }
        customerOrderMapper.pageOrderByAccountId(page, status, accountId);
    }

    @Override
    public Map<String, Integer> statOrderByAccountId(String accountId) {
        //统计各种状态的订单数量
        String sql = "select status,count(1) as c from customer_order where account_id = ? group by status";
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, accountId);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, Integer> map = Maps.newHashMap();
            for (Map<String, Object> m : list) {
                map.put(m.get("status").toString(), Integer.parseInt(m.get("c").toString()));
            }
            return map;
        }
        return Maps.newHashMap();
    }

    @Override
    public void refundOrder(String orderId, String clientIp) throws Exception {
        //验证订单状态
        CustomerOrder order = getById(orderId);
        if (Objects.isNull(order)) {
            throw new RuntimeException("订单不存在");
        }
        if (!StringUtils.equals(order.getStatus(), ExConstants.ORDER_STATUS_待登记)) {
            throw new RuntimeException("订单状态不正确");
        }
        //退款
        //获取所有支付单
        List<CustomerRegBill> bills = customerRegBillService.listWithFeePayRecords4RefundByOrderId(order.getId());
        if (CollectionUtils.isEmpty(bills)) {
            throw new RuntimeException("账单不存在");
        }

        customerRegBillService.refundBatch(bills, clientIp, "");

        //更新订单状态
        order.setStatus(ExConstants.ORDER_STATUS_已取消);
        order.setUpdateTime(new Date());
        updateById(order);
    }

    @Override
    public void cancelOrder(String orderId) throws Exception {
        //验证订单状态
        CustomerOrder order = getById(orderId);
        if (Objects.isNull(order)) {
            throw new Exception("订单不存在");
        }
        if (!StringUtils.equals(order.getStatus(), ExConstants.ORDER_STATUS_待支付)) {
            throw new RuntimeException("订单状态不正确");
        }
        //更新订单状态
        order.setStatus(ExConstants.ORDER_STATUS_已取消);
        order.setUpdateTime(new Date());
        updateById(order);

        customerRegService.removeById(order.getCustomerRegId());
        //TODO 取消订单后，需要更新库存
    }

    @Override
    public void sendExamBookNotify() throws Exception {
        List<AutoSmsSetting> reportSmsSetting = autoSmsSettingService.listAutoSmsSetting(ExConstants.SMS_BIZ_TYPE_预约到检通知);
        if (reportSmsSetting == null || reportSmsSetting.isEmpty()) {
            return;
        }
        AutoSmsSetting smsSetting = reportSmsSetting.get(0);
        String msgTemplate = smsSetting.getTemplateContent();
        List<CustomerOrder> orders = list(new LambdaQueryWrapper<CustomerOrder>()
                .ne(CustomerOrder::getNotifyFlag,"已发送")
                .eq(CustomerOrder::getStatus,ExConstants.ORDER_STATUS_待登记)
                .between(CustomerOrder::getBookTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
        if (CollectionUtils.isNotEmpty(orders)){
            orders.forEach(order->{
                try {
                    CustomerReg reg = customerRegService.getById(order.getCustomerRegId());
                    if (Objects.nonNull(reg)) {
                        String phone = reg.getPhone();
                        //构造短信内容
                        Map<String, Object> params = new HashMap<>();
                        params.put("name", reg.getName());
                        params.put("examNo", reg.getExamNo());
                        params.put("genderTitle", StringUtils.isNotBlank(reg.getGender()) ? StringUtils.equals(reg.getGender(), "男") ? "先生" : "女士" : "");
                        params.put("companyName", reg.getCompanyName());
                        params.put("companyRegName", reg.getCompanyRegName());
                        params.put("bookDate", DateUtil.format(order.getBookTime(), "yyyy-MM-dd"));
                        params.put("bookTime", order.getBookTime());

//                params.put("mpShortUrl", shortUrl);
                        //使用模板生成短信内容
                        String content = MustacheUtil.render(msgTemplate, params);
                        SmsResult smsResult = smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "1", phone, content, reg.getId(), ExConstants.SMS_BIZ_TYPE_预约到检通知);
                        if (smsResult != null) {
                            if (smsResult.isSuccess()) {
                                order.setNotifyFlag("已发送");
                                updateById(order);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("预约到检通知发送失败", e);
                }
            });
        }

    }

}
