<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.appointment.mapper.AppointmentMapper">
    <select id="getTimeoutAppointments" resultMap="BaseResultMap">
        select *
        from appointment
        where schedule_date <![CDATA[<]]> DATE_SUB(CURRENT_DATE,interval 1 day) and (status='PAID' or status='SUBMITTED')
    </select>

    <select id="statCurrDay" resultType="java.util.Map">
        select count(1) as TOTAL, sum(case when status='PAID' then 1 else 0 end) as PAID,sum(case when status='DETECTED' then 1 else 0 end) as DETECTED,sum(case when status='REPORTED' then 1 else 0 end) as REPORTED,sum(case when status='CANCELED' then 1 else 0 end) as CANCELED,sum(case when status='REFUNDED' then 1 else 0 end) as REFUNDED,sum(case when status='SUBMITTED' then 1 else 0 end) as SUBMITTED from appointment where schedule_date=CURRENT_DATE <if test="tenantId!=null"> and tenant_id=#{tenantId}</if>
    </select>

    <select id="stat" resultType="java.util.Map">
        select count(1) as TOTAL, sum(case when status='PAID' then 1 else 0 end) as PAID,sum(case when status='DETECTED' then 1 else 0 end) as DETECTED,sum(case when status='REPORTED' then 1 else 0 end) as REPORTED,sum(case when status='CANCELED' then 1 else 0 end) as CANCELED,sum(case when status='REFUNDED' then 1 else 0 end) as REFUNDED,sum(case when status='SUBMITTED' then 1 else 0 end) as SUBMITTED from appointment where 1=1 <if test="startTime!=null"> and schedule_date <![CDATA[>=]]> #{startTime}</if> <if test="endTime!=null"> and schedule_date <![CDATA[<]]> #{endTime}</if>  <if test="tenantId!=null"> and tenant_id=#{tenantId}</if>
    </select>

    <select id="getUnreported" resultMap="BaseResultMap">
        select * from appointment where now() <![CDATA[>=]]> date_add(check_time,interval  #{hours} hour) and status='DETECTED' and fix_remark is null <if test="tenantId!=null"> and tenant_id=#{tenantId}</if> <if test="id!=null"> and id =#{id}</if> <if test="name!=null"> and name =#{name}</if> <if test="appointTime!=null"> and schedule_date=#{appointTime}</if> <if test="barcode!=null"> and barcode=#{barcode}</if> <if test="idcard!=null"> id_card=#{idcard}</if> order by check_time desc
    </select>

    <select id="pageUnreported" resultMap="BaseResultMap">
        select * from appointment where now() <![CDATA[>=]]> date_add(check_time,interval  #{hours} hour) and status='DETECTED' and fix_remark is null <if test="tenantId!=null"> and tenant_id=#{tenantId}</if> <if test="id!=null"> and id =#{id}</if> <if test="name!=null"> and name =#{name}</if> <if test="appointTime!=null"> and schedule_date=#{appointTime}</if> <if test="barcode!=null"> and barcode=#{barcode}</if> <if test="idcard!=null"> id_card=#{idcard}</if> order by check_time desc
    </select>

    <select id="countUnreported" resultType="java.lang.Long">
        select count(1) from appointment where now() <![CDATA[>=]]> date_add(check_time,interval  #{hours} hour) and status='DETECTED' and fix_remark is null <if test="tenantId!=null"> and tenant_id=#{tenantId}</if>
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.appointment.entity.Appointment">
        <id column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="sys_org_code" property="sysOrgCode" />
        <result column="name" property="name" />
        <result column="gender" property="gender" />
        <result column="id_card" property="idCard" />
        <result column="seq" property="seq" />
        <result column="schedule_id" property="scheduleId" />
        <result column="schedule_time" property="scheduleTime" />
        <result column="profile_id" property="profileId" />
        <result column="order_fee" property="orderFee" />
        <result column="mchid" property="mchid" />
        <result column="pay_time" property="payTime" />
        <result column="prepay_id" property="prepayId" />
        <result column="survey_id" property="surveyId" />
        <result column="appid" property="appid" />
        <result column="status" property="status" />
        <result column="user_id" property="userId" />
        <result column="wx_fee_rate" property="wxFeeRate" />
        <result column="open_id" property="openId" />
        <result column="tenant_id" property="tenantId" />
        <result column="schedule_date" property="scheduleDate" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="detection_item" property="detectionItem" />
        <result column="detection_item_id" property="detectionItemId" />
        <result column="detection_reason" property="detectionReason" />
        <result column="check_time" property="checkTime" />
        <result column="report_id" property="reportId" />
        <result column="req_id" property="reqId" />
        <result column="barcode" property="barcode" />
    </resultMap>
</mapper>