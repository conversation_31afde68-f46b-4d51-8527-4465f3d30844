package org.jeecg.modules.appointment.entity;

import cn.hutool.setting.profile.Profile;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

/**
 * @Description: 预约
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Data
@TableName("appointment")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "appointment对象", description = "预约")
public class Appointment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Excel(name = "预约单号", width = 15, orderNum = "0")
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @Excel(name = "提交时间", width = 15, format = "yyyy-MM-dd HH:mm:ss", orderNum = "9")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 姓名
     */
    @Excel(name = "姓名", width = 15, orderNum = "1")
    @ApiModelProperty(value = "姓名")
    private String name;
    @Excel(name = "联系电话", width = 15, orderNum = "2")
    private String phone;
    /**
     * 身份账号
     */
    @Excel(name = "身份账号", width = 15, orderNum = "3")
    @ApiModelProperty(value = "身份账号")
    private String idCard;
    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer seq;
    /**
     * 排班ID
     */
    //@Excel(name = "排班ID", width = 15)
    @ApiModelProperty(value = "排班ID")
    private String scheduleId;
    @Excel(name = "预约日期", width = 15, format = "yyyy-MM-dd", orderNum = "3")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate scheduleDate;
    /**
     * 预约日期
     */
    @Excel(name = "预约时段", width = 15, orderNum = "4")
    @ApiModelProperty(value = "预约时段")
    private String scheduleTime;
    /**
     * 档案ID
     */
    //@Excel(name = "档案ID", width = 15)
    @ApiModelProperty(value = "档案ID")
    private String profileId;
    /**
     * 检测费用
     */
    @Excel(name = "检测费用", width = 15, isStatistics = true, orderNum = "5")
    @ApiModelProperty(value = "检测费用")
    private BigDecimal orderFee;
    /**
     * 状态
     */
    @Excel(name = "状态", dicCode = "appointment_status", width = 15, orderNum = "7")
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 支付时间
     */
    @Excel(name = "支付时间", width = 20, format = "yyyy-MM-dd HH:mm:ss", orderNum = "8")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
    /**
     * 流调问卷ID
     */
    //@Excel(name = "流调问卷ID", width = 15)
    @ApiModelProperty(value = "流调问卷ID")
    private String surveyId;
    /**
     * 微信预支付ID
     */
    //@Excel(name = "微信预支付ID", width = 15)
    @ApiModelProperty(value = "微信预支付ID")
    private String prepayId;
    /**
     * 小程序ID
     */
    //@Excel(name = "小程序ID", width = 15)
    @ApiModelProperty(value = "小程序ID")
    private String appid;
    /**
     * 微信商户ID
     */
    @Excel(name = "微信商户ID", width = 15, orderNum = "7")
    @ApiModelProperty(value = "微信商户ID")
    private String mchid;
    /**
     * 用户ID
     */
    //@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;
    private BigDecimal wxFeeRate;
    private String openId;
    private String tenantId;

    /**
     * 开始时间
     */
    //@Excel(name = "开始时间", width = 15, format = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    @JSONField(format = "HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private LocalTime startTime;
    /**
     * 结束时间
     */
    //@Excel(name = "结束时间", width = 15, format = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    @JSONField(format = "HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private LocalTime endTime;

    @Excel(name = "检测项目", width = 15, orderNum = "4")
    private String detectionItem;
    private String detectionItemId;
    /**
     * 结束时间
     */
    @Excel(name = "采样时间", width = 15, format = "yyyy-MM-dd HH:mm:ss", orderNum = "5")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "核验时间")
    private LocalDateTime checkTime;
    private String reportId;
    private String gender;
    private Long reqId;
    @Excel(name = "检测原因", width = 15, orderNum = "6")
    private String detectionReason;
    @TableField(exist = false)
    private Profile profile;
    private String reportJsonFlag;
    @Excel(name = "样本条码", width = 15)
    private String barcode;
    @Excel(name = "报告结果", width = 15)
    private String reportResult;
    /**
     * 报告时间
     */
    @Excel(name = "报告时间", width = 15, format = "yyyy-MM-dd HH:mm:ss", orderNum = "5")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报告时间")
    private LocalDateTime reportTime;
    private String departId;
    private String departName;
    @Excel(name = "预约场景", width = 15)
    private String caseParam;
    @Excel(name = "报告是否提交", width = 15)
    private String platformCommited;
    @Excel(name = "报告提交错误提示", width = 15)
    private String plarformCommitErrmsg;
    private String fixRemark;
    @TableField(exist = false)
    private String preBarcode;
//    @TableField(exist = false)
//    private CovidSurvey survey;
    @TableField(exist = false)
    private Integer printTimesOnce;

}
