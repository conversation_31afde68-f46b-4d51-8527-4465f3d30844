package org.jeecg.modules.appointment.service;

import org.jeecg.modules.appointment.entity.AppointmentUnavailableConfig;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: appointment_unavailable_config
 * @Author: jeecg-boot
 * @Date:   2025-06-09
 * @Version: V1.0
 */
public interface IAppointmentUnavailableConfigService extends IService<AppointmentUnavailableConfig> {

}
