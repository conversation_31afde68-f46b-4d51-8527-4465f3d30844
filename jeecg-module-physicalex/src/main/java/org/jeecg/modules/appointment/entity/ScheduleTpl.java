package org.jeecg.modules.appointment.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;

/**
 * @Description: 预约排班
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Data
@TableName("schedule_tpl")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "schedule_tpl对象", description = "预约排班")
public class ScheduleTpl implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属日期
     */
    @Excel(name = "所属设置", width = 15)
    @ApiModelProperty(value = "所属设置")
    private String settingId;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 开始时间
     */
    @Excel(name = "开始时间", width = 15, format = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    @JSONField(format = "HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private LocalTime startTime;
    /**
     * 结束时间
     */
    @Excel(name = "结束时间", width = 15, format = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    @JSONField(format = "HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private LocalTime endTime;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 启用
     */
    @Excel(name = "启用", width = 15)
    @ApiModelProperty(value = "启用")
    private String enableFlag;
    /**
     * 租户
     */
    @Excel(name = "租户", width = 15)
    @ApiModelProperty(value = "租户")
    private String tenantId;
    /**
     * 限制数量
     */
    @Excel(name = "限制数量", width = 15)
    @ApiModelProperty(value = "限制数量")
    private Integer limitAmount;
    @TableLogic(value = "0",delval = "1")
    private String delFlag;

    @TableField(exist = false)
    private Integer availableAmount;

    @TableField(exist = false)
    private Boolean updateSchedule;

}
