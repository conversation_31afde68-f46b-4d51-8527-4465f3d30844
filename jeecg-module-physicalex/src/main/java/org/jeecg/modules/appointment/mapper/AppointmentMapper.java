package org.jeecg.modules.appointment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.appointment.entity.Appointment;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Description: 预约
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
public interface AppointmentMapper extends BaseMapper<Appointment> {
    List<Appointment> getTimeoutAppointments();

    Map<String, Long> statCurrDay(@Param("tenantId") String tenantId);

    Map<String, Long> stat(@Param("tenantId") String tenantId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<Appointment> getUnreported(@Param("tenantId") String tenantId,Integer hours,@Param("id")String id,@Param("name")String name,@Param("appointTime")String appointTime,@Param("barcode")String barcode,@Param("idcard")String idcard);

    Page<Appointment> pageUnreported(Page<Appointment> page,@Param("tenantId") String tenantId,@Param("hours") Integer hours,@Param("id")String id,@Param("name")String name,@Param("appointTime")String appointTime,@Param("barcode")String barcode,@Param("idcard")String idcard);

    Long  countUnreported(@Param("tenantId") String tenantId,@Param("hours") Integer hours);

    // Map<String,Long> stat();
}
