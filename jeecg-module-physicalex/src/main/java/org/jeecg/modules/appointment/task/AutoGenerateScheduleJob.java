package org.jeecg.modules.appointment.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.appointment.service.IAppointmentScheduleService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoGenerateScheduleJob implements Job {

    @Autowired
    private IAppointmentScheduleService appointmentScheduleService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                appointmentScheduleService.generateSchedule(null,null);
            } catch (Exception e) {
                log.error("生成自动排班异常！", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("生成自动排班任务正在执行中!");
        }
    }
}