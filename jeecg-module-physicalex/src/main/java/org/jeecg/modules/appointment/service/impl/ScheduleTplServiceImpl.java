package org.jeecg.modules.appointment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
/*import org.jeecg.common.constant.ConstantDict;
import org.jeecg.modules.appointment.entity.DetectionSetting;
import org.jeecg.modules.order.entity.ScheduleTpl;
import org.jeecg.modules.appointment.exception.AppointmentException;
import org.jeecg.modules.appointment.mapper.ScheduleTplMapper;
import org.jeecg.modules.appointment.service.IDetectionSettingService;
import org.jeecg.modules.appointment.service.IScheduleTplService;
import org.jeecg.modules.uid.UidGenerator;*/
import org.jeecg.modules.appointment.entity.AppointmentSetting;
import org.jeecg.modules.appointment.exception.AppointmentException;
import org.jeecg.modules.appointment.uid.UidGenerator;
import org.jeecg.modules.appointment.entity.ScheduleTpl;
import org.jeecg.modules.appointment.mapper.ScheduleTplMapper;
import org.jeecg.modules.appointment.service.IAppointmentSettingService;
import org.jeecg.modules.appointment.service.IScheduleTplService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 预约排班
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Service
public class ScheduleTplServiceImpl extends ServiceImpl<ScheduleTplMapper, ScheduleTpl> implements IScheduleTplService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private IAppointmentSettingService iAppointmentSettingService;
    @Autowired
    private UidGenerator uidGenerator;
    @Autowired
    private ScheduleTplMapper ScheduleTplMapper;
    @Autowired
    private StockService stockService;

    @Override
    public void generateScheduleTpl(String settingId) throws AppointmentException {
        AppointmentSetting setting = iAppointmentSettingService.getById(settingId);

        if (setting != null) {

            LocalTime startTime = LocalTime.parse(setting.getDayStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
            LocalTime endTime = LocalTime.parse(setting.getDayEndTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
            List<ScheduleTpl> dayScheduleList = generateSchedule4Day(settingId, startTime, endTime, setting.getIntervalVal(), setting.getIntervalUnit(), setting.getIntervalLimit(), setting.getTenantId());

            jdbcTemplate.update("delete from schedule_tpl where setting_id=?", settingId);
            saveBatch(dayScheduleList);
        } else {
            throw new AppointmentException("未找到可用的预约设置！");
        }
    }

    @Override
    public void updateScheduleTpl(String settingId) throws AppointmentException {
        AppointmentSetting setting = iAppointmentSettingService.getById(settingId);
        if (setting != null) {

            jdbcTemplate.update("update schedule_tpl set limit_amount=? where setting_id=?",setting.getIntervalLimit(), settingId);
        } else {
            throw new AppointmentException("未找到可用的预约设置！");
        }
    }

    private List<ScheduleTpl> generateSchedule4Day(String settingId, LocalTime startTime, LocalTime
            endTime, Integer intervalVal, String intervalUnit, Integer intervalLimit, String tenantId) {
        List<ScheduleTpl> dayScheduleList = new ArrayList<>();
        if (StringUtils.equals(intervalUnit, "d")) {
            ScheduleTpl schedule = new ScheduleTpl();
            schedule.setSettingId(settingId);
            schedule.setEnableFlag("1");
            schedule.setStartTime(startTime);
            schedule.setEndTime(endTime);
            schedule.setLimitAmount(intervalLimit);
            schedule.setTenantId(tenantId);
            dayScheduleList.add(schedule);
        } else if (StringUtils.equals(intervalUnit, "h")) {
            Duration duration = Duration.between(startTime, endTime);
            Long durationHours = duration.toHours();
            Long part = durationHours / intervalVal;//分段数

            for (int i = 0; i <= part; i++) {
                LocalTime t0 = startTime.plusHours(i * intervalVal);
                LocalTime t1 = t0.plusHours(intervalVal);

                if (t0.isAfter(t1)) {
                    t1 = endTime;
                }

                ScheduleTpl schedule = new ScheduleTpl();
                schedule.setSettingId(settingId);
                schedule.setEnableFlag("1");
                schedule.setStartTime(t0);
                schedule.setEndTime(t1);
                schedule.setLimitAmount(intervalLimit);
                schedule.setTenantId(tenantId);
                dayScheduleList.add(schedule);

                if (t0.isAfter(t1)) {
                    break;
                }
            }
        } else if (StringUtils.equals(intervalUnit, "m")) {
            Duration duration = Duration.between(startTime, endTime);
            Long durationHours = duration.toMinutes();
            Long part = durationHours / intervalVal;//分段数

            for (int i = 0; i <= part; i++) {
                LocalTime t0 = startTime.plusMinutes(i * intervalVal);
                LocalTime t1 = t0.plusMinutes(intervalVal);

                if (t0.isAfter(t1)) {
                    t1 = endTime;
                }

                ScheduleTpl schedule = new ScheduleTpl();
                schedule.setSettingId(settingId);
                schedule.setEnableFlag("1");
                schedule.setStartTime(t0);
                schedule.setEndTime(t1);
                schedule.setLimitAmount(intervalLimit);
                schedule.setTenantId(tenantId);
                dayScheduleList.add(schedule);

                if (t0.isAfter(t1)) {
                    break;
                }
            }
        }

        return dayScheduleList;

    }

    @Override
    public List<ScheduleTpl> selectByMainId(String mainId) {
        return ScheduleTplMapper.selectByMainId(mainId);
    }
}
