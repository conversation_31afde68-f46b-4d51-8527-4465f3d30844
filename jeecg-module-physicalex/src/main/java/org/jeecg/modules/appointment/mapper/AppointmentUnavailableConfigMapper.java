package org.jeecg.modules.appointment.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.appointment.entity.AppointmentUnavailableConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: appointment_unavailable_config
 * @Author: jeecg-boot
 * @Date:   2025-06-09
 * @Version: V1.0
 */
public interface AppointmentUnavailableConfigMapper extends BaseMapper<AppointmentUnavailableConfig> {

}
