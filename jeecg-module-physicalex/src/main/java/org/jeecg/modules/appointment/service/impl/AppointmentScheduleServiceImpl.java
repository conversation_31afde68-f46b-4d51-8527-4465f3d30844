package org.jeecg.modules.appointment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.appointment.entity.*;
import org.jeecg.modules.appointment.exception.AppointmentException;
import org.jeecg.modules.appointment.service.*;
import org.jeecg.modules.appointment.uid.UidGenerator;
import org.jeecg.modules.appointment.mapper.AppointmentScheduleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 预约排班
 * @Author: jeecg-boot
 * @Date: 2022-01-19
 * @Version: V1.0
 */
@Service
public class AppointmentScheduleServiceImpl extends ServiceImpl<AppointmentScheduleMapper, AppointmentSchedule> implements IAppointmentScheduleService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private IAppointmentSettingService iAppointmentSettingService;
    @Autowired
    private UidGenerator uidGenerator;
    @Autowired
    private AppointmentScheduleMapper appointmentScheduleMapper;
    @Autowired
    private StockService stockService;
    @Autowired
    private IScheduleTplService scheduleTplService;
    @Autowired
    private ICustomerOrderService customerOrderService;
    @Autowired
    private IAppointmentUnavailableConfigService appointmentUnavailableConfigService;

    @Override
    public void generateSchedule(String tenantId,String settingId) throws AppointmentException {
        QueryWrapper<AppointmentSetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable_flag", "1");
        if (StringUtils.isNotBlank(settingId)){
            queryWrapper.eq("id", settingId);
        }else{
            queryWrapper.eq("auto_job_flag","1");
        }
        //queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.last("limit 1");
        AppointmentSetting setting = iAppointmentSettingService.getOne(queryWrapper);

        if (setting != null) {

            Integer preDays = setting.getPreDays();
            QueryWrapper<ScheduleTpl> scheduleTplQueryWrapper = new QueryWrapper<>();
            scheduleTplQueryWrapper.eq("setting_id", setting.getId());
            scheduleTplQueryWrapper.orderByAsc("start_time");
            List<ScheduleTpl> scheduleTpls = scheduleTplService.list(scheduleTplQueryWrapper);
            for (int i = 0; i < preDays; i++) {
                LocalDate currentDay = LocalDate.now().plusDays(i);
                List<AppointmentSchedule> dayScheduleList = generateSchedule4DayFromTpl(currentDay, scheduleTpls);
                Integer existCount = jdbcTemplate.queryForObject("select count(1) from appointment_schedule where own_date=? and enable_flag='1'", Integer.class, currentDay);
                if (existCount == 0) {
                    saveBatch(dayScheduleList);
                    for (AppointmentSchedule schedule : dayScheduleList) {
                        stockService.initStock(schedule.getId());
                    }
                }
            }
        } else {
            throw new AppointmentException("未找到可用的预约设置！");
        }
    }

    @Override
    public void saveScheduleByTpl(ScheduleTpl scheduleTpl) throws AppointmentException {

        AppointmentSetting setting = iAppointmentSettingService.getById(scheduleTpl.getSettingId());

        if (setting != null) {
            Integer preDays = setting.getPreDays();
            for (int i = 0; i < preDays; i++) {
                LocalDate currentDay = LocalDate.now().plusDays(i);
                List<AppointmentSchedule> dayScheduleList = generateSchedule4DayFromTpl(currentDay, Arrays.asList(scheduleTpl));

                saveBatch(dayScheduleList);
                for (AppointmentSchedule schedule : dayScheduleList) {
                    stockService.initStock(schedule.getId());
                }
            }
        } else {
            throw new AppointmentException("未找到可用的预约设置！");
        }
    }

    @Override
    public void updateSchedule(String tenantId) throws AppointmentException {

        QueryWrapper<AppointmentSetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable_flag", "true");
        queryWrapper.eq("tenant_id","1");
        queryWrapper.last("limit 1");
        AppointmentSetting setting = iAppointmentSettingService.getOne(queryWrapper);

        if (setting != null) {

            updateScheduleBySetting(tenantId, setting.getId());
        } else {
            throw new AppointmentException("未找到可用的预约设置！");
        }
    }

    @Override
    public void generateScheduleBySetting(String settingId) {

        AppointmentSetting setting = iAppointmentSettingService.getById(settingId);

        Integer preDays = setting.getPreDays();
        QueryWrapper<ScheduleTpl> scheduleTplQueryWrapper = new QueryWrapper<>();
        scheduleTplQueryWrapper.eq("setting_id", setting.getId());
        scheduleTplQueryWrapper.orderByAsc("start_time");
        List<ScheduleTpl> scheduleTpls = scheduleTplService.list(scheduleTplQueryWrapper);
        for (int i = 0; i < preDays; i++) {
            LocalDate currentDay = LocalDate.now().plusDays(i);
            List<AppointmentSchedule> dayScheduleList = generateSchedule4DayFromTpl(currentDay, scheduleTpls);
            Integer existCount = jdbcTemplate.queryForObject("select count(1) from appointment_schedule where own_date=? and enable_flag='1'", Integer.class, currentDay);
            if (existCount == 0) {
                saveBatch(dayScheduleList);
                for (AppointmentSchedule schedule : dayScheduleList) {
                    stockService.initStock(schedule.getId());
                }
            }
        }
    }

    @Override
    public void updateScheduleBySetting(String tenantId, String settingId) {

        AppointmentSetting setting = iAppointmentSettingService.getById(settingId);
        QueryWrapper<ScheduleTpl> scheduleTplQueryWrapper = new QueryWrapper<>();
        scheduleTplQueryWrapper.eq("setting_id", setting.getId());
        List<ScheduleTpl> scheduleTpls = scheduleTplService.list(scheduleTplQueryWrapper);

        List<Object[]> batchUpdateParam = new ArrayList<>();
        for (ScheduleTpl tpl : scheduleTpls) {
            List<AppointmentSchedule> scheduleList = appointmentScheduleMapper.getValidScheduleByTpl(tenantId, tpl.getId());
            for (AppointmentSchedule schedule : scheduleList) {

                Object[] param = new Object[3];
                param[0] = tpl.getEnableFlag();
                param[1] = tpl.getLimitAmount();
                param[2] = schedule.getId();
                batchUpdateParam.add(param);
            }
        }
        //批量更新预约排班
        jdbcTemplate.batchUpdate("update appointment_schedule set enable_flag=?,limit_amount=? where id=?", batchUpdateParam);

        //更新缓存中的库存
        for (Object[] param : batchUpdateParam) {
            String scheduleId = (String) param[2];
            stockService.initStock(scheduleId);
        }
    }

    @Override
    public void updateValidScheduleByTpl(String tenantId, Integer limitAmount, String enableFlag, LocalTime startTime, LocalTime endTime, String... tplId) {
        appointmentScheduleMapper.updateValidScheduleByTpls(limitAmount, enableFlag, startTime, endTime, Arrays.asList(tplId));
        List<AppointmentSchedule> scheduleList = getValidScheduleByTpl(tenantId, tplId);
        for (AppointmentSchedule schedule : scheduleList) {
            stockService.initStock(schedule.getId());
        }
    }

    @Override
    public List<AppointmentSchedule> getValidScheduleBySetting(String tenantId, String settingId) {
        return appointmentScheduleMapper.getValidScheduleByTpl(tenantId, settingId);
    }

    @Override
    public List<AppointmentSchedule> getValidScheduleByTpl(String tenantId, String... tplId) {
        return appointmentScheduleMapper.getValidScheduleByTpls(tenantId, Arrays.asList(tplId));
    }

    @Override
    public List<DaySchedule> getAllSchedule(String tenantId) {
        LocalDate currentDate=LocalDate.now();
        List<LocalDate> dayList = jdbcTemplate.queryForList("select distinct(own_date) from appointment_schedule where tenant_id=? and own_date>=CURRENT_DATE order by own_date", LocalDate.class, tenantId);
        List<DaySchedule> dayScheduleList = new ArrayList<>();
        for (LocalDate day : dayList) {
            DaySchedule daySchedule = new DaySchedule();
            daySchedule.setOwnDate(day);
            List<AppointmentSchedule> scheduleList = getSchedule4Day(day);
            AppointmentUnavailableConfig unavailableConfig = appointmentUnavailableConfigService.getOne(new LambdaQueryWrapper<AppointmentUnavailableConfig>().eq(AppointmentUnavailableConfig::getUnavailableDate, day).last("limit 1"));
            if (Objects.nonNull(unavailableConfig)) {
                if (Objects.isNull(unavailableConfig.getStartTime()) || Objects.isNull(unavailableConfig.getEndTime())) {
                    for (AppointmentSchedule schedule : scheduleList) {
                        schedule.setEnableFlag("0");
                    }
                } else {
                    LocalTime unavailableStartTime = unavailableConfig.getStartTime();
                    LocalTime unavailableEndTime = unavailableConfig.getEndTime();
                    LocalTime currentTime = LocalTime.now();
                    
                    for (AppointmentSchedule schedule : scheduleList) {
                        LocalTime scheduleStartTime = schedule.getStartTime();
                        LocalTime scheduleEndTime = schedule.getEndTime();
                        boolean isOutsideUnavailableRange = (scheduleEndTime.isBefore(unavailableStartTime) || scheduleStartTime.isAfter(unavailableEndTime));
                        if (day.isBefore(currentDate)) {
                            schedule.setAvailableFlag("disable");
                        } else if (day.isEqual(currentDate)) {
                            if (scheduleEndTime.isBefore(currentTime)) {
                                schedule.setAvailableFlag("disable");
                            } else {
                                schedule.setAvailableFlag(isOutsideUnavailableRange ? "enable" : "disable");
                            }
                        } else {
                            schedule.setAvailableFlag(isOutsideUnavailableRange ? "enable" : "disable");
                        }
                        if ("enable".equals(schedule.getAvailableFlag())) {
                            Integer availableAmount = stockService.getStock(schedule.getId());
                            schedule.setAvailableAmount(availableAmount);
                            schedule.setAppointmentAmount(schedule.getLimitAmount() - availableAmount);
                        } else {
                            schedule.setAvailableAmount(0);
                            schedule.setAppointmentAmount(0);
                        }
                    }
                }
            } else {
                for (AppointmentSchedule schedule : scheduleList) {
                    LocalTime endTime = schedule.getEndTime();
                    if (day.isBefore(currentDate)) {
                        schedule.setAvailableFlag("disable");
                    } else if (day.isEqual(currentDate) && endTime.isBefore(LocalTime.now())) {
                        schedule.setAvailableFlag("disable");
                    } else {
                        schedule.setAvailableFlag("enable");
                    }
                    Integer availableAmount = stockService.getStock(schedule.getId());
                    schedule.setAvailableAmount(availableAmount);
                    schedule.setAppointmentAmount(schedule.getLimitAmount() - availableAmount);
                }
            }
            daySchedule.setScheduleList(scheduleList);
            dayScheduleList.add(daySchedule);
        }

        return dayScheduleList;
    }
    @Override
    public List<AppointmentSchedule> getDaySchedule(String tenantId, LocalDate day) {
        List<AppointmentSchedule> scheduleList = getSchedule4Day(day);
        for (AppointmentSchedule schedule : scheduleList) {
            Integer availableAmount = stockService.getStock(schedule.getId());
            schedule.setAvailableAmount(availableAmount);
        }

        return scheduleList;
    }

    @Override
    @Cacheable(cacheNames = "schedule-cache")
    public List<AppointmentSchedule> getSchedule4Day(LocalDate date) {

        return appointmentScheduleMapper.getSchedule4Day(date);
    }

    @Override
    public Integer getScheduleAvailableCount(AppointmentSchedule schedule) {
        Integer limitAmount = schedule.getLimitAmount();
        Integer appontmentCount = jdbcTemplate.queryForObject("select count(1) from appointment where schedule_id=? and status !='0'", Integer.class, schedule.getId());

        return limitAmount - appontmentCount >= 0 ? limitAmount - appontmentCount : 0;
    }

    @Override
    public Integer getScheduleAvailableCount(String scheduleId) {

        Integer limitAmount = 0;
        try {
            limitAmount = jdbcTemplate.queryForObject("select limit_amount from appointment_schedule where id=?", Integer.class, scheduleId);
        } catch (Exception e) {
            limitAmount = 0;
        }

        Integer appontmentCount = jdbcTemplate.queryForObject("select count(1) from appointment where schedule_id=? and status !='0'", Integer.class, scheduleId);

        return limitAmount - appontmentCount >= 0 ? limitAmount - appontmentCount : 0;
    }

    private List<AppointmentSchedule> generateSchedule4Day(LocalDate ownDate, LocalTime startTime, LocalTime endTime, Integer intervalVal, String intervalUnit, Integer intervalLimit, String tenantId) {
        List<AppointmentSchedule> dayScheduleList = new ArrayList<>();
        if (StringUtils.equals(intervalUnit, "d")) {
            AppointmentSchedule schedule = new AppointmentSchedule();
            schedule.setId(uidGenerator.getSUID());
            schedule.setOwnDate(ownDate);
            schedule.setEnableFlag("1");
            schedule.setStartTime(startTime);
            schedule.setEndTime(endTime);
            schedule.setLimitAmount(intervalLimit);
            schedule.setTenantId(tenantId);
            dayScheduleList.add(schedule);
        } else if (StringUtils.equals(intervalUnit, "h")) {
            Duration duration = Duration.between(startTime, endTime);
            Long durationHours = duration.toHours();
            Long part = durationHours / intervalVal;//分段数

            for (int i = 0; i <= part; i++) {
                LocalTime t0 = startTime.plusHours(i * intervalVal);
                LocalTime t1 = t0.plusHours(intervalVal);

                if (t0.isAfter(t1)) {
                    t1 = endTime;
                }

                AppointmentSchedule schedule = new AppointmentSchedule();
                schedule.setId(uidGenerator.getSUID());
                schedule.setOwnDate(ownDate);
                schedule.setEnableFlag("1");
                schedule.setStartTime(t0);
                schedule.setEndTime(t1);
                schedule.setLimitAmount(intervalLimit);
                schedule.setTenantId(tenantId);
                dayScheduleList.add(schedule);

                if (t0.isAfter(t1)) {
                    break;
                }
            }
        } else if (StringUtils.equals(intervalUnit, "m")) {
            Duration duration = Duration.between(startTime, endTime);
            Long durationHours = duration.toMinutes();
            Long part = durationHours / intervalVal;//分段数

            for (int i = 0; i <= part; i++) {
                LocalTime t0 = startTime.plusMinutes(i * intervalVal);
                LocalTime t1 = t0.plusMinutes(intervalVal);

                if (t0.isAfter(t1)) {
                    t1 = endTime;
                }

                AppointmentSchedule schedule = new AppointmentSchedule();
                schedule.setId(uidGenerator.getSUID());
                schedule.setOwnDate(ownDate);
                schedule.setEnableFlag("1");
                schedule.setStartTime(t0);
                schedule.setEndTime(t1);
                schedule.setLimitAmount(intervalLimit);
                schedule.setTenantId(tenantId);
                dayScheduleList.add(schedule);

                if (t0.isAfter(t1)) {
                    break;
                }
            }
        }

        return dayScheduleList;
    }

    private List<AppointmentSchedule> generateSchedule4DayFromTpl(LocalDate ownDate, List<ScheduleTpl> scheduleTpls) {

        List<AppointmentSchedule> dayScheduleList = new ArrayList<>();

        for (ScheduleTpl scheduleTpl : scheduleTpls) {
            AppointmentSchedule schedule = new AppointmentSchedule();
            //schedule.setId(uidGenerator.getSUID());
            schedule.setOwnDate(ownDate);
            schedule.setEnableFlag(scheduleTpl.getEnableFlag());
            schedule.setStartTime(scheduleTpl.getStartTime());
            schedule.setEndTime(scheduleTpl.getEndTime());
            schedule.setLimitAmount(scheduleTpl.getLimitAmount());
            schedule.setTenantId(scheduleTpl.getTenantId());
            schedule.setSettingId(schedule.getSettingId());
            schedule.setScheduleTplId(scheduleTpl.getId());
            dayScheduleList.add(schedule);
        }
        return dayScheduleList;
    }

    @Override
    public void batchUpdateEnableFlag(List<String> idList, String enableFlag) {
        LambdaUpdateWrapper<AppointmentSchedule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AppointmentSchedule::getEnableFlag, enableFlag).in(AppointmentSchedule::getId, idList);
        update(updateWrapper);

    }
}
