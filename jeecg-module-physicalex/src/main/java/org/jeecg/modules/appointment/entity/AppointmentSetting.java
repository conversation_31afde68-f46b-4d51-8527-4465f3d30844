package org.jeecg.modules.appointment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 检测设置
 * @Author: jeecg-boot
 * @Date:   2022-01-19
 * @Version: V1.0
 */
@Data
@TableName("appointment_setting")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="appointment_setting", description="预约设置")
public class AppointmentSetting implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**租户*/
	@Excel(name = "租户", width = 15)
    @ApiModelProperty(value = "租户")
    private String tenantId;
	/**可约天数*/
	@Excel(name = "可约天数", width = 15)
    @ApiModelProperty(value = "可约天数")
    private Integer preDays;
	/**是否开启*/
	@Excel(name = "是否开启", width = 15)
    @ApiModelProperty(value = "是否开启")
    private String enableFlag;
	/**单次费用*/
	@Excel(name = "单次费用", width = 15)
    @ApiModelProperty(value = "单次费用")
    private BigDecimal preFee;
	/**每日开始时间*/
//	@Excel(name = "每日开始时间", width = 15, format = "yyyy-MM-dd")
//	@JsonFormat(timezone = "GMT+8",pattern = "HH:mm:ss")
//    @DateTimeFormat(pattern="HH:mm:ss")
    @ApiModelProperty(value = "每日开始时间")
    private String dayStartTime;
	/**每日结束时间*/
//	@Excel(name = "每日结束时间", width = 15, format = "HH:mm:ss")
//	@JsonFormat(timezone = "GMT+8",pattern = "HH:mm:ss")
//    @DateTimeFormat(pattern="HH:mm:ss")
    @ApiModelProperty(value = "每日结束时间")
    private String dayEndTime;
	/**间隔数值*/
	@Excel(name = "间隔数值", width = 15)
    @ApiModelProperty(value = "间隔数值")
    private Integer intervalVal;
	/**间隔单位*/
	@Excel(name = "间隔单位", width = 15)
    @Dict(dicCode = "interval_unit")
    @ApiModelProperty(value = "间隔单位")
    private String intervalUnit;
    /**间隔限制人数*/
    @Excel(name = "间隔限制人数", width = 15)
    @ApiModelProperty(value = "间隔限制人数")
	private Integer intervalLimit;
    @TableLogic
    private String delFlag;
    private String enableBarcode;
    @ApiModelProperty(value = "定时任务使用的标志")
    private String autoJobFlag;

    @TableField(exist = false)
    private Boolean updateSchedule;
}
