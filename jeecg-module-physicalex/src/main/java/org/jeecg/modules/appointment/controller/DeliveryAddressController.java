package org.jeecg.modules.appointment.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.appointment.entity.DeliveryAddress;
import org.jeecg.modules.appointment.service.IDeliveryAddressService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: delivery_address
 * @Author: jeecg-boot
 * @Date:   2024-10-11
 * @Version: V1.0
 */
@Api(tags="delivery_address")
@RestController
@RequestMapping("/order/deliveryAddress")
@Slf4j
public class DeliveryAddressController extends JeecgController<DeliveryAddress, IDeliveryAddressService> {
	@Autowired
	private IDeliveryAddressService deliveryAddressService;
	
	/**
	 * 分页列表查询
	 *
	 * @param deliveryAddress
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "delivery_address-分页列表查询")
	@ApiOperation(value="delivery_address-分页列表查询", notes="delivery_address-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<DeliveryAddress>> queryPageList(DeliveryAddress deliveryAddress,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DeliveryAddress> queryWrapper = QueryGenerator.initQueryWrapper(deliveryAddress, req.getParameterMap());
		Page<DeliveryAddress> page = new Page<DeliveryAddress>(pageNo, pageSize);
		IPage<DeliveryAddress> pageList = deliveryAddressService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param deliveryAddress
	 * @return
	 */
	@AutoLog(value = "delivery_address-添加")
	@ApiOperation(value="delivery_address-添加", notes="delivery_address-添加")
//	@RequiresPermissions("order:delivery_address:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody DeliveryAddress deliveryAddress) {
		deliveryAddressService.save(deliveryAddress);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param deliveryAddress
	 * @return
	 */
	@AutoLog(value = "delivery_address-编辑")
	@ApiOperation(value="delivery_address-编辑", notes="delivery_address-编辑")
//	@RequiresPermissions("order:delivery_address:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody DeliveryAddress deliveryAddress) {
		deliveryAddressService.updateById(deliveryAddress);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "delivery_address-通过id删除")
	@ApiOperation(value="delivery_address-通过id删除", notes="delivery_address-通过id删除")
//	@RequiresPermissions("order:delivery_address:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		deliveryAddressService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "delivery_address-批量删除")
	@ApiOperation(value="delivery_address-批量删除", notes="delivery_address-批量删除")
//	@RequiresPermissions("order:delivery_address:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.deliveryAddressService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "delivery_address-通过id查询")
	@ApiOperation(value="delivery_address-通过id查询", notes="delivery_address-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<DeliveryAddress> queryById(@RequestParam(name="id",required=true) String id) {
		DeliveryAddress deliveryAddress = deliveryAddressService.getById(id);
		if(deliveryAddress==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(deliveryAddress);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param deliveryAddress
    */
    @RequiresPermissions("order:delivery_address:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DeliveryAddress deliveryAddress) {
        return super.exportXls(request, deliveryAddress, DeliveryAddress.class, "delivery_address");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("order:delivery_address:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DeliveryAddress.class);
    }

}
