package org.jeecg.modules.appointment.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.appointment.entity.AppointmentUnavailableConfig;
import org.jeecg.modules.appointment.service.IAppointmentUnavailableConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: appointment_unavailable_config
 * @Author: jeecg-boot
 * @Date:   2025-06-09
 * @Version: V1.0
 */
@Api(tags="appointment_unavailable_config")
@RestController
@RequestMapping("/appointment/appointmentUnavailableConfig")
@Slf4j
public class AppointmentUnavailableConfigController extends JeecgController<AppointmentUnavailableConfig, IAppointmentUnavailableConfigService> {
	@Autowired
	private IAppointmentUnavailableConfigService appointmentUnavailableConfigService;
	
	/**
	 * 分页列表查询
	 *
	 * @param appointmentUnavailableConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "appointment_unavailable_config-分页列表查询")
	@ApiOperation(value="appointment_unavailable_config-分页列表查询", notes="appointment_unavailable_config-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AppointmentUnavailableConfig>> queryPageList(AppointmentUnavailableConfig appointmentUnavailableConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<AppointmentUnavailableConfig> queryWrapper = QueryGenerator.initQueryWrapper(appointmentUnavailableConfig, req.getParameterMap());
		Page<AppointmentUnavailableConfig> page = new Page<AppointmentUnavailableConfig>(pageNo, pageSize);
		IPage<AppointmentUnavailableConfig> pageList = appointmentUnavailableConfigService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param appointmentUnavailableConfig
	 * @return
	 */
	@AutoLog(value = "appointment_unavailable_config-添加")
	@ApiOperation(value="appointment_unavailable_config-添加", notes="appointment_unavailable_config-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AppointmentUnavailableConfig appointmentUnavailableConfig) {
		appointmentUnavailableConfigService.save(appointmentUnavailableConfig);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param appointmentUnavailableConfig
	 * @return
	 */
	@AutoLog(value = "appointment_unavailable_config-编辑")
	@ApiOperation(value="appointment_unavailable_config-编辑", notes="appointment_unavailable_config-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AppointmentUnavailableConfig appointmentUnavailableConfig) {
		appointmentUnavailableConfigService.updateById(appointmentUnavailableConfig);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "appointment_unavailable_config-通过id删除")
	@ApiOperation(value="appointment_unavailable_config-通过id删除", notes="appointment_unavailable_config-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		appointmentUnavailableConfigService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "appointment_unavailable_config-批量删除")
	@ApiOperation(value="appointment_unavailable_config-批量删除", notes="appointment_unavailable_config-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.appointmentUnavailableConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "appointment_unavailable_config-通过id查询")
	@ApiOperation(value="appointment_unavailable_config-通过id查询", notes="appointment_unavailable_config-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AppointmentUnavailableConfig> queryById(@RequestParam(name="id",required=true) String id) {
		AppointmentUnavailableConfig appointmentUnavailableConfig = appointmentUnavailableConfigService.getById(id);
		if(appointmentUnavailableConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(appointmentUnavailableConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param appointmentUnavailableConfig
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AppointmentUnavailableConfig appointmentUnavailableConfig) {
        return super.exportXls(request, appointmentUnavailableConfig, AppointmentUnavailableConfig.class, "appointment_unavailable_config");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AppointmentUnavailableConfig.class);
    }

}
