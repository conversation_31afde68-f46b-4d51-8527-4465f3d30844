package org.jeecg.modules.appointment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.MustacheUtil;
import org.jeecg.modules.appointment.entity.CompanyAppointment;
import org.jeecg.modules.appointment.mapper.CompanyAppointmentMapper;
import org.jeecg.modules.appointment.service.ICompanyAppointmentService;
import org.jeecg.modules.basicinfo.entity.SmsNotifySetting;
import org.jeecg.modules.basicinfo.service.ISmsNotifySettingService;
import org.jeecg.modules.sms.bo.SmsResult;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: company_appointment
 * @Author: jeecg-boot
 * @Date: 2024-10-28
 * @Version: V1.0
 */
@Service
@Slf4j
public class CompanyAppointmentServiceImpl extends ServiceImpl<CompanyAppointmentMapper, CompanyAppointment> implements ICompanyAppointmentService {
    @Autowired
    private CompanyAppointmentMapper companyAppointmentMapper;
    @Autowired
    private ISmsRecordsService smsRecordsService;
    @Autowired
    private ISmsNotifySettingService smsNotifySettingService;

    @Override
    public void executeNotify() {

        SmsNotifySetting notifySetting = smsNotifySettingService.getByBusinessType(ExConstants.SMS_NOTIFY_MODULE_团检接洽);
        if (notifySetting == null) {
            log.error("未配置团检接洽业务的短信通知设置");
            return;
        }
        // 1. 查询预约信息
        LambdaQueryWrapper<CompanyAppointment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(CompanyAppointment::getNotifyStatus, 1);
        queryWrapper.ne(CompanyAppointment::getDealStatus, 1);
        List<CompanyAppointment> appointmentList = companyAppointmentMapper.selectList(queryWrapper);
        for (CompanyAppointment appointment : appointmentList) {
            // 2. 发送短信通知
            Map<String, Object> params = new HashMap<>();
            params.put("company", appointment.getCompanyName());
            params.put("count", appointment.getExamCount());
            params.put("pepoleBudget", appointment.getPepoleBudget());
            params.put("phone", appointment.getContactPhone());
            params.put("concat", appointment.getContactName());
            String content = MustacheUtil.render(notifySetting.getContent(), params);
            try {
                String targetPhone = StringUtils.replaceChars(notifySetting.getTargetPhone(), "，", ",");
                List<String> phones = List.of(StringUtils.split(targetPhone, ","));
                for (String phone : phones) {
                    SmsResult smsResult = smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "0", phone, content, appointment.getId(), ExConstants.SMS_BIZ_TYPE_团检接洽);
                }
                LambdaUpdateWrapper<CompanyAppointment> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(CompanyAppointment::getId, appointment.getId());
                updateWrapper.set(CompanyAppointment::getNotifyStatus, 1);
                companyAppointmentMapper.update(null, updateWrapper);
            } catch (Exception e) {
                log.error("团检接洽业务的短信通知失败", e);
            }
        }
    }

    @Override
    public void updateDealStatus(String id) {
        LambdaUpdateWrapper<CompanyAppointment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CompanyAppointment::getId, id);
        updateWrapper.set(CompanyAppointment::getDealStatus, 1);
        companyAppointmentMapper.update(null, updateWrapper);
    }

}
