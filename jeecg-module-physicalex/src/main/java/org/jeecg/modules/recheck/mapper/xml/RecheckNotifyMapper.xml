<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.recheck.mapper.RecheckNotifyMapper">

    <select id="countByCustomerRegId" resultType="java.lang.Long">
        select count(1) from recheck_notify where customer_reg_id = #{customerRegId}
    </select>
    <select id="listByCustomerRegId" resultType="org.jeecg.modules.recheck.entity.RecheckNotify">
        select * from recheck_notify where customer_reg_id = #{customerRegId}
    </select>
</mapper>