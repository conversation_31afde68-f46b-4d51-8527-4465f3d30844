package org.jeecg.modules.recheck.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.recheck.service.IRecheckNotifyService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoSendRecheckNotifyJob implements Job {

    @Autowired
    private IRecheckNotifyService recheckNotifyService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                recheckNotifyService.sendRecheckNotifyJob();
            } catch (Exception e) {
                log.error("复查提醒发送异常", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("复查提醒正在发送!");
        }
    }
}