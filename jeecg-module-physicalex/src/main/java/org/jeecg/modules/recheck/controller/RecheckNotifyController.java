package org.jeecg.modules.recheck.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.recheck.entity.RecheckNotify;
import org.jeecg.modules.recheck.service.IRecheckNotifyService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;

/**
 * @Description: 复查提醒
 * @Author: jeecg-boot
 * @Date: 2024-05-28
 * @Version: V1.0
 */
@Api(tags = "复查提醒")
@RestController
@RequestMapping("/recheck/recheckNotify")
@Slf4j
public class RecheckNotifyController extends JeecgController<RecheckNotify, IRecheckNotifyService> {
    @Autowired
    private IRecheckNotifyService recheckNotifyService;
    @Autowired
    private ICustomerRegService customerRegService;

    //countByCustomerRegId
    @ApiOperation(value = "复查提醒-分页列表查询", notes = "复查提醒-分页列表查询")
    @GetMapping(value = "/countByCustomerRegId")
    public Result<?> countByCustomerRegId(String customerRegId) {
        Long count = recheckNotifyService.countByCustomerRegId(customerRegId);
        return Result.OK(count);
    }


    /**
     * 分页列表查询
     *
     * @param recheckNotify
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "复查提醒-分页列表查询")
    @ApiOperation(value = "复查提醒-分页列表查询", notes = "复查提醒-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<RecheckNotify>> queryPageList(RecheckNotify recheckNotify,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        QueryWrapper<RecheckNotify> queryWrapper = QueryGenerator.initQueryWrapper(recheckNotify, req.getParameterMap());
        Page<RecheckNotify> page = new Page<RecheckNotify>(pageNo, pageSize);
        IPage<RecheckNotify> pageList = recheckNotifyService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param recheckNotify
     * @return
     */
    @AutoLog(value = "复查提醒-添加")
    @ApiOperation(value = "复查提醒-添加", notes = "复查提醒-添加")
    @RequiresPermissions("recheck:recheck_notify:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody RecheckNotify recheckNotify) {
        CustomerReg reg = customerRegService.getById(recheckNotify.getCustomerRegId());
        if (Objects.nonNull(reg)){
            recheckNotify.setPhone(reg.getPhone());
            recheckNotify.setName(reg.getName());
        }
        recheckNotify.setNotifyResult("待发送");
        recheckNotifyService.save(recheckNotify);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param recheckNotify
     * @return
     */
    @AutoLog(value = "复查提醒-编辑")
    @ApiOperation(value = "复查提醒-编辑", notes = "复查提醒-编辑")
    @RequiresPermissions("recheck:recheck_notify:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RecheckNotify recheckNotify) {
        recheckNotifyService.updateById(recheckNotify);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "复查提醒-通过id删除")
    @ApiOperation(value = "复查提醒-通过id删除", notes = "复查提醒-通过id删除")
    //@RequiresPermissions("recheck:recheck_notify:delete")
    @RequiresPermissions("recheck:recheck_notify:add")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        recheckNotifyService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "复查提醒-批量删除")
    @ApiOperation(value = "复查提醒-批量删除", notes = "复查提醒-批量删除")
    @RequiresPermissions("recheck:recheck_notify:add")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.recheckNotifyService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "复查提醒-通过id查询")
    @ApiOperation(value = "复查提醒-通过id查询", notes = "复查提醒-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<RecheckNotify> queryById(@RequestParam(name = "id", required = true) String id) {
        RecheckNotify recheckNotify = recheckNotifyService.getById(id);
        if (recheckNotify == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(recheckNotify);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param recheckNotify
     */
    @RequiresPermissions("recheck:recheck_notify:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RecheckNotify recheckNotify) {
        return super.exportXls(request, recheckNotify, RecheckNotify.class, "复查提醒");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("recheck:recheck_notify:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RecheckNotify.class);
    }

}
