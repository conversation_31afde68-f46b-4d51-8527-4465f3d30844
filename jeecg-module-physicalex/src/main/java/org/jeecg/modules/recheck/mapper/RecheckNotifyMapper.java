package org.jeecg.modules.recheck.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.recheck.entity.RecheckNotify;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 复查提醒
 * @Author: jeecg-boot
 * @Date:   2024-05-28
 * @Version: V1.0
 */
public interface RecheckNotifyMapper extends BaseMapper<RecheckNotify> {

    Long countByCustomerRegId(@Param("customerRegId") String customerRegId);

    List<RecheckNotify> listByCustomerRegId(@Param("customerRegId") String customerRegId);
}
