package org.jeecg.modules.recheck.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.MustacheUtil;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.basicinfo.entity.AutoSmsSetting;
import org.jeecg.modules.basicinfo.service.IAutoSmsSettingService;
import org.jeecg.modules.recheck.entity.RecheckNotify;
import org.jeecg.modules.recheck.mapper.RecheckNotifyMapper;
import org.jeecg.modules.recheck.service.IRecheckNotifyService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.sms.bo.SmsResult;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.testng.collections.Maps;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 复查提醒
 * @Author: jeecg-boot
 * @Date:   2024-05-28
 * @Version: V1.0
 */
@Service
public class RecheckNotifyServiceImpl extends ServiceImpl<RecheckNotifyMapper, RecheckNotify> implements IRecheckNotifyService {

    @Autowired
    private RecheckNotifyMapper recheckNotifyMapper;
    @Autowired
    private IAutoSmsSettingService autoSmsSettingService;
    @Autowired
    private ISmsRecordsService smsRecordsService;
    @Autowired
    private ICustomerRegService customerRegService;

    @Override
    public Long countByCustomerRegId(String customerRegId) {
        Long count = recheckNotifyMapper.countByCustomerRegId(customerRegId);
        return count!=null?count:0L;
    }

    @Override
    public void sendRecheckNotifyJob() {
        List<AutoSmsSetting> reportSmsSetting = autoSmsSettingService.listAutoSmsSetting(ExConstants.SMS_BIZ_TYPE_复查提醒);
        if (reportSmsSetting == null || reportSmsSetting.isEmpty()) {
            return;
        }
        AutoSmsSetting smsSetting = reportSmsSetting.get(0);
        String msgTemplate = smsSetting.getTemplateContent();
        List<RecheckNotify> notifies = list(new LambdaQueryWrapper<RecheckNotify>()
                .ne(RecheckNotify::getNotifyResult,"已发送")
                .between(RecheckNotify::getTargetDate, DateUtil.beginOfDay(new Date()),DateUtil.endOfDay(new Date())));

        if (CollectionUtils.isNotEmpty(notifies)){
            List<String> regIds = notifies.stream().map(RecheckNotify::getCustomerRegId).toList();
            Map<String, CustomerReg> regMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(regIds)) {
                List<CustomerReg> customerRegs = customerRegService.listByIds(regIds);
                regMap = customerRegs.stream().collect(Collectors.toMap(CustomerReg::getId, Function.identity(),
                        (existing, replacement) -> existing
                ));
            }
            for (RecheckNotify notify:notifies){
                try {
                    CustomerReg reg = regMap.get(notify.getCustomerRegId());
                    if (Objects.nonNull(reg)) {
                        String phone = reg.getPhone();
                        //构造短信内容
                        Map<String, Object> params = new HashMap<>();
                        params.put("name", reg.getName());
                        params.put("examNo", reg.getExamNo());
                        params.put("genderTitle", StringUtils.isNotBlank(reg.getGender()) ? StringUtils.equals(reg.getGender(), "男") ? "先生" : "女士" : "");
                        params.put("targetDate", DateUtil.format(notify.getTargetDate(), "yyyy-MM-dd"));
                        params.put("itemGroup",notify.getItemGroup());
                        params.put("riskFactor",notify.getRiskFactor());
                        params.put("regTime",reg.getRegTime());
                        params.put("regDate",DateUtil.format(reg.getRegTime(), "yyyy-MM-dd"));

                        //使用模板生成短信内容
                        String content = MustacheUtil.render(msgTemplate, params);
                        SmsResult smsResult = smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "1", phone, content, notify.getId(), ExConstants.SMS_BIZ_TYPE_复查提醒);
                        if (smsResult != null) {
                            if (smsResult.isSuccess()) {
                                notify.setNotifyResult("已发送");
                                notify.setNotifyTime(new Date());
                                updateById(notify);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("预约到检通知发送失败", e);
                }
            }
        }

    }

}
