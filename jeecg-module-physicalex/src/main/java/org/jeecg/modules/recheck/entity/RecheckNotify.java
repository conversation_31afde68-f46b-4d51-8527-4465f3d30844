package org.jeecg.modules.recheck.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 复查提醒
 * @Author: jeecg-boot
 * @Date:   2024-05-28
 * @Version: V1.0
 */
@Data
@TableName("recheck_notify")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="recheck_notify对象", description="复查提醒")
public class RecheckNotify implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**复查项目*/
	@Excel(name = "复查项目", width = 15)
    @ApiModelProperty(value = "复查项目")
    private java.lang.String itemGroup;
	/**危害因素*/
	@Excel(name = "危害因素", width = 15)
    @ApiModelProperty(value = "危害因素")
    private java.lang.String riskFactor;
	/**复查日期*/
	@Excel(name = "复查日期", width = 20, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "复查日期")
    private java.util.Date targetDate;
	/**复查备注*/
	@Excel(name = "复查备注", width = 15)
    @ApiModelProperty(value = "复查备注")
    private java.lang.String remark;
	/**通知内容*/
	@Excel(name = "通知内容", width = 15)
    @ApiModelProperty(value = "通知内容")
    private java.lang.String content;
	/**通知时间*/
	@Excel(name = "通知时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "通知时间")
    private java.util.Date notifyTime;
	/**通知结果*/
	@Excel(name = "通知结果", width = 15)
    @ApiModelProperty(value = "通知结果")
    private java.lang.String notifyResult;
	/**通知方式*/
	@Excel(name = "通知方式", width = 15)
    @ApiModelProperty(value = "通知方式")
    private java.lang.String notifyMethod;
	/**登记ID*/
	@Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
	/**总检ID*/
	@Excel(name = "总检ID", width = 15)
    @ApiModelProperty(value = "总检ID")
    private java.lang.String summaryId;
	/**体检人*/
	@Excel(name = "体检人", width = 15)
    @ApiModelProperty(value = "体检人")
    private java.lang.String name;
	/**联系电话*/
	@Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private java.lang.String phone;
}
