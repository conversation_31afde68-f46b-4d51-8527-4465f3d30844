package org.jeecg.modules.datamanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.util.List;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface CustomerRegDataManageMapper extends BaseMapper<CustomerReg> {

    Page<CustomerReg> pageCustomerReg(Page<CustomerReg> page, @Param("departmentIds") List<String> departmentId, @Param("name") String name, @Param("gender") String gender, @Param("idCard") String idCard, @Param("phone") String phone, @Param("regDateStart") String regDateStart, @Param("regDateEnd") String regDateEnd, @Param("examNo") String examNo, @Param("examCardNo") String examCardNo, @Param("interfaceSyncStatus") String interfaceSyncStatus, @Param("companyRegId") String companyRegId, @Param("teamId") String teamId, @Param("itemGroupId") String itemGroupId, @Param("itemId") String itemId);
}
