package org.jeecg.modules.datamanage.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 登记项目变更记录
 * @Author: jeecg-boot
 * @Date:   2025-03-20
 * @Version: V1.0
 */
@Data
@TableName("reg_group_change_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="reg_group_change_record对象", description="登记项目变更记录")
public class RegGroupChangeRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**体检号*/
	@Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;
	/**检客姓名*/
	@Excel(name = "检客姓名", width = 15)
    @ApiModelProperty(value = "检客姓名")
    private java.lang.String customerName;
	/**customer_reg_item_group_id*/
	@Excel(name = "customer_reg_item_group_id", width = 15)
    @ApiModelProperty(value = "customer_reg_item_group_id")
    private java.lang.String regGroupId;
	/**项目名称*/
	@Excel(name = "项目名称", width = 15)
    @ApiModelProperty(value = "项目名称")
    private java.lang.String regGroupName;
	/**变更原因*/
	@Excel(name = "变更原因", width = 15)
    @ApiModelProperty(value = "变更原因")
    private java.lang.String reason;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**操作人*/
    @ApiModelProperty(value = "操作人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
	/**变更前检查状态*/
	@Excel(name = "变更前检查状态", width = 15)
    @ApiModelProperty(value = "变更前检查状态")
    private java.lang.String oldCheckStatus;
	/**变更后检查状态*/
	@Excel(name = "变更后检查状态", width = 15)
    @ApiModelProperty(value = "变更后检查状态")
    private java.lang.String newCheckStatus;
	/**变更前接口状态*/
	@Excel(name = "变更前接口状态", width = 15)
    @ApiModelProperty(value = "变更前接口状态")
    private java.lang.String oldInferfaceStatus;
	/**变更后接口状态*/
	@Excel(name = "变更后接口状态", width = 15)
    @ApiModelProperty(value = "变更后接口状态")
    private java.lang.String newInterfaceStatus;
    @Excel(name = "变更凭证", width = 15)
    @ApiModelProperty(value = "变更凭证")
    private java.lang.String changePic;
    @Excel(name = "检查部位名称", width = 15)
    @ApiModelProperty(value = "检查部位名称")
    private java.lang.String checkPartName;
    @Excel(name = "检查部位编码", width = 15)
    @ApiModelProperty(value = "检查部位编码")
    private java.lang.String checkPartCode;


}
