package org.jeecg.modules.datamanage.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.datamanage.entity.RegGroupChangeRecord;
import org.jeecg.modules.datamanage.service.IRegGroupChangeRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 登记项目变更记录
 * @Author: jeecg-boot
 * @Date:   2025-03-20
 * @Version: V1.0
 */
@Api(tags="登记项目变更记录")
@RestController
@RequestMapping("/datamange/regGroupChangeRecord")
@Slf4j
public class RegGroupChangeRecordController extends JeecgController<RegGroupChangeRecord, IRegGroupChangeRecordService> {
	@Autowired
	private IRegGroupChangeRecordService regGroupChangeRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param regGroupChangeRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "登记项目变更记录-分页列表查询")
	@ApiOperation(value="登记项目变更记录-分页列表查询", notes="登记项目变更记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RegGroupChangeRecord>> queryPageList(RegGroupChangeRecord regGroupChangeRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
//        QueryWrapper<RegGroupChangeRecord> queryWrapper = QueryGenerator.initQueryWrapper(regGroupChangeRecord, req.getParameterMap());
		String examNo = req.getParameter("examNo");
		String customerName = req.getParameter("customerName");
		String regGroupName = req.getParameter("regGroupName");
		String createTimeStart = req.getParameter("createTimeStart");
		String createTimeEnd = req.getParameter("createTimeEnd");
		String createBy = req.getParameter("createBy");
		LambdaQueryWrapper<RegGroupChangeRecord> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtils.isNotBlank(examNo)) {
			queryWrapper.like(RegGroupChangeRecord::getExamNo,examNo);
		}
		if (StringUtils.isNotBlank(customerName)) {
			queryWrapper.like(RegGroupChangeRecord::getCustomerName,customerName);
		}
		if (StringUtils.isNotBlank(regGroupName)) {
			queryWrapper.like(RegGroupChangeRecord::getRegGroupName,regGroupName);
		}
		if (StringUtils.isNotBlank(createBy)) {
			queryWrapper.like(RegGroupChangeRecord::getCreateBy,createBy);
		}
		if (StringUtils.isNotBlank(createTimeStart)) {
			queryWrapper.between(RegGroupChangeRecord::getCreateTime,createTimeStart,createTimeEnd);
		}
		Page<RegGroupChangeRecord> page = new Page<RegGroupChangeRecord>(pageNo, pageSize);
		IPage<RegGroupChangeRecord> pageList = regGroupChangeRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param regGroupChangeRecord
	 * @return
	 */
	@AutoLog(value = "登记项目变更记录-添加")
	@ApiOperation(value="登记项目变更记录-添加", notes="登记项目变更记录-添加")
	@RequiresPermissions("datamange:reg_group_change_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RegGroupChangeRecord regGroupChangeRecord) {
		regGroupChangeRecordService.save(regGroupChangeRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param regGroupChangeRecord
	 * @return
	 */
	@AutoLog(value = "登记项目变更记录-编辑")
	@ApiOperation(value="登记项目变更记录-编辑", notes="登记项目变更记录-编辑")
	@RequiresPermissions("datamange:reg_group_change_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RegGroupChangeRecord regGroupChangeRecord) {
		regGroupChangeRecordService.updateById(regGroupChangeRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "登记项目变更记录-通过id删除")
	@ApiOperation(value="登记项目变更记录-通过id删除", notes="登记项目变更记录-通过id删除")
	@RequiresPermissions("datamange:reg_group_change_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		regGroupChangeRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "登记项目变更记录-批量删除")
	@ApiOperation(value="登记项目变更记录-批量删除", notes="登记项目变更记录-批量删除")
	@RequiresPermissions("datamange:reg_group_change_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.regGroupChangeRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "登记项目变更记录-通过id查询")
	@ApiOperation(value="登记项目变更记录-通过id查询", notes="登记项目变更记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RegGroupChangeRecord> queryById(@RequestParam(name="id",required=true) String id) {
		RegGroupChangeRecord regGroupChangeRecord = regGroupChangeRecordService.getById(id);
		if(regGroupChangeRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(regGroupChangeRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param regGroupChangeRecord
    */
    @RequiresPermissions("datamange:reg_group_change_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RegGroupChangeRecord regGroupChangeRecord) {
        return super.exportXls(request, regGroupChangeRecord, RegGroupChangeRecord.class, "登记项目变更记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("datamange:reg_group_change_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RegGroupChangeRecord.class);
    }

}
