package org.jeecg.modules.datamanage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.service.IItemGroupService;
import org.jeecg.modules.basicinfo.service.ISysUserInterfaceService;
import org.jeecg.modules.datamanage.entity.RegGroupChangeRecord;
import org.jeecg.modules.datamanage.mapper.CustomerRegDataManageMapper;
import org.jeecg.modules.datamanage.mapper.RegGroupChangeRecordMapper;
import org.jeecg.modules.datamanage.service.ICustomerRegDataManageService;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.station.bo.DepartStat;
import org.jeecg.modules.station.bo.StatusStat;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static java.util.stream.Collectors.groupingBy;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Service
public class CustomerRegDataManageServiceImpl extends ServiceImpl<CustomerRegMapper, CustomerReg> implements ICustomerRegDataManageService {
    @Autowired
    private CustomerRegDataManageMapper customerRegDataMangeMapper;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private CommonAPI commonAPI;
    @Autowired
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private RegGroupChangeRecordMapper regGroupChangeRecordMapper;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private ICustomerRegItemResultService customerRegItemResultService;
    @Autowired
    private ISysUserInterfaceService sysUserInterfaceService;

    @Override
    public void pageCustomerReg(Page<CustomerReg> page, List<String> departmentIds, String name, String gender, String idCard, String phone, String regDateStart, String regDateEnd, String examNo, String examCardNo, String interfaceSyncStatus, String companyRegId, String teamId, String itemGroupId, String itemId) {
        customerRegDataMangeMapper.pageCustomerReg(page, departmentIds, name, gender, idCard, phone, regDateStart, regDateEnd, examNo, examCardNo, interfaceSyncStatus, companyRegId, teamId, itemGroupId, itemId);

        if (page.getRecords().isEmpty()) {
            return;
        }
        List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("interfaceSyncStatus");

        page.getRecords().forEach(customerReg -> {
            List<StatusStat> interfaceStatusStatList = customerRegItemGroupMapper.statCustomerRegItemGroupInterfaceStatus(customerReg.getId(), departmentIds);

            interfaceStatusStatList.forEach(statusStat -> {
                String valueToSearch = statusStat.getStatus(); // 替换为你要搜索的值
                Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> valueToSearch.equals(dictModel.getValue())).findFirst();

                if (optionalDictModel.isPresent()) {
                    String color = optionalDictModel.get().getColor();
                    statusStat.setColor(color);
                }
            });

            customerReg.setInterfaceStatusStatList(interfaceStatusStatList);
        });
    }


    @Override
    public List<CustomerRegItemGroup> getGroupsByRegIdAndDepartId(String regId, String departmentId) {
        List<CustomerRegItemGroup> groups = customerRegItemGroupMapper.getGroupsByRegIdAndDepartId(regId, departmentId,null);
        if (CollectionUtils.isNotEmpty(groups)) {
            List<String> ids = groups.stream().map(CustomerRegItemGroup::getId).toList();
            List<RegGroupChangeRecord> regGroupChangeRecords = regGroupChangeRecordMapper.selectList(new LambdaQueryWrapper<RegGroupChangeRecord>().in(RegGroupChangeRecord::getRegGroupId, ids).orderByDesc(RegGroupChangeRecord::getCreateTime));
            Map<String, List<RegGroupChangeRecord>> map = regGroupChangeRecords.stream().collect(groupingBy(RegGroupChangeRecord::getRegGroupId));
            for (CustomerRegItemGroup group : groups) {
                List<RegGroupChangeRecord> records = map.get(group.getId());
                if(CollectionUtils.isNotEmpty(records)){
                    group.setChangeReason(records.get(0).getReason());
                    group.setChangePic(records.get(0).getChangePic());
                }
            }
        }

        return groups;
    }


    @Override
    public List<DepartStat> regGroupsGroupByDepart(String customerRegId) {

        List<DepartStat> list = customerRegItemGroupMapper.regGroupsGroupByDepart(customerRegId);
        for (DepartStat departStat : list) {
            SysDepart depart = sysDepartMapper.selectById(departStat.getDepartmentId());
            departStat.setDepart(depart);
        }
        //按照depart的guideOrder排序
        list.sort(Comparator.comparingInt(o -> o.getDepart().getGuideSort()));

        return list;
    }

    @Override
    public void  copyResult(CustomerRegItemGroup reqFromGroup, List<String> toGroupIdList) {
        CustomerRegItemResult fromResult = customerRegItemResultService.getOne(new LambdaQueryWrapper<CustomerRegItemResult>().eq(CustomerRegItemResult::getCustomerRegItemGroupId, reqFromGroup.getId()).last("limit 1"));
        List<CustomerRegItemGroup> toGroupList = customerRegItemGroupService.listByIds(toGroupIdList);
        List<CustomerRegItemResult> toResultList = Lists.newArrayList();
        List<CustomerRegItemGroup> finalToGroupList = Lists.newArrayList();
        for (CustomerRegItemGroup toGroup : toGroupList) {
            //前端传递的结果不准确，从数据库查询原大项结果
            CustomerRegItemGroup fromGroup = customerRegItemGroupService.getById(reqFromGroup.getId());
            //复制大项结果
            toGroup.setCheckTime(fromGroup.getCheckTime());
            toGroup.setCheckStatus(fromGroup.getCheckStatus());
            toGroup.setCheckConclusion(fromGroup.getCheckConclusion());
            toGroup.setInterfaceSyncStatus(fromGroup.getInterfaceSyncStatus());
            toGroup.setReportPics(fromGroup.getReportPics());
            toGroup.setReportPdf(fromGroup.getReportPdf());
            toGroup.setReportPdfInterface(fromGroup.getReportPdfInterface());
            toGroup.setPic(fromGroup.getPic());
            toGroup.setReportDoctorCode(fromGroup.getReportDoctorCode());
            toGroup.setReportDoctorName(fromGroup.getReportDoctorName());
            toGroup.setReportDoctorSignPic(fromGroup.getReportDoctorSignPic());
            toGroup.setReportTime(fromGroup.getReportTime());
            toGroup.setCheckDoctorCode(fromGroup.getCheckDoctorCode());
            toGroup.setCheckDoctorName(fromGroup.getCheckDoctorName());
            toGroup.setCheckDoctorSignPic(fromGroup.getCheckDoctorSignPic());
            toGroup.setAuditDoctorCode(fromGroup.getAuditDoctorCode());
            toGroup.setAuditDoctorName(fromGroup.getAuditDoctorName());
            toGroup.setAuditDoctorSignPic(fromGroup.getAuditDoctorSignPic());
            toGroup.setAuditTime(fromGroup.getAuditTime());
            toGroup.setAbnormalFlag(fromGroup.getAbnormalFlag());
            toGroup.setCheckBillNo(fromGroup.getCheckBillNo());
            toGroup.setPicSyncStatus(fromGroup.getPicSyncStatus());
            toGroup.setHisApplyNo(toGroup.getId());
            toGroup.setPicSyncStatus("0");

            //复制小项结果
            List<CustomerRegItemResult> list = customerRegItemResultService.list(new LambdaQueryWrapper<CustomerRegItemResult>().eq(CustomerRegItemResult::getCustomerRegItemGroupId, toGroup.getId()));
            if(CollectionUtils.isNotEmpty(list)){
                throw new RuntimeException("目标项目已存在结果，无法复制");
            }
            CustomerRegItemResult toResult = new CustomerRegItemResult();
            BeanUtils.copyProperties(fromResult, toResult);
            toResult.setId(null);
            toResult.setItemGroupId(toGroup.getItemGroupId());
            toResult.setItemGroupName(toGroup.getItemGroupName());
            toResult.setGroupHisCode(toGroup.getHisCode());
            toResult.setGroupHisName(toGroup.getHisName());
            toResult.setItemName(toGroup.getHisName());
            toResult.setItemCode(toGroup.getHisCode());
            toResult.setItemHisCode(toGroup.getHisCode());
            toResult.setItemHisName(toGroup.getHisName());
            toResult.setAbandonFlag(toGroup.getAbandonFlag());
            toResult.setCheckPartCode(toGroup.getCheckPartCode());
            toResult.setCheckPartName(toGroup.getCheckPartName());
            toResult.setCustomerRegItemGroupId(toGroup.getId());
            toResult.setPic(toResult.getPic());
            toResult.setUpdateTime(new Date());
            toResultList.add(toResult);
            finalToGroupList.add(toGroup);
        }
        customerRegItemGroupService.updateBatchById(finalToGroupList);
        customerRegItemResultService.saveBatch(toResultList);
    }

    @Override
    public void addResult(CustomerRegItemResult customerRegItemResult) {

        String customerRegItemGroupId = customerRegItemResult.getCustomerRegItemGroupId();
        CustomerRegItemGroup customerRegItemGroup = customerRegItemGroupService.getById(customerRegItemGroupId);
        customerRegItemGroup.setHisApplyNo(customerRegItemGroup.getId());
        customerRegItemGroup.setCheckBillNo(customerRegItemResult.getCheckBillNo());
        customerRegItemGroup.setCheckStatus(ExConstants.CHECK_STATUS_已检);
        customerRegItemGroup.setCheckConclusion(customerRegItemResult.getCheckConclusion());
        customerRegItemGroup.setAbnormalFlag(customerRegItemResult.getAbnormalFlag());
        customerRegItemGroup.setUpdateTime(new Date());
        customerRegItemGroup.setReportDoctorName(customerRegItemResult.getReportDoctorName());
        customerRegItemGroup.setReportDoctorCode(sysUserInterfaceService.getEmployeNoByNameAndAdminDeptCode(customerRegItemResult.getReportDoctorName(),customerRegItemGroup.getDepartmentCode()));
        customerRegItemGroup.setAuditDoctorName(customerRegItemResult.getAuditDoctorName());
        customerRegItemGroup.setAuditDoctorCode(sysUserInterfaceService.getEmployeNoByNameAndAdminDeptCode(customerRegItemResult.getAuditDoctorName(),customerRegItemGroup.getDepartmentCode()));
        customerRegItemGroup.setCheckDoctorName(customerRegItemResult.getDoctorName());
        customerRegItemGroup.setCheckDoctorCode(sysUserInterfaceService.getEmployeNoByNameAndAdminDeptCode(customerRegItemResult.getDoctorName(),customerRegItemGroup.getDepartmentCode()));
        customerRegItemGroup.setCheckTime(customerRegItemResult.getCheckTime());
        customerRegItemGroup.setAuditTime(customerRegItemResult.getAuditTime());
        customerRegItemGroup.setReportTime(customerRegItemResult.getReportTime());
        if(StringUtils.isNotBlank(customerRegItemResult.getReportPdfInterface())){
            customerRegItemGroup.setReportPdfInterface(customerRegItemResult.getReportPdfInterface());
            customerRegItemGroup.setPicSyncStatus("0");
            customerRegItemResult.setPic(null);

        }else{
            customerRegItemGroup.setPic(customerRegItemResult.getPic());
            customerRegItemGroup.setReportPics(customerRegItemResult.getPic());
        }

        customerRegItemGroupService.updateById(customerRegItemGroup);
        //补充小项结果西段
        customerRegItemResult.setCustomerRegItemGroupId(customerRegItemGroup.getId());
        customerRegItemResult.setExamNo(customerRegItemGroup.getExamNo());
        customerRegItemResult.setDepartmentId(customerRegItemGroup.getDepartmentId());
        customerRegItemResult.setCheckDepartmentCode(customerRegItemGroup.getDepartmentCode());
        customerRegItemResult.setCheckDepartmentName(customerRegItemGroup.getDepartmentName());
        customerRegItemResult.setItemGroupId(customerRegItemGroup.getItemGroupId());
        customerRegItemResult.setItemGroupName(customerRegItemGroup.getItemGroupName());
        customerRegItemResult.setGroupHisCode(customerRegItemGroup.getHisCode());
        customerRegItemResult.setGroupHisName(customerRegItemGroup.getHisName());
        customerRegItemResult.setItemName(customerRegItemGroup.getHisName());
        customerRegItemResult.setItemCode(customerRegItemGroup.getHisCode());
        customerRegItemResult.setItemHisName(customerRegItemGroup.getHisName());
        customerRegItemResult.setItemHisCode(customerRegItemGroup.getHisCode());
        customerRegItemResult.setCheckPartCode(customerRegItemGroup.getCheckPartCode());
        customerRegItemResult.setCheckPartName(customerRegItemGroup.getCheckPartName());
        customerRegItemResult.setValueType("说明型");
        customerRegItemResult.setUpdateTime(customerRegItemGroup.getUpdateTime());
        customerRegItemResult.setAbnormalFlagDesc(StringUtils.equals(customerRegItemResult.getAbnormalFlag(),"1")?"异常":"正常");
        customerRegItemResultService.saveOrUpdate(customerRegItemResult);
    }

    @Override
    public CustomerRegItemResult getResultByCustomerRegGroupId(String customerRegGroupId) {
        CustomerRegItemResult result = customerRegItemResultService.getOne(new LambdaQueryWrapper<CustomerRegItemResult>().eq(CustomerRegItemResult::getCustomerRegItemGroupId, customerRegGroupId).last("limit 1"));
        CustomerRegItemGroup group = customerRegItemGroupService.getById(customerRegGroupId);
        if (Objects.nonNull(result)) {
            result = customerRegItemResultService.getOne(new LambdaQueryWrapper<CustomerRegItemResult>().eq(CustomerRegItemResult::getCustomerRegId, group.getCustomerRegId()).eq(CustomerRegItemResult::getItemGroupId,group.getItemGroupId()).last("limit 1"));
        }
        if (Objects.nonNull(result)) {
            result.setCheckTime(group.getCheckTime());
            result.setAuditDoctorName(group.getAuditDoctorName());
            result.setReportDoctorName(group.getReportDoctorName());
            result.setAuditTime(group.getAuditTime());
            result.setReportTime(group.getReportTime());
            result.setReportPdfInterface(group.getReportPdfInterface());
            return result;
        }
        return null;
    }
}
