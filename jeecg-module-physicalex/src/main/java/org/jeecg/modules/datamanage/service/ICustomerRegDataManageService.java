package org.jeecg.modules.datamanage.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.station.bo.DepartStat;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

import java.util.List;

/**
 * @Description: 工作站客户登记
 * @Author: jeecg-boot
 * @Date:   2024-04-19
 * @Version: V1.0
 */
public interface ICustomerRegDataManageService extends IService<CustomerReg> {

    void pageCustomerReg(Page<CustomerReg> page,List<String> departmentIds,  String name, String gender, String idCard, String phone, String regDateStart, String regDateEnd, String examNo, String examCardNo, String interfaceSyncStatus, String companyRegId, String teamId, String itemGroupId, String itemId);

    List<CustomerRegItemGroup> getGroupsByRegIdAndDepartId(String regId, String departmentId);

    List<DepartStat> regGroupsGroupByDepart(String customerRegId);

    void copyResult(CustomerRegItemGroup reqFromGroup, List<String> toGroupIdList);

    void addResult(CustomerRegItemResult customerRegItemResult);

    CustomerRegItemResult getResultByCustomerRegGroupId(String customerRegGroupId);

}
