package org.jeecg.modules.datamanage.controller;

import cn.hutool.core.convert.ConvertException;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.datamanage.service.ICustomerRegDataManageService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.bo.DepartStat;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 工作站客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Api(tags = "数据管理")
@RestController
@RequestMapping("/dataManage/statusManage")
@Slf4j
public class CustomerRegDataManageController extends JeecgController<CustomerReg, ICustomerRegService> {
    @Autowired
    private ICustomerRegDataManageService customerRegDataMangeService;
    @Autowired
    private ICustomerRegService customerRegService;

    /**
     *
     * @return
     */
    @RequestMapping(value = "/regGroupsGroupByDepart", method = RequestMethod.GET)
    public Result<?> regGroupsGroupByDepart(String customerRegId) {
        List<DepartStat> departStats = customerRegDataMangeService.regGroupsGroupByDepart(customerRegId);
        return Result.ok(departStats);
    }

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "接口数据排查-分页列表查询", notes = "接口数据排查-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerReg>> queryPageList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String gender = StringUtils.trimToNull(req.getParameter("gender"));
        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String phone = StringUtils.trimToNull(req.getParameter("phone"));
        String regDateStart = StringUtils.trimToNull(req.getParameter("regDateStart"));
        String regDateEnd = StringUtils.trimToNull(req.getParameter("regDateEnd"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String examCardNo = StringUtils.trimToNull(req.getParameter("examCardNo"));
        String interfaceSyncStatus = StringUtils.trimToNull(req.getParameter("interfaceSyncStatus"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String itemGroupId = StringUtils.trimToNull(req.getParameter("itemGroupId"));
        String itemId = StringUtils.trimToNull(req.getParameter("itemId"));
        String ignoreDepartment = StringUtils.trimToNull(req.getParameter("ignoreDepartment"));

        String departmentId = StringUtils.trimToNull(req.getParameter("departmentId"));

        if (!StringUtils.equals(ignoreDepartment,"1")&&StringUtils.isBlank(departmentId)) {
            return Result.OK(page);
        }
        List<String> departmentIds = null;
        if (StringUtils.isNotBlank(departmentId)) {
            departmentIds = Arrays.asList(StringUtils.split(departmentId, ","));
        }


        customerRegDataMangeService.pageCustomerReg(page, departmentIds, name, gender, idCard, phone, regDateStart, regDateEnd, examNo, examCardNo, interfaceSyncStatus, companyRegId, teamId, itemGroupId, itemId);
        return Result.OK(page);
    }


    @ApiOperation(value = "接口数据排查-根据登记id获取大项列表", notes = "接口数据排查-根据登记id获取大项列表")
    @GetMapping(value = "/getGroupsByRegIdAndDepartId")
    public Result<List<CustomerRegItemGroup>> getGroupsByRegId(String regId,String departmentId) {
        List<CustomerRegItemGroup> groups = customerRegDataMangeService.getGroupsByRegIdAndDepartId(regId,departmentId);
        return Result.OK(groups);
    }

    @ApiOperation(value = "接口数据排查-复制结果", notes = "接口数据排查-复制结果")
    @PostMapping(value = "/copyResult")
    public Result<?> copyResult(@RequestBody JSONObject jsonObject) {
        try {
            List<String> toGroupIdList = jsonObject.getBeanList("toGroupIdList", String.class);
            Validate.notEmpty(toGroupIdList, "目标项目不能为空");
            CustomerRegItemGroup fromGroup = jsonObject.get("fromGroup", CustomerRegItemGroup.class);
            Validate.notEmpty(toGroupIdList, "原项目不能为空");
            customerRegDataMangeService.copyResult(fromGroup, toGroupIdList);
        } catch (ConvertException e) {
            return Result.error("复制失败"+e.getMessage());
        }

        return Result.OK("复制成功");
    }
    @ApiOperation(value = "接口数据排查-新增结果录入", notes = "接口数据排查-新增结果录入")
    @PostMapping(value = "/addResult")
    public Result<?> addResult(@RequestBody CustomerRegItemResult result) {
        try {
            customerRegDataMangeService.addResult(result);
        } catch (ConvertException e) {
            return Result.error("结果录入失败"+e.getMessage());
        }

        return Result.OK("结果录入成功");
    }
    @ApiOperation(value = "接口数据排查-查询结果", notes = "接口数据排查-查询结果")
    @GetMapping(value = "/getResultByCustomerRegGroupId")
    public Result<?> getResultByCustomerRegGroupId(@RequestParam String customerRegGroupId) {
        try {
            CustomerRegItemResult result = customerRegDataMangeService.getResultByCustomerRegGroupId(customerRegGroupId);
            return Result.OK(result);
        } catch (ConvertException e) {
            return Result.error("查询失败"+e.getMessage());
        }

    }

}
