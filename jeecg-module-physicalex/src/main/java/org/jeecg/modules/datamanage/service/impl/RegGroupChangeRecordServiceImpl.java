package org.jeecg.modules.datamanage.service.impl;

import org.jeecg.modules.datamanage.entity.RegGroupChangeRecord;
import org.jeecg.modules.datamanage.mapper.RegGroupChangeRecordMapper;
import org.jeecg.modules.datamanage.service.IRegGroupChangeRecordService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 登记项目变更记录
 * @Author: jeecg-boot
 * @Date:   2025-03-20
 * @Version: V1.0
 */
@Service
public class RegGroupChangeRecordServiceImpl extends ServiceImpl<RegGroupChangeRecordMapper, RegGroupChangeRecord> implements IRegGroupChangeRecordService {

}
