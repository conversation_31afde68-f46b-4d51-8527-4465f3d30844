<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.datamanage.mapper.CustomerRegDataManageMapper">

    <select id="pageCustomerReg" resultType="org.jeecg.modules.reg.entity.CustomerReg"
            parameterType="java.lang.String">
        select reg.* from customer_reg reg
        <where>
            reg.del_flag = 0 and status = '已登记'
            <if test="name!=null">and reg.name like concat('%',#{name},'%')</if>
            <if test="gender!=null">and reg.gender = #{gender}</if>
            <if test="phone!=null">and reg.phone = #{phone}</if>
            <if test="idCard!=null">and reg.id_card = #{idCard}</if>
            <if test="regDateStart!=null">and reg.reg_time &gt;= #{regDateStart}</if>
            <if test="regDateEnd!=null">and reg.reg_time &lt;= #{regDateEnd}</if>
            <if test="examNo!=null">and reg.exam_no like concat('%',#{examNo},'%') </if>
            <if test="examCardNo!=null">and reg.exam_card_no = #{examCardNo}</if>
            <if test="companyRegId!=null">and reg.company_reg_id = #{companyRegId}</if>
            <if test="teamId!=null">and reg.team_id = #{teamId}</if>
            <if test="departmentIds!=null or itemGroupId!=null or itemId!=null or interfaceSyncStatus!=null">
                and exists (
                select g.id from customer_reg_item_group g
                <if test="itemId!=null">join itemgroup_item item on g.id = item.group_id</if>
                where reg.id = g.customer_reg_id
                <if test="interfaceSyncStatus">and g.interface_sync_status = #{interfaceSyncStatus}</if>
                <if test="itemGroupId!=null">and g.item_group_id = #{itemGroupId}</if>
                <if test="itemId!=null">and item.item_id = #{itemId}</if>
                <if test="departmentIds!=null and departmentIds.size()>0">and g.department_id in <foreach collection="departmentIds" item="departId" separator="," open="(" close=")"> #{departId}</foreach> </if>
                )
            </if>

        </where>
        order by reg.reg_time desc,reg.serial_no desc
    </select>
</mapper>