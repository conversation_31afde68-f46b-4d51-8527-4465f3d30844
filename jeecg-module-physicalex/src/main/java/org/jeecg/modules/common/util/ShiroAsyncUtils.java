package org.jeecg.modules.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;

/**
 * Shiro异步线程上下文工具类
 * 用于在异步线程中正确处理Shiro的SecurityManager和Subject上下文
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Slf4j
public class ShiroAsyncUtils {

    /**
     * 在异步线程开始时绑定Shiro上下文
     * 这个方法应该在异步方法的开始处调用
     */
    public static void bindShiroContext() {
        try {
            // 获取当前的SecurityManager
            SecurityManager securityManager = SecurityUtils.getSecurityManager();
            if (securityManager != null) {
                // 在异步线程中绑定SecurityManager
                ThreadContext.bind(securityManager);
                log.debug("Successfully bound SecurityManager to async thread");
            } else {
                log.warn("SecurityManager is null, cannot bind to async thread");
            }
        } catch (Exception e) {
            // 如果获取SecurityManager失败，记录警告但不影响主要功能
            log.warn("Failed to bind SecurityManager in async thread, this may cause some logging issues but won't affect main functionality: {}", e.getMessage());
        }
    }

    /**
     * 在异步线程结束时清理Shiro上下文
     * 这个方法应该在异步方法的finally块中调用
     */
    public static void unbindShiroContext() {
        try {
            // 清理异步线程中的Shiro上下文
            ThreadContext.unbindSecurityManager();
            ThreadContext.unbindSubject();
            log.debug("Successfully unbound Shiro context from async thread");
        } catch (Exception e) {
            log.debug("Failed to unbind Shiro context in async thread: {}", e.getMessage());
        }
    }

    /**
     * 执行带有Shiro上下文的异步任务
     * 这是一个便利方法，自动处理上下文的绑定和清理
     * 
     * @param task 要执行的任务
     */
    public static void executeWithShiroContext(Runnable task) {
        try {
            bindShiroContext();
            task.run();
        } finally {
            unbindShiroContext();
        }
    }

    /**
     * 检查当前线程是否有可用的Shiro上下文
     * 
     * @return true如果有可用的SecurityManager，false否则
     */
    public static boolean hasShiroContext() {
        try {
            SecurityManager securityManager = SecurityUtils.getSecurityManager();
            return securityManager != null;
        } catch (Exception e) {
            log.debug("Error checking Shiro context: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 安全地获取当前用户Subject
     * 在异步线程中使用，如果获取失败不会抛出异常
     * 
     * @return Subject对象，如果获取失败返回null
     */
    public static Subject getCurrentSubjectSafely() {
        try {
            if (hasShiroContext()) {
                return SecurityUtils.getSubject();
            }
        } catch (Exception e) {
            log.debug("Failed to get current subject safely: {}", e.getMessage());
        }
        return null;
    }
}
