package org.jeecg.modules.survey.bo;

import lombok.Data;
import org.jeecg.modules.basicinfo.bo.ItemGroupLabel;
import org.jeecg.modules.basicinfo.entity.ItemGroup;

import java.math.BigDecimal;
import java.util.List;

@Data
public class RecommendProject{
    private List<RiskRecommendItem> riskRecommendItems;
    private List<RecommendCheckProject> recommendCheckProjects;
    private List<ItemGroup> recommendItemGroups;
    @Data
    public static class RiskRecommendItem {
        private String checkPart;

        private String riskFactor;

        private List<String> riskFactors;

        private List<String> recommendItems;
    }
    @Data
    public static class RecommendCheckProject {
        private String projectName;
        private String level;
        private BigDecimal price;
        private Integer itemCount;
        private List<ItemGroupLabel> labels;
    }
}

