package org.jeecg.modules.survey.service.impl;

import org.jeecg.modules.survey.entity.HealthCheckItemRecommendation;
import org.jeecg.modules.survey.mapper.HealthCheckItemRecommendationMapper;
import org.jeecg.modules.survey.service.IHealthCheckItemRecommendationService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: health_check_item_recommendation
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Service
public class HealthCheckItemRecommendationServiceImpl extends ServiceImpl<HealthCheckItemRecommendationMapper, HealthCheckItemRecommendation> implements IHealthCheckItemRecommendationService {

}
