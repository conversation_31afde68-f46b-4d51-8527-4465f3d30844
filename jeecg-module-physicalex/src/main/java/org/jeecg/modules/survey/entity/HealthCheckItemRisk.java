package org.jeecg.modules.survey.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: health_check_item_risk
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Data
@TableName("health_check_item_risk")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="health_check_item_risk对象", description="health_check_item_risk")
public class HealthCheckItemRisk implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.Integer id;
	/**projectType*/
	@Excel(name = "projectType", width = 15)
    @ApiModelProperty(value = "projectType")
    private java.lang.String projectType;
    /**checkPart*/
    @Excel(name = "checkPart", width = 15)
    @ApiModelProperty(value = "checkPart")
    private java.lang.String checkPart;
	/**project*/
	@Excel(name = "project", width = 15)
    @ApiModelProperty(value = "project")
    private java.lang.String project;
	/**targetPopulation*/
	@Excel(name = "targetPopulation", width = 15)
    @ApiModelProperty(value = "targetPopulation")
    private java.lang.String targetPopulation;
	/**riskFactor*/
	@Excel(name = "riskFactor", width = 15)
    @ApiModelProperty(value = "riskFactor")
    private java.lang.String riskFactor;
}
