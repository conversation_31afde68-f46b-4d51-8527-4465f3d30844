package org.jeecg.modules.survey.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: disease_survey_answer
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Data
@TableName("disease_survey_answer")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="disease_survey_answer对象", description="disease_survey_answer")
public class DiseaseSurveyAnswer implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private java.lang.Integer id;
	/**customerId*/
	@Excel(name = "customerId", width = 15)
    @ApiModelProperty(value = "customerId")
    private java.lang.String customerId;
	/**customerRegId*/
	@Excel(name = "customerRegId", width = 15)
    @ApiModelProperty(value = "customerRegId")
    private java.lang.String customerRegId;
	/**answerJson*/
	@Excel(name = "answerJson", width = 15)
    @ApiModelProperty(value = "answerJson")
    private java.lang.String answerJson;
	/**answerTime*/
	@Excel(name = "answerTime", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "answerTime")
    private java.util.Date answerTime;
    /**answerTime*/
    @Excel(name = "updateTime", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
	/**answerBy*/
	@Excel(name = "answerBy", width = 15)
    @ApiModelProperty(value = "answerBy")
    private java.lang.String answerBy;
    @ApiModelProperty(value = "风险因子")
    private java.lang.String riskFactor;
    @ApiModelProperty(value = "推荐项目id")
    private java.lang.String recommendItemId;
}
