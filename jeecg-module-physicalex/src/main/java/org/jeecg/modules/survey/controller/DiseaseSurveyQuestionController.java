package org.jeecg.modules.survey.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.service.ICustomerService;
import org.jeecg.modules.survey.entity.DiseaseSurveyQuestion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(tags = "调研问卷")
@RestController
@RequestMapping("/survey/DiseaseSurveyQuestion")
@Slf4j
public class DiseaseSurveyQuestionController {
    @Autowired
    private ICustomerService customerService;
    @ApiOperation(value = "疾病调研-获取调研题目", notes = "疾病调研-获取调研题目")
    @GetMapping(value = "/list")
    public Result<?> list(@RequestParam("customerId")String customerId) {

        ClassPathResource resource = new ClassPathResource("/diseaseSurveyQuestion.json");
        try (InputStream input = resource.getInputStream()) {
            String text = IOUtils.toString(input, StandardCharsets.UTF_8);
            List<Map> questions = JSONObject.parseArray(text, Map.class);
            Customer customer = customerService.getById(customerId);
//            List<Map> list = Lists.newArrayList();
            if (Objects.nonNull(customer)) {
                String gender = customer.getGender();
                questions = questions.stream().filter(i -> StringUtils.equalsAny(String.valueOf(i.get("genderLimit")), gender, "不限")).collect(Collectors.toList());
            }
            return Result.OK("获取成功！", questions);
        }catch (Exception e){
            e.printStackTrace();
            return Result.error("获取失败！");
        }
    }

    @ApiOperation(value = "疾病调研-获取调研题目2", notes = "疾病调研-获取调研题目2")
    @GetMapping(value = "/list2")
    public Result<?> list2(@RequestParam("customerId")String customerId) {

        ClassPathResource resource = new ClassPathResource("/diseaseSurveyQuestion.json");
        try (InputStream input = resource.getInputStream()) {
            String text = IOUtils.toString(input, StandardCharsets.UTF_8);
            List<DiseaseSurveyQuestion> questions = JSONObject.parseArray(text, DiseaseSurveyQuestion.class);
            return Result.OK("获取成功！", questions);
        }catch (Exception e){
            e.printStackTrace();
            return Result.error("获取失败！");
        }
    }
}
