package org.jeecg.modules.survey.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DiseaseSurveyQuestion implements Serializable {
    private String title;
    private String type;
    private Integer seq;
    private String genderLimit;
    private List<DiseaseQuestionOption> questList;

    @Data
    public class DiseaseQuestionOption implements Serializable{
        private String name;
        private Boolean chk;
        private List<SubDiseaseQuestionOption> subquestList;
        @Data
        public class SubDiseaseQuestionOption implements Serializable {
            private String name;
            private Boolean chk;
        }
    }

}
