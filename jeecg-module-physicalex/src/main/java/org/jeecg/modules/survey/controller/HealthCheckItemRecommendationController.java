package org.jeecg.modules.survey.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.survey.entity.HealthCheckItemRecommendation;
import org.jeecg.modules.survey.service.IHealthCheckItemRecommendationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: health_check_item_recommendation
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Api(tags="health_check_item_recommendation")
@RestController
@RequestMapping("/survey/healthCheckItemRecommendation")
@Slf4j
public class HealthCheckItemRecommendationController extends JeecgController<HealthCheckItemRecommendation, IHealthCheckItemRecommendationService> {
	@Autowired
	private IHealthCheckItemRecommendationService healthCheckItemRecommendationService;
	
	/**
	 * 分页列表查询
	 *
	 * @param healthCheckItemRecommendation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "health_check_item_recommendation-分页列表查询")
	@ApiOperation(value="health_check_item_recommendation-分页列表查询", notes="health_check_item_recommendation-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<HealthCheckItemRecommendation>> queryPageList(HealthCheckItemRecommendation healthCheckItemRecommendation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<HealthCheckItemRecommendation> queryWrapper = QueryGenerator.initQueryWrapper(healthCheckItemRecommendation, req.getParameterMap());
		Page<HealthCheckItemRecommendation> page = new Page<HealthCheckItemRecommendation>(pageNo, pageSize);
		IPage<HealthCheckItemRecommendation> pageList = healthCheckItemRecommendationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param healthCheckItemRecommendation
	 * @return
	 */
	@AutoLog(value = "health_check_item_recommendation-添加")
	@ApiOperation(value="health_check_item_recommendation-添加", notes="health_check_item_recommendation-添加")
	@RequiresPermissions("survey:health_check_item_recommendation:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody HealthCheckItemRecommendation healthCheckItemRecommendation) {
		healthCheckItemRecommendationService.save(healthCheckItemRecommendation);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param healthCheckItemRecommendation
	 * @return
	 */
	@AutoLog(value = "health_check_item_recommendation-编辑")
	@ApiOperation(value="health_check_item_recommendation-编辑", notes="health_check_item_recommendation-编辑")
	@RequiresPermissions("survey:health_check_item_recommendation:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody HealthCheckItemRecommendation healthCheckItemRecommendation) {
		healthCheckItemRecommendationService.updateById(healthCheckItemRecommendation);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "health_check_item_recommendation-通过id删除")
	@ApiOperation(value="health_check_item_recommendation-通过id删除", notes="health_check_item_recommendation-通过id删除")
	@RequiresPermissions("survey:health_check_item_recommendation:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		healthCheckItemRecommendationService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "health_check_item_recommendation-批量删除")
	@ApiOperation(value="health_check_item_recommendation-批量删除", notes="health_check_item_recommendation-批量删除")
	@RequiresPermissions("survey:health_check_item_recommendation:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.healthCheckItemRecommendationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "health_check_item_recommendation-通过id查询")
	@ApiOperation(value="health_check_item_recommendation-通过id查询", notes="health_check_item_recommendation-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<HealthCheckItemRecommendation> queryById(@RequestParam(name="id",required=true) String id) {
		HealthCheckItemRecommendation healthCheckItemRecommendation = healthCheckItemRecommendationService.getById(id);
		if(healthCheckItemRecommendation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(healthCheckItemRecommendation);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param healthCheckItemRecommendation
    */
    @RequiresPermissions("survey:health_check_item_recommendation:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HealthCheckItemRecommendation healthCheckItemRecommendation) {
        return super.exportXls(request, healthCheckItemRecommendation, HealthCheckItemRecommendation.class, "health_check_item_recommendation");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("survey:health_check_item_recommendation:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, HealthCheckItemRecommendation.class);
    }

}
