package org.jeecg.modules.survey.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.basicinfo.bo.ItemGroupLabel;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.mapper.CustomerMapper;
import org.jeecg.modules.survey.bo.RecommendProject;
import org.jeecg.modules.survey.entity.DiseaseSurveyAnswer;
import org.jeecg.modules.survey.entity.DiseaseSurveyQuestion;
import org.jeecg.modules.survey.entity.HealthCheckItemRecommendation;
import org.jeecg.modules.survey.entity.HealthCheckItemRisk;
import org.jeecg.modules.survey.mapper.DiseaseSurveyAnswerMapper;
import org.jeecg.modules.survey.mapper.HealthCheckItemRecommendationMapper;
import org.jeecg.modules.survey.mapper.HealthCheckItemRiskMapper;
import org.jeecg.modules.survey.service.IDiseaseSurveyAnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: disease_survey_answer
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Service
public class DiseaseSurveyAnswerServiceImpl extends ServiceImpl<DiseaseSurveyAnswerMapper, DiseaseSurveyAnswer> implements IDiseaseSurveyAnswerService {
    @Autowired
    private DiseaseSurveyAnswerMapper answerMapper;
    @Autowired
    private HealthCheckItemRecommendationMapper recommendationMapper;
    @Autowired
    private HealthCheckItemRiskMapper riskMapper;
    @Autowired
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Override
    public RecommendProject queryAnalysisById(String id) {
        DiseaseSurveyAnswer diseaseSurveyAnswer = answerMapper.selectById(id);
        RecommendProject recommendProject = new RecommendProject();
        if (diseaseSurveyAnswer != null) {
            //分析问卷结果
            String answerJson = diseaseSurveyAnswer.getAnswerJson();
            List<DiseaseSurveyQuestion> answers = JSONObject.parseArray(answerJson, DiseaseSurveyQuestion.class);
            List<DiseaseSurveyQuestion> abnormalAnswers = Lists.newArrayList();
            List<String> abnormalOptions = Lists.newArrayList();
            answers.forEach(i -> {
                List<DiseaseSurveyQuestion.DiseaseQuestionOption> options = i.getQuestList().stream().filter(answer -> !StringUtils.equals(answer.getName(), "无") && answer.getChk()).collect(Collectors.toList());
                i.setQuestList(options);
                abnormalOptions.addAll(i.getQuestList().stream().filter(DiseaseSurveyQuestion.DiseaseQuestionOption::getChk)
                        .map(DiseaseSurveyQuestion.DiseaseQuestionOption::getName).collect(Collectors.toList()));
                abnormalAnswers.add(i);
            });

            List<HealthCheckItemRisk> itemRisks = riskMapper.selectList(new LambdaQueryWrapper<HealthCheckItemRisk>()
                    .in(HealthCheckItemRisk::getRiskFactor, abnormalOptions));
            if (CollectionUtils.isNotEmpty(itemRisks)) {
                Map<String, List<HealthCheckItemRisk>> riskMap = itemRisks.stream().collect(Collectors.groupingBy(HealthCheckItemRisk::getCheckPart));
                //根据风险因素查推荐项目
                List<HealthCheckItemRecommendation> recommendItems = recommendationMapper.selectList(new LambdaQueryWrapper<HealthCheckItemRecommendation>()
                        .in(HealthCheckItemRecommendation::getCheckPart, riskMap.keySet()));
                Map<String, List<HealthCheckItemRecommendation>> recommendMap = recommendItems.stream().filter(i -> StringUtils.isNotBlank(i.getItemGroupId()))
                        .collect(Collectors.groupingBy(HealthCheckItemRecommendation::getCheckPart));

                List<RecommendProject.RiskRecommendItem> riskRecommendItems = Lists.newArrayList();
                //获取答题人信息
                List<ItemGroup> recoGroups = Lists.newArrayList();
                Customer customer = customerMapper.selectById(diseaseSurveyAnswer.getCustomerId());
                for (String checkPart : riskMap.keySet()) {
                    RecommendProject.RiskRecommendItem recommendItem = new RecommendProject.RiskRecommendItem();
                    recommendItem.setCheckPart(checkPart);
                    recommendItem.setRiskFactor(riskMap.get(checkPart).stream().map(HealthCheckItemRisk::getRiskFactor).collect(Collectors.joining(" ")));
                    recommendItem.setRiskFactors(riskMap.get(checkPart).stream().map(HealthCheckItemRisk::getRiskFactor).collect(Collectors.toList()));
                    List<HealthCheckItemRecommendation> recommendations = recommendMap.get(checkPart);
                    if (CollectionUtils.isNotEmpty(recommendations)) {
                        List<String> groupIds = Lists.newArrayList();
                        recommendations.forEach(r -> {
                            String itemGroupId = StringUtils.isNotBlank(r.getItemGroupId()) ? r.getItemGroupId() : "";
                            groupIds.addAll(Arrays.asList(itemGroupId.split(",")));
                        });
                        recoGroups = itemGroupMapper.selectList(new LambdaQueryWrapper<ItemGroup>().in(ItemGroup::getId, groupIds)).stream().filter(i -> StringUtils.equalsAny(i.getSexLimit(), "不限", customer.getGender())).collect(Collectors.toList());
                        List<String> recoGroupNames = recoGroups.stream().map(ItemGroup::getName).collect(Collectors.toList());
                        recommendItem.setRecommendItems(recoGroupNames);
                    }
                    riskRecommendItems.add(recommendItem);
                }
                recommendProject.setRiskRecommendItems(riskRecommendItems);
                Set<String> groupItemIds = recoGroups.stream().map(ItemGroup::getId).collect(Collectors.toSet());
//            List<ItemGroup> itemGroups = itemGroupMapper.selectBatchIds(groupItemIds);

                recommendProject.setRecommendItemGroups(recoGroups);

                diseaseSurveyAnswer.setRiskFactor(abnormalOptions.stream().collect(Collectors.joining(",")));
                diseaseSurveyAnswer.setRecommendItemId(groupItemIds.stream().collect(Collectors.joining(",")));
                answerMapper.updateById(diseaseSurveyAnswer);
            }
        }
        return recommendProject;
    }

    @Override
    public List<ItemGroup> queryRecommendGroups(String id) {
        DiseaseSurveyAnswer diseaseSurveyAnswer = answerMapper.selectById(id);
        if (Objects.nonNull(diseaseSurveyAnswer)){
            String recommendItemId = diseaseSurveyAnswer.getRecommendItemId();
            if (StringUtils.isNotBlank(recommendItemId)){
                List<String> groupItemIds = Arrays.asList(StringUtils.split(recommendItemId, ","));
                List<ItemGroup> itemGroups = itemGroupMapper.selectBatchIds(groupItemIds);
               /* itemGroups.forEach(i->{
                    i.setIsRecommend(true);
                });*/
                return itemGroups;
            }
        }
        return Collections.singletonList(new ItemGroup());
    }

    public RecommendProject queryAnalysisById2(String id) {
        DiseaseSurveyAnswer diseaseSurveyAnswer = answerMapper.selectById(id);
        RecommendProject recommendProject = new RecommendProject();
        if (diseaseSurveyAnswer != null) {
            //分析问卷结果
            String answerJson = diseaseSurveyAnswer.getAnswerJson();
            List<DiseaseSurveyQuestion> answers = JSONObject.parseArray(answerJson, DiseaseSurveyQuestion.class);
            List<DiseaseSurveyQuestion> abnormalAnswers = Lists.newArrayList();
            List<String> abnormalOptions = Lists.newArrayList();
            answers.forEach(i -> {
                List<DiseaseSurveyQuestion.DiseaseQuestionOption> options = i.getQuestList().stream().filter(answer -> !StringUtils.equals(answer.getName(), "无") && answer.getChk()).collect(Collectors.toList());
                i.setQuestList(options);
                abnormalOptions.addAll(i.getQuestList().stream().filter(DiseaseSurveyQuestion.DiseaseQuestionOption::getChk)
                        .map(DiseaseSurveyQuestion.DiseaseQuestionOption::getName).collect(Collectors.toList()));
                abnormalAnswers.add(i);
            });

            List<HealthCheckItemRisk> itemRisks = riskMapper.selectList(new LambdaQueryWrapper<HealthCheckItemRisk>()
                    .in(HealthCheckItemRisk::getRiskFactor, abnormalOptions));
            Map<String, List<HealthCheckItemRisk>> riskMap = itemRisks.stream().collect(Collectors.groupingBy(HealthCheckItemRisk::getCheckPart));
            //根据风险因素查推荐项目
            List<HealthCheckItemRecommendation> recommendItems = recommendationMapper.selectList(new LambdaQueryWrapper<HealthCheckItemRecommendation>()
                    .in(HealthCheckItemRecommendation::getCheckPart, riskMap.keySet()));
            Map<String, List<HealthCheckItemRecommendation>> recommendMap = recommendItems.stream().filter(i -> StringUtils.isNotBlank(i.getItemGroupId()))
                    .collect(Collectors.groupingBy(HealthCheckItemRecommendation::getCheckPart));
            List<RecommendProject.RiskRecommendItem> riskRecommendItems = Lists.newArrayList();
            List<HealthCheckItemRecommendation> recommendList = Lists.newArrayList();

            for (String checkPart : riskMap.keySet()) {
                RecommendProject.RiskRecommendItem recommendItem = new RecommendProject.RiskRecommendItem();
                recommendItem.setCheckPart(checkPart);
                recommendItem.setRiskFactor(riskMap.get(checkPart).stream().map(HealthCheckItemRisk::getRiskFactor).collect(Collectors.joining(" ")));
                List<HealthCheckItemRecommendation> recommendations = recommendMap.get(checkPart);
                if (CollectionUtils.isNotEmpty(recommendations)) {
                    recommendItem.setRecommendItems(recommendations.stream().map(HealthCheckItemRecommendation::getItemGroupName).collect(Collectors.toList()));
                    recommendList.addAll(recommendations);
                }
                riskRecommendItems.add(recommendItem);
            }
            recommendProject.setRiskRecommendItems(riskRecommendItems);

            Map<String, List<HealthCheckItemRecommendation>> recommendLevelMap = recommendList.stream().collect(Collectors.groupingBy(HealthCheckItemRecommendation::getRecommendType));
            List<RecommendProject.RecommendCheckProject> projecList = Lists.newArrayList();
            for (String recommendLevel : recommendLevelMap.keySet()) {
                RecommendProject.RecommendCheckProject recommendCheckProject = new RecommendProject.RecommendCheckProject();
                recommendCheckProject.setLevel(recommendLevel);
                recommendCheckProject.setProjectName(recommendLevel + "检查方案");
                List<HealthCheckItemRecommendation> recommendations = recommendLevelMap.get(recommendLevel);
                List<String> groupItemIds = recommendations.stream().map(HealthCheckItemRecommendation::getItemGroupId).collect(Collectors.toList());
                List<ItemGroup> itemGroups = itemGroupMapper.selectBatchIds(groupItemIds);
                List<ItemGroupLabel> labels = Lists.newArrayList();
                //按检查部位分类的逻辑
                Map<String, List<HealthCheckItemRecommendation>> checkPartMap = recommendations.stream().collect(Collectors.groupingBy(HealthCheckItemRecommendation::getCheckPart));
                for (String checkPart : checkPartMap.keySet()) {
                    ItemGroupLabel itemGroupLabel = new ItemGroupLabel();
                    itemGroupLabel.setLabel(checkPart);
                    List<String> groupIds = checkPartMap.get(checkPart).stream().map(HealthCheckItemRecommendation::getItemGroupId).collect(Collectors.toList());
                    List<ItemGroup> groups = itemGroupMapper.selectBatchIds(groupIds);
                    if (CollectionUtils.isNotEmpty(groups)) {
                        itemGroupLabel.setItemList(groups);
                    }
                    labels.add(itemGroupLabel);
                }
                recommendCheckProject.setLabels(labels);
                //按科室分类的逻辑
              /*  if (CollectionUtils.isNotEmpty(itemGroups)) {
                    Map<String, List<ItemGroup>> groupbyDepartMap = itemGroups.stream().collect(Collectors.groupingBy(ItemGroup::getDepartmentName));
                    for (String departName : groupbyDepartMap.keySet()) {
                        ItemGroupLabel itemGroupLabel = new ItemGroupLabel();
                        itemGroupLabel.setLabel(departName);
                        itemGroupLabel.setItemList(groupbyDepartMap.get(departName));
                        labels.add(itemGroupLabel);
                    }
                    recommendCheckProject.setLabels(labels);
                }*/
                recommendCheckProject.setItemCount(itemGroups.size());
                recommendCheckProject.setPrice(itemGroups.stream().map(ItemGroup::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                projecList.add(recommendCheckProject);
            }
            recommendProject.setRecommendCheckProjects(projecList);

        }
        return recommendProject;
    }
}
