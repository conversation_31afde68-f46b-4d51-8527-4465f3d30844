package org.jeecg.modules.survey.service;

import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.survey.bo.RecommendProject;
import org.jeecg.modules.survey.entity.DiseaseSurveyAnswer;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: disease_survey_answer
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
public interface IDiseaseSurveyAnswerService extends IService<DiseaseSurveyAnswer> {
    RecommendProject queryAnalysisById(String id);

    List<ItemGroup> queryRecommendGroups(String id);

}
