package org.jeecg.modules.survey.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.survey.entity.HealthCheckItemRisk;
import org.jeecg.modules.survey.service.IHealthCheckItemRiskService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: health_check_item_risk
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Api(tags="health_check_item_risk")
@RestController
@RequestMapping("/survey/healthCheckItemRisk")
@Slf4j
public class HealthCheckItemRiskController extends JeecgController<HealthCheckItemRisk, IHealthCheckItemRiskService> {
	@Autowired
	private IHealthCheckItemRiskService healthCheckItemRiskService;
	
	/**
	 * 分页列表查询
	 *
	 * @param healthCheckItemRisk
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "health_check_item_risk-分页列表查询")
	@ApiOperation(value="health_check_item_risk-分页列表查询", notes="health_check_item_risk-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<HealthCheckItemRisk>> queryPageList(HealthCheckItemRisk healthCheckItemRisk,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<HealthCheckItemRisk> queryWrapper = QueryGenerator.initQueryWrapper(healthCheckItemRisk, req.getParameterMap());
		Page<HealthCheckItemRisk> page = new Page<HealthCheckItemRisk>(pageNo, pageSize);
		IPage<HealthCheckItemRisk> pageList = healthCheckItemRiskService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param healthCheckItemRisk
	 * @return
	 */
	@AutoLog(value = "health_check_item_risk-添加")
	@ApiOperation(value="health_check_item_risk-添加", notes="health_check_item_risk-添加")
	@RequiresPermissions("survey:health_check_item_risk:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody HealthCheckItemRisk healthCheckItemRisk) {
		healthCheckItemRiskService.save(healthCheckItemRisk);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param healthCheckItemRisk
	 * @return
	 */
	@AutoLog(value = "health_check_item_risk-编辑")
	@ApiOperation(value="health_check_item_risk-编辑", notes="health_check_item_risk-编辑")
	@RequiresPermissions("survey:health_check_item_risk:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody HealthCheckItemRisk healthCheckItemRisk) {
		healthCheckItemRiskService.updateById(healthCheckItemRisk);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "health_check_item_risk-通过id删除")
	@ApiOperation(value="health_check_item_risk-通过id删除", notes="health_check_item_risk-通过id删除")
	@RequiresPermissions("survey:health_check_item_risk:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		healthCheckItemRiskService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "health_check_item_risk-批量删除")
	@ApiOperation(value="health_check_item_risk-批量删除", notes="health_check_item_risk-批量删除")
	@RequiresPermissions("survey:health_check_item_risk:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.healthCheckItemRiskService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "health_check_item_risk-通过id查询")
	@ApiOperation(value="health_check_item_risk-通过id查询", notes="health_check_item_risk-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<HealthCheckItemRisk> queryById(@RequestParam(name="id",required=true) String id) {
		HealthCheckItemRisk healthCheckItemRisk = healthCheckItemRiskService.getById(id);
		if(healthCheckItemRisk==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(healthCheckItemRisk);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param healthCheckItemRisk
    */
    @RequiresPermissions("survey:health_check_item_risk:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HealthCheckItemRisk healthCheckItemRisk) {
        return super.exportXls(request, healthCheckItemRisk, HealthCheckItemRisk.class, "health_check_item_risk");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("survey:health_check_item_risk:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, HealthCheckItemRisk.class);
    }

}
