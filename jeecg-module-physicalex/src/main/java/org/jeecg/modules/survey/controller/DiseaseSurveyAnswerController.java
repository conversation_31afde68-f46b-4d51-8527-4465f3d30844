package org.jeecg.modules.survey.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.survey.bo.RecommendProject;
import org.jeecg.modules.survey.entity.DiseaseSurveyAnswer;
import org.jeecg.modules.survey.service.IDiseaseSurveyAnswerService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 疾病调研答案
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Api(tags="疾病调研答案")
@RestController
@RequestMapping("/survey/diseaseSurveyAnswer")
@Slf4j
public class DiseaseSurveyAnswerController extends JeecgController<DiseaseSurveyAnswer, IDiseaseSurveyAnswerService> {
	@Autowired
	private IDiseaseSurveyAnswerService diseaseSurveyAnswerService;
	 @Autowired
	 private ICustomerRegService customerRegService;
	
	/**
	 * 分页列表查询
	 *
	 * @param diseaseSurveyAnswer
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "疾病调研答案-分页列表查询")
	@ApiOperation(value="疾病调研答案-分页列表查询", notes="疾病调研答案-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<DiseaseSurveyAnswer>> queryPageList(DiseaseSurveyAnswer diseaseSurveyAnswer,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DiseaseSurveyAnswer> queryWrapper = QueryGenerator.initQueryWrapper(diseaseSurveyAnswer, req.getParameterMap());
		Page<DiseaseSurveyAnswer> page = new Page<DiseaseSurveyAnswer>(pageNo, pageSize);
		IPage<DiseaseSurveyAnswer> pageList = diseaseSurveyAnswerService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param diseaseSurveyAnswer
	 * @return
	 */
	@AutoLog(value = "疾病调研答案-添加")
	@ApiOperation(value="疾病调研答案-添加", notes="疾病调研答案-添加")
//	@RequiresPermissions("survey:疾病调研答案:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody DiseaseSurveyAnswer diseaseSurveyAnswer) {
		List<CustomerReg> regs = customerRegService.list(new LambdaQueryWrapper<CustomerReg>()
				.eq(CustomerReg::getCustomerId, diseaseSurveyAnswer.getCustomerId()).orderByDesc(CustomerReg::getRegTime));
		if (CollectionUtils.isNotEmpty(regs)){
			CustomerReg latestReg = regs.get(0);
			diseaseSurveyAnswer.setCustomerRegId(latestReg.getId());
		}
		diseaseSurveyAnswer.setAnswerBy(diseaseSurveyAnswer.getAnswerBy());
		diseaseSurveyAnswer.setAnswerTime(new Date());
		diseaseSurveyAnswerService.save(diseaseSurveyAnswer);



//		return Result.OK("添加成功！");
		return Result.OK("添加成功！",String.valueOf(diseaseSurveyAnswer.getId()));
	}
	 /**
	  * 获取分析后调研结果
	  *
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "疾病调研答案-通过id查询")
	 @ApiOperation(value="疾病调研答案-获取分析后调研结果", notes="疾病调研答案-获取分析后调研结果")
	 @GetMapping(value = "/queryAnalysisById")
	 public Result<RecommendProject> queryAnalysisById(@RequestParam(name="id",required=true) String id) {
		 RecommendProject  recommendProject= diseaseSurveyAnswerService.queryAnalysisById(id);
		 return Result.OK(recommendProject);
	 }
	
	/**
	 *  编辑
	 *
	 * @param diseaseSurveyAnswer
	 * @return
	 */
	@AutoLog(value = "疾病调研答案-编辑")
	@ApiOperation(value="疾病调研答案-编辑", notes="疾病调研答案-编辑")
	@RequiresPermissions("survey:疾病调研答案:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody DiseaseSurveyAnswer diseaseSurveyAnswer) {
		diseaseSurveyAnswer.setUpdateTime(new Date());
		diseaseSurveyAnswerService.updateById(diseaseSurveyAnswer);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "疾病调研答案-通过id删除")
	@ApiOperation(value="疾病调研答案-通过id删除", notes="疾病调研答案-通过id删除")
	@RequiresPermissions("survey:疾病调研答案:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		diseaseSurveyAnswerService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "疾病调研答案-批量删除")
	@ApiOperation(value="疾病调研答案-批量删除", notes="疾病调研答案-批量删除")
	@RequiresPermissions("survey:疾病调研答案:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.diseaseSurveyAnswerService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "疾病调研答案-通过id查询")
	@ApiOperation(value="疾病调研答案-通过id查询", notes="疾病调研答案-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<DiseaseSurveyAnswer> queryById(@RequestParam(name="id",required=true) String id) {
		DiseaseSurveyAnswer diseaseSurveyAnswer = diseaseSurveyAnswerService.getById(id);
		if(diseaseSurveyAnswer==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(diseaseSurveyAnswer);
	}
	 /**
	  * 通过customerId查询最新调研结果
	  *
	  * @param customerId
	  * @return
	  */
	 //@AutoLog(value = "疾病调研答案-通过customerId查询最新调研结果")
	 @ApiOperation(value="疾病调研答案-通过customerId查询最新调研结果", notes="疾病调研答案-通过customerId查询最新调研结果")
	 @GetMapping(value = "/queryByCustomerId")
	 public Result<List<DiseaseSurveyAnswer>> queryByCustomerId(@RequestParam(name="customerId",required=true) String customerId) {
		 List<DiseaseSurveyAnswer> list = diseaseSurveyAnswerService.list(new LambdaQueryWrapper<DiseaseSurveyAnswer>()
				 .eq(DiseaseSurveyAnswer::getCustomerId, customerId).orderByDesc(DiseaseSurveyAnswer::getId));
		 if(CollectionUtils.isNotEmpty(list)) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(list);
	 }


	 @ApiOperation(value = "疾病调研-获取调研问卷测试人数", notes = "疾病调研-获取调研问卷测试人数")
	 @GetMapping(value = "/getSurveyTestedCount")
	 public Result<?> getSurveyTestedCount() {
		 List<DiseaseSurveyAnswer> list = diseaseSurveyAnswerService.list(new LambdaQueryWrapper<DiseaseSurveyAnswer>().select(DiseaseSurveyAnswer::getId));
		 return Result.OK(CollectionUtils.isNotEmpty(list)?list.size():0);
	 }

    /**
    * 导出excel
    *
    * @param request
    * @param diseaseSurveyAnswer
    */
    @RequiresPermissions("survey:疾病调研答案:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DiseaseSurveyAnswer diseaseSurveyAnswer) {
        return super.exportXls(request, diseaseSurveyAnswer, DiseaseSurveyAnswer.class, "疾病调研答案");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("survey:疾病调研答案:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DiseaseSurveyAnswer.class);
    }

}
