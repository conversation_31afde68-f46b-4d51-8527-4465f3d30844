package org.jeecg.modules.survey.service.impl;

import org.jeecg.modules.survey.service.ISuggestionFeedbackService;
import org.jeecg.modules.survey.entity.SuggestionFeedback;
import org.jeecg.modules.survey.mapper.SuggestionFeedbackMapper;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: suggestion_feedback
 * @Author: jeecg-boot
 * @Date:   2024-10-28
 * @Version: V1.0
 */
@Service
public class SuggestionFeedbackServiceImpl extends ServiceImpl<SuggestionFeedbackMapper, SuggestionFeedback> implements ISuggestionFeedbackService {

}
