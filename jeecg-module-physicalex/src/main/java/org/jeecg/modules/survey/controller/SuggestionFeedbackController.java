package org.jeecg.modules.survey.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.survey.entity.SuggestionFeedback;
import org.jeecg.modules.survey.service.ISuggestionFeedbackService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: suggestion_feedback
 * @Author: jeecg-boot
 * @Date:   2024-10-28
 * @Version: V1.0
 */
@Api(tags="suggestion_feedback")
@RestController
@RequestMapping("/survey/suggestionFeedback")
@Slf4j
public class SuggestionFeedbackController extends JeecgController<SuggestionFeedback, ISuggestionFeedbackService> {
	@Autowired
	private ISuggestionFeedbackService suggestionFeedbackService;
	
	/**
	 * 分页列表查询
	 *
	 * @param suggestionFeedback
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "suggestion_feedback-分页列表查询")
	@ApiOperation(value="suggestion_feedback-分页列表查询", notes="suggestion_feedback-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SuggestionFeedback>> queryPageList(SuggestionFeedback suggestionFeedback,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SuggestionFeedback> queryWrapper = QueryGenerator.initQueryWrapper(suggestionFeedback, req.getParameterMap());
		Page<SuggestionFeedback> page = new Page<SuggestionFeedback>(pageNo, pageSize);
		IPage<SuggestionFeedback> pageList = suggestionFeedbackService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param suggestionFeedback
	 * @return
	 */
	@AutoLog(value = "suggestion_feedback-添加")
	@ApiOperation(value="suggestion_feedback-添加", notes="suggestion_feedback-添加")
//	@RequiresPermissions("wx.survey:suggestion_feedback:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SuggestionFeedback suggestionFeedback) {
		suggestionFeedbackService.save(suggestionFeedback);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param suggestionFeedback
	 * @return
	 */
	@AutoLog(value = "suggestion_feedback-编辑")
	@ApiOperation(value="suggestion_feedback-编辑", notes="suggestion_feedback-编辑")
//	@RequiresPermissions("wx.survey:suggestion_feedback:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SuggestionFeedback suggestionFeedback) {
		suggestionFeedbackService.updateById(suggestionFeedback);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "suggestion_feedback-通过id删除")
	@ApiOperation(value="suggestion_feedback-通过id删除", notes="suggestion_feedback-通过id删除")
//	@RequiresPermissions("wx.survey:suggestion_feedback:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		suggestionFeedbackService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "suggestion_feedback-批量删除")
	@ApiOperation(value="suggestion_feedback-批量删除", notes="suggestion_feedback-批量删除")
//	@RequiresPermissions("wx.survey:suggestion_feedback:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.suggestionFeedbackService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "suggestion_feedback-通过id查询")
	@ApiOperation(value="suggestion_feedback-通过id查询", notes="suggestion_feedback-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SuggestionFeedback> queryById(@RequestParam(name="id",required=true) String id) {
		SuggestionFeedback suggestionFeedback = suggestionFeedbackService.getById(id);
		if(suggestionFeedback==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(suggestionFeedback);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param suggestionFeedback
    */
//    @RequiresPermissions("wx.survey:suggestion_feedback:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SuggestionFeedback suggestionFeedback) {
        return super.exportXls(request, suggestionFeedback, SuggestionFeedback.class, "suggestion_feedback");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
//    @RequiresPermissions("wx.survey:suggestion_feedback:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SuggestionFeedback.class);
    }

}
