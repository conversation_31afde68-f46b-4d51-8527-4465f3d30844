package org.jeecg.modules.survey.service.impl;

import org.jeecg.modules.survey.entity.HealthCheckItemRisk;
import org.jeecg.modules.survey.mapper.HealthCheckItemRiskMapper;
import org.jeecg.modules.survey.service.IHealthCheckItemRiskService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: health_check_item_risk
 * @Author: jeecg-boot
 * @Date:   2024-10-10
 * @Version: V1.0
 */
@Service
public class HealthCheckItemRiskServiceImpl extends ServiceImpl<HealthCheckItemRiskMapper, HealthCheckItemRisk> implements IHealthCheckItemRiskService {

}
