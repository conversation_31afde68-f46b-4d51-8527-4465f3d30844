package org.jeecg.modules.complexdiagnoise;

import org.mvel2.MVEL;
import org.mvel2.compiler.ExecutableStatement;

import java.util.HashMap;
import java.util.Map;

public class MVELExample {

    public static void main(String[] args) {
        // 创建一个用于存储变量的上下文
        Map<String, Object> variables = new HashMap<>();
        variables.put("conditionA", true);
        variables.put("conditionB", false);
        variables.put("conditionC", true);
        variables.put("conditionD", true);

        // 定义逻辑表达式，使用 and 和 or 代替 && 和 ||
        String expression = "(conditionA || conditionB) && (conditionC && conditionD)";

        // 注意：这里的逻辑表达式有误，应该是使用 and 连接两个子表达式，
        // 但在子表达式内部应该使用 and 和 or 的一致方式，否则逻辑会出错。
        // 正确的表达式应该是：
        // String expression = "(conditionA or conditionB) and (conditionC or conditionD)";
        // 或者保持原始的逻辑：
        // String expression = "(conditionA || conditionB) && (conditionC && conditionD)";
        ExecutableStatement stmt = (ExecutableStatement) MVEL.compileExpression(expression);
        // 在给定的上下文中评估表达式并获取结果

        Boolean result = (Boolean) MVEL.evalToBoolean(expression, variables);


        // 输出结果
        System.out.println("Result of the expression: " + result);
    }
}
