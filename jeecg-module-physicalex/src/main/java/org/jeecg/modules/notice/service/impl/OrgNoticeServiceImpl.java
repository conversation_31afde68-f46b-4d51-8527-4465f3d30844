package org.jeecg.modules.notice.service.impl;

import org.jeecg.modules.notice.entity.OrgNotice;
import org.jeecg.modules.notice.mapper.OrgNoticeMapper;
import org.jeecg.modules.notice.service.IOrgNoticeService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 消息通知
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Service
public class OrgNoticeServiceImpl extends ServiceImpl<OrgNoticeMapper, OrgNotice> implements IOrgNoticeService {

}
