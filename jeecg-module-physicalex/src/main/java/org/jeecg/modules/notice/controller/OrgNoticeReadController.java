package org.jeecg.modules.notice.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.notice.entity.OrgNoticeRead;
import org.jeecg.modules.notice.service.IOrgNoticeReadService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 通知阅读记录
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Api(tags="通知阅读记录")
@RestController
@RequestMapping("/notice/orgNoticeRead")
@Slf4j
public class OrgNoticeReadController extends JeecgController<OrgNoticeRead, IOrgNoticeReadService> {
	@Autowired
	private IOrgNoticeReadService orgNoticeReadService;
	
	/**
	 * 分页列表查询
	 *
	 * @param orgNoticeRead
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "通知阅读记录-分页列表查询")
	@ApiOperation(value="通知阅读记录-分页列表查询", notes="通知阅读记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<OrgNoticeRead>> queryPageList(OrgNoticeRead orgNoticeRead,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<OrgNoticeRead> queryWrapper = QueryGenerator.initQueryWrapper(orgNoticeRead, req.getParameterMap());
		Page<OrgNoticeRead> page = new Page<OrgNoticeRead>(pageNo, pageSize);
		IPage<OrgNoticeRead> pageList = orgNoticeReadService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param orgNoticeRead
	 * @return
	 */
	@AutoLog(value = "通知阅读记录-添加")
	@ApiOperation(value="通知阅读记录-添加", notes="通知阅读记录-添加")
	@RequiresPermissions("notice:org_notice_read:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody OrgNoticeRead orgNoticeRead) {
		orgNoticeReadService.save(orgNoticeRead);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param orgNoticeRead
	 * @return
	 */
	@AutoLog(value = "通知阅读记录-编辑")
	@ApiOperation(value="通知阅读记录-编辑", notes="通知阅读记录-编辑")
	@RequiresPermissions("notice:org_notice_read:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody OrgNoticeRead orgNoticeRead) {
		orgNoticeReadService.updateById(orgNoticeRead);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "通知阅读记录-通过id删除")
	@ApiOperation(value="通知阅读记录-通过id删除", notes="通知阅读记录-通过id删除")
	@RequiresPermissions("notice:org_notice_read:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		orgNoticeReadService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "通知阅读记录-批量删除")
	@ApiOperation(value="通知阅读记录-批量删除", notes="通知阅读记录-批量删除")
	@RequiresPermissions("notice:org_notice_read:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.orgNoticeReadService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "通知阅读记录-通过id查询")
	@ApiOperation(value="通知阅读记录-通过id查询", notes="通知阅读记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<OrgNoticeRead> queryById(@RequestParam(name="id",required=true) String id) {
		OrgNoticeRead orgNoticeRead = orgNoticeReadService.getById(id);
		if(orgNoticeRead==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(orgNoticeRead);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param orgNoticeRead
    */
    @RequiresPermissions("notice:org_notice_read:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrgNoticeRead orgNoticeRead) {
        return super.exportXls(request, orgNoticeRead, OrgNoticeRead.class, "通知阅读记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("notice:org_notice_read:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OrgNoticeRead.class);
    }

}
