package org.jeecg.modules.notice.service.impl;

import org.jeecg.modules.notice.entity.OrgNoticeRead;
import org.jeecg.modules.notice.mapper.OrgNoticeReadMapper;
import org.jeecg.modules.notice.service.IOrgNoticeReadService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 通知阅读记录
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Service
public class OrgNoticeReadServiceImpl extends ServiceImpl<OrgNoticeReadMapper, OrgNoticeRead> implements IOrgNoticeReadService {

}
