package org.jeecg.modules.notice.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.notice.entity.OrgNotice;
import org.jeecg.modules.notice.entity.OrgNoticeRead;
import org.jeecg.modules.notice.service.IOrgNoticeReadService;
import org.jeecg.modules.notice.service.IOrgNoticeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 消息通知
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Api(tags="消息通知")
@RestController
@RequestMapping("/notice/orgNotice")
@Slf4j
public class OrgNoticeController extends JeecgController<OrgNotice, IOrgNoticeService> {
	@Autowired
	private IOrgNoticeService orgNoticeService;
	@Autowired
	private IOrgNoticeReadService orgNoticeReadService;
	
	/**
	 * 分页列表查询
	 *
	 * @param orgNotice
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "消息通知-分页列表查询")
	@ApiOperation(value="消息通知-分页列表查询", notes="消息通知-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<OrgNotice>> queryPageList(OrgNotice orgNotice,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<OrgNotice> queryWrapper = QueryGenerator.initQueryWrapper(orgNotice, req.getParameterMap());
		Page<OrgNotice> page = new Page<OrgNotice>(pageNo, pageSize);
		IPage<OrgNotice> pageList = orgNoticeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	 /**
	  * 公告列表查询
	  *
	  * @param

	  * @return
	  */
	 //@AutoLog(value = "消息通知-分页列表查询")
	 @ApiOperation(value="消息通知-公告列表查询", notes="消息通知-公告列表查询")
	 @GetMapping(value = "/publishedList")
	 public Result< List<OrgNotice>> publishedList(@RequestParam("customerId") String customerId) {
		 List<OrgNotice> notices = orgNoticeService.list(new LambdaQueryWrapper<OrgNotice>()
				 .eq(OrgNotice::getState, "1").orderByDesc(OrgNotice::getPublishTime));
		 List<OrgNotice> unreadNotices= Lists.newArrayList();
		 if (CollectionUtils.isNotEmpty(notices)){
			 Map<Integer, List<OrgNotice>> noticesMap = notices.stream().collect(Collectors.groupingBy(OrgNotice::getId));
			 List<OrgNoticeRead> readRecords = orgNoticeReadService.list(new LambdaQueryWrapper<OrgNoticeRead>()
					 .eq(OrgNoticeRead::getCustomerId, customerId));
			 Set<String> readNoticeIds = readRecords.stream().map(OrgNoticeRead::getNoticeId).collect(Collectors.toSet());
			 HashSet<Integer> publishedUnReadNoticeIds = new HashSet<>(noticesMap.keySet());
			 publishedUnReadNoticeIds.removeAll(readNoticeIds);
			 if (CollectionUtils.isNotEmpty(publishedUnReadNoticeIds)) {
				 for (Integer noticeId : publishedUnReadNoticeIds) {
					 unreadNotices.add(noticesMap.get(noticeId).get(0));
				 }
			 }

		 }

		 return Result.OK(unreadNotices);
	 }
	/**
	 *   添加
	 *
	 * @param orgNotice
	 * @return
	 */
	@AutoLog(value = "消息通知-添加")
	@ApiOperation(value="消息通知-添加", notes="消息通知-添加")
	@RequiresPermissions("notice:org_notice:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody OrgNotice orgNotice) {
		Date now=new Date();
		orgNotice.setUpdateTime(now);
		orgNotice.setCreateTime(now);
		orgNoticeService.save(orgNotice);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param orgNotice
	 * @return
	 */
	@AutoLog(value = "消息通知-编辑")
	@ApiOperation(value="消息通知-编辑", notes="消息通知-编辑")
	@RequiresPermissions("notice:org_notice:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody OrgNotice orgNotice) {
		Date now=new Date();
		orgNotice.setUpdateTime(now);
		orgNoticeService.updateById(orgNotice);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "消息通知-通过id删除")
	@ApiOperation(value="消息通知-通过id删除", notes="消息通知-通过id删除")
	@RequiresPermissions("notice:org_notice:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		orgNoticeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "消息通知-批量删除")
	@ApiOperation(value="消息通知-批量删除", notes="消息通知-批量删除")
	@RequiresPermissions("notice:org_notice:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.orgNoticeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "消息通知-通过id查询")
	@ApiOperation(value="消息通知-通过id查询", notes="消息通知-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<OrgNotice> queryById(@RequestParam(name="id",required=true) String id) {
		OrgNotice orgNotice = orgNoticeService.getById(id);
		if(orgNotice==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(orgNotice);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param orgNotice
    */
    @RequiresPermissions("notice:org_notice:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrgNotice orgNotice) {
        return super.exportXls(request, orgNotice, OrgNotice.class, "消息通知");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("notice:org_notice:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OrgNotice.class);
    }

}
