package org.jeecg.modules.sms.service;

import org.jeecg.config.ProxyFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

//@FeignClient(name = "mdSmsClient", url = "${biz.sms.md.serviceURL}",configuration = ProxyFeignConfig.class)
public interface MDSmsClient {
    //@PostMapping("/sms")
    //String sendSms(@RequestParam("action") String action, @RequestParam("account") String account, @RequestParam("password") String password, @RequestParam("mobile") String mobile, @RequestParam("content") String content, @RequestParam("extno") String extno, @RequestParam("rt") String rt);

    String sendSms(String action, String account, String password, String mobile, String content, String extno, String rt);

    String sendSmsUnicode(String action, String account, String password, String mobile, String content, String extno, String rt);


}
