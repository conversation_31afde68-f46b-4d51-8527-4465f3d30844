package org.jeecg.modules.sms.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 短信记录
 * @Author: jeecg-boot
 * @Date:   2024-11-10
 * @Version: V1.0
 */
@Data
@TableName("sms_records")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sms_records对象", description="短信记录")
public class SmsRecords implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**收信人手机号*/
	@Excel(name = "收信人手机号", width = 15)
    @ApiModelProperty(value = "收信人手机号")
    private java.lang.String phone;
	/**短信内容*/
	@Excel(name = "短信内容", width = 15)
    @ApiModelProperty(value = "短信内容")
    private java.lang.String content;
	/**发送结果*/
	@Excel(name = "发送结果", width = 15)
    @ApiModelProperty(value = "发送结果")
    private java.lang.String result;
	/**业务ID*/
	@Excel(name = "业务ID", width = 15)
    @ApiModelProperty(value = "业务ID")
    private java.lang.String bizId;
	/**业务类型*/
	@Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private java.lang.String bizType;
	/**发送时间*/
	@Excel(name = "发送时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发送时间")
    private java.util.Date sendTime;
	/**信息服务商*/
	@Excel(name = "信息服务商", width = 15)
    @ApiModelProperty(value = "信息服务商")
    private java.lang.String smsServer;
	/**是否成功*/
	@Excel(name = "是否成功", width = 15, dicCode = "success_flag")
	@Dict(dicCode = "success_flag")
    @ApiModelProperty(value = "是否成功")
    private java.lang.String state;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
    private java.lang.String tenantId;

    //创建时间、创建人
    private String createBy;
    private Date createTime;
    private String remark;
    private Integer retryCount;
    private String retryAble;

    //立即，定时
    private String sendMethod;
}
