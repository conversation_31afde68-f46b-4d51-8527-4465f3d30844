package org.jeecg.modules.sms.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.mzlion.easyokhttp.HttpClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.sms.entity.SmsRecords;
import org.jeecg.modules.sms.service.ISmsRecordsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 短信记录
 * @Author: jeecg-boot
 * @Date: 2024-11-10
 * @Version: V1.0
 */
@Api(tags = "短信记录")
@RestController
@RequestMapping("/sms/smsRecords")
@Slf4j
public class SmsRecordsController extends JeecgController<SmsRecords, ISmsRecordsService> {
    @Autowired
    private ISmsRecordsService smsRecordsService;

    /**
     * 分页列表查询
     *
     * @param smsRecords
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "短信记录-分页列表查询")
    @ApiOperation(value = "短信记录-分页列表查询", notes = "短信记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<SmsRecords>> queryPageList(SmsRecords smsRecords, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("state", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<SmsRecords> queryWrapper = QueryGenerator.initQueryWrapper(smsRecords, req.getParameterMap(), customeRuleMap);
        Page<SmsRecords> page = new Page<SmsRecords>(pageNo, pageSize);
        IPage<SmsRecords> pageList = smsRecordsService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param smsRecords
     * @return
     */
    @AutoLog(value = "短信记录-添加")
    @ApiOperation(value = "短信记录-添加", notes = "短信记录-添加")
    @RequiresPermissions("sms:sms_records:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody SmsRecords smsRecords) {
        smsRecordsService.save(smsRecords);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param smsRecords
     * @return
     */
    @AutoLog(value = "短信记录-编辑")
    @ApiOperation(value = "短信记录-编辑", notes = "短信记录-编辑")
    @RequiresPermissions("sms:sms_records:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody SmsRecords smsRecords) {
        smsRecordsService.updateById(smsRecords);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "短信记录-通过id删除")
    @ApiOperation(value = "短信记录-通过id删除", notes = "短信记录-通过id删除")
    @RequiresPermissions("sms:sms_records:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        smsRecordsService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "短信记录-批量删除")
    @ApiOperation(value = "短信记录-批量删除", notes = "短信记录-批量删除")
    @RequiresPermissions("sms:sms_records:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.smsRecordsService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "短信记录-通过id查询")
    @ApiOperation(value = "短信记录-通过id查询", notes = "短信记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<SmsRecords> queryById(@RequestParam(name = "id", required = true) String id) {
        SmsRecords smsRecords = smsRecordsService.getById(id);
        if (smsRecords == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(smsRecords);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param smsRecords
     */
    @RequiresPermissions("sms:sms_records:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SmsRecords smsRecords) {
        return super.exportXls(request, smsRecords, SmsRecords.class, "短信记录");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("sms:sms_records:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SmsRecords.class);
    }

    public static void main(String[] args) {
        Map<String, String> param = new HashMap<>();
        param.put("action", "send");
        param.put("account", "618191");
        param.put("password", "W2KDaUORsc");
        param.put("mobile", "***********");
        String content = "【乌兰察布市中心医院体检中心】" + "请登录心理测评系统完成心理测评。网址：http://psy.yingyangyun.cn/h5 用户名：1010406  密码：r6hsg8";
        //param.put("content", URLEncoder.encode(content, "UTF-8"));
        param.put("content", content);
        param.put("extno", "");
        param.put("rt", "json");

        Map<String, String> param2 = new HashMap<>();
        param2.put("action", "send");
        param2.put("account", "618190");
        param2.put("password", "nWIlTwobK5f");
        param2.put("mobile", "***********");
        String content2 = "【杭锦后旗医院】" + "您的验证码是：1234。请不要把验证码泄露给其他人。";
        //param.put("content", URLEncoder.encode(content, "UTF-8"));
        param2.put("content", content2);
        param2.put("extno", "");
        param2.put("rt", "json");

        String result = HttpClient.post("http://8.142.148.197:7862/sms").param(param).execute().asString();
        System.out.println(result);
    }

}
