package org.jeecg.modules.sms.service;

import org.jeecg.modules.sms.bo.SmsResult;
import org.jeecg.modules.sms.entity.SmsRecords;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 短信记录
 * @Author: jeecg-boot
 * @Date: 2024-11-10
 * @Version: V1.0
 */
public interface ISmsRecordsService extends IService<SmsRecords> {

    SmsResult sendSms(String phone, String content) throws Exception;

    SmsResult sendSmsUnicode(String phone, String content) throws Exception;

    SmsRecords saveSms(String phone, String content, String bizId, String bizType);

    public SmsResult saveAndSendSms(String sendMethod, String retryable, String phone, String content, String bizId, String bizType);

    public SmsResult saveAndSendSmsUnicode(String sendMethod, String retryable, String phone, String content, String bizId, String bizType);

    String getCode(int codeLength);

    //发送短信验证码
    String sendSmsCode(String phone,String bizCode) throws Exception;

    //校验短信验证码
    boolean checkSmsCode(String phone, String code,String bizCode) throws Exception;

}
