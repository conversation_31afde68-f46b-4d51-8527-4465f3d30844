package org.jeecg.modules.sms.service.impl;

import org.jeecg.modules.sms.service.MDSmsClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import okhttp3.Response;
import org.jeecg.excommons.utils.ProxyOkHttpUtil;

import java.net.URLEncoder;

@Service
public class MDSmsCliemtImpl implements MDSmsClient {
    @Value("${biz.sms.md.serviceURL}")
    private String serviceURL;
    @Autowired
    private ProxyOkHttpUtil ProxyOkHttpUtil;

    @Override
    public String sendSms(String action, String account, String password, String mobile, String content, String extno, String rt) {
        String url = serviceURL;
        String params = String.format("action=%s&account=%s&password=%s&mobile=%s&content=%s&extno=%s&rt=%s", action, account, password, mobile, content, extno, rt);

        try {
          return ProxyOkHttpUtil.get(url+"?"+params, null);
        } catch (Exception e) {
            throw new RuntimeException("发送短信时出现异常: " + e.getMessage(), e);
        }
    }

    @Override
    public String sendSmsUnicode(String action, String account, String password, String mobile, String content, String extno, String rt) {
            String url = serviceURL;
            try {
                // 对每个参数进行URL编码
                String params = String.format("action=%s&account=%s&password=%s&mobile=%s&content=%s&extno=%s&rt=%s",
                        URLEncoder.encode(action, "UTF-8"),
                        URLEncoder.encode(account, "UTF-8"),
                        URLEncoder.encode(password, "UTF-8"),
                        URLEncoder.encode(mobile, "UTF-8"),
                        URLEncoder.encode(content, "UTF-8"), // 关键：编码包含特殊字符的内容
                        URLEncoder.encode(extno, "UTF-8"),
                        URLEncoder.encode(rt, "UTF-8"));

                return ProxyOkHttpUtil.get(url + "?" + params, null);
            } catch (Exception e) {
                throw new RuntimeException("发送短信时出现异常: " + e.getMessage(), e);
            }
    }
}
