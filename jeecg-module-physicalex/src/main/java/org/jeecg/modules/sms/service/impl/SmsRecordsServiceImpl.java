package org.jeecg.modules.sms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.sms.MDProperties;
import org.jeecg.modules.basicinfo.entity.SmsCounter;
import org.jeecg.modules.basicinfo.mapper.SmsCounterMapper;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.sms.bo.SmsResult;
import org.jeecg.modules.sms.entity.SmsRecords;
import org.jeecg.modules.sms.mapper.SmsRecordsMapper;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.jeecg.modules.sms.service.MDSmsClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description: 短信记录
 * @Author: jeecg-boot
 * @Date: 2024-11-10
 * @Version: V1.0
 */
@Service
@Slf4j
public class SmsRecordsServiceImpl extends ServiceImpl<SmsRecordsMapper, SmsRecords> implements ISmsRecordsService {

    @Autowired
    private SmsRecordsMapper smsRecordsMapper;
    @Autowired
    private MDSmsClient mdSmsClient;
    @Autowired
    private MDProperties mdProperties;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SmsCounterMapper smsCounterMapper;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public SmsResult sendSms(String phone, String content) throws Exception {
        try {
            String mdSmsAccount = sysSettingService.getValueByCode("mdSmsAccount");
            String mdSmsPassword = sysSettingService.getValueByCode("mdSmsPassword");
            String mdSmsExtno = sysSettingService.getValueByCode("mdSmsExtno");
            String mdSignCode = sysSettingService.getValueByCode("mdSignCode");
            if (StringUtils.isNotBlank(mdSmsAccount)) {
                mdProperties.setAccount(mdSmsAccount);
            }
            if (StringUtils.isNotBlank(mdSmsPassword)) {
                mdProperties.setPassword(mdSmsPassword);
            }
            if (StringUtils.isNotBlank(mdSmsExtno)) {
                mdProperties.setExtno(mdSmsExtno);
            }
            if (StringUtils.isNotBlank(mdSignCode)) {
                mdProperties.setSignCode(mdSignCode);
            }
            content = mdProperties.getSignCode() + content;
            String result = mdSmsClient.sendSms("send", mdProperties.getAccount(), mdProperties.getPassword(), phone, content, mdProperties.getExtno(), "json");
            JSONObject jsonObject = JSONObject.parseObject(result);
            SmsResult smsResult = new SmsResult();
            String status = jsonObject.getString("status");
            smsResult.setSuccess(StringUtils.isNotBlank(status) && "0".equals(status));
            log.info("发送短信内容：" + content+"。是否发送成功："+smsResult.isSuccess());

            Integer balance = jsonObject.getInteger("balance");
            smsResult.setBalance(balance);
            jdbcTemplate.update("update sms_counter set totalamount = ? where id = '1'", balance);

            if (balance == 1000 || balance == 500 || balance == 100 || balance == 50 || balance == 10) {
                LambdaQueryWrapper<SmsCounter> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SmsCounter::getId, 1);
                queryWrapper.last("limit 1");
                SmsCounter smsCounter = smsCounterMapper.selectOne(queryWrapper);
                if (smsCounter != null) {
                    String notifyPhones = smsCounter.getNotifyphones();
                    List<String> phones = Arrays.asList(notifyPhones.split(","));
                    for (String notifyPhone : phones) {
                        mdSmsClient.sendSms("send", mdProperties.getAccount(), mdProperties.getPassword(), notifyPhone, mdSignCode + "短信余额不足，请及时充值,剩余数量：" + balance, mdProperties.getExtno(), "json");
                    }
                }
            }

            smsResult.setMsg(jsonObject.getString("msg"));
            JSONArray list = jsonObject.getJSONArray("list");
            smsResult.setResult(JSONArray.toJSONString(list));

            return smsResult;
        } catch (Exception e) {
            throw new Exception("发送短信异常:" + e.getMessage());
        }
    }

    @Override
    public SmsResult sendSmsUnicode(String phone, String content) throws Exception {
        try {
            String mdSmsAccount = sysSettingService.getValueByCode("mdSmsAccount");
            String mdSmsPassword = sysSettingService.getValueByCode("mdSmsPassword");
            String mdSmsExtno = sysSettingService.getValueByCode("mdSmsExtno");
            String mdSignCode = sysSettingService.getValueByCode("mdSignCode");
            if (StringUtils.isNotBlank(mdSmsAccount)) {
                mdProperties.setAccount(mdSmsAccount);
            }
            if (StringUtils.isNotBlank(mdSmsPassword)) {
                mdProperties.setPassword(mdSmsPassword);
            }
            if (StringUtils.isNotBlank(mdSmsExtno)) {
                mdProperties.setExtno(mdSmsExtno);
            }
            if (StringUtils.isNotBlank(mdSignCode)) {
                mdProperties.setSignCode(mdSignCode);
            }
            content = mdProperties.getSignCode() + content;
            log.info("发送短信内容：" + content);

            String result = mdSmsClient.sendSmsUnicode("send", mdProperties.getAccount(), mdProperties.getPassword(), phone, content, mdProperties.getExtno(), "json");
            JSONObject jsonObject = JSONObject.parseObject(result);
            SmsResult smsResult = new SmsResult();
            String status = jsonObject.getString("status");
            smsResult.setSuccess(StringUtils.isNotBlank(status) && "0".equals(status));

            Integer balance = jsonObject.getInteger("balance");
            smsResult.setBalance(balance);
            jdbcTemplate.update("update sms_counter set totalamount = ? where id = '1'", balance);

            if (balance == 1000 || balance == 500 || balance == 100 || balance == 50 || balance == 10) {
                LambdaQueryWrapper<SmsCounter> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SmsCounter::getId, 1);
                queryWrapper.last("limit 1");
                SmsCounter smsCounter = smsCounterMapper.selectOne(queryWrapper);
                if (smsCounter != null) {
                    String notifyPhones = smsCounter.getNotifyphones();
                    List<String> phones = Arrays.asList(notifyPhones.split(","));
                    for (String notifyPhone : phones) {
                        mdSmsClient.sendSmsUnicode("send", mdProperties.getAccount(), mdProperties.getPassword(), notifyPhone, mdSignCode + "短信余额不足，请及时充值,剩余数量：" + balance, mdProperties.getExtno(), "json");
                    }
                }
            }

            smsResult.setMsg(jsonObject.getString("msg"));
            JSONArray list = jsonObject.getJSONArray("list");
            smsResult.setResult(JSONArray.toJSONString(list));

            return smsResult;
        } catch (Exception e) {
            throw new Exception("发送短信异常:" + e.getMessage());
        }
    }

    @Override
    public SmsRecords saveSms(String phone, String content, String bizId, String bizType) {
        SmsRecords records = new SmsRecords();
        records.setCreateTime(new Date());
        records.setBizId(bizId);
        records.setBizType(bizType);
        records.setPhone(phone);
        records.setContent(content);
        records.setSendMethod(ExConstants.SMS_SEND_METHOD_TIMING);
        records.setSmsServer("漫道科技");
        smsRecordsMapper.insert(records);
        return records;
    }

    @Override
    public SmsResult saveAndSendSms(String sendMethod, String retryable, String phone, String content, String bizId, String bizType) {
        try {
            SmsRecords records = new SmsRecords();
            records.setCreateTime(new Date());
            records.setBizId(bizId);
            records.setBizType(bizType);
            records.setPhone(phone);
            records.setContent(StringUtils.substring(content,0,500));
            records.setSendMethod(sendMethod);
            records.setRetryAble(retryable);
            records.setSmsServer("漫道科技");
            smsRecordsMapper.insert(records);

            SmsResult sendResult = sendSms(phone, content);
            if (sendResult.isSuccess()) {
                records.setSendTime(new Date());
                records.setState(ExConstants.SMS_STATE_SUCCESS);
            } else {
                records.setSendTime(new Date());
                records.setState(ExConstants.SMS_STATE_FAIL);
                records.setRemark(sendResult.getMsg());
            }

            records.setResult(sendResult.getResult());
            smsRecordsMapper.updateById(records);

            return sendResult;
        } catch (Exception e) {
            log.error("发短信异常:" +e.getMessage(),e);
            return new SmsResult(false, "发短信异常:" + e.getMessage());
        }
    }

    @Override
    public SmsResult saveAndSendSmsUnicode(String sendMethod, String retryable, String phone, String content, String bizId, String bizType) {
        try {
            SmsRecords records = new SmsRecords();
            records.setCreateTime(new Date());
            records.setBizId(bizId);
            records.setBizType(bizType);
            records.setPhone(phone);
            records.setContent(content);
            records.setSendMethod(sendMethod);
            records.setRetryAble(retryable);
            records.setSmsServer("漫道科技");
            smsRecordsMapper.insert(records);

            SmsResult sendResult = sendSmsUnicode(phone, content);
            if (sendResult.isSuccess()) {
                records.setSendTime(new Date());
                records.setState(ExConstants.SMS_STATE_SUCCESS);
            } else {
                records.setSendTime(new Date());
                records.setState(ExConstants.SMS_STATE_FAIL);
                records.setRemark(sendResult.getMsg());
            }

            records.setResult(sendResult.getResult());
            smsRecordsMapper.updateById(records);

            return sendResult;
        } catch (Exception e) {
            return new SmsResult(false, "发短信异常:" + e.getMessage());
        }
    }

    @Override
    public String getCode(int codeLength) {
        String charValue = "";
        for (int i = 0; i < codeLength; i++) {
            char c = (char) (randomInt(0, 10) + '0');
            charValue += String.valueOf(c);
        }
        return charValue;
    }

    @Override
    public String sendSmsCode(String phone, String bizCode) throws Exception {
        bizCode = StringUtils.isBlank(bizCode) ? "default" : bizCode;
        //防刷, 5分钟内只能发送一次
        String key = "smsCode::" + bizCode + "::" + phone;
        if (redisUtil.hasKey(key)) {
            throw new Exception("短信发送太频繁，请稍后再试");
        }
        String code = getCode(6);
        String content = "您的验证码是：" + code + "。请不要把验证码泄露给其他人。";
        SmsResult smsResult = sendSms(phone, content);
        //if (smsResult.isSuccess()) {
        saveSms(phone, content, code, "smsCode");
        // 保存验证码到redis
        boolean isSuccess = redisUtil.set(key, code, 300);
        if (!isSuccess) {
            throw new Exception("短信发送失败，请稍后再试");
        }
        //}
        return code;
    }

    @Override
    public boolean checkSmsCode(String phone, String code, String bizCode) throws Exception {
        bizCode = StringUtils.isBlank(bizCode) ? "default" : bizCode;
        String key = "smsCode::" + bizCode + "::" + phone;
        if (!redisUtil.hasKey(key)) {
            throw new Exception("验证码已过期，请重新获取");
        }
        String redisCode = (String) redisUtil.get(key);
        if (!code.equals(redisCode)) {
            throw new Exception("验证码错误");
        }
        // 验证成功后删除验证码
        //redisUtil.del(key);
        return true;
    }

    private int randomInt(int from, int to) {
        SecureRandom r = new SecureRandom();
        return from + r.nextInt(to - from);
    }
}
