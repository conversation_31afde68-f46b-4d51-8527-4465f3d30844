package org.jeecg.modules.socketio.service;

public interface SocketIOService {
    /**
     * 推送消息给指定客户端
     * @param sessionId 客户端sessionId
     * @param eventName 事件名称
     * @param message 消息内容
     */
    void pushMessageToClient(String sessionId, String eventName, String message);

    /**
     * 推送消息给所有客户端
     * @param eventName 事件名称
     * @param message 消息内容
     */
    void pushMessageToAllClients(String eventName, String message);
}