package org.jeecg.modules.socketio.service.impl;

import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.annotation.OnConnect;
import com.corundumstudio.socketio.annotation.OnDisconnect;
import com.corundumstudio.socketio.annotation.OnEvent;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.socketio.service.SocketIOService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class SocketIOServiceImpl implements SocketIOService {

    @Autowired
    private SocketIOServer socketIOServer;

    // 存储客户端连接
    private final Map<String, SocketIOClient> clientMap = new ConcurrentHashMap<>();

    // 最大同时处理任务数（例如设置为5）
    private static final int MAX_CONCURRENT = 5;
    // 当前正在处理的任务数
    private final AtomicInteger activeTaskCount = new AtomicInteger(0);
    // 等待队列，存储等待处理的请求任务
    private final ConcurrentLinkedQueue<AiStreamRequestTask> waitingQueue = new ConcurrentLinkedQueue<>();

   // @PostConstruct
    private void startServer() {
        socketIOServer.addListeners(this);
        socketIOServer.start();
        log.info("SocketIO服务已启动");
    }

    //@PreDestroy
    private void stopServer() {
        if (socketIOServer != null) {
            socketIOServer.stop();
            log.info("SocketIO服务已停止");
        }
    }

    //@OnConnect
    public void onConnect(SocketIOClient client) {
        String sessionId = client.getSessionId().toString();
        clientMap.put(sessionId, client);
        log.info("客户端连接成功，sessionId: {}", sessionId);
    }

    //@OnDisconnect
    public void onDisconnect(SocketIOClient client) {
        String sessionId = client.getSessionId().toString();
        clientMap.remove(sessionId);
        log.info("客户端断开连接，sessionId: {}", sessionId);
        // 如果该客户端在等待队列中，移除之
        waitingQueue.removeIf(task -> task.client.getSessionId().toString().equals(sessionId));
        updateQueueStatus();
    }

    /**
     * 处理 aiStreamRequest 事件
     */
    //@OnEvent("aiStreamRequest")
    public void onAiStreamRequest(SocketIOClient client, String data) {
        log.info("收到AI流式接口调用请求，sessionId: {}, 请求数据: {}", client.getSessionId(), data);
        // 若当前处理任务数未达到上限，则直接启动任务
        if (activeTaskCount.get() < MAX_CONCURRENT) {
            activeTaskCount.incrementAndGet();
            processTask(client, data);
        } else {
            // 否则，将请求加入等待队列，并发送等待状态
            AiStreamRequestTask task = new AiStreamRequestTask(client, data);
            waitingQueue.add(task);
            int position = waitingQueue.size();
            log.info("请求已加入等待队列，当前排队位置：{}", position);
            client.sendEvent("queueStatus", "已排队，当前排队位置：" + position);
        }
    }

    /**
     * 处理请求任务（模拟调用 AI 流式接口，每秒返回一段数据，共返回10段）
     */
    private void processTask(SocketIOClient client, String data) {
        new Thread(() -> {
            try {
                for (int i = 0; i < 10; i++) {
                    String partialResponse = "AI响应分段 " + (i + 1) + "：处理数据 -> " + data;
                    Thread.sleep(1000); // 模拟流式返回延时
                    client.sendEvent("aiStreamResponse", partialResponse);
                    log.info("推送给客户端 {}: {}", client.getSessionId(), partialResponse);
                }
            } catch (InterruptedException e) {
                log.error("AI流式接口调用处理中断", e);
                client.sendEvent("aiStreamResponse", "AI流式处理被中断");
            } finally {
                activeTaskCount.decrementAndGet();
                processNextTask();
            }
        }).start();
    }

    /**
     * 检查等待队列，如有等待任务则启动下一个任务，并更新排队状态
     */
    private void processNextTask() {
        AiStreamRequestTask nextTask = waitingQueue.poll();
        if (nextTask != null) {
            activeTaskCount.incrementAndGet();
            processTask(nextTask.client, nextTask.data);
            updateQueueStatus();
        }
    }

    /**
     * 仅更新等待队列中各任务的排队状态通知
     */
    private void updateQueueStatus() {
        int position = 1;
        for (AiStreamRequestTask task : waitingQueue) {
            task.client.sendEvent("queueStatus", "已排队，当前排队位置：" + position);
            position++;
        }
    }

    /**
     * 封装 AI 流式请求任务数据结构
     */
    private static class AiStreamRequestTask {
        SocketIOClient client;
        String data;

        public AiStreamRequestTask(SocketIOClient client, String data) {
            this.client = client;
            this.data = data;
        }
    }

    @Override
    public void pushMessageToClient(String sessionId, String eventName, String message) {
        SocketIOClient client = clientMap.get(sessionId);
        if (client != null) {
            client.sendEvent(eventName, message);
        } else {
            log.error("客户端不存在，sessionId: {}", sessionId);
        }
    }

    @Override
    public void pushMessageToAllClients(String eventName, String message) {
        for (SocketIOClient client : clientMap.values()) {
            client.sendEvent(eventName, message);
        }
    }
}
