package org.jeecg.modules.mobile.reg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.mobile.reg.entity.CustomeraccountCustomer;
import org.jeecg.modules.mobile.reg.mapper.CustomeraccountCustomerMapper;
import org.jeecg.modules.mobile.reg.service.ICustomeraccountCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class CustomeraccountCustomerServiceImpl extends ServiceImpl<CustomeraccountCustomerMapper, CustomeraccountCustomer> implements ICustomeraccountCustomerService {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getRelation(String customerId, String accountId) {

        String relationType = null;
        try {
            relationType = jdbcTemplate.queryForObject("select relation_type from customeraccount_customer where customer_id=? and account_id=? limit 1", String.class, customerId, accountId);
        } catch (Exception ignored) {
        }

        return relationType;
    }

    @Override
    public void bindAccountAndCustomer(String accountId, String customerId) throws Exception {
        //1检查是否有对应的账号
        Integer count = jdbcTemplate.queryForObject("select count(1) from customer_account where id=?", Integer.class, accountId);
        if (count == 0) {
            throw new Exception("账号不存在！");
        }
        //2检查是否有对应的用户
        count = jdbcTemplate.queryForObject("select count(1) from customer where id=?", Integer.class, customerId);
        if (count == 0) {
            throw new Exception("档案不存在！");
        }
        //3解绑原有关系
        jdbcTemplate.update("delete from customeraccount_customer where customer_id=? and account_id=?", customerId,accountId);
        //4绑定新关系
        CustomeraccountCustomer customeraccountCustomer = new CustomeraccountCustomer();
        customeraccountCustomer.setAccountId(accountId);
        customeraccountCustomer.setCustomerId(customerId);
        customeraccountCustomer.setRelationType(null);
        customeraccountCustomer.setDefaultState("1");
        save(customeraccountCustomer);
    }
}
