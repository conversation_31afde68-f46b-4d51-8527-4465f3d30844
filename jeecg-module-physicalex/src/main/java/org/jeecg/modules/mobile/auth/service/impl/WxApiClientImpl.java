package org.jeecg.modules.mobile.auth.service.impl;

import com.alibaba.fastjson.JSONObject;
import okhttp3.Response;
import org.jeecg.excommons.utils.ProxyOkHttpUtil;
import org.jeecg.modules.mobile.auth.service.WxApiClient;
import org.jeecg.modules.mobile.bo.ShortLinkRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class WxApiClientImpl implements WxApiClient {
    @Autowired
    private ProxyOkHttpUtil proxyOkHttpUtil;


    @Override
    public String getSessionInfo(String appid, String secret, String jsCode, String grantType) throws IOException {
        String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=%s", appid, secret, jsCode, grantType);
        return proxyOkHttpUtil.get(url, null);
    }

    @Override
    public String getAccessToken(String appid, String secret, String grantType) throws IOException {
        String url = String.format("https://api.weixin.qq.com/cgi-bin/token?appid=%s&secret=%s&grant_type=%s", appid, secret, grantType);
        return proxyOkHttpUtil.get(url, null);
    }

    @Override
    public JSONObject getShortUrl(ShortLinkRequest param, String accessToken) throws IOException {
        String url = String.format("https://api.weixin.qq.com/wxa/genwxashortlink?access_token=%s", accessToken);
        String resp = proxyOkHttpUtil.post(url, JSONObject.toJSONString(param), null);
        return JSONObject.parseObject(resp);
    }

    @Override
    public JSONObject sendSubscribeMessage(JSONObject param, String accessToken) throws IOException {
        String url = String.format("https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s", accessToken);
        String resp = proxyOkHttpUtil.post(url, param.toJSONString(), null);
        return JSONObject.parseObject(resp);
    }
}
