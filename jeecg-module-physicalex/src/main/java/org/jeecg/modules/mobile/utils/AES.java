package org.jeecg.modules.mobile.utils;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;

@Slf4j
public class AES {

    private static boolean initialized = false;

    static {
        initialize();
    }

    /**
     * AES decryption
     *
     * @param content  Encrypted content
     * @param keyByte  Key bytes
     * @param ivByte   Initialization vector bytes
     * @return Decrypted byte array
     */
    public byte[] decrypt(byte[] content, byte[] keyByte, byte[] ivByte) {
        if (!initialized) {
            initialize();
        }
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec sKeySpec = new SecretKeySpec(keyByte, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivByte);
            cipher.init(Cipher.DECRYPT_MODE, sKeySpec, ivSpec);
            return cipher.doFinal(content);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | NoSuchProviderException e) {
            log.error("Cipher instantiation failed", e);
        } catch (InvalidKeyException | InvalidAlgorithmParameterException e) {
            log.error("Cipher initialization failed", e);
        } catch (IllegalBlockSizeException | BadPaddingException e) {
            log.error("Decryption process failed", e);
        } catch (Exception e) {
            log.error("Decryption exception", e);
        }
        return null;
    }

    /**
     * Initialize BouncyCastle provider
     */
    private static synchronized void initialize() {
        if (initialized) return;
        try {
            Security.addProvider(new BouncyCastleProvider());
            initialized = true;
        } catch (Exception e) {
            log.error("Failed to register BouncyCastleProvider", e);
        }
    }
}