package org.jeecg.modules.mobile.summary.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.Report4SmallApp;
import org.jeecg.modules.summary.service.ICustomerRegReportService;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.jeecg.modules.summary.service.ISummaryAdviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-05-20
 * @Version: V1.0
 */
@Api(tags = "总检")
@RestController(value = "wxCustomerRegSummaryController")
@RequestMapping("/summary/customerRegSummary")
@Slf4j
public class CustomerRegSummaryController extends JeecgController<CustomerRegSummary, ICustomerRegSummaryService> {
    @Autowired
    private ICustomerRegSummaryService customerRegSummaryService;
    @Autowired
    private ICustomerRegDepartSummaryService customerRegDepartSummaryService;
    @Autowired
    private ISummaryAdviceService summaryAdviceService;
    @Autowired
    private ICustomerRegReportService customerRegReportService;
    @Autowired
    private ICustomerRegItemResultService customerRegItemResultService;
    @Autowired
    private ICustomerRegService customerRegService;

    @ApiOperation(value = "微信端-获取体检报告数据", notes = "微信端-获取体检报告数据")
    @GetMapping(value = "/getReport4SmallApp")
    public Result<?> getReport4SmallApp(String customerId) {

        Report4SmallApp report4SmallApp = customerRegReportService.getReport4SmallApp(customerId);
        if (Objects.nonNull(report4SmallApp)) {
            return Result.OK("获取成功！", report4SmallApp);
        }
        return Result.error("获取报告失败！");
    }

    @ApiOperation(value = "微信端-获取体检报告数据", notes = "微信端-获取体检报告数据")
    @GetMapping(value = "/getReport4SmallAppV2")
    public Result<?> getReport4SmallAppV2(String customerRegId) {
        try {
            Report4SmallApp report4SmallApp = customerRegReportService.getReport4SmallAppByRegId(customerRegId);
            return Result.OK("获取成功！", report4SmallApp);
        } catch (Exception e) {
            log.error("getReport4SmallAppV2获取报告失败：", e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "微信端-获取当前账户相关体检人的报告列表", notes = "微信端-获取当前账户相关体检人的报告列表")
    @GetMapping(value = "/getReportsByOpenId")
    public Result<?> getReportsByOpenId(String openId) {

        List<Report4SmallApp> report4SmallApps = customerRegReportService.getReportsByOpenId(openId);
        if (CollectionUtils.isNotEmpty(report4SmallApps)) {
            return Result.OK("获取成功！", report4SmallApps);
        }
        return Result.error("获取报告失败！");
    }

    @ApiOperation(value = "微信端-获取电子报告", notes = "微信端-获取电子报告")
    @GetMapping(value = "/getEReportByRegId")
    public Result<?> getEReportByRegId(String customerRegId) {
        CustomerReg customerReg = customerRegReportService.getEReportByRegId(customerRegId);
        if (Objects.nonNull(customerReg)) {
            return Result.OK("获取成功！", customerReg);
        }
        return Result.error("获取报告失败！");
    }

    @ApiOperation(value = "微信端-获取电子报告", notes = "微信端-获取电子报告")
    @GetMapping(value = "/getEReportByRegIdV2")
    public Result<?> getEReportByRegIdV2(String customerRegId) {
        try {
            String reportUrl = customerRegReportService.getEReportUrlByRegId(customerRegId);
            return Result.OK("获取成功！", reportUrl);
        } catch (Exception e) {
            log.error("getEReportByRegIdV2获取报告失败：", e);
            return Result.error(e.getMessage());
        }
    }

}
