package org.jeecg.modules.mobile.auth.service.impl;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.config.WxProperties;
import org.jeecg.excommons.utils.ProxyOkHttpUtil;
import org.jeecg.modules.mobile.auth.service.WxApiClient;
import org.jeecg.modules.mobile.auth.service.WxApiService;
import org.jeecg.modules.mobile.bo.ShortLinkRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@CacheConfig(cacheNames = "wxAccessKeyCache", cacheManager = "wxAccessCacheManager")
@Service
public class WxApiServiceImpl implements WxApiService {
    @Autowired
    private WxApiClient wxApiClient;
    @Autowired
    private WxProperties wxProperties;
    @Autowired
    private ProxyOkHttpUtil proxyOkHttpUtil;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public JSONObject getSessionInfo(String jsCode, String grantType) throws Exception {
        if (StringUtils.isBlank(wxProperties.getAppid()) || StringUtils.isBlank(wxProperties.getMpSeceret())) {
            throw new Exception("appid或mpSeceret为空");
        }
        return JSONObject.parseObject(wxApiClient.getSessionInfo(wxProperties.getAppid(), wxProperties.getMpSeceret(), jsCode, grantType));
    }

    @Cacheable(unless = "#result == null")
    @Override
    public String getAccessToken() throws Exception {
        if (StringUtils.isBlank(wxProperties.getAppid()) || StringUtils.isBlank(wxProperties.getMpSeceret())) {
            throw new Exception("appid或mpSeceret为空");
        }
        String resp = wxApiClient.getAccessToken(wxProperties.getAppid(), wxProperties.getMpSeceret(), "client_credential");
        JSONObject result = JSONObject.parseObject(resp);
        if (result == null) {
            throw new Exception("获取access_token失败,返回结果为空");
        }
        if (result.containsKey("access_token")) {
            return result.getString("access_token");
        } else {
            String errmsg = result.getString("errmsg");
            throw new Exception("获取access_token失败,errmsg:" + errmsg);
        }
    }

    @Override
    public String getShortUrl(ShortLinkRequest param, String accessToken) throws Exception {
        JSONObject result = wxApiClient.getShortUrl(param, accessToken);
        if (result == null) {
            throw new Exception("获取短链接失败,返回结果为空");
        }
        if (result.containsKey("link")) {
            String shortUrl = result.getString("link");
            if (StringUtils.isBlank(result.getString("link"))) {
                throw new Exception("获取短链接失败,short_url为空");
            }
            return shortUrl;
        } else {
            String errmsg = result.getString("errmsg");
            throw new Exception("获取短链接失败,errmsg:" + errmsg);
        }
    }

    @Override
    public String getOpenIdByWxpubCode(String code) throws Exception {
        // 1. 获取 access_token 和 openid
        String accessTokenUrl = String.format("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code", wxProperties.getAppid(), wxProperties.getMpSeceret(), code);

        String response = proxyOkHttpUtil.get(accessTokenUrl, null);
        if (StringUtils.isBlank(response)) {
            throw new Exception("获取access_token失败,返回结果为空");
        }
        try {
            JSONObject jsonResponse = JSONObject.parseObject(response);
            if (jsonResponse.containsKey("errcode")) {
                throw new Exception("获取access_token失败,errmsg:" + jsonResponse.getString("errmsg"));
            }
            String accessToken = jsonResponse.getString("access_token");
            return jsonResponse.getString("openid");
        } catch (Exception e) {
            throw new Exception("获取用户信息失败:" + e.getMessage());
        }
    }

    @Override
    public String getJsapiTicket() throws Exception {

        String JSAPI_TICKET_URL = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi";
        String accessToken = getAccessToken();
        String url = String.format(JSAPI_TICKET_URL, accessToken);
        String response = proxyOkHttpUtil.get(url, null);
        JSONObject json = JSONObject.parseObject(response);

        if (json.getInteger("errcode") == 0) {
            return json.getString("ticket");
        } else {
            throw new RuntimeException("获取jsapi_ticket失败: " + json);
        }
    }


    @Override
    public String getAccessToken4JsSdk() throws Exception {
        String cacheKey = "wxAccessToken4JsSdk";
        String cachedToken = (String) redisUtil.get(cacheKey);

        if (StringUtils.isNotBlank(cachedToken)) {
            return cachedToken;
        }

        String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
        String APPID = wxProperties.getAppid();
        String APPSECRET = wxProperties.getMpSeceret();
        String url = String.format(ACCESS_TOKEN_URL, APPID, APPSECRET);
        String response = proxyOkHttpUtil.get(url, null);
        JSONObject json = JSONObject.parseObject(response);

        if (json.containsKey("access_token")) {
            String accessToken = json.getString("access_token");
            // Cache the access token with an expiration time (e.g., 2 hours)
            redisUtil.set(cacheKey, accessToken, 7200);
            return accessToken;
        } else {
            throw new Exception("Failed to get access_token: " + json);
        }
    }

    @Override
    public Map<String, String> generateSignature(String url) throws Exception {
        String APPID = wxProperties.getAppid();

        String jsapiTicket = getJsapiTicket();
        String nonceStr = generateNonceStr(); // 生成随机字符串
        long timestamp = System.currentTimeMillis() / 1000;

        // 拼接签名字符串
        String str = String.format("jsapi_ticket=%s&noncestr=%s&timestamp=%d&url=%s", jsapiTicket, nonceStr, timestamp, url);

        // SHA1 加密
        String signature = DigestUtils.sha1Hex(str);

        Map<String, String> config = new HashMap<>();
        config.put("appId", APPID);
        config.put("timestamp", String.valueOf(timestamp));
        config.put("nonceStr", nonceStr);
        config.put("signature", signature);
        return config;
    }

    private String generateNonceStr() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
}
