package org.jeecg.modules.mobile.health.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.mobile.health.service.HealthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Api(tags = "客户登记")
@RestController
@RequestMapping("/reg/health")
@Slf4j
public class HealthController extends Je<PERSON>g<PERSON>ontroller<CustomerReg, ICustomerRegService> {
    @Autowired
    private HealthService healthService;


    @ApiOperation(value = "健康趋势", notes = "健康趋势")
    @GetMapping(value = "/itemResult")
    public Result<?> getItemResult(String idCard) {

        return Result.ok(healthService.getItemResult(idCard));
    }

}
