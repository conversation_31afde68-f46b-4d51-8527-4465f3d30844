package org.jeecg.modules.mobile.reg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@TableName("customeraccount_customer")
@ApiModel(value = "CustomeraccountCustomer对象", description = "")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class CustomeraccountCustomer implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String customerId;

    private String accountId;

    private String openId;

    private String relationType;

    private String defaultState;

    private String idCard;


}
