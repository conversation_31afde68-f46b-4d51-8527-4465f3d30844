package org.jeecg.modules.mobile.reg.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.ItemSuit;
import org.jeecg.modules.basicinfo.mapper.ItemSuitMapper;
import org.jeecg.modules.reg.bo.CustomerExamTrack;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerMapper;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.ICustomerService;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.appointment.mapper.CustomerOrderMapper;
import org.jeecg.modules.mobile.bo.CustomerAccountRelation;
import org.jeecg.modules.mobile.reg.entity.CustomerAccount;
import org.jeecg.modules.mobile.reg.entity.CustomeraccountCustomer;
import org.jeecg.modules.mobile.reg.mapper.CustomerAccountMapper;
import org.jeecg.modules.mobile.reg.mapper.CustomeraccountCustomerMapper;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 体检人员账户表
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
@Service
public class CustomerAccountServiceImpl extends ServiceImpl<CustomerAccountMapper, CustomerAccount> implements ICustomerAccountService {
    @Autowired
    private CustomerAccountMapper customerAccountMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private CustomeraccountCustomerMapper customeraccountCustomerMapper;
    @Autowired
    private SequenceGenerator sequenceGenerator;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Autowired
    private CustomerOrderMapper customerOrderMapper;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private ItemSuitMapper itemSuitMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Customer saveAccount(Customer reqCustomer) throws Exception {
        if (!(IdcardUtil.isValidCard15(reqCustomer.getIdCard()) || IdcardUtil.isValidCard18(reqCustomer.getIdCard(), false))) {
            throw new RuntimeException("身份证号不合法，请确认后重新输入！");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = null;
        if (Objects.nonNull(sysUser)) {
            username = sysUser.getUsername();
        }
        Date now = new Date();
        Integer age = null;
        if (reqCustomer.getBirthday() != null) {
            //根据出生日期计算年龄
            age = DateUtil.ageOfNow(reqCustomer.getBirthday());
        }
        // reqCustomer.setAddress(reqCustomer.getProvince() + reqCustomer.getCity());
        Customer customer = customerMapper.selectOne(new LambdaQueryWrapper<Customer>().eq(Customer::getIdCard, reqCustomer.getIdCard()).last("limit 1"));

        CustomerAccount account = customerAccountMapper.selectById(reqCustomer.getAccountId());
        String defaultProfile = reqCustomer.getDefaultProfile();
        if (StringUtils.equals("1", defaultProfile)) {
            jdbcTemplate.update("update customeraccount_customer set default_state = '0' where account_id = ?", reqCustomer.getAccountId());
        }

        if (Objects.nonNull(customer)) {
            reqCustomer.setId(customer.getId());
            reqCustomer.setAge(age);
            reqCustomer.setUpdateTime(now);
            reqCustomer.setUpdateBy(username);
            customerMapper.updateById(reqCustomer);
            CustomeraccountCustomer cac = customeraccountCustomerMapper.selectOne(new LambdaQueryWrapper<CustomeraccountCustomer>().eq(CustomeraccountCustomer::getAccountId, account.getId()).eq(CustomeraccountCustomer::getCustomerId, customer.getId()));
            if (Objects.isNull(cac)) {
                cac = new CustomeraccountCustomer();
                cac.setAccountId(reqCustomer.getAccountId());
                cac.setCustomerId(customer.getId());
                cac.setOpenId(reqCustomer.getOpenId());
                cac.setDefaultState(defaultProfile);
                cac.setRelationType(reqCustomer.getRelationType());
                customeraccountCustomerMapper.insert(cac);
            } else {
                cac.setDefaultState(defaultProfile);
                cac.setRelationType(reqCustomer.getRelationType());
                customeraccountCustomerMapper.updateById(cac);
            }
        } else {
            reqCustomer.setArchivesNum(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_ARCHIVE_NO));
            reqCustomer.setAge(age);
            reqCustomer.setCreateTime(now);
            reqCustomer.setCreateBy(username);
            //customerMapper.insert(reqCustomer);
            customerService.saveCustomer(reqCustomer);

            String customerId = reqCustomer.getId();
            CustomeraccountCustomer cac = new CustomeraccountCustomer();
            cac.setAccountId(account.getId());
            cac.setCustomerId(customerId);
            cac.setOpenId(reqCustomer.getOpenId());
            cac.setDefaultState(defaultProfile);
            cac.setRelationType(reqCustomer.getRelationType());
            customeraccountCustomerMapper.insert(cac);
        }

        return reqCustomer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Customer updateAccount(Customer customer) {
        if (!(IdcardUtil.isValidCard15(customer.getIdCard()) || IdcardUtil.isValidCard18(customer.getIdCard(), false))) {
            throw new RuntimeException("身份证号不合法，请确认后重新输入！");
        }
        Date now = new Date();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = null;
        if (Objects.nonNull(sysUser)) {
            username = sysUser.getUsername();
        }
        Integer age = null;
        if (customer.getBirthday() != null) {
            //根据出生日期计算年龄
            age = DateUtil.ageOfNow(customer.getBirthday());
        }
        customer.setAddress(customer.getProvince() + customer.getCity());
//        if (Objects.nonNull(customer)) {
        customer.setArchivesNum(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_ARCHIVE_NO));
        customer.setAge(age);
        customer.setUpdateTime(now);
        customer.setUpdateBy(username);
        customerMapper.updateById(customer);
        CustomeraccountCustomer cac = customeraccountCustomerMapper.selectById(customer.getAccountCustomerId());
        cac.setRelationType(customer.getRelationType());
        customeraccountCustomerMapper.updateById(cac);
//        } else {
//            customer = new Customer();
//            customer.setArchivesNum(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_ARCHIVE_NO));
//            customer.setAge(age);
//            customer.setCreateTime(now);
//            customer.setCreateBy(username);
//            customerMapper.insert(customer);
//            CustomeraccountCustomer cac = customeraccountCustomerMapper.selectById(customer.getAccountCustomerId());
//            cac.setRelationType(customer.getRelationType());
//            customeraccountCustomerMapper.updateById(cac);
//        }
//        customerAccount.setUpdateTime(now);
//        customerAccount.setUpdateBy(sysUser.getUsername());
//        customerAccountMapper.updateById(customerAccount);
        return customer;
    }

    @Override
    public List<Customer> getCustomersByAccountId(String accountId, String relationType) {
        List<Customer> customers = customerMapper.getCustomersByAccountId(accountId, relationType);
        return customers;
    }

    @Override
    public Customer getCustomersByOpenIdAndIdCard(String openId, String relationType, String idCard) {
        CustomerAccount account = customerAccountMapper.selectOne(new LambdaQueryWrapper<CustomerAccount>().eq(CustomerAccount::getOpenId, openId));
        Validate.notNull(account, "未查询到相关账户信息！");
        Customer customers = customerMapper.getCustomersByAccountIdAndIdCard(account.getId(), relationType, idCard);
        return customers;
    }


    @Override
    public Customer getDefaultCustomerByAccountId(String accountId) {

        List<Customer> customerList = customerMapper.getCustomersByAccountId(accountId, null);
        if (CollectionUtils.isNotEmpty(customerList)) {
            return customerList.get(0);
        }
        return null;
    }

    @Override
    public Customer getDefaultCustomerByOpenId(String openId) {
        return customerMapper.getDefaultCustomerByOpenId(openId);
    }

    @Override
    public CustomerExamTrack queryExamInfoByCustomerId(String customerId) {
        List<CustomerReg> regs = customerRegMapper.selectList(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getCustomerId, customerId).orderByDesc(CustomerReg::getExamNo));
        CustomerExamTrack examTrack = new CustomerExamTrack();
        examTrack.setCustomerId(customerId);
        if (CollectionUtils.isNotEmpty(regs)) {
            CustomerReg latestReg = regs.get(0);
            String status = latestReg.getStatus();
            examTrack.setExamNo(latestReg.getExamNo());
            examTrack.setStateDesc(status);
            Customer customer = customerMapper.selectById(customerId);
            examTrack.setName(Objects.nonNull(customer) ? customer.getName() : latestReg.getName());
            List<CustomerRegItemGroup> itemGroups = customerRegItemGroupMapper.listWithItemGroupByReg(latestReg.getId(), null, false);
            //查询项目检查数量
            Long uncheckedCount = itemGroups.stream().filter(i -> StringUtils.equals(i.getCheckStatus(), "未检")).count();
            examTrack.setUncheckedCount(uncheckedCount.intValue());
            examTrack.setCheckedCount(itemGroups.size() - uncheckedCount.intValue());
            //查询套餐
            List<String> suitIds = itemGroups.stream().map(CustomerRegItemGroup::getItemSuitId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(suitIds)) {
                List<ItemSuit> itemSuits = itemSuitMapper.selectBatchIds(suitIds);
                if (CollectionUtils.isNotEmpty(itemSuits)) {
                    List<String> suitNames = itemSuits.stream().filter(i -> !StringUtils.equalsAny(i.getSuitType(), "系统套餐", "加项", "组单")).map(i -> i.getName() + " " + i.getPrice().setScale(2, RoundingMode.HALF_UP) + "元")
//                            .collect(Collectors.joining())
                            .collect(Collectors.toList());
                    examTrack.setSuitName(CollectionUtils.isNotEmpty(suitNames) ? suitNames.get(0) : "");
                }
            }

            if (StringUtils.equals(status, "已登记")) {
                CustomerRegSummary summary = customerRegSummaryMapper.getByRegId(latestReg.getId());
                if (Objects.nonNull(summary) && StringUtils.equals(summary.getStatus(), "审核通过")) {
                    examTrack.setStateDesc("已出报告");
                    /*List<AdviceBean> summaryJson = summary.getSummaryJson();
                    examTrack.setAbnormalRetCount(summaryJson.size());*/
                    //计算大项异常个数
                    Long abnormalCount = itemGroups.stream().filter(g -> StringUtils.equals(g.getAbnormalFlag(), "1")).count();
                    examTrack.setAbnormalRetCount(abnormalCount.intValue());

                }
            } else {
                List<CustomerOrder> payedOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<CustomerOrder>().eq(CustomerOrder::getCustomerId, customerId)/*.eq(CustomerOrder::getStatus, ORDER_STATUS_PAID)*/.orderByDesc(CustomerOrder::getPayTime));
                examTrack.setBookTime(CollectionUtils.isNotEmpty(payedOrders) ? payedOrders.get(0).getBookTime() : null);
            }

            return examTrack;
        }
        return null;
    }

    @Override
    public CustomerExamTrack queryExamInfoByOpenId(String openId) {
        Customer defaultCustomer = getDefaultCustomerByOpenId(openId);
        if (Objects.nonNull(defaultCustomer)) {
            return queryExamInfoByCustomerId(defaultCustomer.getId());
        }
        return null;
    }

    @Override
    public CustomerAccount getAccountByOpenId(String openId) {
        LambdaQueryWrapper<CustomerAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerAccount::getOpenId, openId);
        queryWrapper.last("limit 1");
        return customerAccountMapper.selectOne(queryWrapper);
    }

    @Override
    public CustomerAccount creatOrBindAccountByPhone(String phone, String openId) throws Exception {
        CustomerAccount account = customerAccountMapper.selectOne(new LambdaQueryWrapper<CustomerAccount>().eq(CustomerAccount::getUserName, phone).last("limit 1"));
        if (Objects.isNull(account)) {
            account = new CustomerAccount();
            if (StringUtils.isNotBlank(openId)) {
                account.setOpenId(openId);
            }
            account.setUserName(phone);
            account.setCreateTime(new Date());
            customerAccountMapper.insert(account);

            autoBindCustomerByPhone(phone);
            return account;
        } else {
            account.setUserName(phone);
            if (StringUtils.isNotBlank(openId)) {
                account.setOpenId(openId);
            }
            customerAccountMapper.updateById(account);
            autoBindCustomerByPhone(phone);
            return account;
        }
    }

    @Override
    public void autoBindCustomerByPhone(String phone) throws Exception {
        CustomerAccount account = customerAccountMapper.selectOne(new LambdaQueryWrapper<CustomerAccount>().eq(CustomerAccount::getUserName, phone).last("limit 1"));
        if (!Objects.isNull(account)) {
            //根据手机号查询客户信息
            LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Customer::getPhone, phone);
            List<Customer> customers = customerMapper.selectList(queryWrapper);

            //建立关联
            if (CollectionUtils.isNotEmpty(customers)) {
                for (Customer customer : customers) {
                    Integer exsitCount = jdbcTemplate.queryForObject("select count(1) from customeraccount_customer where account_id = ? and customer_id = ?", Integer.class, account.getId(), customer.getId());

                    if (exsitCount == null || exsitCount == 0) {
                        CustomeraccountCustomer cac = new CustomeraccountCustomer();
                        cac.setAccountId(account.getId());
                        cac.setCustomerId(customer.getId());
                        cac.setOpenId(account.getOpenId());
                        cac.setDefaultState("0");
                        cac.setRelationType("其他");
                        customeraccountCustomerMapper.insert(cac);
                    }
                }
            }
        }
    }

    @Override
    public void autoBindCustomer(String accountId) {
        if (StringUtils.isNotBlank(accountId)) {
            //根据手机号查询客户信息
            CustomerAccount account = getById(accountId);
            if (Objects.nonNull(account)) {
                try {
                    autoBindCustomerByPhone(account.getUserName());
                } catch (Exception e) {
                    log.error("自动绑定客户信息失败！", e);
                }
            }
        }
    }

    @Override
    public List<CustomerAccountRelation> getCustomerAccountRelation(String accountId) {

        return customerAccountMapper.getCustomerAccountRelation(accountId);
    }

    @Override
    public Customer saveCustomerAccountRelation(CustomerAccountRelation customerAccountRelation) throws Exception {

        Customer customer = customerAccountRelation.getCustomer();
        if (Objects.isNull(customer)) {
            throw new RuntimeException("请求数据中没有档案信息！");
        }
        if (StringUtils.isBlank(customer.getIdCard())) {
            throw new RuntimeException("身份证号不能为空！");
        }
        if (StringUtils.isBlank(customerAccountRelation.getAccountId())) {
            throw new RuntimeException("请求数据中没有账号ID！");
        }

        String id = StringUtils.trimToNull(customerAccountRelation.getId());
        CustomeraccountCustomer cac = null;
        if (StringUtils.isNotBlank(id)) {
            cac = customeraccountCustomerMapper.selectById(id);
        }

        if(cac==null){
            //需要校验是否已经绑定了相同证件号的档案
            String accountId = customerAccountRelation.getAccountId();
            Integer existIdcardCount = jdbcTemplate.queryForObject("select count(1) from customeraccount_customer where account_id = ? and id_card = ?", Integer.class, accountId, customer.getIdCard());

            if (existIdcardCount != null && existIdcardCount > 0) {
                throw new Exception("账号已存在相同证件号的档案！");
            }
        }


        if (StringUtils.isBlank(customer.getId())) {
            customer.setArchivesNum(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_ARCHIVE_NO));
            customerMapper.insert(customer);
        } else {
            customerMapper.updateById(customer);
        }

        String defaultProfile = customerAccountRelation.getDefaultState();
        if (StringUtils.equals("1", defaultProfile)) {
            jdbcTemplate.update("update customeraccount_customer set default_state = '0' where account_id = ?", customerAccountRelation.getAccountId());
        }

        if (Objects.isNull(cac)) {
            cac = new CustomeraccountCustomer();
            cac.setAccountId(customerAccountRelation.getAccountId());
            cac.setCustomerId(customer.getId());
            cac.setOpenId(customerAccountRelation.getOpenId());
            cac.setDefaultState(defaultProfile);
            cac.setRelationType(customerAccountRelation.getRelationType());
            cac.setIdCard(customer.getIdCard());
            customeraccountCustomerMapper.insert(cac);
        } else {
            cac.setRelationType(customerAccountRelation.getRelationType());
            cac.setDefaultState(defaultProfile);
            cac.setIdCard(customer.getIdCard());

            customeraccountCustomerMapper.updateById(cac);
        }

        return customer;
    }

    @Override
    public CustomerAccountRelation getCustomerAccountRelationById(String id) {
        return customerAccountMapper.getCustomerAccountRelationById(id);
    }

    @Override
    public void setDefaultCustomer(String accountId, String customerId) throws Exception {
        jdbcTemplate.update("update customeraccount_customer set default_state = '0' where account_id = ?", accountId);
        jdbcTemplate.update("update customeraccount_customer set default_state = '1' where account_id = ? and customer_id = ?", accountId, customerId);
    }

}