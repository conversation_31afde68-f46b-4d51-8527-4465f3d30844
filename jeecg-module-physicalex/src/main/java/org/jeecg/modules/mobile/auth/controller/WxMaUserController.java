package org.jeecg.modules.mobile.auth.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.config.WxProperties;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.jeecg.modules.mobile.auth.service.WxApiClient;
import org.jeecg.modules.mobile.auth.service.WxApiService;
import org.jeecg.modules.mobile.reg.entity.CustomerAccount;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;
import org.jeecg.modules.mobile.utils.CryptUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 微信小程序用户接口
 *
 * <AUTHOR>
@RestController
@Slf4j
@Api(tags = "微信用户登录")
@RequestMapping("/wx/user")
public class WxMaUserController {

    @Autowired
    private WxProperties wxProperties;
    @Autowired
    private ICustomerAccountService customerAccountService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private WxApiClient wxApiClient;
    @Autowired
    private WxApiService wxApiService;
    @Autowired
    private ISmsRecordsService smsRecordsService;

    /**
     * 登录接口
     */
    @GetMapping("/login")
    @ApiOperation(value = "微信用户登录-微信用户登录", notes = "微信用户登录-微信用户登录")
    public Result<?> login(@RequestParam("code") String code, @RequestParam("phone") String phone, @RequestParam("nickname") String nickname, @RequestParam("iv") String iv) {
        if (StringUtils.isBlank(code)) {
            return Result.error("code 为空！");
        }

        try {

            String result = wxApiClient.getSessionInfo(wxProperties.getAppid(), wxProperties.getMpSeceret(), code, "authorization_code");
            JSONObject responseJSON = JSONObject.parseObject(result);
            String sessionKey = responseJSON.getString("session_key");
            String openId = responseJSON.getString("openid");
            log.info("sessionKey: {}, openid: {},encodePhone:{},iv:{}", sessionKey, openId, phone, iv);

            // 解密手机号信息
            String appid = wxProperties.getAppid();
            String decryptedData = CryptUtils.decrypt(appid, phone, sessionKey, iv);
            JSONObject phoneInfo = JSONObject.parseObject(decryptedData);
            String decodePhone = phoneInfo.getString("purePhoneNumber");

            CustomerAccount account = customerAccountService.getOne(new LambdaQueryWrapper<CustomerAccount>().eq(CustomerAccount::getOpenId, openId));


            if (Objects.isNull(account)) {
                account = new CustomerAccount();
                account.setOpenId(openId);
                account.setNickname(nickname);
                account.setUserName(decodePhone);
                account.setCreateTime(new Date());
                customerAccountService.save(account);
            }

            //List<Customer> customers = customerAccountService.getCustomersByAccountId(account.getId(), "本人");
            //Customer customer = CollectionUtils.isEmpty(customers) ? new Customer() : customers.get(0);
            //customer.setUserName(decodePhone);
            //customer.setOpenId(openId);
            //customer.setAccountId(account.getId());


            String token = JwtUtil.sign(CommonConstant.WX_USERNAME_PREFIX + decodePhone, CommonConstant.WX_DEFAULT_LOGIN_PASSWORD);
            String tokenKey = CommonConstant.PREFIX_WXUSER_TOKEN + token;
            redisUtil.set(tokenKey, token);
            redisUtil.expire(tokenKey, JwtUtil.EXPIRE_TIME * 2 / 1000);
            //customer.setToken(token);


            //兼容处理
            account.setToken(token);
            account.setAccountId(account.getId());

            return Result.OK(account);
        } catch (Exception e) {
            log.error("登录失败：", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "根据code获取账号信息", notes = "根据code获取账号信息")
    //根据code获取账号信息
    @GetMapping("/getAccountByWxpubCode")
    public Result<?> getAccountByWxpubCode(@RequestParam("code") String code) {
        if (StringUtils.isBlank(code)) {
            return Result.error("code 为空！");
        }

        try {
            String openId = wxApiService.getOpenIdByWxpubCode(code);
            CustomerAccount account = customerAccountService.getAccountByOpenId(openId);
            if (account != null && StringUtils.isNotBlank(account.getUserName())) {
                String token = JwtUtil.sign(CommonConstant.WX_USERNAME_PREFIX + account.getUserName(), CommonConstant.WX_DEFAULT_LOGIN_PASSWORD);
                String tokenKey = CommonConstant.PREFIX_WXUSER_TOKEN + token;
                redisUtil.set(tokenKey, token);
                redisUtil.expire(tokenKey, JwtUtil.EXPIRE_TIME * 2 / 1000);
                account.setToken(token);
                account.setAccountId(account.getId());
                return Result.OK(account);
            } else {
                return Result.error("请绑定手机号", openId);
            }
        } catch (Exception e) {
            log.error("登录失败：", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "发送短信验证码-发送短信验证码", notes = "发送短信验证码-发送短信验证码")
    //发送短信验证码
    @GetMapping("/sendSms")
    public Result<?> sendSms(@RequestParam("phone") String phone, @RequestParam("bizCode") String bizCode) {
        if (StringUtils.isBlank(phone)) {
            return Result.error("手机号为空！");
        }
        try {
            smsRecordsService.sendSmsCode(phone, bizCode);
            return Result.OK();
        } catch (Exception e) {
            log.error("发送短信失败：", e);
            return Result.error("发送短信失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "发送短信验证码-校验短信验证码", notes = "发送短信验证码-校验短信验证码")
    //验证短信验证码
    @GetMapping("/checkSms")
    public Result<?> checkSms(String phone, String smsCode, String bizCode) {
        if (StringUtils.isBlank(phone)) {
            return Result.error("手机号为空！");
        }
        if (StringUtils.isBlank(smsCode)) {
            return Result.error("验证码为空！");
        }
        try {
            boolean result = smsRecordsService.checkSmsCode(phone, smsCode, bizCode);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("验证短信失败：", e);
            return Result.error("验证短信失败: " + e.getMessage());
        }
    }


    @ApiOperation(value = "绑定手机号", notes = "绑定手机号")
    //绑定手机号
    @PostMapping("/bindPhone")
    public Result<?> bindPhone(@RequestBody JSONObject json) {
        String phone = StringUtils.trim(json.getString("phone"));
        String openId = json.getString("openId");
        String smsCode = StringUtils.trim(json.getString("smsCode"));

        if (StringUtils.isBlank(phone)) {
            return Result.error("手机号为空！");
        }
        if (StringUtils.isBlank(smsCode)) {
            return Result.error("验证码为空！");
        }

        try {
            smsRecordsService.checkSmsCode(phone, smsCode, "login");
        } catch (Exception e) {
            log.error("验证短信失败：", e);
            return Result.error("验证短信失败: " + e.getMessage());
        }


        try {
            CustomerAccount account = customerAccountService.creatOrBindAccountByPhone(phone, openId);
            String token = JwtUtil.sign(CommonConstant.WX_USERNAME_PREFIX + account.getUserName(), CommonConstant.WX_DEFAULT_LOGIN_PASSWORD);
            String tokenKey = CommonConstant.PREFIX_WXUSER_TOKEN + token;
            redisUtil.set(tokenKey, token);
            redisUtil.expire(tokenKey, JwtUtil.EXPIRE_TIME * 2000 / 1000);
            account.setToken(token);
            account.setAccountId(account.getId());
            return Result.OK(account);
        } catch (Exception e) {
            return Result.error("绑定手机号失败: " + e.getMessage());
        }
    }


    /**
     * 获取用户信息接口
     */
    @GetMapping("/info")
    public Result<?> info(@PathVariable String appid, @RequestParam("sessionKey") String sessionKey, @RequestParam("signature") String signature, @RequestParam("rawData") String rawData, @RequestParam("encryptedData") String encryptedData, @RequestParam("iv") String iv) {

        String decryptedData = CryptUtils.decrypt(appid, encryptedData, sessionKey, iv);
        JSONObject userInfo = JSONObject.parseObject(decryptedData);
        String decodePhone = userInfo.getString("phoneNumber");
        String decodeNickName = userInfo.getString("nickName");
        String decodeAvatarUrl = userInfo.getString("avatarUrl");

        return Result.ok(userInfo);
    }

    /**
     * 获取用户绑定手机号信息
     */
    @GetMapping("/phone")
    public Result<?> phone(@PathVariable String appid, @RequestParam("sessionKey") String sessionKey, @RequestParam("encryptedData") String encryptedData, @RequestParam("iv") String iv) {

        String decryptedData = CryptUtils.decrypt(appid, encryptedData, sessionKey, iv);
        JSONObject phoneInfo = JSONObject.parseObject(decryptedData);
        String decodePhone = phoneInfo.getString("purePhoneNumber");

        return Result.ok(decodePhone);
    }

    @GetMapping("/getWxConfig")
    public Result<?> getWxConfig(@RequestParam("url") String url) {
        try {
            // 去除 URL 的 # 后部分
            String decodedUrl = URLDecoder.decode(url, StandardCharsets.UTF_8);
            String cleanUrl = decodedUrl.split("#")[0];

             return Result.OK("生成签名成功",wxApiService.generateSignature(cleanUrl));
        } catch (Exception e) {
            log.error("生成签名失败", e);
            Map<String, String> error = new HashMap<>();
            error.put("error", "生成签名失败");
            return Result.error("生成签名失败", error);
        }
    }

}

