package org.jeecg.modules.mobile.health.service.impl;

import cn.hutool.core.date.DateUtil;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.mapper.CustomerRegItemResultMapper;
import org.jeecg.modules.mobile.health.service.HealthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HealServideImpl implements HealthService {

    @Autowired
    private CustomerRegItemResultMapper itemResultMapper;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;

    @Override
    public List<CustomerRegItemResult> getItemResult(String idCard) {

        List<CustomerRegItemResult> list = itemResultMapper.getByIdcard(idCard);
        list.forEach(item -> {
            item.setCheckYear(DateUtil.format(item.getCreateTime(), "yyyy-MM"));
        });
        //根据item_his_code分组,然后找到有异常小项的分组
        return list;
    }
}
