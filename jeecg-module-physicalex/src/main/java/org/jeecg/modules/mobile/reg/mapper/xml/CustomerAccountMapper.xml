<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.mobile.reg.mapper.CustomerAccountMapper">

    <select id="getCustomerAccountRelation" resultMap="customerAccountRelation">
        select * from customeraccount_customer cc join customer c on cc.customer_id = c.id where cc.account_id = #{accountId} order by cc.default_state desc,c.create_time desc
    </select>
    <select id="getCustomerAccountRelationById" resultMap="customerAccountRelation">
        select *,c.id_card as c_id_card from customeraccount_customer cc join customer c on cc.customer_id = c.id where cc.id = #{id}
    </select>

    <resultMap id="customerAccountRelation" type="org.jeecg.modules.mobile.bo.CustomerAccountRelation">
        <id property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="relationType" column="relation_type"/>
        <result property="defaultState" column="default_state"/>
        <association property="customer" javaType="org.jeecg.modules.reg.entity.Customer">
            <id property="id" column="customer_id"/>
            <result property="avatar" column="avatar"/>
            <result property="name" column="name"/>
            <result property="phone" column="phone"/>
            <result property="archivesNum" column="archives_num"/>
            <result property="idCard" column="c_id_card"/>
            <result property="gender" column="gender"/>
            <result property="nation" column="nation"/>
            <result property="age" column="age"/>
            <result property="ageUnit" column="age_unit"/>
            <result property="customerType" column="customer_type"/>
            <result property="duty" column="duty"/>
            <result property="birthday" column="birthday"/>
            <result property="marriageStatus" column="marriage_status"/>
            <result property="telPhone" column="tel_phone"/>
            <result property="height" column="height"/>
            <result property="weight" column="weight"/>
            <result property="address" column="address"/>
            <result property="postCode" column="post_code"/>
            <result property="email" column="email"/>
            <result property="secretLevel" column="secret_level"/>
            <result property="pwd" column="pwd"/>
            <result property="workId" column="work_id"/>
            <result property="postStateId" column="post_state_id"/>
            <result property="createTime" column="create_time"/>
            <result property="createBy" column="create_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="companyName" column="company_name"/>
            <result property="province" column="province"/>
            <result property="city" column="city"/>
            <result property="area" column="area"/>
            <result property="street" column="street"/>
            <result property="provinceCode" column="province_code"/>
            <result property="cityCode" column="city_code"/>
            <result property="areaCode" column="area_code"/>
            <result property="streetCode" column="street_code"/>
            <result property="addressDetail" column="address_detail"/>
            <result property="healthCardNo" column="health_card_no"/>
            <result property="nationCode" column="nation_code"/>
            <result property="marriageStatusCode" column="marriage_status_code"/>
            <result property="idCardType" column="id_card_type"/>
            <result property="hisPid" column="his_pid"/>
            <result property="icCardNo" column="ic_card_no"/>
            <result property="hisHealthNo" column="his_health_no"/>
            <result property="cardType" column="card_type"/>
            <result property="interfaceStatus" column="interface_status"/>
            <result property="country" column="country"/>
            <result property="countryCode" column="country_code"/>
            <result property="accountId" column="account_id"/>
            <result property="openId" column="open_id"/>
            <result property="accountCustomerId" column="account_customer_id"/>
            <result property="relationType" column="relation_type"/>
            <result property="token" column="token"/>
            <result property="userName" column="user_name"/>
            <result property="defaultProfile" column="default_profile"/>
            <result property="examNo" column="exam_no"/>
        </association>
    </resultMap>


</mapper>