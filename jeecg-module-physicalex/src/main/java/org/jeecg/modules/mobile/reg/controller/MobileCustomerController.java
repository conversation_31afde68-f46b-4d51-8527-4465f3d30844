package org.jeecg.modules.mobile.reg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.reg.bo.CustomerExamTrack;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.service.ICustomerService;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;
import org.jeecg.modules.mobile.reg.service.ICustomeraccountCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 档案表
 * @Author: jeecg-boot
 * @Date: 2024-06-14
 * @Version: V1.0
 */
@Api(tags = "档案表")
@RestController
@RequestMapping("/reg/customer")
@Slf4j
public class MobileCustomerController extends JeecgController<Customer, ICustomerService> {
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private ICustomerAccountService customerAccountService;
    @Autowired
    private ICustomeraccountCustomerService customeraccountCustomerService;


    /**
     * 微信端-根据customerId查询体检人的体检状态等信息
     *
     * @param customerId
     * @return
     */
    //@AutoLog(value = "档案表-根据customerId查询体检人的体检状态等信息")
    @ApiOperation(value = "档案表-根据customerId查询体检人的体检状态等信息", notes = "档案表-根据customerId查询体检人的体检状态等信息")
    @GetMapping(value = "/queryExamInfoByCustomerId")
    public Result<CustomerExamTrack> queryExamInfoByCustomerId(@RequestParam(name = "customerId", required = true) String customerId) {
        CustomerExamTrack customerExamTrack = customerAccountService.queryExamInfoByCustomerId(customerId);
        if (customerExamTrack == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerExamTrack);
    }

    /**
     * 微信端-根据openId查询体检人的体检状态等信息
     *
     * @param openId
     * @return
     */
    //@AutoLog(value = "档案表-根据customerId查询体检人的体检状态等信息")
    @ApiOperation(value = "档案表-根据customerId查询体检人的体检状态等信息", notes = "档案表-根据customerId查询体检人的体检状态等信息")
    @GetMapping(value = "/queryExamInfoByOpenId")
    public Result<CustomerExamTrack> queryExamInfoByOpenId(@RequestParam(name = "openId", required = true) String openId) {
        CustomerExamTrack customerExamTrack = customerAccountService.queryExamInfoByOpenId(openId);
        if (customerExamTrack == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerExamTrack);
    }


    @ApiOperation(value = "档案表-通过身份证号查询", notes = "档案表-通过身份证号查询")
    @GetMapping(value = "/queryByIdCard")
    public Result<Customer> queryByIdCard(@RequestParam(name = "idCard", required = true) String idCard) {

        Customer customer = customerService.getOne(new LambdaQueryWrapper<Customer>().eq(Customer::getIdCard, idCard).last("limit 1"));
        if (customer == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customer);
    }

    @ApiOperation(value = "档案表-通过id获取档案", notes = "档案表-通过id获取档案")
    @GetMapping(value = "/queryCustomerById")
    public Result<Customer> queryCustomerById(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "accountId", required = true) String accountId) {

        Customer customer = customerService.getById(id);
        if (customer == null) {
            return Result.error("未找到对应数据");
        }
        String relationType = customeraccountCustomerService.getRelation(id, accountId);
        customer.setRelationType(relationType);
        return Result.OK(customer);
    }

}
