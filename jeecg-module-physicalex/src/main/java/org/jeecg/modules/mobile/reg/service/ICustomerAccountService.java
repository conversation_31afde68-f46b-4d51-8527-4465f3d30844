package org.jeecg.modules.mobile.reg.service;

import org.jeecg.modules.reg.bo.CustomerExamTrack;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.mobile.bo.CustomerAccountRelation;
import org.jeecg.modules.mobile.reg.entity.CustomerAccount;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 体检人员账户表
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
public interface ICustomerAccountService extends IService<CustomerAccount> {

    Customer saveAccount(Customer customer) throws Exception;

    Customer updateAccount(Customer customer);

    List<Customer> getCustomersByAccountId(String accountId, String relationType);

    Customer getCustomersByOpenIdAndIdCard(String openId, String relationType, String idCard);

    Customer getDefaultCustomerByAccountId(String accountId);

    Customer getDefaultCustomerByOpenId(String openId);

    CustomerExamTrack queryExamInfoByCustomerId(String customerId);

    CustomerExamTrack queryExamInfoByOpenId(String openId);

    CustomerAccount getAccountByOpenId(String openId);

    CustomerAccount creatOrBindAccountByPhone(String phone, String openId) throws Exception;

    void autoBindCustomerByPhone(String phone) throws Exception;

    void autoBindCustomer(String accountId);

    List<CustomerAccountRelation> getCustomerAccountRelation(String accountId);

    Customer saveCustomerAccountRelation(CustomerAccountRelation customerAccountRelation) throws Exception;

    CustomerAccountRelation getCustomerAccountRelationById(String id);

    void setDefaultCustomer(String accountId, String customerId) throws Exception;

}
