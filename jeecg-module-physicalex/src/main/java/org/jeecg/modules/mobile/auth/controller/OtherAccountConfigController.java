package org.jeecg.modules.mobile.auth.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信小程序用户接口
 *
 * <AUTHOR>
@RestController
@Slf4j
@Api(tags = "微信用户登录")
@RequestMapping("/wx/user")
public class OtherAccountConfigController {

    @Autowired
    private ISysSettingService sysSettingService;

    //getOtherAccountConfig
    @ApiOperation(value = "获取第三方账号配置")
    @GetMapping(value = "/getOtherAccountConfig")
    public Result<?> getOtherAccountConfig() {
        JSONObject result = new JSONObject();
        JSONObject wxConfig = new JSONObject();
        wxConfig.put("appid", sysSettingService.getValueByCode("appid"));
        wxConfig.put("mchId", sysSettingService.getValueByCode("mchId"));
        wxConfig.put("authConfigStatus", sysSettingService.getValueByCode("authConfigStatus"));

        result.put("wxConfig", wxConfig);


        return Result.OK(wxConfig);
    }


}

