package org.jeecg.modules.mobile.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class FileUrlUtilsTest {
    // 验证核心需求场景
    @Test
    void testCoreRequirement() {
        String result = FileUrlUtils.replaceUrl(
                "http://192.168.130.18:9000/images/1.jpg",
                "https://wlcb.natapp4.cc/minio"
        );
        assertEquals("https://wlcb.natapp4.cc/minio/images/1.jpg", result);
    }

    // 验证路径合并逻辑
    @Test
    void testPathMerging() {
        // Case 1: 替换前缀带路径，原始URL带路径
        assertEquals(
                "https://target.com/base/path/child",
                FileUrlUtils.replaceUrl(
                        "http://old.com/path/child",
                        "https://target.com/base"
                )
        );

        // Case 2: 替换前缀路径带斜杠结尾
        assertEquals(
                "https://target.com/merged/images/1.jpg",
                FileUrlUtils.replaceUrl(
                        "http://old.com/images/1.jpg",
                        "https://target.com/merged/"
                )
        );

        // Case 3: 原始URL无路径
        assertEquals(
                "https://target.com/minio",
                FileUrlUtils.replaceUrl(
                        "http://old.com",
                        "https://target.com/minio"
                )
        );
    }

    // 验证端口和协议处理
    @Test
    void testPortAndScheme() {
        // 替换前缀带非常规端口
        assertEquals(
                "https://wlcb.natapp4.cc:8443/minio/data",
                FileUrlUtils.replaceUrl(
                        "http://192.168.1.1:9000/data",
                        "https://wlcb.natapp4.cc:8443/minio"
                )
        );
    }

    // 验证空路径边界条件
    @Test
    void testEdgeCases() {
        // 原始URL只有根路径
        assertEquals(
                "https://target.com/minio/",
                FileUrlUtils.replaceUrl(
                        "http://old.com/",
                        "https://target.com/minio"
                )
        );

        // 替换前缀和原始URL都无路径
        assertEquals(
                "https://target.com",
                FileUrlUtils.replaceUrl(
                        "http://old.com",
                        "https://target.com"
                )
        );
    }

    // 验证特殊字符编码
    @Test
    void testSpecialCharacters() {
        assertEquals(
                "https://target.com/minio/%E4%B8%AD%E6%96%87.jpg",
                FileUrlUtils.replaceUrl(
                        "http://old.com/中文.jpg",
                        "https://target.com/minio"
                )
        );
    }
}