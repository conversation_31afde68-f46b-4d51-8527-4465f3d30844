package org.jeecg.modules.mobile.card.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;


import org.jeecg.modules.fee.entity.CardGoods;
import org.jeecg.modules.fee.entity.CardOrder;
import org.jeecg.modules.fee.service.ICardGoodsService;
import org.jeecg.modules.fee.service.ICardOrderService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: card_goods
 * @Author: jeecg-boot
 * @Date:   2024-10-29
 * @Version: V1.0
 */
@Api(tags="card_goods")
@RestController(value = "wxCardGoodsController")
@RequestMapping("/card/cardGoods")
@Slf4j
public class CardGoodsController extends JeecgController<CardGoods, ICardGoodsService> {
	@Autowired
	private ICardGoodsService cardGoodsService;
	 @Autowired
	 private ICardOrderService cardOrderService;
	

	 @ApiOperation(value="体检卡商品-在售体检卡分页列表查询", notes="体检卡商品-在售体检卡分页列表查询")
	 @GetMapping(value = "/onSaleList")
	 public Result<IPage<CardGoods>> queryOnSaleList(CardGoods cardGoods,
																				  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
																				  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
																				  HttpServletRequest req) {
		 QueryWrapper<CardGoods> queryWrapper = QueryGenerator.initQueryWrapper(cardGoods, req.getParameterMap());
		 queryWrapper.eq("enable_flag","1").eq("sale_online_flag","1");
		 Page<CardGoods> page = new Page<CardGoods>(pageNo, pageSize);
		 IPage<CardGoods> pageList = cardGoodsService.page(page, queryWrapper);
		 List<CardGoods> goodsList = pageList.getRecords();
		 if (CollectionUtils.isNotEmpty(goodsList)){
			 List<String> goodIds = goodsList.stream().map(CardGoods::getId).collect(Collectors.toList());
			 List<CardOrder> orders = cardOrderService.list(new LambdaQueryWrapper<CardOrder>().in(CardOrder::getGoodsId, goodIds));
			 if (CollectionUtils.isNotEmpty(orders)){
				 Map<String, List<CardOrder>>	orderMap = orders.stream().collect(Collectors.groupingBy(CardOrder::getGoodsId));
				 pageList.getRecords().forEach(goods -> {
					 List<CardOrder> cardOrders = orderMap.get(goods.getId());
					 goods.setBuyerCount(CollectionUtils.isNotEmpty(cardOrders)?cardOrders.size():0);
				 });
			 }
		 }

		 return Result.OK(pageList);
	 }


}
