package org.jeecg.modules.mobile.auth.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.mobile.reg.entity.CustomerAccount;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;
import org.jeecg.modules.msb.bo.MsbUserInfo;
import org.jeecg.modules.msb.bo.MsbUserInfoResponse;
import org.jeecg.modules.msb.service.MsbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信小程序用户接口
 *
 * <AUTHOR>
@RestController
@Slf4j
@Api(tags = "微信用户登录")
@RequestMapping("/wx/user")
public class MsbUserController {

    @Autowired
    private MsbService msbService;
    @Autowired
    private ICustomerAccountService customerAccountService;
    @Autowired
    private RedisUtil redisUtil;


    @ApiOperation(value = "通过手机号登录或创建账号", notes = "通过手机号登录或创建账号")
    //绑定手机号
    @GetMapping("/bindMsb")
    public Result<?> bindPhone(String code) {
        if (StringUtils.isBlank(code)) {
            return Result.error("code 为空！");
        }
        String msbToken = null;
        try {
            msbToken = msbService.getToken(code);
            MsbUserInfo msbUserInfo = msbService.getUserInfo(msbToken);
            String phone = msbUserInfo.getMobilePhone();
            if (StringUtils.isBlank(phone)) {
                return Result.error("手机号为空！");
            }

            CustomerAccount account = customerAccountService.creatOrBindAccountByPhone(phone, null);
            String token = JwtUtil.sign(CommonConstant.WX_USERNAME_PREFIX + account.getUserName(), CommonConstant.WX_DEFAULT_LOGIN_PASSWORD);
            String tokenKey = CommonConstant.PREFIX_WXUSER_TOKEN + token;
            redisUtil.set(tokenKey, token);
            redisUtil.expire(tokenKey, JwtUtil.EXPIRE_TIME * 2000 / 1000);
            account.setToken(token);
            account.setAccountId(account.getId());
            return Result.OK(account);
        } catch (Exception e) {
            log.error("获取token失败：", e);
            return Result.error("获取token失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "通过手机号登录或创建账号", notes = "通过手机号登录或创建账号")
    //绑定手机号
    @GetMapping("/bindMsbByPhone")
    public Result<?> bindMsbByPhone(String phone) {
        try {
            if (StringUtils.isBlank(phone)) {
                return Result.error("手机号为空！");
            }

            CustomerAccount account = customerAccountService.creatOrBindAccountByPhone(phone, null);
            String token = JwtUtil.sign(CommonConstant.WX_USERNAME_PREFIX + account.getUserName(), CommonConstant.WX_DEFAULT_LOGIN_PASSWORD);
            String tokenKey = CommonConstant.PREFIX_WXUSER_TOKEN + token;
            redisUtil.set(tokenKey, token);
            redisUtil.expire(tokenKey, JwtUtil.EXPIRE_TIME * 200000 / 1000);
            account.setToken(token);
            account.setAccountId(account.getId());
            return Result.OK(account);
        } catch (Exception e) {
            log.error("获取token失败：", e);
            return Result.error("获取token失败: " + e.getMessage());
        }
    }

}

