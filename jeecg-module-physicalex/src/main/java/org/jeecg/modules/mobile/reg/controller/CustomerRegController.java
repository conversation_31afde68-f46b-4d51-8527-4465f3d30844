package org.jeecg.modules.mobile.reg.controller;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecg.modules.reg.dto.DependentItemResultDTO;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Api(tags = "客户登记")
@RestController(value = "wxCustomerRegController")
@RequestMapping("/reg/customerReg")
@Slf4j
public class CustomerRegController extends JeecgController<CustomerReg, ICustomerRegService> {
    @Autowired
    private ICustomerRegService customerRegService;

    @Autowired
    private IItemGroupRelationService itemGroupRelationService;

    @Autowired
    private ICustomerRegItemResultService customerRegItemResultService;


    @ApiOperation(value = "微信端-获取导引单数据", notes = "微信端-获取导引单数据")
    @GetMapping(value = "/getElecGuidanceSheet")
    public Result<Map<String, List<CustomerRegItemGroup>>> getElecGuidanceSheet(@RequestParam(name = "id", required = true) String id) {
        Map<String, List<CustomerRegItemGroup>> elecGuidanceSheet = customerRegService.getElecGuidanceSheet(id);
        return Result.OK(elecGuidanceSheet);
    }


    /**
     * 添加组合
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-添加组合")
    @ApiOperation(value = "客户登记-添加组合", notes = "客户登记-添加组合")
    @PostMapping(value = "/addItemGroup4SmallApp")
    public Result<?> addItemGroup4SmallApp(@RequestBody JSONArray info) {
        List<CustomerRegItemGroup> list = info.toJavaList(CustomerRegItemGroup.class);
        try {
            customerRegService.addItemGroup4SmallApp(list);
            return Result.OK("添加成功!", list);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    /**
     * 微信端-获取团检套餐
     *
     * @param idCard
     * @return
     */
    @AutoLog(value = "微信端-获取团检套餐")
    @ApiOperation(value = "微信端-获取团检套餐", notes = "微信端-获取团检套餐")
    @GetMapping(value = "/getCompanyItemGroups")
    public Result<?> getCompanyItemGroups(String idCard) {

        try {
            CompanyReg companyReg = customerRegService.getCompanyItemGroups(idCard);
            return Result.OK(companyReg);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }




}
