package org.jeecg.modules.mobile.auth.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.mobile.bo.ShortLinkRequest;

import java.io.IOException;
import java.util.Map;

public interface WxApiService {
    public JSONObject getSessionInfo(String jsCode, String grantType) throws Exception;

    public String getAccessToken() throws Exception;

    public String getShortUrl(ShortLinkRequest param, String accessToken) throws Exception;

    String getOpenIdByWxpubCode(String code) throws Exception;

    String getJsapiTicket() throws Exception;

    String getAccessToken4JsSdk() throws Exception;

    Map<String, String> generateSignature(String url) throws Exception;

}
