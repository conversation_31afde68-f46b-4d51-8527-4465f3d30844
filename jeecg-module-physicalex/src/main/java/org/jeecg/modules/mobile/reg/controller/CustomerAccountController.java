package org.jeecg.modules.mobile.reg.controller;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.mobile.bo.CustomerAccountRelation;
import org.jeecg.modules.mobile.reg.entity.CustomerAccount;
import org.jeecg.modules.mobile.reg.entity.CustomeraccountCustomer;
import org.jeecg.modules.reg.mapper.CustomerMapper;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.mobile.reg.service.ICustomeraccountCustomerService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 体检人员账户表
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
@Api(tags = "体检人员账户表")
@RestController
@RequestMapping("/reg/customerAccount")
@Slf4j
public class CustomerAccountController extends JeecgController<CustomerAccount, ICustomerAccountService> {
    @Autowired
    private ICustomerAccountService customerAccountService;
    @Autowired
    private ICustomeraccountCustomerService customeraccountCustomerService;
    @Autowired
    private SequenceGenerator sequenceGenerator;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CustomerMapper customerMapper;

    /**
     * 绑定账号和档案
     *
     * @param accountId
     * @param customerId
     * @return
     */
    @ApiOperation(value = "绑定账号和档案", notes = "绑定账号和档案")
    @GetMapping(value = "/bindAccountAndCustomer")
    public Result<String> bindAccountAndCustomer(@RequestParam(name = "accountId") String accountId, @RequestParam(name = "customerId") String customerId) {
        try {

            customeraccountCustomerService.bindAccountAndCustomer(accountId, customerId);
            return Result.OK("绑定成功！");
        } catch (Exception e) {
            return Result.error("绑定失败！");
        }
    }


    /**
     * 分页列表查询
     *
     * @param customerAccount
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检人员账户表-分页列表查询")
    @ApiOperation(value = "体检人员账户表-分页列表查询", notes = "体检人员账户表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerAccount>> queryPageList(CustomerAccount customerAccount, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CustomerAccount> queryWrapper = QueryGenerator.initQueryWrapper(customerAccount, req.getParameterMap());
        Page<CustomerAccount> page = new Page<CustomerAccount>(pageNo, pageSize);
        IPage<CustomerAccount> pageList = customerAccountService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param customer
     * @return
     */
    @AutoLog(value = "体检人员账户表-添加")
    @ApiOperation(value = "体检人员账户表-添加", notes = "体检人员账户表-添加")
//	@RequiresPermissions("reg:customer_account:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Customer customer) {
        try {
            Customer savCustomer = customerAccountService.saveAccount(customer);
            return Result.OK(savCustomer);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("添加失败！" + e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param customer
     * @return
     */
    @AutoLog(value = "体检人员账户表-编辑")
    @ApiOperation(value = "体检人员账户表-编辑", notes = "体检人员账户表-编辑")
//	@RequiresPermissions("reg:customer_account:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody Customer customer) {
        try {
            Customer updateCustomer = customerAccountService.updateAccount(customer);
            return Result.OK(updateCustomer);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("编辑失败！" + e.getMessage());
        }
    }

    /**
     * 账户关联用户档案
     *
     * @param customeraccountCustomerList
     * @return
     */
    @AutoLog(value = "体检人员账户表-账户关联用户档案")
    @ApiOperation(value = "体检人员账户表-账户关联用户档案", notes = "体检人员账户表-账户关联用户档案")
//	 @RequiresPermissions("reg:customer_account:add")
    @RequestMapping(value = "/relationCustomer", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> relationCustomer(@RequestBody List<CustomeraccountCustomer> customeraccountCustomerList) {
        try {
            Validate.notEmpty(customeraccountCustomerList, "关联信息不能为空！");
            String accountId = customeraccountCustomerList.get(0).getAccountId();
            //删除原有关联关系
            customeraccountCustomerService.remove(new LambdaQueryWrapper<CustomeraccountCustomer>().eq(CustomeraccountCustomer::getAccountId, accountId));
            customeraccountCustomerService.saveBatch(customeraccountCustomerList);
            return Result.OK("编辑成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("编辑失败！");
        }
    }

    /**
     * 通过id删除
     *
     * @param accountCustomerId
     * @return
     */
    @AutoLog(value = "体检人员账户表-通过id删除")
    @ApiOperation(value = "体检人员账户表-通过id删除", notes = "体检人员账户表-通过id删除")
//	@RequiresPermissions("reg:customer_account:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "accountCustomerId", required = true) String accountCustomerId) {
        CustomeraccountCustomer accountCustomer = customeraccountCustomerService.getById(accountCustomerId);
        //删除账户档案关联表
        if (Objects.nonNull(accountCustomer)) {
            customeraccountCustomerService.removeById(accountCustomerId);
            //删除档案表
            customerAccountService.removeById(accountCustomer.getCustomerId());
        }
        return Result.OK("删除成功!");
    }

    //deleteCustomerAccountRelation
    @ApiOperation(value = "体检人员账户表-删除关联关系", notes = "体检人员账户表-删除关联关系")
    @DeleteMapping(value = "/deleteCustomerAccountRelation")
    public Result<String> deleteCustomerAccountRelation(@RequestParam(name = "id", required = true) String id) {
        try {
            customeraccountCustomerService.removeById(id);
            return Result.OK("删除成功!");
        } catch (Exception e) {
            return Result.error("删除失败！");
        }
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检人员账户表-批量删除")
    @ApiOperation(value = "体检人员账户表-批量删除", notes = "体检人员账户表-批量删除")
    @RequiresPermissions("reg:customer_account:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerAccountService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过账户id查询关联的本人用户档案
     *
     * @param accountId
     * @return
     */
    //@AutoLog(value = "体检人员账户表-通过id查询")
    @ApiOperation(value = "体检人员账户表-通过账户id查询关联的本人用户档案", notes = "体检人员账户表-通过账户id查询关联的本人用户档案")
    @GetMapping(value = "/queryCustomerByAccountId")
    public Result<Customer> queryCustomerByAccountId(@RequestParam(name = "accountId") String accountId) {
        try {
            List<Customer> customers = customerAccountService.getCustomersByAccountId(accountId, "本人");
            if (CollectionUtils.isNotEmpty(customers)) {
                return Result.OK(customers.get(0));
            }
            return Result.error("查询失败！");
        } catch (Exception e) {
            return Result.error("查询失败！");
        }
    }

    @ApiOperation(value = "体检人员账户表-微信端通过openId查询默认用户档案", notes = "体检人员账户表-微信端通过openId查询默认用户档案")
    @GetMapping(value = "/queryCustomerByOpenId")
    public Result<Customer> queryCustomerByOpenId(@RequestParam(name = "openId") String openId) {
        try {
            Customer customer = customerAccountService.getDefaultCustomerByOpenId(openId);
            if (Objects.nonNull(customer)) {
                customer.setUserName(customer.getPhone());
                return Result.OK(customer);
            }
        } catch (Exception e) {
            return Result.error("查询失败！");
        }
        return Result.error("查询失败！");
    }

    @ApiOperation(value = "体检人员账户表-微信端通过customerId查询默认用户档案", notes = "体检人员账户表-微信端通过customerId查询默认用户档案")
    @GetMapping(value = "/queryDefaultCustomerByAccountId")
    public Result<Customer> queryDefaultCustomerByAccountId(@RequestParam(name = "accountId") String accountId) {

        Customer customer = customerAccountService.getDefaultCustomerByAccountId(accountId);

        if (Objects.nonNull(customer)) {
            customer.setUserName(customer.getPhone());
            return Result.OK(customer);
        } else {
            return Result.error("没有默认档案！");
        }
    }


    /**
     * 通过微信openId查询关联的用户档案
     *
     * @param openId
     * @return
     */
    //@AutoLog(value = "体检人员账户表-通过微信openId查询关联的用户档案")
    @ApiOperation(value = "体检人员账户表-通过微信openId查询关联的用户档案", notes = "体检人员账户表-通过微信openId查询关联的用户档案")
    @GetMapping(value = "/queryRelationCustomersByOpenId")
    public Result<List<Customer>> queryRelationCustomersByOpenId(@RequestParam(name = "openId") String openId, @RequestParam(name = "relationType") String relationType) {
        try {
            CustomerAccount account = customerAccountService.getOne(new LambdaQueryWrapper<CustomerAccount>().eq(CustomerAccount::getOpenId, openId));
            if (Objects.isNull(account)) {
                return Result.error("用户尚未注册，请注册后操作！");
            }
            List<Customer> customers = customerAccountService.getCustomersByAccountId(account.getId(), relationType);

            return Result.OK(customers);

        } catch (Exception e) {
            return Result.error("查询失败！");
        }
    }

    //通过accountId获取档案列表
    @ApiOperation(value = "体检人员账户表-通过accountId获取档案列表", notes = "体检人员账户表-通过accountId获取档案列表")
    @GetMapping(value = "/queryCustomersByAccountId")
    public Result<List<Customer>> queryCustomersByAccountId(@RequestParam(name = "accountId") String accountId) {
        try {
            List<Customer> customers = customerAccountService.getCustomersByAccountId(accountId, null);
            return Result.OK(customers);
        } catch (Exception e) {
            return Result.error("查询失败！");
        }
    }

    //通过accountId获取档案列表
    @ApiOperation(value = "体检人员账户表-通过accountId获取档案列表", notes = "体检人员账户表-通过accountId获取档案列表")
    @GetMapping(value = "/getCustomersAccountRelation")
    public Result<?> getCustomersAccountRelation(@RequestParam(name = "accountId") String accountId) {
        try {
            List<CustomerAccountRelation> customers = customerAccountService.getCustomerAccountRelation(accountId);
            return Result.OK(customers);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("查询失败！");
        }
    }


    @ApiOperation(value = "体检人员账户表-保存档案和关联关系", notes = "体检人员账户表-保存档案和关联关系")
    @PostMapping(value = "/saveCustomerAccountRelation")
    public Result<?> saveCustomerAccountRelation(@RequestBody CustomerAccountRelation customerAccountRelation) {
        try {
            Customer customer = customerAccountService.saveCustomerAccountRelation(customerAccountRelation);
            return Result.OK("保存成功！",customer);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    //setDefaultCustomer
    @ApiOperation(value = "体检人员账户表-设置默认档案", notes = "体检人员账户表-设置默认档案")
    @GetMapping(value = "/setDefaultCustomer")
    public Result<?> setDefaultCustomer(@RequestParam(name = "accountId") String accountId, @RequestParam(name = "customerId") String customerId) {
        try {
            customerAccountService.setDefaultCustomer(accountId, customerId);
            return Result.OK("设置成功！");
        } catch (Exception e) {
            return Result.error("设置失败！");
        }
    }

    //getCustomerAccountRelationById
    @ApiOperation(value = "体检人员账户表-通过id查询", notes = "体检人员账户表-通过id查询")
    @GetMapping(value = "/getCustomerAccountRelationById")
    public Result<?> getCustomerAccountRelationById(@RequestParam(name = "id") String id) {
        try {
            CustomerAccountRelation customerAccountRelation = customerAccountService.getCustomerAccountRelationById(id);
            return Result.OK(customerAccountRelation);
        } catch (Exception e) {
            return Result.error("查询失败！");
        }
    }


    /**
     * 切换亲友
     *
     * @param openId
     * @return
     */
    @ApiOperation(value = "体检人员账户表-切换亲友", notes = "体检人员账户表-切换亲友")
    @GetMapping(value = "/changeRelationCustomer")
    public Result<Customer> changeRelationCustomer(@RequestParam(name = "openId") String openId, @RequestParam(name = "customerId") String customerId) {
        try {
            //查询切换用户信息
            Customer customer = customerAccountService.getDefaultCustomerByOpenId(openId);
            Validate.notNull(customer, "未查询到相关亲友信息！");
            //变更用户状态
            List<CustomeraccountCustomer> customeraccountCustomers = customeraccountCustomerService.list(new LambdaQueryWrapper<CustomeraccountCustomer>().eq(CustomeraccountCustomer::getOpenId, openId));
            if (CollectionUtils.isNotEmpty(customeraccountCustomers)) {
                customeraccountCustomers.forEach(customeraccountCustomer -> {
                    if (StringUtils.equals(customeraccountCustomer.getCustomerId(), customerId)) {
                        customeraccountCustomer.setDefaultState("1");
                    } else {
                        customeraccountCustomer.setDefaultState("0");
                    }
                });
            }
            customeraccountCustomerService.saveOrUpdateBatch(customeraccountCustomers);
            return Result.OK(customer);

        } catch (Exception e) {
            return Result.error("查询失败！");
        }
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检人员账户表-通过id查询")
    @ApiOperation(value = "体检人员账户表-通过id查询", notes = "体检人员账户表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerAccount> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerAccount customerAccount = customerAccountService.getById(id);
        if (customerAccount == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerAccount);
    }


    /**
     * 导出excel
     *
     * @param request
     * @param customerAccount
     */
    @RequiresPermissions("reg:customer_account:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerAccount customerAccount) {
        return super.exportXls(request, customerAccount, CustomerAccount.class, "体检人员账户表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("reg:customer_account:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerAccount.class);
    }

}
