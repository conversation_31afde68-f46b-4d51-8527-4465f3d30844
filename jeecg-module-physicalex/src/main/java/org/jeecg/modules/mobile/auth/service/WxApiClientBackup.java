package org.jeecg.modules.mobile.auth.service;

import com.alibaba.fastjson.JSONObject;
import feign.Headers;
import org.jeecg.config.ProxyFeignConfig;
import org.jeecg.modules.mobile.bo.ShortLinkRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;

@FeignClient(name = "proxiedClient", url = "https://api.weixin.qq.com", configuration = ProxyFeignConfig.class)
public interface WxApiClientBackup {

    @GetMapping("/sns/jscode2session")
    String getSessionInfo(@RequestParam("appid") String appid, @RequestParam("secret") String secret, @RequestParam("js_code") String jsCode, @RequestParam("grant_type") String grantType) throws IOException;

    @GetMapping("/cgi-bin/token")
    String getAccessToken(@RequestParam("appid") String appid, @RequestParam("secret") String secret, @RequestParam("grant_type") String grantType) throws IOException;

    @PostMapping("/wxa/genwxashortlink")
    @Headers("Content-Type: application/json")
    JSONObject getShortUrl(@RequestBody ShortLinkRequest param, @RequestParam("access_token") String accessToken) throws IOException;

    @PostMapping("/cgi-bin/message/subscribe/send")
    JSONObject sendSubscribeMessage(@RequestBody JSONObject param, @RequestParam("access_token") String accessToken) throws IOException;
}
