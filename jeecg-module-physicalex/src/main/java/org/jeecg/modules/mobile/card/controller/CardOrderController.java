package org.jeecg.modules.mobile.card.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;

import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.fee.entity.CardOrder;
import org.jeecg.modules.fee.service.ICardOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: card_order
 * @Author: jeecg-boot
 * @Date:   2024-10-29
 * @Version: V1.0
 */
@Api(tags="card_order")
@RestController(value = "wxCardOrderController")
@RequestMapping("/card/cardOrder")
@Slf4j
public class CardOrderController extends JeecgController<CardOrder, ICardOrderService> {
	 @Autowired
	private ICardOrderService cardOrderService;
	

	
	/**
	 *   添加
	 *
	 * @param cardOrder
	 * @return
	 */
	@AutoLog(value = "card_order-添加")
	@ApiOperation(value="card_order-添加", notes="card_order-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CardOrder cardOrder) {

		CardOrder order = cardOrderService.saveCardOrder4SmallApp(cardOrder);
		return Result.OK("添加成功！",order);
	}
	
	/**
	 *  编辑
	 *
	 * @param cardOrder
	 * @return
	 */
	@AutoLog(value = "card_order-编辑")
	@ApiOperation(value="card_order-编辑", notes="card_order-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CardOrder cardOrder) {
		cardOrderService.updateById(cardOrder);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "card_order-通过id删除")
	@ApiOperation(value="card_order-通过id删除", notes="card_order-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cardOrderService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "card_order-批量删除")
	@ApiOperation(value="card_order-批量删除", notes="card_order-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cardOrderService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "card_order-通过id查询")
	@ApiOperation(value="card_order-通过id查询", notes="card_order-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CardOrder> queryById(@RequestParam(name="id",required=true) String id) {
		CardOrder cardOrder = cardOrderService.getById(id);
		if(cardOrder==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cardOrder);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cardOrder
    */
    @RequiresPermissions("wx.card:card_order:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CardOrder cardOrder) {
        return super.exportXls(request, cardOrder, CardOrder.class, "card_order");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("wx.card:card_order:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CardOrder.class);
    }

}
