package org.jeecg.modules.mobile.index.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.basicinfo.service.ISuitCategoryService;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Api(tags = "客户登记")
@RestController
@RequestMapping("/reg/index")
@Slf4j
public class IndexController extends JeecgController<CustomerReg, ICustomerRegService> {
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private ISuitCategoryService suitCategoryService;
    @Autowired
    private ICustomerAccountService customerAccountService;

    @ApiOperation(value = "微信端-首页数据", notes = "微信端-首页数据")
    @GetMapping(value = "/data")
    public Result<?> data(String accountId, String years) {
        JSONObject result = new JSONObject();
        //获取套餐分类
        //List<SuitCategory> suitCategoryList = suitCategoryService.list();
        //result.put("suitCategoryList", suitCategoryList);
        //获取默认档案
        customerAccountService.autoBindCustomer(accountId);
        Customer customer = customerAccountService.getDefaultCustomerByAccountId(accountId);
        if (customer != null) {
            result.put("customer", customer);
            years = years == null ? "1" : years;
            Integer year = null;
            try {
                year = Integer.parseInt(years);
            } catch (Exception e) {
                year = 1;
            }
            Map<String, Integer> customerStat = customerRegService.statByCustomer(customer.getId(), year);
            result.put("customerStat", customerStat);
        }
        return Result.OK(result);
    }



}
