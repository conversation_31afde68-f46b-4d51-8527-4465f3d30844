package org.jeecg.modules.mobile.reg.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.mobile.bo.CustomerAccountRelation;
import org.jeecg.modules.mobile.reg.entity.CustomerAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * @Description: 体检人员账户表
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
public interface CustomerAccountMapper extends BaseMapper<CustomerAccount> {


    List<CustomerAccountRelation> getCustomerAccountRelation(@Param("accountId") String accountId);

    CustomerAccountRelation getCustomerAccountRelationById(@Param("id") String id);
}
