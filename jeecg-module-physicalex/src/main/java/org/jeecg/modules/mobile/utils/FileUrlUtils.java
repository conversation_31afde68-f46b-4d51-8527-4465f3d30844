package org.jeecg.modules.mobile.utils;

import java.net.URI;
import java.net.URISyntaxException;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class FileUrlUtils {
    public static String replaceUrl(String originalUrl, String replacePrefix) {
        if (originalUrl == null || originalUrl.isEmpty() || replacePrefix == null || replacePrefix.isEmpty()) {
            return null;
        }

        try {
            URI originalUri = new URI(originalUrl);
            URI replaceUri = new URI(replacePrefix);

            if (replaceUri.getScheme() == null || replaceUri.getHost() == null) {
                return null;
            }

            String mergedPath = mergePaths(
                    replaceUri.getPath(),
                    originalUri.getPath()
            );

            // 规范化路径中的连续斜杠
            mergedPath = mergedPath.replaceAll("/{2,}", "/");

            URI newUri = new URI(
                    replaceUri.getScheme(),
                    replaceUri.getUserInfo(),
                    replaceUri.getHost(),
                    replaceUri.getPort(),
                    mergedPath,
                    originalUri.getQuery(),
                    originalUri.getFragment()
            );

            return newUri.toString();
        } catch (URISyntaxException e) {
            return null;
        }
    }

    private static String mergePaths(String basePath, String originalPath) {
        basePath = (basePath == null) ? "" : basePath;
        originalPath = (originalPath == null) ? "" : originalPath;

        // 移除basePath末尾和originalPath开头的斜杠
        basePath = basePath.endsWith("/") ? basePath.substring(0, basePath.length() - 1) : basePath;
        originalPath = originalPath.startsWith("/") ? originalPath.substring(1) : originalPath;

        String mergedPath;
        if (basePath.isEmpty() && originalPath.isEmpty()) {
            mergedPath = "";
        } else if (basePath.isEmpty()) {
            mergedPath = "/" + originalPath;
        } else if (originalPath.isEmpty()) {
            mergedPath = basePath;
        } else {
            mergedPath = basePath + "/" + originalPath;
        }

        return mergedPath;
    }

    public static void main(String[] args) {
        String result = FileUrlUtils.replaceUrl(
                "http://192.168.130.18:9000/images//1894180837130375170.pdf",
                "https://wlcb.yingyangyun.cn/minio"
        );
        System.out.println(result);
        assertEquals("https://wlcb.yingyangyun.cn/minio/images/1894180837130375170.pdf", result);
    }
}