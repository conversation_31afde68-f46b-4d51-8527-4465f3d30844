package org.jeecg.modules.mobile.auth.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.mobile.bo.ShortLinkRequest;

import java.io.IOException;


public interface WxApiClient {


    String getSessionInfo(String appid, String secret, String jsCode, String grantType) throws IOException;


    String getAccessToken(String appid, String secret, String grantType) throws IOException;


    JSONObject getShortUrl(ShortLinkRequest param, String accessToken) throws IOException;


    JSONObject sendSubscribeMessage(J<PERSON>NObject param, String accessToken) throws IOException;
}
