package org.jeecg.modules.mobile.reg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.mobile.reg.entity.CustomeraccountCustomer;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface ICustomeraccountCustomerService extends IService<CustomeraccountCustomer> {

    String getRelation(String customerId, String accountId);

    void bindAccountAndCustomer(String accountId, String customerId) throws Exception;

}
