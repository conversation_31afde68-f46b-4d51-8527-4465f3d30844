package org.jeecg.modules.consult.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.consult.entity.PhyMessage;

import java.util.List;

/**
 * @Description: im历史消息
 * @Author: jeecg-boot
 * @Date: 2021-10-13
 * @Version: V1.0
 */
public interface IPhyMessageService extends IService<PhyMessage> {

    List<PhyMessage> getHistoryMessage(String senderId, String receiverId, String lastTimestamp);

    Page<PhyMessage> pageSenderMessage(Page<PhyMessage> page, String senderId);

    List<PhyMessage> listSenderMessage(String senderId);



    PhyMessage saveMessage(PhyMessage message);

    PhyMessage sendNewMsg2Ai(PhyMessage message);


}
