package org.jeecg.modules.consult.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.excommons.utils.HttpClientUtil;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.consult.entity.PhyMessage;
import org.jeecg.modules.consult.mapper.PhyMessageMapper;
import org.jeecg.modules.summary.service.ISummaryAdviceService;
import org.jeecg.modules.consult.service.IPhyMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;

/**
 * @Description: im历史消息
 * @Author: jeecg-boot
 * @Date: 2021-10-13
 * @Version: V1.0
 */
@Service
@Slf4j
public class PhyMessageServiceImpl extends ServiceImpl<PhyMessageMapper, PhyMessage> implements IPhyMessageService {

    @Autowired
    private PhyMessageMapper messageMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private ISummaryAdviceService summaryAdviceService;

    @Override
    public List<PhyMessage> getHistoryMessage(String senderId, String receiverId, String lastTimestamp) {

        List<PhyMessage> historyList = messageMapper.getHistoryMessage(senderId, receiverId, lastTimestamp);

        return historyList;
    }

    @Override
    public Page<PhyMessage> pageSenderMessage(Page<PhyMessage> page, String senderId) {
        messageMapper.pageSenderMessage(page, senderId);
        return page;
    }

    @Override
    public List<PhyMessage> listSenderMessage(String senderId) {
        return messageMapper.listSenderMessage(senderId);
    }


    @Override
    public PhyMessage saveMessage(PhyMessage message) {
        message.setTimestamp(new java.sql.Timestamp(System.currentTimeMillis()));
        save(message);
        return sendNewMsg2Ai2(message);

    }

    @Override
    public PhyMessage sendNewMsg2Ai(PhyMessage message) {
        try { //发送订阅消息

        /*    String aiUrl = sysSettingService.getValueByCode("ai_url");

            if (org.apache.commons.lang.StringUtils.isBlank(aiUrl)) {
                throw new RuntimeException("AI总检未配置！");
            }*/
            JSONObject payload = message.getPayload();
            if (Objects.nonNull(payload)) {
                if (Objects.equals(message.getType(), "text")) {
                    String messageText = payload.getString("text");
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("model", "Baichuan4");
                    jsonObject.put("temperature", 0.3);
                    jsonObject.put("stream", false);

                  /*  JSONObject sysText = new JSONObject();
                    sysText.put("role","system");
                    sysText.put("content",messageText);*/

                    JSONObject userText = new JSONObject();
                    userText.put("role", "user");
                    userText.put("content", messageText);

                    List<JSONObject> msgTexts = new LinkedList<>();
//                    msgTexts.add(sysText);
                    msgTexts.add(userText);
                    jsonObject.put("messages", msgTexts);

                    List<String> headers = Arrays.asList("Content-Type", "application/json", "Authorization", "Bearer sk-b50a65d8ed55209feed541ef9b8324e2");
                    String result = HttpClientUtil.sendPostWithHeader("https://api.baichuan-ai.com/v1/chat/completions", jsonObject.toJSONString(), headers);
                    JSONObject resultJson = JSONObject.parseObject(result);
                    log.info(resultJson.toJSONString());
                    //存储ai回复的消息
                    PhyMessage aiMsg = new PhyMessage();
                    aiMsg.setMessageId(resultJson.getString("id"));
                    aiMsg.setTimestamp(new java.sql.Timestamp(System.currentTimeMillis()));
                    JSONArray choices = resultJson.getJSONArray("choices");
                    List<JSONObject> choiceList = choices.toJavaList(JSONObject.class);
                    JSONObject choice = choiceList.get(0);
                    JSONObject msgObj = choice.getJSONObject("message");
                    String content = msgObj.getString("content");
                    JSONObject jo = new JSONObject();
                    jo.put("text", content);
                    aiMsg.setPayload(jo);
                    aiMsg.setReceiverType("user");
                    aiMsg.setReceiver(message.getSender());
                    aiMsg.setReceiverId(message.getSenderId());
                    aiMsg.setSender(msgObj.getString("role"));
                    aiMsg.setSenderId(msgObj.getString("role"));
                    aiMsg.setType(message.getType());
                    aiMsg.setStatus("1");
                    Timestamp timestamp = new java.sql.Timestamp(System.currentTimeMillis());
                    aiMsg.setTimestamp(timestamp);

                    messageMapper.insert(aiMsg);
                    return aiMsg;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public PhyMessage sendNewMsg2Ai2(PhyMessage message) {
        try { //发送订阅消息

/*            String aiUrl = sysSettingService.getValueByCode("ai_url");

            if (org.apache.commons.lang.StringUtils.isBlank(aiUrl)) {
                throw new RuntimeException("AI总检未配置！");
            }*/
            JSONObject payload = message.getPayload();
            if (Objects.nonNull(payload)) {
                if (Objects.equals(message.getType(), "text")) {
                    String messageText = payload.getString("text");
                    String result = summaryAdviceService.getAIReply(messageText);

                    //存储ai回复的消息
                    PhyMessage aiMsg = new PhyMessage();
                    aiMsg.setMessageId(String.valueOf(System.currentTimeMillis()));
                    aiMsg.setTimestamp(new java.sql.Timestamp(System.currentTimeMillis()));
                    String ret = result;
                    JSONObject jo = new JSONObject();
                    jo.put("text", ret);
                    aiMsg.setPayload(jo);
                    aiMsg.setReceiverType("user");
                    aiMsg.setReceiver(message.getSender());
                    aiMsg.setReceiverId(message.getSenderId());
                    aiMsg.setSender("assistant");
                    aiMsg.setSenderId("assistant");
                    aiMsg.setType(message.getType());
                    aiMsg.setStatus("1");
                    messageMapper.insert(aiMsg);
                    return aiMsg;
                }
            }
        } catch (Exception e) {
            log.error("AI回复消息失败", e);
        }
        return null;
    }
}








