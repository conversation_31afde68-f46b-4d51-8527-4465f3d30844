package org.jeecg.modules.consult.entity;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @Description: im历史消息
 * @Author: jeecg-boot
 * @Date: 2021-10-13
 * @Version: V1.0
 */
@Data
@TableName("phy_message")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "phy_message对象", description = "历史消息")
public class PhyMessage /*implements Serializable*/ {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 咨询订单ID
     */
    @Excel(name = "咨询订单ID", width = 15)
    @ApiModelProperty(value = "咨询订单ID")
    private String orderId;
    /**
     * 消息类型
     * text:文本，audio:音频，image:图片，video：视频
     */
    @Excel(name = "消息类型", width = 15)
    @ApiModelProperty(value = "消息类型")
    private String type;
    /**
     * 消息发送时间
     */
    @Excel(name = "消息发送时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "消息发送时间")
    private Timestamp timestamp;
    /**
     * 发送者ID
     */
    @Excel(name = "发送者ID", width = 15)
    @ApiModelProperty(value = "发送者ID")
    private String senderId;
    /**
     * 发送者
     */
    @Excel(name = "发送者", width = 15)
    @ApiModelProperty(value = "发送者")
    private String sender;
    /**
     * 发送者ID
     */
    @Excel(name = "接收者ID", width = 15)
    @ApiModelProperty(value = "接收者ID")
    private String receiverId;
    /**
     * 发送者
     */
    @Excel(name = "接收者", width = 15)
    @ApiModelProperty(value = "接收者")
    private String receiver;
    /**
     * 接受者类型，doctor,user
     */
    @Excel(name = "接受者类型", width = 15)
    @ApiModelProperty(value = "接受者类型")
    private String receiverType;
    /**
     * 消息内容
     */
    @Excel(name = "消息内容", width = 15)
    @ApiModelProperty(value = "消息内容")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject payload;
    /**
     * 消息状态
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 消息状态
     */
    @Excel(name = "messageId", width = 15)
    @ApiModelProperty(value = "messageId")
    private String messageId;

    private String receiverReaded;

    @TableField(exist = false)
    private Integer symbol;

}
