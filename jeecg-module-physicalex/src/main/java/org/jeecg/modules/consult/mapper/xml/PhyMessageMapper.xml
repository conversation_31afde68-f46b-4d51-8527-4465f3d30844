<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.consult.mapper.PhyMessageMapper">

    <select id="getHistoryMessage" resultMap="PhyMessageResultMap">
        select * from phy_message where timestamp  <![CDATA[<]]>  #{lastTimestamp} and ((sender_id=#{senderId} and receiver_id=#{receiverId}) or (sender_id=#{receiverId} and receiver_id=#{senderId})) order by timestamp desc
    </select>

    <select id="pageSenderMessage" resultMap="PhyMessageResultMap">
        select * from phy_message where sender_id=#{senderId} or receiver_id=#{senderId} order by timestamp
    </select>
    <select id="listSenderMessage" resultMap="PhyMessageResultMap">
        select * from phy_message where sender_id=#{senderId} or receiver_id=#{senderId} order by timestamp
    </select>

    <resultMap id="PhyMessageResultMap" type="org.jeecg.modules.consult.entity.PhyMessage">
        <id column="id" property="id"></id>
        <result column="type" property="type"></result>
        <result column="timestamp" property="timestamp"></result>
        <result column="sender_id" property="senderId"></result>
        <result column="receiver_id" property="receiverId"></result>
        <result column="payload" property="payload" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"></result>
        <result column="order_id" property="orderId"></result>
        <result column="message_id" property="messageId"></result>
        <result column="status" property="status"></result>
        <result column="receiver_readed" property="receiverReaded"></result>
    </resultMap>
</mapper>