package org.jeecg.modules.consult.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.consult.entity.PhyMessage;

import java.util.List;

/**
 * @Description: im历史消息
 * @Author: jeecg-boot
 * @Date: 2021-10-13
 * @Version: V1.0
 */
public interface PhyMessageMapper extends BaseMapper<PhyMessage> {

    List<PhyMessage> getHistoryMessage(@Param("senderId") String senderId, @Param("receiverId") String receiverId, @Param("lastTimestamp") String lastTimestamp);

    Page<PhyMessage> pageSenderMessage(Page<PhyMessage> page, @Param("senderId") String senderId);

    List<PhyMessage> listSenderMessage( @Param("senderId") String senderId);
}
