package org.jeecg.modules.consult.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.consult.entity.PhyMessage;
import org.jeecg.modules.consult.service.IPhyMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: im历史消息
 * @Author: jeecg-boot
 * @Date: 2021-10-13
 * @Version: V1.0
 */
@Api(tags = "phy历史消息")
@RestController
@RequestMapping("/consult/phyMessage")
@Slf4j
public class PhyMessageController extends JeecgController<PhyMessage, IPhyMessageService> {
    @Autowired
    private IPhyMessageService phyMessageService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 分页列表查询
     *
     * @param imMessage
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "phy历史消息-分页列表查询")
    @ApiOperation(value = "phy历史消息-分页列表查询", notes = "phy历史消息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(PhyMessage imMessage,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        Page<PhyMessage> page = new Page<PhyMessage>(pageNo, pageSize);
        String senderId = req.getParameter("senderId");
        IPage<PhyMessage> pageList = phyMessageService.pageSenderMessage(page, senderId);
//        List<PhyMessage> phyMessages = phyMessageService.listSenderMessage( senderId);

        pageList.getRecords().forEach(msg->{
            msg.setSymbol(StringUtils.equals(msg.getReceiverType(),"ai") ? 0:1);
        });
//        phyMessages.forEach(msg->{
//            msg.setSymbol(StringUtils.equals(msg.getReceiverType(),"ai")?0:1);
//        });
        return Result.OK(pageList);
    }

    /**
     * 查询历史消息
     *
     * @return
     */
    @AutoLog(value = "phy历史消息-分页列表查询")
    @ApiOperation(value = "phy历史消息-分页列表查询", notes = "phy历史消息-分页列表查询")
    @GetMapping(value = "/listHistory")
    public Result<?> pageHistoryMessage(String senderId, String receiverId, String lastTimestamp) {

        List<PhyMessage> historyList = phyMessageService.getHistoryMessage(senderId, receiverId, lastTimestamp);
        return Result.OK(historyList);
    }




    /**
     * 添加
     *
     * @param phyMessage
     * @return
     */
    @AutoLog(value = "phy历史消息-添加")
    @ApiOperation(value = "phy历史消息-添加", notes = "phy历史消息-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody PhyMessage phyMessage) {

        PhyMessage saveMessage = phyMessageService.saveMessage(phyMessage);
        if (Objects.nonNull(saveMessage)){

            return Result.OK(saveMessage);
        }
        return Result.error("服务繁忙，请稍后重试！");
    }

    /**
     * 编辑
     *
     * @param imMessage
     * @return
     */
    @AutoLog(value = "pny历史消息-编辑")
    @ApiOperation(value = "phy历史消息-编辑", notes = "phy历史消息-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody PhyMessage imMessage) {
        phyMessageService.updateById(imMessage);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "phy历史消息-通过id删除")
    @ApiOperation(value = "phy历史消息-通过id删除", notes = "phy历史消息-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        phyMessageService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "phy历史消息-批量删除")
    @ApiOperation(value = "phy历史消息-批量删除", notes = "phy历史消息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.phyMessageService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "phy历史消息-通过id查询")
    @ApiOperation(value = "phy历史消息-通过id查询", notes = "phy历史消息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        PhyMessage phyMessage = phyMessageService.getById(id);
        if (phyMessage == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(phyMessage);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param phyMessage
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PhyMessage phyMessage) {
        return super.exportXls(request, phyMessage, PhyMessage.class, "im历史消息");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PhyMessage.class);
    }

}
