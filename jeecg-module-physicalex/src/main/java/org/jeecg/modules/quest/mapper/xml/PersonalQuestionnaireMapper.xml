<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.quest.mapper.PersonalQuestionnaireMapper">

    <select id="getByQuestId" resultType="org.jeecg.modules.quest.entity.PersonalQuestionnaire">
        select id,personal_id,personal_name,complete_flag from personal_questionnaire where questionnaire_id = #{questId} order by create_time desc
    </select>
    <select id="getByQuestIdAndBizId" resultType="org.jeecg.modules.quest.entity.PersonalQuestionnaire">
        select * from personal_questionnaire where questionnaire_id = #{questId} and personal_id = #{bizId}
    </select>

    <select id="queryByQuestAndCard" resultType="org.jeecg.modules.quest.entity.PersonalQuestionnaire">
        select id,questionnaire_id,state,visit_no,complete_flag from personal_questionnaire where questionnaire_id=#{questId} and visit_no=#{account} order by create_time desc limit 1
    </select>
</mapper>