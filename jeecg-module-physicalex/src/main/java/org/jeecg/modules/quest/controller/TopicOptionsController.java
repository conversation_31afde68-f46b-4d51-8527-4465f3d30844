package org.jeecg.modules.quest.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.quest.entity.TopicOptions;
import org.jeecg.modules.quest.service.ITopicOptionsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 问卷题目选项
 * @Author: jeecg-boot
 * @Date:   2024-11-19
 * @Version: V1.0
 */
@Api(tags="问卷题目选项")
@RestController
@RequestMapping("/quest/topicOptions")
@Slf4j
public class TopicOptionsController extends JeecgController<TopicOptions, ITopicOptionsService> {
	@Autowired
	private ITopicOptionsService topicOptionsService;

	 /**
	  * getOptonsByContentId
	  */
	 @ApiOperation(value="通过题目id获取选项", notes="通过题目id获取选项")
	 @GetMapping(value = "/getOptonsByContentId")
	 public Result<List<TopicOptions>> getOptonsByContentId(@RequestParam(name="contentId",required=true) String contentId) {
		 List<TopicOptions> options = topicOptionsService.listByQuestContentId(contentId);
		 return Result.OK(options);
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param topicOptions
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "问卷题目选项-分页列表查询")
	@ApiOperation(value="问卷题目选项-分页列表查询", notes="问卷题目选项-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TopicOptions>> queryPageList(TopicOptions topicOptions,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<TopicOptions> queryWrapper = QueryGenerator.initQueryWrapper(topicOptions, req.getParameterMap());
		Page<TopicOptions> page = new Page<TopicOptions>(pageNo, pageSize);
		IPage<TopicOptions> pageList = topicOptionsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param topicOptions
	 * @return
	 */
	@AutoLog(value = "问卷题目选项-添加")
	@ApiOperation(value="问卷题目选项-添加", notes="问卷题目选项-添加")
	@RequiresPermissions("quest:topic_options:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TopicOptions topicOptions) {
		topicOptionsService.save(topicOptions);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param topicOptions
	 * @return
	 */
	@AutoLog(value = "问卷题目选项-编辑")
	@ApiOperation(value="问卷题目选项-编辑", notes="问卷题目选项-编辑")
	@RequiresPermissions("quest:topic_options:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TopicOptions topicOptions) {
		topicOptionsService.updateById(topicOptions);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "问卷题目选项-通过id删除")
	@ApiOperation(value="问卷题目选项-通过id删除", notes="问卷题目选项-通过id删除")
	//@RequiresPermissions("quest:topic_options:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		topicOptionsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "问卷题目选项-批量删除")
	@ApiOperation(value="问卷题目选项-批量删除", notes="问卷题目选项-批量删除")
	@RequiresPermissions("quest:topic_options:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.topicOptionsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "问卷题目选项-通过id查询")
	@ApiOperation(value="问卷题目选项-通过id查询", notes="问卷题目选项-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TopicOptions> queryById(@RequestParam(name="id",required=true) String id) {
		TopicOptions topicOptions = topicOptionsService.getById(id);
		if(topicOptions==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(topicOptions);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param topicOptions
    */
    @RequiresPermissions("quest:topic_options:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TopicOptions topicOptions) {
        return super.exportXls(request, topicOptions, TopicOptions.class, "问卷题目选项");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("quest:topic_options:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TopicOptions.class);
    }

}
