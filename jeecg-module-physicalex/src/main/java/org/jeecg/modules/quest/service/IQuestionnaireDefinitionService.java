package org.jeecg.modules.quest.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.quest.entity.QuestionnaireContent;
import org.jeecg.modules.quest.entity.QuestionnaireCalculation;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.beans.factory.annotation.Autowired;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 问卷定义
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface IQuestionnaireDefinitionService extends IService<QuestionnaireDefinition> {

	QuestionnaireDefinition getFilledDefinition(String id);

	List<QuestionnaireContent> getQuestContents(String questId);

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	List<QuestionnaireDefinition> listByModual(String module);

	void clearCache();

	JSONObject statQuestContents(String questId, String tenantId);

}
