package org.jeecg.modules.quest.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 问卷定义
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface QuestionnaireDefinitionMapper extends BaseMapper<QuestionnaireDefinition> {

    List<QuestionnaireDefinition> listByModual(@Param("module") String module);
}
