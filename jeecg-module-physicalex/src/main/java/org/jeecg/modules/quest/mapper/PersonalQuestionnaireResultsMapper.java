package org.jeecg.modules.quest.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.quest.entity.PersonalQuestionnaireResults;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

/**
 * @Description: 问卷答题题目记录
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface PersonalQuestionnaireResultsMapper extends BaseMapper<PersonalQuestionnaireResults> {

    public CustomerReg getCustomerRegByPersonalQuesId(@Param("personalQuesId") String personalQuesId);
    public CustomerRegItemResult getCustomerRegResultByQuestId(@Param("questId") String questId,@Param("regId") String regId);
    public CustomerRegItemResult getQuestResultByPersonalQuestId(@Param("personalQuestId") String personalQuestId);
    public QuestionnaireDefinition getQuesDefinitionByPersonalQuestId(@Param("personalQuestId") String personalQuestId);

}
