package org.jeecg.modules.quest.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.quest.entity.PersonalQuestionnaire;
import org.jeecg.modules.quest.entity.PersonalQuestionnaireResults;
import org.jeecg.modules.quest.service.IPersonalQuestionnaireResultsService;
import org.jeecg.modules.quest.service.IPersonalQuestionnaireService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 问卷答题
 * @Author: jeecg-boot
 * @Date: 2024-11-13
 * @Version: V1.0
 */
@Api(tags = "问卷答题")
@RestController
@RequestMapping("/quest/personalQuestionnaire")
@Slf4j
public class PersonalQuestionnaireController extends JeecgController<PersonalQuestionnaire, IPersonalQuestionnaireService> {
    @Autowired
    private IPersonalQuestionnaireService personalQuestionnaireService;
    @Autowired
    private IPersonalQuestionnaireResultsService personalQuestionnaireResultsService;
    /**
     * getByQuestIdAndBizId
     */
    @AutoLog(value = "问卷答题-通过questId和bizId查询")
    @ApiOperation(value = "问卷答题-通过questId和bizId查询", notes = "问卷答题-通过questId和bizId查询")
    @GetMapping(value = "/getByQuestIdAndBizId")
    public Result<?> getByQuestIdAndBizId(String questId, String bizId) {

        List<PersonalQuestionnaire> personalQuestionnaire = personalQuestionnaireService.getByQuestIdAndBizId(questId, bizId);
        return Result.OK(personalQuestionnaire);
    }

    /**
     * getByQuestId
     */
    @AutoLog(value = "问卷答题-通过questId查询")
    @ApiOperation(value = "问卷答题-通过questId查询", notes = "问卷答题-通过questId查询")
    @GetMapping(value = "/getByQuestId")
    public Result<?> getByQuestId(String questId) {

        List<PersonalQuestionnaire> personalQuestionnaire = personalQuestionnaireService.getByQuestId(questId);

        return Result.OK(personalQuestionnaire);
    }

    /**
     * 分页列表查询
     *
     * @param personalQuestionnaire
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "问卷答题-分页列表查询")
    @ApiOperation(value = "问卷答题-分页列表查询", notes = "问卷答题-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<PersonalQuestionnaire>> queryPageList(PersonalQuestionnaire personalQuestionnaire, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<PersonalQuestionnaire> queryWrapper = QueryGenerator.initQueryWrapper(personalQuestionnaire, req.getParameterMap());
        Page<PersonalQuestionnaire> page = new Page<PersonalQuestionnaire>(pageNo, pageSize);
        IPage<PersonalQuestionnaire> pageList = personalQuestionnaireService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param personalQuestionnaire
     * @return
     */
    @AutoLog(value = "问卷答题-添加")
    @ApiOperation(value = "问卷答题-添加", notes = "问卷答题-添加")
    @RequiresPermissions("quest:personal_questionnaire:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody PersonalQuestionnaire personalQuestionnaire) {
        personalQuestionnaireService.save(personalQuestionnaire);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param personalQuestionnaire
     * @return
     */
    @AutoLog(value = "问卷答题-编辑")
    @ApiOperation(value = "问卷答题-编辑", notes = "问卷答题-编辑")
    @RequiresPermissions("quest:personal_questionnaire:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody PersonalQuestionnaire personalQuestionnaire) {
        personalQuestionnaireService.updateById(personalQuestionnaire);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "问卷答题-通过id删除")
    @ApiOperation(value = "问卷答题-通过id删除", notes = "问卷答题-通过id删除")
    @RequiresPermissions("quest:personal_questionnaire:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        personalQuestionnaireService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "问卷答题-批量删除")
    @ApiOperation(value = "问卷答题-批量删除", notes = "问卷答题-批量删除")
    @RequiresPermissions("quest:personal_questionnaire:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.personalQuestionnaireService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "问卷答题-通过id查询")
    @ApiOperation(value = "问卷答题-通过id查询", notes = "问卷答题-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<PersonalQuestionnaire> queryById(@RequestParam(name = "id", required = true) String id) {
        PersonalQuestionnaire personalQuestionnaire = personalQuestionnaireService.getById(id);
        if (personalQuestionnaire == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(personalQuestionnaire);
    }

    /**
     * 添加
     *
     * @param model
     * @return
     */
    @AutoLog(value = "个人问卷结果-填写")
    @ApiOperation(value = "个人问卷结果-填写", notes = "个人问卷结果-填写")
    @PostMapping(value = "/saveQuset")
    public Result<?> saveQuset(@RequestBody JSONObject model) {

        PersonalQuestionnaire personalQuestionnaire = personalQuestionnaireService.savePersonalQuestionnaire(model);
        return Result.OK("添加成功！", personalQuestionnaire.getId());
    }


    /**
     * 导出excel
     *
     * @param request
     * @param personalQuestionnaire
     */
    @RequiresPermissions("quest:personal_questionnaire:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PersonalQuestionnaire personalQuestionnaire) {
        return super.exportXls(request, personalQuestionnaire, PersonalQuestionnaire.class, "问卷答题");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("quest:personal_questionnaire:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PersonalQuestionnaire.class);
    }
    /**
     * 通过主表ID查询
     *
     * @return
     */
    @AutoLog(value = "个人问卷结果-通过主表ID查询")
    @ApiOperation(value = "个人问卷结果-通过主表ID查询", notes = "个人问卷结果-通过主表ID查询")
    @GetMapping(value = "/listPersonalQuestionnaireResultsByMainId")
    public Result<?> listPersonalQuestionnaireResultsByMainId(PersonalQuestionnaireResults personalQuestionnaireResults,
                                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                              HttpServletRequest req) {
        QueryWrapper<PersonalQuestionnaireResults> queryWrapper = QueryGenerator.initQueryWrapper(personalQuestionnaireResults, req.getParameterMap());
        Page<PersonalQuestionnaireResults> page = new Page<PersonalQuestionnaireResults>(pageNo, pageSize);
        IPage<PersonalQuestionnaireResults> pageList = personalQuestionnaireResultsService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

}
