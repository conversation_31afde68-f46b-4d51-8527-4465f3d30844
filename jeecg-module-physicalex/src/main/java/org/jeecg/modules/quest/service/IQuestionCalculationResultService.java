package org.jeecg.modules.quest.service;

import org.jeecg.modules.quest.entity.QuestionCalculationResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 问卷计算结果
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface IQuestionCalculationResultService extends IService<QuestionCalculationResult> {

    List<QuestionCalculationResult> getCalResultByCalId(String calId);
}
