package org.jeecg.modules.quest.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.quest.entity.QuestionCalculationResult;
import org.jeecg.modules.quest.service.IQuestionCalculationResultService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 问卷计算结果
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Api(tags="问卷计算结果")
@RestController
@RequestMapping("/quest/questionCalculationResult")
@Slf4j
public class QuestionCalculationResultController extends JeecgController<QuestionCalculationResult, IQuestionCalculationResultService> {
	@Autowired
	private IQuestionCalculationResultService questionCalculationResultService;

	 /**getCalResultByCalId/
	  *
	  */
	 @ApiOperation(value="通过计算id获取结果", notes="通过计算id获取结果")
	 @GetMapping(value = "/getCalResultByCalId")
	 public Result<List<QuestionCalculationResult>> getCalResultByCalId(@RequestParam(name="calId",required=true) String calId) {
		 List<QuestionCalculationResult> list = questionCalculationResultService.getCalResultByCalId(calId);
		 return Result.OK(list);
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param questionCalculationResult
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "问卷计算结果-分页列表查询")
	@ApiOperation(value="问卷计算结果-分页列表查询", notes="问卷计算结果-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<QuestionCalculationResult>> queryPageList(QuestionCalculationResult questionCalculationResult,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<QuestionCalculationResult> queryWrapper = QueryGenerator.initQueryWrapper(questionCalculationResult, req.getParameterMap());
		Page<QuestionCalculationResult> page = new Page<QuestionCalculationResult>(pageNo, pageSize);
		IPage<QuestionCalculationResult> pageList = questionCalculationResultService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param questionCalculationResult
	 * @return
	 */
	@AutoLog(value = "问卷计算结果-添加")
	@ApiOperation(value="问卷计算结果-添加", notes="问卷计算结果-添加")
	@RequiresPermissions("quest:question_calculation_result:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody QuestionCalculationResult questionCalculationResult) {
		questionCalculationResultService.save(questionCalculationResult);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param questionCalculationResult
	 * @return
	 */
	@AutoLog(value = "问卷计算结果-编辑")
	@ApiOperation(value="问卷计算结果-编辑", notes="问卷计算结果-编辑")
	@RequiresPermissions("quest:question_calculation_result:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody QuestionCalculationResult questionCalculationResult) {
		questionCalculationResultService.updateById(questionCalculationResult);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "问卷计算结果-通过id删除")
	@ApiOperation(value="问卷计算结果-通过id删除", notes="问卷计算结果-通过id删除")
	//@RequiresPermissions("quest:question_calculation_result:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		questionCalculationResultService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "问卷计算结果-批量删除")
	@ApiOperation(value="问卷计算结果-批量删除", notes="问卷计算结果-批量删除")
	@RequiresPermissions("quest:question_calculation_result:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.questionCalculationResultService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "问卷计算结果-通过id查询")
	@ApiOperation(value="问卷计算结果-通过id查询", notes="问卷计算结果-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<QuestionCalculationResult> queryById(@RequestParam(name="id",required=true) String id) {
		QuestionCalculationResult questionCalculationResult = questionCalculationResultService.getById(id);
		if(questionCalculationResult==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(questionCalculationResult);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param questionCalculationResult
    */
    @RequiresPermissions("quest:question_calculation_result:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QuestionCalculationResult questionCalculationResult) {
        return super.exportXls(request, questionCalculationResult, QuestionCalculationResult.class, "问卷计算结果");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("quest:question_calculation_result:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QuestionCalculationResult.class);
    }

}
