package org.jeecg.modules.quest.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 问卷定义
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Data
@TableName("questionnaire_definition")
@ApiModel(value="questionnaire_definition对象", description="问卷定义")
public class QuestionnaireDefinition implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    @ApiModelProperty(value = "编码")
    private String code;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**问卷名称*/
    @Excel(name = "问卷名称", width = 15)
    @ApiModelProperty(value = "问卷名称")
    private java.lang.String name;
	/**适用模块*/
    @Excel(name = "适用模块", width = 15, dicCode = "quest_suit_module")
    @Dict(dicCode = "quest_suit_module")
    @ApiModelProperty(value = "适用模块")
    private java.lang.String module;
	/**适用人群*/
    @Excel(name = "适用人群", width = 15)
    @ApiModelProperty(value = "适用人群")
    private java.lang.String forPeople;
	/**介绍*/
    @Excel(name = "介绍", width = 15)
    private transient java.lang.String introduceString;
    /**启用标志*/
    @Excel(name = "启用标志", width = 15)
    private String enableFlag;
    /**预计耗时*/
    private Integer needsMinutes;

    private byte[] introduce;

    public byte[] getIntroduce(){
        if(introduceString==null){
            return null;
        }
        try {
            return introduceString.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getIntroduceString(){
        if(introduce==null || introduce.length==0){
            return "";
        }
        try {
            return new String(introduce,"UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }
	/**图片*/
    @Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
    private java.lang.String pic;
	/**是否删除*/
    @Excel(name = "是否删除", width = 15)
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    private java.lang.String delFlag;

    @TableField(exist = false)
    private Long testCount;
    @TableField(exist = false)
    private Integer dimensionCount;
    @TableField(exist = false)
    private Integer qeustionCount;

    @TableField(exist = false)
    private PersonalQuestionnaire personalQuestionnaire;
    @TableField(exist = false)
    private String suitId;
    @TableField(exist = false)
    private String suitCode;
}
