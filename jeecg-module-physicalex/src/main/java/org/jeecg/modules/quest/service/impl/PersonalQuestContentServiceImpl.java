package org.jeecg.modules.quest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang.StringUtils;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.*;
import org.apache.lucene.search.*;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.apache.lucene.util.BytesRef;
import org.jeecg.excommons.utils.PunctuationAnalyzer;
import org.jeecg.modules.quest.entity.PersonalQuestContent;
import org.jeecg.modules.quest.mapper.PersonalQuestContentMapper;
import org.jeecg.modules.quest.service.IPersonalQuestContentService;
import org.jeecg.modules.summary.entity.SummaryAdvice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.util.*;

/**
 * @Description: 问卷答题详情
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Service
public class PersonalQuestContentServiceImpl extends ServiceImpl<PersonalQuestContentMapper, PersonalQuestContent> implements IPersonalQuestContentService {
    @Autowired
    private Analyzer hanlpAnalyzer;
    @Value("${biz.lucene.path}")
    private String lucenePath;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    private final Object lock = new Object();
    @Override
    public void createLuceneIndex() {
        synchronized (lock) {
            Collection<Document> docs = new ArrayList<>();

            // 获取 indexTime 为空的数据，创建索引
            LambdaQueryWrapper<PersonalQuestContent> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNull(PersonalQuestContent::getIndexTime);
            queryWrapper.isNotNull(PersonalQuestContent::getAnswerText);
            queryWrapper.eq(PersonalQuestContent::getQuestType, "输入框");
            List<PersonalQuestContent> list = this.list(queryWrapper);
            for (PersonalQuestContent personalQuestContent : list) {
                Document document = new Document();
                document.add(new StringField("id", String.valueOf(personalQuestContent.getId()), Field.Store.YES));
                document.add(new StringField("personalQuestId", StringUtils.trimToEmpty(personalQuestContent.getPersonalQuestId()), Field.Store.YES));
                // 将 questId 字段改为 StringField
                document.add(new StringField("questId", StringUtils.trimToEmpty(personalQuestContent.getQuestId()), Field.Store.YES));
                document.add(new StringField("questContentId", StringUtils.trimToEmpty(personalQuestContent.getQuestContentId()), Field.Store.YES));

                // 定义 answerText 字段的 FieldType，启用 Term Vector
                FieldType fieldType = new FieldType();
                fieldType.setIndexOptions(IndexOptions.DOCS_AND_FREQS_AND_POSITIONS);
                fieldType.setTokenized(true);
                fieldType.setStored(true);
                fieldType.setStoreTermVectors(true);

                // 添加 answerText 字段
                document.add(new Field("answerText", StringUtils.trimToEmpty(personalQuestContent.getAnswerText()), fieldType));
                docs.add(document);
            }
            if (docs.isEmpty()) {
                return;
            }

            Analyzer analyzer = new PunctuationAnalyzer();
            // 索引写出工具的配置对象
            IndexWriterConfig conf = new IndexWriterConfig(analyzer);
            // 设置打开方式
            conf.setOpenMode(IndexWriterConfig.OpenMode.CREATE_OR_APPEND);

            IndexWriter indexWriter = null;
            try {
                Directory directory = FSDirectory.open(FileSystems.getDefault().getPath(lucenePath));
                indexWriter = new IndexWriter(directory, conf);

                // 把文档集合交给 IndexWriter
                indexWriter.addDocuments(docs);
                // 提交
                indexWriter.commit();

                // 更新数据库中的 indexTime
                String updateSql = "UPDATE personal_quest_content SET index_time = ? WHERE id = ?";
                List<Object[]> batchArgs = new ArrayList<>();
                for (PersonalQuestContent questContent : list) {
                    Object[] values = new Object[]{new java.util.Date(), questContent.getId()};
                    batchArgs.add(values);
                }
                jdbcTemplate.batchUpdate(updateSql, batchArgs);
            } catch (Exception e) {
                log.error("创建索引失败", e);
            } finally {
                if (indexWriter != null) {
                    try {
                        indexWriter.close();
                    } catch (IOException e) {
                        // handle exception
                    }
                }
            }
        }
    }




}
