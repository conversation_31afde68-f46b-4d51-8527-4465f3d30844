package org.jeecg.modules.quest.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 问卷答题详情
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Data
@TableName("personal_quest_content")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="personal_quest_content对象", description="问卷答题详情")
public class PersonalQuestContent implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**问卷选项ID*/
	//@Excel(name = "问卷选项ID", width = 15)
    @ApiModelProperty(value = "问卷选项ID")
    private java.lang.String selectedOptionId;
    /**题目*/
    @Excel(name = "题目", width = 50)
    @ApiModelProperty(value = "题目")
    private java.lang.String questContent;
    /**题目*/
    //@Excel(name = "题目类型", width = 50)
    @ApiModelProperty(value = "题目类型")
    private java.lang.String questType;
	/**文本*/
	@Excel(name = "答案", width = 50)
    @ApiModelProperty(value = "答案")
    private java.lang.String answerText;
	/**个人问卷ID*/
	//@Excel(name = "个人问卷ID", width = 15)
    @ApiModelProperty(value = "个人问卷ID")
    private java.lang.String personalQuestId;
	/**问卷ID*/
	//@Excel(name = "问卷ID", width = 15)
    @ApiModelProperty(value = "问卷ID")
    private java.lang.String questId;
	/**问卷内容ID*/
	//@Excel(name = "问卷内容ID", width = 15)
    @ApiModelProperty(value = "问卷内容ID")
    private java.lang.String questContentId;
	/**租户ID*/
	//@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
    private java.lang.String tenantId;
    /**索引时间*/
    //@Excel(name = "索引时间", width = 15)
    @ApiModelProperty(value = "索引时间")
    private Date indexTime;

}
