<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.quest.mapper.QuestionnaireContentMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  questionnaire_content 
		WHERE
			 definition_id = #{mainId} 
	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.quest.entity.QuestionnaireContent">
		SELECT * 
		FROM  questionnaire_content
		WHERE del_flag='0' and  definition_id = #{mainId}  order by sort
	</select>
    <select id="getNextQuestSeq" resultType="java.lang.Integer">
		SELECT IFNULL(MAX(sort),0)+1 FROM questionnaire_content WHERE definition_id = #{questId}
	</select>
</mapper>
