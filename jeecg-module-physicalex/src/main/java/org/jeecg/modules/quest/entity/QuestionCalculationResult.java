package org.jeecg.modules.quest.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 问卷计算结果
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Data
@TableName("question_calculation_result")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="question_calculation_result对象", description="问卷计算结果")
public class QuestionCalculationResult implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**结果*/
	@Excel(name = "结果", width = 15)
    @ApiModelProperty(value = "结果")
    private java.lang.String result;
    /**建议*/
    @Excel(name = "建议", width = 15)
    @ApiModelProperty(value = "建议")
    private java.lang.String adviseText;
	/**问卷计算id*/
	@Excel(name = "问卷计算id", width = 15)
    @ApiModelProperty(value = "问卷计算id")
    private java.lang.String calculationId;
	/**问卷ID*/
	@Excel(name = "问卷ID", width = 15)
    @ApiModelProperty(value = "问卷ID")
    private java.lang.String questionnaireId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    private java.lang.String delFlag;
	/**性别*/
	@Excel(name = "性别限制", width = 15)
    @ApiModelProperty(value = "性别限制")
    private java.lang.String genderLimit;
    /**计算脚本*/
    @Excel(name = "计算脚本", width = 15)
    @ApiModelProperty(value = "计算脚本")
    private String groovyScript;
}
