package org.jeecg.modules.quest.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 问卷答题题目记录
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Data
@TableName("personal_questionnaire_results")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="personal_questionnaire_results对象", description="问卷答题题目记录")
public class PersonalQuestionnaireResults implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**个人问卷id*/
	@Excel(name = "个人问卷id", width = 15)
    @ApiModelProperty(value = "个人问卷id")
    private java.lang.String personalQuestionnaireId;
	/**计分方式id*/
	@Excel(name = "计分方式id", width = 15)
    @ApiModelProperty(value = "计分方式id")
    private java.lang.String questionnaireCalculationId;
	/**计分标题*/
	@Excel(name = "计分标题", width = 15)
    @ApiModelProperty(value = "计分标题")
    private java.lang.String title;
	/**得分*/
	@Excel(name = "得分", width = 15)
    @ApiModelProperty(value = "得分")
    private java.lang.String score;
	/**结果*/
	@Excel(name = "结果", width = 15)
    @ApiModelProperty(value = "结果")
    private java.lang.String result;
	/**显示类型*/
	@Excel(name = "显示类型", width = 15)
    @ApiModelProperty(value = "显示类型")
    private java.lang.String displayType;
	/**排序号*/
	@Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.Integer orderNo;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String unit;
	/**建议*/
	@Excel(name = "建议", width = 15)
    @ApiModelProperty(value = "建议")
    private java.lang.String adviseText;
}
