package org.jeecg.modules.quest.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 问卷答题
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Data
@TableName("personal_questionnaire")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="personal_questionnaire对象", description="问卷答题")
public class PersonalQuestionnaire implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**问卷名称*/
	@Excel(name = "问卷名称", width = 15)
    @ApiModelProperty(value = "问卷名称")
    private java.lang.String name;
	/**问卷ID*/
	@Excel(name = "问卷ID", width = 15)
    @ApiModelProperty(value = "问卷ID")
    private java.lang.String questionnaireId;
	/**人员id*/
	@Excel(name = "人员id", width = 15)
    @ApiModelProperty(value = "人员id")
    private java.lang.String personalId;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String personalName;
	/**租户id*/
	@Excel(name = "租户id", width = 15)
    @ApiModelProperty(value = "租户id")
    private java.lang.String tenantId;
	/**问卷内容*/
	@Excel(name = "问卷内容", width = 15)
    @ApiModelProperty(value = "问卷内容")
    private java.lang.String content;
	/**是否完成*/
	@Excel(name = "是否完成", width = 15)
    @ApiModelProperty(value = "是否完成")
    private java.lang.String completeFlag;
	/**state 空 未审核/  0 未审核 /1 审核通过 /2审核不通过*/
	@Excel(name = "state 空 未审核/  0 未审核 /1 审核通过 /2审核不通过", width = 15)
    @ApiModelProperty(value = "state 空 未审核/  0 未审核 /1 审核通过 /2审核不通过")
    private java.lang.String state;
	/**adviseText*/
	@Excel(name = "adviseText", width = 15)
    @ApiModelProperty(value = "adviseText")
    private java.lang.String adviseText;
	/**visitNo*/
	@Excel(name = "visitNo", width = 15)
    @ApiModelProperty(value = "visitNo")
    private java.lang.String visitNo;
	/**taskId*/
	@Excel(name = "taskId", width = 15)
    @ApiModelProperty(value = "taskId")
    private java.lang.String taskId;
    /**业务模块*/
    @Excel(name = "module", width = 15)
    @ApiModelProperty(value = "module")
    private String module;
    /**答卷用时*/
    @Excel(name = "costSeconds", width = 15)
    @ApiModelProperty(value = "costSeconds")
    private String costSeconds;
    /**参考标准*/
    @Excel(name = "refRange", width = 15)
    @ApiModelProperty(value = "refRange")
    private String refRange;

    @TableField(exist = false)
    private BigDecimal totalScore;
    @TableField(exist = false)
    private BigDecimal averageScore;
    @TableField(exist = false)
    private String finalResult;
    @TableField(exist = false)
    private String finalAdvice;

}
