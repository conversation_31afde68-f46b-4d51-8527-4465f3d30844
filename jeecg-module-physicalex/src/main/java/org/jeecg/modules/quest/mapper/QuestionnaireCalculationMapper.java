package org.jeecg.modules.quest.mapper;

import java.util.List;
import org.jeecg.modules.quest.entity.QuestionnaireCalculation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 问卷计算维度
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface QuestionnaireCalculationMapper extends BaseMapper<QuestionnaireCalculation> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

   /**
    * 通过主表id查询子表数据
    *
    * @param mainId 主表id
    * @return List<QuestionnaireCalculation>
    */
	public List<QuestionnaireCalculation> selectByMainId(@Param("mainId") String mainId);

}
