package org.jeecg.modules.quest.service;

import org.jeecg.modules.quest.entity.QuestionnaireContent;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 题目列表
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface IQuestionnaireContentService extends IService<QuestionnaireContent> {

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId
   * @return List<QuestionnaireContent>
   */
	public List<QuestionnaireContent> selectByMainId(String mainId);

    void saveOrUpdateQuestContent(QuestionnaireContent questionnaireContent);

    Integer getNextQuestSeq(String questId);
}
