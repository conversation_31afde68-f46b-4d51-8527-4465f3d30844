package org.jeecg.modules.quest.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.quest.entity.QuestionCalculationResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 问卷计算结果
 * @Author: jeecg-boot
 * @Date: 2024-11-13
 * @Version: V1.0
 */
public interface QuestionCalculationResultMapper extends BaseMapper<QuestionCalculationResult> {

    public List<QuestionCalculationResult> getCalResultByCalId(@Param("calId") String calId);
}
