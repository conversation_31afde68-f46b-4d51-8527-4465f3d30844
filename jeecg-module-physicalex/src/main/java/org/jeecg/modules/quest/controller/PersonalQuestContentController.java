package org.jeecg.modules.quest.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.quest.entity.PersonalQuestContent;
import org.jeecg.modules.quest.service.IPersonalQuestContentService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 问卷答题详情
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Api(tags="问卷答题详情")
@RestController
@RequestMapping("/quest/personalQuestContent")
@Slf4j
public class PersonalQuestContentController extends JeecgController<PersonalQuestContent, IPersonalQuestContentService> {
	@Autowired
	private IPersonalQuestContentService personalQuestContentService;
	
	/**
	 * 分页列表查询
	 *
	 * @param personalQuestContent
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "问卷答题详情-分页列表查询")
	@ApiOperation(value="问卷答题详情-分页列表查询", notes="问卷答题详情-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PersonalQuestContent>> queryPageList(PersonalQuestContent personalQuestContent,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<PersonalQuestContent> queryWrapper = QueryGenerator.initQueryWrapper(personalQuestContent, req.getParameterMap());
		Page<PersonalQuestContent> page = new Page<PersonalQuestContent>(pageNo, pageSize);
		IPage<PersonalQuestContent> pageList = personalQuestContentService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param personalQuestContent
	 * @return
	 */
	@AutoLog(value = "问卷答题详情-添加")
	@ApiOperation(value="问卷答题详情-添加", notes="问卷答题详情-添加")
	@RequiresPermissions("quest:personal_quest_content:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PersonalQuestContent personalQuestContent) {
		personalQuestContentService.save(personalQuestContent);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param personalQuestContent
	 * @return
	 */
	@AutoLog(value = "问卷答题详情-编辑")
	@ApiOperation(value="问卷答题详情-编辑", notes="问卷答题详情-编辑")
	@RequiresPermissions("quest:personal_quest_content:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PersonalQuestContent personalQuestContent) {
		personalQuestContentService.updateById(personalQuestContent);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "问卷答题详情-通过id删除")
	@ApiOperation(value="问卷答题详情-通过id删除", notes="问卷答题详情-通过id删除")
	@RequiresPermissions("quest:personal_quest_content:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		personalQuestContentService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "问卷答题详情-批量删除")
	@ApiOperation(value="问卷答题详情-批量删除", notes="问卷答题详情-批量删除")
	@RequiresPermissions("quest:personal_quest_content:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.personalQuestContentService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "问卷答题详情-通过id查询")
	@ApiOperation(value="问卷答题详情-通过id查询", notes="问卷答题详情-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PersonalQuestContent> queryById(@RequestParam(name="id",required=true) String id) {
		PersonalQuestContent personalQuestContent = personalQuestContentService.getById(id);
		if(personalQuestContent==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(personalQuestContent);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param personalQuestContent
    */
    @RequiresPermissions("quest:personal_quest_content:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PersonalQuestContent personalQuestContent) {
        return super.exportXls(request, personalQuestContent, PersonalQuestContent.class, "问卷答题详情");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("quest:personal_quest_content:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PersonalQuestContent.class);
    }

}
