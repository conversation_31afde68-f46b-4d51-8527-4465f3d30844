<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.quest.mapper.PersonalQuestionnaireResultsMapper">

    <select id="getCustomerRegByPersonalQuesId" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select r.* from customer_reg r join personal_questionnaire q on r.id=q.personal_id where q.id=#{personalQuesId}
    </select>
    <select id="getCustomerRegResultByQuestId"
            resultType="org.jeecg.modules.station.entity.CustomerRegItemResult">
        select r.value,
               r.abnormal_flag,
               r.abnormal_flag_desc,
               r.check_conclusion,
               r.exam_no,
               r.id,
               r.customer_reg_id
                from customer_reg_item_result r join questionnaire_definition q on r.item_his_code=q.code where q.id=#{questId} and r.customer_reg_id=#{regId}
    </select>
    <select id="getQuestResultByPersonalQuestId"
            resultType="org.jeecg.modules.station.entity.CustomerRegItemResult">
        select r.value,
               r.abnormal_flag,
               r.abnormal_flag_desc,
               r.check_conclusion,
               r.exam_no,
               r.id,
               r.customer_reg_id,
               q.advise_text as psyAdviceText,
               q.id as personalQuestId,
               q.ref_range as psyRefRange
        from customer_reg_item_result r join personal_questionnaire q on r.check_bill_no=q.id where q.id=#{personalQuestId}
    </select>
    <select id="getQuesDefinitionByPersonalQuestId"
            resultType="org.jeecg.modules.quest.entity.QuestionnaireDefinition">
        select d.*
        from questionnaire_definition d join personal_questionnaire q on d.id=q.questionnaire_id where q.id=#{personalQuestId}
    </select>

</mapper>