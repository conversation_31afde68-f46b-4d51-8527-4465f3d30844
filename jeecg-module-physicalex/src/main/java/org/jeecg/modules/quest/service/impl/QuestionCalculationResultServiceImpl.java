package org.jeecg.modules.quest.service.impl;

import org.jeecg.modules.quest.entity.QuestionCalculationResult;
import org.jeecg.modules.quest.mapper.QuestionCalculationResultMapper;
import org.jeecg.modules.quest.service.IQuestionCalculationResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 问卷计算结果
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Service
public class QuestionCalculationResultServiceImpl extends ServiceImpl<QuestionCalculationResultMapper, QuestionCalculationResult> implements IQuestionCalculationResultService {
@Autowired
private QuestionCalculationResultMapper questionCalculationResultMapper;

    @Override
    public List<QuestionCalculationResult> getCalResultByCalId(String calId) {
        return questionCalculationResultMapper.getCalResultByCalId(calId);
    }
}
