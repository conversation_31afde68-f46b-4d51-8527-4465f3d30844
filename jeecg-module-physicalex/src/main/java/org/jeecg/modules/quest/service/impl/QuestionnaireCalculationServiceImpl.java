package org.jeecg.modules.quest.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.quest.entity.QuestionCalculationResult;
import org.jeecg.modules.quest.entity.QuestionnaireCalculation;
import org.jeecg.modules.quest.mapper.QuestionCalculationResultMapper;
import org.jeecg.modules.quest.mapper.QuestionnaireCalculationMapper;
import org.jeecg.modules.quest.service.IQuestionnaireCalculationService;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 问卷计算维度
 * @Author: jeecg-boot
 * @Date: 2024-11-13
 * @Version: V1.0
 */
@Service
public class QuestionnaireCalculationServiceImpl extends ServiceImpl<QuestionnaireCalculationMapper, QuestionnaireCalculation> implements IQuestionnaireCalculationService {

    @Autowired
    private QuestionnaireCalculationMapper questionnaireCalculationMapper;
    @Autowired
    private QuestionCalculationResultMapper questionCalculationResultMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<QuestionnaireCalculation> selectByMainId(String mainId) {
        return questionnaireCalculationMapper.selectByMainId(mainId);
    }

    @Override
    public void saveOrUpdateCalculation(QuestionnaireCalculation questionnaireCalculation) {
        List<QuestionCalculationResult> resultList = questionnaireCalculation.getResultList();
        if (StringUtils.isNotBlank(questionnaireCalculation.getId())) {
            this.updateById(questionnaireCalculation);
        } else {
            this.save(questionnaireCalculation);
        }

        resultList.forEach(result -> {
            result.setQuestionnaireId(questionnaireCalculation.getQuestionnaireId());
            result.setCalculationId(questionnaireCalculation.getId());
            if (StringUtils.isNotBlank(result.getId())) {
                questionCalculationResultMapper.updateById(result);
            } else {
                questionCalculationResultMapper.insert(result);
            }
        });
    }
}
