package org.jeecg.modules.quest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.quest.entity.TopicOptions;
import org.jeecg.modules.quest.mapper.TopicOptionsMapper;
import org.jeecg.modules.quest.service.ITopicOptionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 问卷题目选项
 * @Author: jeecg-boot
 * @Date:   2024-11-19
 * @Version: V1.0
 */
@Service
public class TopicOptionsServiceImpl extends ServiceImpl<TopicOptionsMapper, TopicOptions> implements ITopicOptionsService {
@Autowired
private TopicOptionsMapper topicOptionsMapper;

    @Override
    public List<TopicOptions> listByQuestContentId(String questContentId) {

        return topicOptionsMapper.listByQuestContentId(questContentId);
    }
}
