package org.jeecg.modules.quest.service;

import org.jeecg.modules.quest.entity.QuestionnaireCalculation;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 问卷计算维度
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface IQuestionnaireCalculationService extends IService<QuestionnaireCalculation> {

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId
   * @return List<QuestionnaireCalculation>
   */
	public List<QuestionnaireCalculation> selectByMainId(String mainId);

    void saveOrUpdateCalculation(QuestionnaireCalculation questionnaireCalculation);
}
