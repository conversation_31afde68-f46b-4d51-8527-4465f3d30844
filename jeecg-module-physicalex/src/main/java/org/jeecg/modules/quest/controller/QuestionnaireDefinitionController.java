package org.jeecg.modules.quest.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.system.query.QueryGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.system.query.QueryRuleEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.quest.entity.PersonalQuestionnaire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.HashMap;

import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.quest.entity.QuestionnaireContent;
import org.jeecg.modules.quest.entity.QuestionnaireCalculation;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;
import org.jeecg.modules.quest.service.IQuestionnaireDefinitionService;
import org.jeecg.modules.quest.service.IQuestionnaireContentService;
import org.jeecg.modules.quest.service.IQuestionnaireCalculationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 问卷定义
 * @Author: jeecg-boot
 * @Date: 2024-11-13
 * @Version: V1.0
 */
@Api(tags = "问卷定义")
@RestController
@RequestMapping("/quest/questionnaireDefinition")
@Slf4j
public class QuestionnaireDefinitionController extends JeecgController<QuestionnaireDefinition, IQuestionnaireDefinitionService> {

    @Autowired
    private IQuestionnaireDefinitionService questionnaireDefinitionService;

    @Autowired
    private IQuestionnaireContentService questionnaireContentService;

    @Autowired
    private IQuestionnaireCalculationService questionnaireCalculationService;

    /*---------------------------------主表处理-begin-------------------------------------*/

    @AutoLog(value = "问卷定义-根据ID获取")
    @ApiOperation(value = "问卷定义-根据ID获取", notes = "问卷定义-根据ID获取")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(String id) {
        QuestionnaireDefinition definition = questionnaireDefinitionService.getById(id);
        if (definition == null) {
            return Result.error("未找到问卷定义");
        }

        return Result.OK(definition);
    }

    @AutoLog(value = "问卷定义-根据ID获取填充了统计信息的问卷定义")
    @ApiOperation(value = "问卷定义-根据ID获取填充了统计信息的问卷定义", notes = "问卷定义-根据ID获取填充了统计信息的问卷定义")
    @GetMapping(value = "/queryFilledById")
    public Result<?> queryFilledById(String id) {
        QuestionnaireDefinition definition = questionnaireDefinitionService.getFilledDefinition(id);
        if (definition == null) {
            return Result.error("未找到问卷定义");
        }

        return Result.OK(definition);
    }

    /**
     * 分页列表查询
     *
     * @param questionnaireDefinition
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "问卷定义-分页列表查询")
    @ApiOperation(value = "问卷定义-分页列表查询", notes = "问卷定义-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<QuestionnaireDefinition>> queryPageList(QuestionnaireDefinition questionnaireDefinition, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<QuestionnaireDefinition> queryWrapper = QueryGenerator.initQueryWrapper(questionnaireDefinition, req.getParameterMap());
        Page<QuestionnaireDefinition> page = new Page<QuestionnaireDefinition>(pageNo, pageSize);
        IPage<QuestionnaireDefinition> pageList = questionnaireDefinitionService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param questionnaireDefinition
     * @return
     */
    @AutoLog(value = "问卷定义-添加")
    @ApiOperation(value = "问卷定义-添加", notes = "问卷定义-添加")
    @RequiresPermissions("quest:questionnaire_definition:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody QuestionnaireDefinition questionnaireDefinition) {
        questionnaireDefinitionService.save(questionnaireDefinition);
        //questionnaireDefinitionService.clearCache();

        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param questionnaireDefinition
     * @return
     */
    @AutoLog(value = "问卷定义-编辑")
    @ApiOperation(value = "问卷定义-编辑", notes = "问卷定义-编辑")
    @RequiresPermissions("quest:questionnaire_definition:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody QuestionnaireDefinition questionnaireDefinition) {
        questionnaireDefinitionService.updateById(questionnaireDefinition);
        questionnaireDefinitionService.clearCache();
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "问卷定义-通过id删除")
    @ApiOperation(value = "问卷定义-通过id删除", notes = "问卷定义-通过id删除")
    @RequiresPermissions("quest:questionnaire_definition:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        questionnaireDefinitionService.delMain(id);
        questionnaireDefinitionService.clearCache();
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "问卷定义-批量删除")
    @ApiOperation(value = "问卷定义-批量删除", notes = "问卷定义-批量删除")
    @RequiresPermissions("quest:questionnaire_definition:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.questionnaireDefinitionService.delBatchMain(Arrays.asList(ids.split(",")));
        questionnaireDefinitionService.clearCache();
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequiresPermissions("quest:questionnaire_definition:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QuestionnaireDefinition questionnaireDefinition) {
        return super.exportXls(request, questionnaireDefinition, QuestionnaireDefinition.class, "问卷定义");
    }

    /**
     * 导入
     *
     * @return
     */
    @RequiresPermissions("quest:questionnaire_definition:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QuestionnaireDefinition.class);
    }
    /*---------------------------------主表处理-end-------------------------------------*/


    /*--------------------------------子表处理-题目列表-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "题目列表-通过主表ID查询")
    @ApiOperation(value = "题目列表-通过主表ID查询", notes = "题目列表-通过主表ID查询")
    @GetMapping(value = "/listQuestionnaireContentByMainId")
    public Result<IPage<QuestionnaireContent>> listQuestionnaireContentByMainId(QuestionnaireContent questionnaireContent, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<QuestionnaireContent> queryWrapper = QueryGenerator.initQueryWrapper(questionnaireContent, req.getParameterMap());
        Page<QuestionnaireContent> page = new Page<QuestionnaireContent>(pageNo, pageSize);
        IPage<QuestionnaireContent> pageList = questionnaireContentService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param questionnaireContent
     * @return
     */
    @AutoLog(value = "题目列表-添加")
    @ApiOperation(value = "题目列表-添加", notes = "题目列表-添加")
    @PostMapping(value = "/addQuestionnaireContent")
    public Result<String> addQuestionnaireContent(@RequestBody QuestionnaireContent questionnaireContent) {
        questionnaireContentService.saveOrUpdateQuestContent(questionnaireContent);
        questionnaireDefinitionService.clearCache();
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param questionnaireContent
     * @return
     */
    @AutoLog(value = "题目列表-编辑")
    @ApiOperation(value = "题目列表-编辑", notes = "题目列表-编辑")
    @RequestMapping(value = "/editQuestionnaireContent", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editQuestionnaireContent(@RequestBody QuestionnaireContent questionnaireContent) {
        questionnaireContentService.saveOrUpdateQuestContent(questionnaireContent);
        questionnaireDefinitionService.clearCache();
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "题目列表-通过id删除")
    @ApiOperation(value = "题目列表-通过id删除", notes = "题目列表-通过id删除")
    @DeleteMapping(value = "/deleteQuestionnaireContent")
    public Result<String> deleteQuestionnaireContent(@RequestParam(name = "id", required = true) String id) {
        questionnaireContentService.removeById(id);
        questionnaireDefinitionService.clearCache();
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "题目列表-批量删除")
    @ApiOperation(value = "题目列表-批量删除", notes = "题目列表-批量删除")
    @DeleteMapping(value = "/deleteBatchQuestionnaireContent")
    public Result<String> deleteBatchQuestionnaireContent(@RequestParam(name = "ids", required = true) String ids) {
        this.questionnaireContentService.removeByIds(Arrays.asList(ids.split(",")));
        questionnaireDefinitionService.clearCache();
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportQuestionnaireContent")
    public ModelAndView exportQuestionnaireContent(HttpServletRequest request, QuestionnaireContent questionnaireContent) {
        // Step.1 组装查询条件
        QueryWrapper<QuestionnaireContent> queryWrapper = QueryGenerator.initQueryWrapper(questionnaireContent, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<QuestionnaireContent> pageList = questionnaireContentService.list(queryWrapper);
        List<QuestionnaireContent> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "题目列表");
        mv.addObject(NormalExcelConstants.CLASS, QuestionnaireContent.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("题目列表报表", "导出人:" + sysUser.getRealname(), "题目列表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importQuestionnaireContent/{mainId}")
    public Result<?> importQuestionnaireContent(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<QuestionnaireContent> list = ExcelImportUtil.importExcel(file.getInputStream(), QuestionnaireContent.class, params);
                for (QuestionnaireContent temp : list) {
                    temp.setDefinitionId(mainId);
                }
                long start = System.currentTimeMillis();
                questionnaireContentService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-题目列表-end----------------------------------------------*/

    /*--------------------------------子表处理-问卷计算维度-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "问卷计算维度-通过主表ID查询")
    @ApiOperation(value = "问卷计算维度-通过主表ID查询", notes = "问卷计算维度-通过主表ID查询")
    @GetMapping(value = "/listQuestionnaireCalculationByMainId")
    public Result<IPage<QuestionnaireCalculation>> listQuestionnaireCalculationByMainId(QuestionnaireCalculation questionnaireCalculation, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<QuestionnaireCalculation> queryWrapper = QueryGenerator.initQueryWrapper(questionnaireCalculation, req.getParameterMap());
        Page<QuestionnaireCalculation> page = new Page<QuestionnaireCalculation>(pageNo, pageSize);
        IPage<QuestionnaireCalculation> pageList = questionnaireCalculationService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param questionnaireCalculation
     * @return
     */
    @AutoLog(value = "问卷计算维度-添加")
    @ApiOperation(value = "问卷计算维度-添加", notes = "问卷计算维度-添加")
    @PostMapping(value = "/addQuestionnaireCalculation")
    public Result<String> addQuestionnaireCalculation(@RequestBody QuestionnaireCalculation questionnaireCalculation) {
        questionnaireCalculationService.saveOrUpdateCalculation(questionnaireCalculation);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param questionnaireCalculation
     * @return
     */
    @AutoLog(value = "问卷计算维度-编辑")
    @ApiOperation(value = "问卷计算维度-编辑", notes = "问卷计算维度-编辑")
    @RequestMapping(value = "/editQuestionnaireCalculation", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editQuestionnaireCalculation(@RequestBody QuestionnaireCalculation questionnaireCalculation) {
        questionnaireCalculationService.saveOrUpdateCalculation(questionnaireCalculation);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "问卷计算维度-通过id删除")
    @ApiOperation(value = "问卷计算维度-通过id删除", notes = "问卷计算维度-通过id删除")
    @DeleteMapping(value = "/deleteQuestionnaireCalculation")
    public Result<String> deleteQuestionnaireCalculation(@RequestParam(name = "id", required = true) String id) {
        questionnaireCalculationService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "问卷计算维度-批量删除")
    @ApiOperation(value = "问卷计算维度-批量删除", notes = "问卷计算维度-批量删除")
    @DeleteMapping(value = "/deleteBatchQuestionnaireCalculation")
    public Result<String> deleteBatchQuestionnaireCalculation(@RequestParam(name = "ids", required = true) String ids) {
        this.questionnaireCalculationService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportQuestionnaireCalculation")
    public ModelAndView exportQuestionnaireCalculation(HttpServletRequest request, QuestionnaireCalculation questionnaireCalculation) {
        // Step.1 组装查询条件
        QueryWrapper<QuestionnaireCalculation> queryWrapper = QueryGenerator.initQueryWrapper(questionnaireCalculation, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<QuestionnaireCalculation> pageList = questionnaireCalculationService.list(queryWrapper);
        List<QuestionnaireCalculation> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "问卷计算维度");
        mv.addObject(NormalExcelConstants.CLASS, QuestionnaireCalculation.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("问卷计算维度报表", "导出人:" + sysUser.getRealname(), "问卷计算维度"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importQuestionnaireCalculation/{mainId}")
    public Result<?> importQuestionnaireCalculation(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<QuestionnaireCalculation> list = ExcelImportUtil.importExcel(file.getInputStream(), QuestionnaireCalculation.class, params);
                for (QuestionnaireCalculation temp : list) {
                    temp.setQuestionnaireId(mainId);
                }
                long start = System.currentTimeMillis();
                questionnaireCalculationService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-问卷计算维度-end----------------------------------------------*/
    @AutoLog(value = "获取问卷")
    @ApiOperation(value = "获取问卷", notes = "获取问卷")
    @GetMapping(value = "/getQuestContentByQuestId")
    public Result<?> getQuestContentByQuestId(@RequestParam(name = "id", required = true) String id) {

        List<QuestionnaireContent> contentList = questionnaireDefinitionService.getQuestContents(id);
        return Result.OK(contentList);
    }

    /**
     * 根据module获取问卷列表
     */
    @AutoLog(value = "问卷答题-根据module获取问卷列表")
    @ApiOperation(value = "问卷答题-根据module获取问卷列表", notes = "问卷答题-根据module获取问卷列表")
    @GetMapping(value = "/listByModual")
    public Result<?> listByModual(@RequestParam(name = "module", required = true) String module) {
        List<QuestionnaireDefinition> list = questionnaireDefinitionService.listByModual(module);
        return Result.OK(list);
    }

    /**
     * getNextQuestSeq
     */
    @AutoLog(value = "问卷答题-获取下一个题目序号")
    @ApiOperation(value = "问卷答题-获取下一个题目序号", notes = "问卷答题-获取下一个题目序号")
    @GetMapping(value = "/getNextQuestSeq")
    public Result<?> getNextQuestSeq(@RequestParam(name = "questId", required = true) String questId) {
        Integer seq = questionnaireContentService.getNextQuestSeq(questId);
        return Result.OK(seq);
    }

    /**
     * 问卷统计
     *
     * @return
     */
    @AutoLog(value = "问卷定义-问卷统计")
    @ApiOperation(value = "问卷定义-问卷统计", notes = "问卷定义-问卷统计")
    @GetMapping(value = "/stats")
    public Result<?> stats(String tenantId, String questId) {
        JSONObject stat = questionnaireDefinitionService.statQuestContents(questId, tenantId);
        return Result.OK(stat);
    }

    @AutoLog(value = "获取问卷")
    @ApiOperation(value = "获取问卷", notes = "获取问卷")
    @GetMapping(value = "/getQuestionnaire")
    public Result<?> getQuestionnaire(@RequestParam(name = "id", required = true) String id) {

        List<QuestionnaireContent> questContents = questionnaireDefinitionService.getQuestContents(id);
        return Result.OK(questContents);
    }
}
