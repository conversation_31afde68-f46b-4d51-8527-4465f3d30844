package org.jeecg.modules.quest.service;

import org.jeecg.modules.quest.entity.PersonalQuestionnaireResults;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.quest.entity.ReportData;

/**
 * @Description: 问卷答题题目记录
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface IPersonalQuestionnaireResultsService extends IService<PersonalQuestionnaireResults> {
    ReportData getPsyReportData(String personalQuestionnaireId);
}
