package org.jeecg.modules.quest.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.quest.entity.QuestionnaireContent;
import org.jeecg.modules.quest.entity.TopicOptions;
import org.jeecg.modules.quest.mapper.QuestionnaireContentMapper;
import org.jeecg.modules.quest.mapper.TopicOptionsMapper;
import org.jeecg.modules.quest.service.IQuestionnaireContentService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 题目列表
 * @Author: jeecg-boot
 * @Date: 2024-11-13
 * @Version: V1.0
 */
@CacheConfig(cacheNames = "questDef")
@Service
public class QuestionnaireContentServiceImpl extends ServiceImpl<QuestionnaireContentMapper, QuestionnaireContent> implements IQuestionnaireContentService {

    @Autowired
    private QuestionnaireContentMapper questionnaireContentMapper;
    @Autowired
    private TopicOptionsMapper topicOptionsMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<QuestionnaireContent> selectByMainId(String mainId) {
        return questionnaireContentMapper.selectByMainId(mainId);
    }

    @CacheEvict(value = "questContents", allEntries = true)
    @Override
    public void saveOrUpdateQuestContent(QuestionnaireContent questionnaireContent) {
        List<TopicOptions> options = questionnaireContent.getOptions();
        if (StringUtils.isNotBlank(questionnaireContent.getId())) {
            questionnaireContentMapper.updateById(questionnaireContent);
        } else {
            questionnaireContentMapper.insert(questionnaireContent);
        }
        if (!(StringUtils.equals(questionnaireContent.getType(), "单选") || StringUtils.equals(questionnaireContent.getType(), "多选"))) {
            jdbcTemplate.update("delete from topic_options where content_id=?", questionnaireContent.getId());
        } else {
            if (options != null && !options.isEmpty()) {
                for (TopicOptions option : options) {
                    option.setContentId(questionnaireContent.getId());
                    option.setQuestionnaireId(questionnaireContent.getDefinitionId());
                    if (StringUtils.isNotBlank(option.getId())) {
                        topicOptionsMapper.updateById(option);
                    } else {
                        topicOptionsMapper.insert(option);
                    }
                }
            }
        }
    }

    @Override
    public Integer getNextQuestSeq(String questId) {
        return questionnaireContentMapper.getNextQuestSeq(questId);
    }
}
