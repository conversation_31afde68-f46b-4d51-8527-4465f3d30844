package org.jeecg.modules.quest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.quest.entity.PersonalQuestionnaire;
import org.jeecg.modules.quest.entity.PersonalQuestionnaireResults;
import org.jeecg.modules.quest.entity.ReportData;
import org.jeecg.modules.quest.mapper.PersonalQuestionnaireMapper;
import org.jeecg.modules.quest.mapper.PersonalQuestionnaireResultsMapper;
import org.jeecg.modules.quest.service.IPersonalQuestionnaireResultsService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description: 问卷答题题目记录
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
@Service
public class PersonalQuestionnaireResultsServiceImpl extends ServiceImpl<PersonalQuestionnaireResultsMapper, PersonalQuestionnaireResults> implements IPersonalQuestionnaireResultsService {
    @Autowired
    private PersonalQuestionnaireResultsMapper personalQuestionnaireResultsMapper;
    @Autowired
    private PersonalQuestionnaireMapper personalQuestionnaireMapper;
    @Override
    public ReportData getPsyReportData(String personalQuestionnaireId) {
        ReportData reportData = new ReportData();
        List<PersonalQuestionnaireResults> quesResults = personalQuestionnaireResultsMapper.selectList(new LambdaQueryWrapper<PersonalQuestionnaireResults>().eq(PersonalQuestionnaireResults::getPersonalQuestionnaireId,personalQuestionnaireId));
        if (CollectionUtils.isNotEmpty(quesResults)){

            Optional<PersonalQuestionnaireResults> firstNonEmptyQuestId = quesResults.stream()
                    .filter(result -> StringUtils.isNotBlank(result.getPersonalQuestionnaireId()))
                    .findFirst();
            PersonalQuestionnaire personalQuestionnaire=null;
            if (firstNonEmptyQuestId.isPresent()) {
                PersonalQuestionnaireResults result = firstNonEmptyQuestId.get();
                personalQuestionnaire = personalQuestionnaireMapper.selectById(result.getPersonalQuestionnaireId());
                CustomerReg customerReg = personalQuestionnaireResultsMapper.getCustomerRegByPersonalQuesId(result.getPersonalQuestionnaireId());
                reportData.setCustomerReg(customerReg);


                BigDecimal totalScore = quesResults.stream()
                        .filter(qr -> qr.getScore() != null && !qr.getScore().trim().isEmpty())
                        .map(qr -> new BigDecimal(qr.getScore().trim()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(1, BigDecimal.ROUND_HALF_UP);
                BigDecimal averageScore = totalScore.divide(BigDecimal.valueOf(quesResults.size()), BigDecimal.ROUND_HALF_UP).setScale(1, BigDecimal.ROUND_HALF_UP);

               quesResults.forEach(qr->{
                   try {
                       String scoreStr = qr.getScore();
                       if (scoreStr != null && !scoreStr.trim().isEmpty()) {
                           BigDecimal newScore = new BigDecimal(scoreStr.trim()).setScale(1, BigDecimal.ROUND_HALF_UP);
                           qr.setScore(String.valueOf(newScore));
                       }
                   } catch (NumberFormatException e) {
                       log.error("心理测评得分装换异常: " + qr.getScore());
                   }
               });
                reportData.setPersonalQuesResultsList(quesResults);
            if (Objects.nonNull(personalQuestionnaire)){
                CustomerRegItemResult itemResultResult = personalQuestionnaireResultsMapper.getCustomerRegResultByQuestId(personalQuestionnaire.getQuestionnaireId(),customerReg.getId());
                personalQuestionnaire.setFinalResult(Objects.nonNull(itemResultResult)?itemResultResult.getCheckConclusion():"");
                personalQuestionnaire.setFinalAdvice(personalQuestionnaire.getAdviseText());
                personalQuestionnaire.setTotalScore(totalScore);
                personalQuestionnaire.setAverageScore(averageScore);
                reportData.setPersonalQuestionnaire(personalQuestionnaire);
            }
            }

        }


        return reportData;
    }
}
