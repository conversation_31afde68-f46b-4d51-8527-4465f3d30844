package org.jeecg.modules.quest.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * @Description: 问卷计算维度
 * @Author: jeecg-boot
 * @Date: 2024-11-13
 * @Version: V1.0
 */
@Data
@TableName("questionnaire_calculation")
@ApiModel(value = "questionnaire_calculation对象", description = "问卷计算维度")
public class QuestionnaireCalculation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 维度
     */
    @Excel(name = "维度", width = 15)
    @ApiModelProperty(value = "维度")
    private java.lang.String dimension;
    /**
     * 显示选择
     */
    @Excel(name = "显示选择", width = 15)
    @ApiModelProperty(value = "显示选择")
    private java.lang.String displaySelection;
    /**
     * 显示结果(得分/结果）
     */
    @Excel(name = "显示结果(得分/结果）", width = 15)
    @ApiModelProperty(value = "显示结果(得分/结果）")
    private java.lang.String displayResult;
    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.Integer sortNumber;
    /**
     * 计算脚本
     */
    @Excel(name = "计算脚本", width = 15)
    @ApiModelProperty(value = "计算脚本")
    private java.lang.String calScript;
    /**
     * 问卷id
     */
    @ApiModelProperty(value = "问卷id")
    private java.lang.String questionnaireId;
    /**
     * 是否删除
     */
    @Excel(name = "是否删除", width = 15)
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    private java.lang.String delFlag;

    @TableField(exist = false)
    private List<QuestionCalculationResult> resultList;
}
