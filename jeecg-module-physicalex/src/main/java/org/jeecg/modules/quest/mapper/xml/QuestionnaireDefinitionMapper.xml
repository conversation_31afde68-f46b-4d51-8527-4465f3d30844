<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.quest.mapper.QuestionnaireDefinitionMapper">

    <select id="listByModual" resultType="org.jeecg.modules.quest.entity.QuestionnaireDefinition">
        SELECT
        *
        FROM
        questionnaire_definition
        WHERE
            enable_flag = '1' and del_flag='0' <if test="module!=null and module!=''"> and module = #{module}</if>
    </select>
</mapper>