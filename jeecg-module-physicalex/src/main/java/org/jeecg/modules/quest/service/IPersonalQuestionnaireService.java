package org.jeecg.modules.quest.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.quest.entity.PersonalQuestionnaire;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.quest.entity.PersonalQuestionnaireResults;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;
import org.jeecg.modules.summary.entity.AdviceBean;

import java.util.List;

/**
 * @Description: 问卷答题
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface IPersonalQuestionnaireService extends IService<PersonalQuestionnaire> {

    public PersonalQuestionnaire savePersonalQuestionnaire(JSONObject model);

    List<PersonalQuestionnaire> getByQuestId(String questId);

    List<PersonalQuestionnaire> getByQuestIdAndBizId(String questId, String bizId);

    PersonalQuestionnaire queryByQuestAndCard(String questId,String account);

    AdviceBean generatePsyFinalResultAndAdvice(List<PersonalQuestionnaireResults> resultsList, QuestionnaireDefinition definition) throws Exception;

}
