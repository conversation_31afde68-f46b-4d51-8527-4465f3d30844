package org.jeecg.modules.quest.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.ScriptCache;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.mapper.ItemInfoMapper;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.psy.entity.PsyCard;
import org.jeecg.modules.psy.entity.PsyCardSetting;
import org.jeecg.modules.psy.entity.QuestSuit;
import org.jeecg.modules.psy.mapper.PsyCardMapper;
import org.jeecg.modules.psy.mapper.QuestSuitMapper;
import org.jeecg.modules.psy.service.IPsyCardService;
import org.jeecg.modules.psy.service.IPsyCardSettingService;
import org.jeecg.modules.quest.entity.*;
import org.jeecg.modules.quest.mapper.*;
import org.jeecg.modules.quest.service.IPersonalQuestContentService;
import org.jeecg.modules.quest.service.IPersonalQuestionnaireService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.mapper.CustomerRegItemResultMapper;
import org.jeecg.modules.summary.entity.AdviceBean;
import org.jeecg.modules.summary.entity.SummaryAdvice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Description: 问卷答题
 * @Author: jeecg-boot
 * @Date: 2024-11-13
 * @Version: V1.0
 */
@Service
public class PersonalQuestionnaireServiceImpl extends ServiceImpl<PersonalQuestionnaireMapper, PersonalQuestionnaire> implements IPersonalQuestionnaireService {

    @Autowired
    private QuestionnaireCalculationMapper questionnaireCalculationMapper;
    @Autowired
    private PersonalQuestionnaireResultsMapper personalQuestionnaireResultsMapper;
    @Autowired
    private QuestionCalculationResultMapper questionCalculationResultMapper;
    @Autowired
    private IPersonalQuestContentService personalQuestContentService;
    @Autowired
    private PersonalQuestionnaireMapper personalQuestionnaireMapper;
    @Autowired
    private IPsyCardSettingService cardSettingService;
    @Autowired
    private PsyCardMapper psyCardMapper;
    @Autowired
    private QuestSuitMapper questSuitMapper;
    @Autowired
    private CustomerRegItemGroupMapper  customerRegItemGroupMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private QuestionnaireDefinitionMapper questionnaireDefinitionMapper;
    @Autowired
    private ItemInfoMapper itemInfoMapper;
    @Autowired
    private CustomerRegItemResultMapper customerRegItemResultMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private AIService aiService;


    // 缓存已编译的脚本
    // 设置缓存容量，例如最大缓存 100 个脚本
    private static final int CACHE_CAPACITY = 100;
    private final Map<String, Class<?>> scriptCache = new ScriptCache(CACHE_CAPACITY);

    private final GroovyClassLoader loader = new GroovyClassLoader();

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PersonalQuestionnaire savePersonalQuestionnaire(JSONObject model) {

        PersonalQuestionnaire personalQuestionnaire = new PersonalQuestionnaire();
        String personalQuestId = model.getString("personalQuestId");

        personalQuestionnaire.setId(personalQuestId);
        personalQuestionnaire.setTaskId(model.getString("taskId"));
        personalQuestionnaire.setPersonalId(model.getString("personalId"));
        personalQuestionnaire.setPersonalName(model.getString("personalName"));
        personalQuestionnaire.setVisitNo(model.getString("visitNo"));
        personalQuestionnaire.setQuestionnaireId(model.getString("questId"));
        personalQuestionnaire.setName(model.getString("name"));
        personalQuestionnaire.setTenantId(model.getString("tenantId"));
        personalQuestionnaire.setCompleteFlag("1");
        personalQuestionnaire.setModule(model.getString("module"));
        personalQuestionnaire.setCostSeconds(model.getString("costSeconds"));
        List<String> selectOptionList = new ArrayList<>();
        List<String> scoreList = new ArrayList<>();
        JSONArray contentList = model.getJSONArray("contentList");
        personalQuestionnaire.setContent(contentList.toJSONString());
        saveOrUpdate(personalQuestionnaire);

        List<PersonalQuestContent> personalQuestContentList = new ArrayList<>();
        List<String> dimensionRets= Lists.newArrayList();
        for (int i = 0; i < contentList.size(); i++) {

            JSONObject questContent = contentList.getJSONObject(i);
            String type = questContent.getString("type");
            String selectOptionId = null;
            String  questAnswer = questContent.getString("answerText");;

            if (StringUtils.equals(type, "单选")) {
                String selectOptionStr = questContent.getString("selectOption");
                if (StringUtils.isNotBlank(selectOptionStr)) {
                    selectOptionList.add(selectOptionStr);
                    selectOptionId = questContent.getString("selectOptionId");
                } else {
                    selectOptionList.add("");
                }
                String scoreStr = questContent.getString("score");
                if (StringUtils.isNotBlank(scoreStr)) {
                    scoreList.add(questContent.getString("score"));
                } else {
                    scoreList.add("0");
                }
            } else if (StringUtils.equals(type, "多选")) {
                String selectOptionStr = questContent.getString("selectOption");
                if (StringUtils.isNotBlank(selectOptionStr)) {
                    selectOptionList.add(selectOptionStr);
                    selectOptionId = questContent.getString("selectOptionId");
                } else {
                    selectOptionList.add("");
                }
                String scoreStr = questContent.getString("score");
                if (StringUtils.isNotBlank(scoreStr)) {
                    scoreList.add(questContent.getString("score"));
                } else {
                    scoreList.add("0");
                }
            }

            //保存个人问卷题目，方便统计
            PersonalQuestContent personalQuestContent = new PersonalQuestContent();
            personalQuestContent.setPersonalQuestId(personalQuestionnaire.getId());
            personalQuestContent.setSelectedOptionId(selectOptionId);
            personalQuestContent.setAnswerText(questAnswer);
            personalQuestContent.setPersonalQuestId(personalQuestionnaire.getId());
            personalQuestContent.setQuestId(personalQuestionnaire.getQuestionnaireId());
            personalQuestContent.setQuestContentId(questContent.getString("id"));
            personalQuestContent.setTenantId(personalQuestionnaire.getTenantId());
            personalQuestContent.setQuestType(type);
            personalQuestContent.setQuestContent(questContent.getString("questContent"));
            personalQuestContentList.add(personalQuestContent);
        }

        personalQuestContentService.saveBatch(personalQuestContentList);

        QueryWrapper<QuestionnaireCalculation> queryWrapper = new QueryWrapper<QuestionnaireCalculation>();
//        queryWrapper.eq("questionnaire_id", model.get("id"));
        queryWrapper.eq("questionnaire_id", model.get("questId"));
        queryWrapper.eq("del_flag", "0");
        List<QuestionnaireCalculation> questCalculationList = questionnaireCalculationMapper.selectList(queryWrapper);

        for (QuestionnaireCalculation calculation : questCalculationList) {

            try {
                // 从缓存中获取已编译的脚本
                String cacheKey = calculation.getId() + StringUtils.stripToEmpty(DateUtil.formatTime(calculation.getUpdateTime()));
                Class<?> groovyClass = scriptCache.get(cacheKey);
                if (groovyClass == null) {
                    synchronized (this) {
                        groovyClass = scriptCache.get(cacheKey);
                        if (groovyClass == null) {
                            groovyClass = loader.parseClass(calculation.getCalScript());
                            scriptCache.put(cacheKey, groovyClass);
                        }
                    }
                }

                Object[] param = {selectOptionList, scoreList};
                String calculationResult = null;
                try {
                    GroovyObject groovyObject = (GroovyObject) groovyClass.getDeclaredConstructor().newInstance();
                    calculationResult = (String) groovyObject.invokeMethod("calculation", param);
                } catch (InstantiationException | IllegalAccessException e) {
                    log.error(e.getMessage(), e);
                }

                if (null != calculationResult) {
                    PersonalQuestionnaireResults personResults = new PersonalQuestionnaireResults();
                    List<QuestionCalculationResult> questionCalculationResultList = questionCalculationResultMapper.getCalResultByCalId(calculation.getId());
                    personResults.setPersonalQuestionnaireId(personalQuestionnaire.getId());

                    personResults.setQuestionnaireCalculationId(calculation.getId());
                    personResults.setDisplayType(calculation.getDisplaySelection());
                    personResults.setScore(calculationResult);
                    personResults.setTitle(calculation.getDimension());
                    personResults.setUnit(calculation.getDisplayResult());
                    personResults.setOrderNo(calculation.getSortNumber());

                    if (!questionCalculationResultList.isEmpty()) {
                        for (QuestionCalculationResult qResult : questionCalculationResultList) {
                            String calResultGroovyScript = qResult.getGroovyScript();
                            boolean resultMatched = false;
                            if (StringUtils.isNotBlank(calResultGroovyScript)) {
                                String cacheKey2 = qResult.getId() + StringUtils.stripToEmpty(DateUtil.formatTime(qResult.getUpdateTime()));
                                Class<?> groovyClass2 = scriptCache.get(cacheKey2);
                                if (groovyClass2 == null) {
                                    synchronized (this) {
                                        groovyClass2 = scriptCache.get(cacheKey2);
                                        if (groovyClass2 == null) {
                                            groovyClass2 = loader.parseClass(calResultGroovyScript);
                                            scriptCache.put(cacheKey2, groovyClass2);
                                        }
                                    }
                                }

                                try {
                                    GroovyObject groovyObjectResult = (GroovyObject) groovyClass2.getDeclaredConstructor().newInstance();
                                    resultMatched = (boolean) groovyObjectResult.invokeMethod("isMatch", calculationResult);
                                } catch (InstantiationException | IllegalAccessException e) {
                                    log.error(e.getMessage(), e);
                                }
                            }

                            if (resultMatched) {
                                personResults.setResult(qResult.getResult());
                                personResults.setAdviseText(qResult.getAdviseText());
                            }
                        }
                    }
                    personalQuestionnaireResultsMapper.insert(personResults);
                    if (StringUtils.isNotBlank(personResults.getResult())) {
                        dimensionRets.add(personResults.getResult());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage(), e);
            }
        }

        CustomerReg customerReg = customerRegMapper.selectOne(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getId, model.getString("personalId")));
        if (Objects.nonNull(customerReg)) {
            CustomerRegItemGroup customerRegItemGroup = customerRegItemGroupMapper.selectOne(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getHisCode,model.getString("suitCode")).eq(CustomerRegItemGroup::getCustomerRegId,customerReg.getId()).last("limit 1"));
            if (Objects.nonNull(customerRegItemGroup)) {
                //存储customer_reg_item_result结果
                CustomerRegItemResult customerRegItemResult = new CustomerRegItemResult();
                customerRegItemResult.setCustomerRegId(customerReg.getId());
                customerRegItemResult.setExamNo(customerReg.getExamNo());
                customerRegItemResult.setItemGroupId(customerRegItemGroup.getItemGroupId());
                customerRegItemResult.setItemGroupName(customerRegItemGroup.getItemGroupName());
                customerRegItemResult.setGroupHisCode(customerRegItemGroup.getHisCode());
                customerRegItemResult.setDepartmentId(customerRegItemGroup.getDepartmentId());
                customerRegItemResult.setCheckDepartmentCode(customerRegItemGroup.getDepartmentCode());
                customerRegItemResult.setCheckDepartmentName(customerRegItemGroup.getDepartmentName());
                customerRegItemResult.setCheckBillNo(personalQuestionnaire.getId());
                ItemInfo itemInfo = itemInfoMapper.selectOne(new LambdaQueryWrapper<ItemInfo>().eq(ItemInfo::getHisCode, model.getString("code")));
                if (Objects.nonNull(itemInfo)){
                    customerRegItemResult.setItemId(itemInfo.getId());
                    customerRegItemResult.setItemCode(itemInfo.getHisCode());
                    customerRegItemResult.setItemName(itemInfo.getName());
                    customerRegItemResult.setItemHisCode(itemInfo.getHisCode());
                    customerRegItemResult.setItemHisName(itemInfo.getHisName());
                }
                customerRegItemResult.setCheckConclusion(StringUtils.join(dimensionRets,";"));
                customerRegItemResult.setCreateTime(new Date());
                customerRegItemResult.setUpdateTime(new Date());
                String existId=null;
                try {
                    existId = jdbcTemplate.queryForObject("select id from customer_reg_item_result where customer_reg_id=? and item_his_code=?", String.class, customerReg.getId(), model.getString("code"));
                } catch (Exception e) {
                }
                if (StringUtils.isNotBlank(existId)) {
                    customerRegItemResult.setId(existId);
                    customerRegItemResultMapper.updateById(customerRegItemResult);
                }else {
                    customerRegItemResultMapper.insert(customerRegItemResult);
                }

                List<QuestionnaireDefinition> questList = questSuitMapper.getQuestListBySuitCode(customerRegItemGroup.getHisCode());
                List<String> questIds = questList.stream().map(QuestionnaireDefinition::getId).toList();
                List<PersonalQuestionnaire> personalQuests = personalQuestionnaireMapper.selectList(new LambdaQueryWrapper<PersonalQuestionnaire>().eq(PersonalQuestionnaire::getPersonalId, model.getString("personalId")));
                List<String> personalQuestIds = personalQuests.stream().map(PersonalQuestionnaire::getQuestionnaireId).toList();
                boolean containsAll = CollectionUtils.containsAll(personalQuestIds, questIds);
                if (containsAll) {
                   //更新customr_reg_item_group的检查状态
                    customerRegItemGroup.setCheckStatus(ExConstants.CHECK_STATUS_已检);
                    customerRegItemGroup.setCheckTime(new Date());
                    customerRegItemGroup.setUpdateTime(new Date());
                    customerRegItemGroupMapper.updateById(customerRegItemGroup);
                }
            }
        }
        return personalQuestionnaire;
    }

    @Override
    public List<PersonalQuestionnaire> getByQuestId(String questId) {
        return personalQuestionnaireMapper.getByQuestId(questId);
    }

    @Override
    public List<PersonalQuestionnaire> getByQuestIdAndBizId(String questId, String bizId) {
        return personalQuestionnaireMapper.getByQuestIdAndBizId(questId, bizId);
    }

    @Override
    public PersonalQuestionnaire queryByQuestAndCard(String questId, String account) {
        return personalQuestionnaireMapper.queryByQuestAndCard(questId, account);
    }

    @Override
    public AdviceBean generatePsyFinalResultAndAdvice(List<PersonalQuestionnaireResults> resultsList, QuestionnaireDefinition definition) throws Exception {
        if (CollectionUtils.isEmpty(resultsList)) {
            return new AdviceBean();
        }

        if (definition == null) {
            return new AdviceBean();
        }

        AdviceBean summaryAdvice = new AdviceBean();

            String useAi = sysSettingService.getValueByCode("ai_enabled");
            if (!org.apache.commons.lang.StringUtils.equals(useAi, "1")) {
                throw new RuntimeException("AI总检未启用！");
            }

            try {
                summaryAdvice= aiService.generatePsyResultAndAdvice(resultsList,definition);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        return summaryAdvice;
    }
}
