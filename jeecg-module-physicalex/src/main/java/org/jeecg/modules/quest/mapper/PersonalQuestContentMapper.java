package org.jeecg.modules.quest.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.quest.entity.PersonalQuestContent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 问卷答题详情
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface PersonalQuestContentMapper extends BaseMapper<PersonalQuestContent> {

    List<PersonalQuestContent> selectByPersonalQuestId(@Param("personalQuestId") String personalQuestId);
}
