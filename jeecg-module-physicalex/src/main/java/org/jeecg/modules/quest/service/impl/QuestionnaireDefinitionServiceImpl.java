package org.jeecg.modules.quest.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.index.*;
import org.apache.lucene.search.*;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.apache.lucene.util.BytesRef;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;
import org.jeecg.modules.quest.entity.QuestionnaireContent;
import org.jeecg.modules.quest.entity.QuestionnaireCalculation;
import org.jeecg.modules.quest.entity.TopicOptions;
import org.jeecg.modules.quest.mapper.QuestionnaireContentMapper;
import org.jeecg.modules.quest.mapper.QuestionnaireCalculationMapper;
import org.jeecg.modules.quest.mapper.QuestionnaireDefinitionMapper;
import org.jeecg.modules.quest.mapper.TopicOptionsMapper;
import org.jeecg.modules.quest.service.IQuestionnaireDefinitionService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.nio.file.FileSystems;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Collection;
import java.util.Map;

/**
 * @Description: 问卷定义
 * @Author: jeecg-boot
 * @Date: 2024-11-13
 * @Version: V1.0
 */
@CacheConfig(cacheNames = "questDef")
@Service
public class QuestionnaireDefinitionServiceImpl extends ServiceImpl<QuestionnaireDefinitionMapper, QuestionnaireDefinition> implements IQuestionnaireDefinitionService {

    @Autowired
    private QuestionnaireDefinitionMapper questionnaireDefinitionMapper;
    @Autowired
    private QuestionnaireContentMapper questionnaireContentMapper;
    @Autowired
    private QuestionnaireCalculationMapper questionnaireCalculationMapper;
    @Autowired
    private TopicOptionsMapper topicOptionsMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Value("${biz.lucene.path}")
    private String lucenePath;

    @Override
    public QuestionnaireDefinition getFilledDefinition(String id) {
        QuestionnaireDefinition definition = getById(id);

        Long testCount = jdbcTemplate.queryForObject("select count(1) from personal_questionnaire where questionnaire_id=?", Long.class, id);
        Integer dimensionCount = jdbcTemplate.queryForObject("select count(1) from questionnaire_calculation where questionnaire_id=?", Integer.class, id);
        Integer qeustionCount = jdbcTemplate.queryForObject("select count(1) from questionnaire_content where definition_id=?", Integer.class, id);

        definition.setTestCount(testCount);
        definition.setDimensionCount(dimensionCount);
        definition.setQeustionCount(qeustionCount);

        return definition;
    }

    @Cacheable(value = "questContents", key = "#questId", unless = "#result == null")
    @Override
    public List<QuestionnaireContent> getQuestContents(String questId) {
        List<QuestionnaireContent> questContentList = questionnaireContentMapper.selectByMainId(questId);
        for (QuestionnaireContent questContent : questContentList) {

            List<TopicOptions> topicOptionsList = topicOptionsMapper.listByQuestContentId(questContent.getId());
            questContent.setOptions(topicOptionsList);
        }
        return questContentList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        questionnaireContentMapper.deleteByMainId(id);
        questionnaireCalculationMapper.deleteByMainId(id);
        questionnaireDefinitionMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            questionnaireContentMapper.deleteByMainId(id.toString());
            questionnaireCalculationMapper.deleteByMainId(id.toString());
            questionnaireDefinitionMapper.deleteById(id);
        }
    }

    @Override
    public List<QuestionnaireDefinition> listByModual(String module) {
        return questionnaireDefinitionMapper.listByModual(module);
    }

    @CacheEvict(value = "questContents", allEntries = true)
    @Override
    public void clearCache() {

    }

    public Map<String, Integer> getWordFrequency(String questId) throws IOException {
        Map<String, Integer> wordFreq = new HashMap<>();

        // 1. 创建索引读取器
        Directory directory = FSDirectory.open(FileSystems.getDefault().getPath(lucenePath));
        IndexReader reader = DirectoryReader.open(directory);
        IndexSearcher searcher = new IndexSearcher(reader);

        // 2. 构建查询，限定 questId
        Query query = new TermQuery(new Term("questId", questId));

        // 3. 搜索匹配的文档
        TopDocs topDocs = searcher.search(query, Integer.MAX_VALUE);

        // 4. 遍历文档，统计词频
        for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
            int docId = scoreDoc.doc;
            Terms terms = reader.getTermVector(docId, "answerText");
            if (terms != null) {
                TermsEnum termsEnum = terms.iterator();
                BytesRef thisTerm;
                while ((thisTerm = termsEnum.next()) != null) {
                    String termText = thisTerm.utf8ToString();
                    int freq = (int) termsEnum.totalTermFreq();
                    wordFreq.put(termText, wordFreq.getOrDefault(termText, 0) + freq);
                }
            }
        }

        reader.close();

        return wordFreq;
    }


    @Override
    public JSONObject statQuestContents(String questId, String tenantId) {
        JSONObject stat = new JSONObject();
        Long total = jdbcTemplate.queryForObject("select count(1) from personal_questionnaire where questionnaire_id=? ", Long.class, questId);
        total = total == null ? 0 : total;
        stat.put("total", total);
        List<QuestionnaireContent> list = questionnaireContentMapper.selectByMainId(questId);
        JSONArray listArray = new JSONArray();
        for (QuestionnaireContent questContent : list) {
            JSONObject questCount = new JSONObject();
            questCount.put("sort", questContent.getSort());
            questCount.put("content", questContent.getContent());
            questCount.put("type", questContent.getType());
            questCount.put("id", questContent.getId());

            if (StringUtils.equals(questCount.getString("type"), "单选")) {
                List<TopicOptions> topicOptionsList = topicOptionsMapper.listByQuestContentId(questContent.getId());
                JSONArray optionList = new JSONArray();
                for (TopicOptions tp : topicOptionsList) {
                    JSONObject option = new JSONObject();
                    option.put("optionNumber", tp.getOptionNumber());
                    option.put("checked", false);
                    option.put("content", tp.getContent());
                    option.put("id", tp.getId());
                    option.put("score", tp.getScore());

                    if (total > 0) {
                        Long count = jdbcTemplate.queryForObject("select count(1) from personal_quest_content where quest_id=? and selected_option_id = ? ", Long.class, questId,  tp.getId());
                        count = count == null ? 0 : count;
                        option.put("count", count);
                        option.put("percent", count * 100 / total);
                    } else {
                        option.put("count", 0);
                        option.put("percent", 0);
                    }
                    optionList.add(option);
                }
                questCount.put("optionsList", optionList);
            } else if (StringUtils.equals(questCount.getString("type"), "多选")) {
                List<TopicOptions> topicOptionsList = topicOptionsMapper.listByQuestContentId(questContent.getId());
                JSONArray optionList = new JSONArray();
                for (TopicOptions tp : topicOptionsList) {
                    JSONObject option = new JSONObject();
                    option.put("optionNumber", tp.getOptionNumber());
                    option.put("checked", false);
                    option.put("content", tp.getContent());
                    option.put("id", tp.getId());
                    option.put("score", tp.getScore());

                    if (total > 0) {
                        Long count = jdbcTemplate.queryForObject("select count(1) from personal_quest_content where quest_id=? and selected_option_id like concat('%',?,'%')", Long.class, questId,  tp.getId());
                        count = count == null ? 0 : count;
                        option.put("count", count);
                        option.put("percent", count * 100 / total);
                    } else {
                        option.put("count", 0);
                        option.put("percent", 0);
                    }
                    optionList.add(option);
                }
                questCount.put("optionsList", optionList);
            } else if (StringUtils.equals(questCount.getString("type"), "输入框")) {
                try {
                    Map<String, Integer> wordFreq = getWordFrequency(questId);
                    // 将结果转换为 JSONArray
                    JSONArray wordFrequencyArray = new JSONArray();
                    for (Map.Entry<String, Integer> entry : wordFreq.entrySet()) {
                        JSONObject wordObj = new JSONObject();
                        wordObj.put("name", entry.getKey());
                        wordObj.put("value", entry.getValue());
                        wordFrequencyArray.add(wordObj);
                    }
                    questCount.put("wordFrequency", wordFrequencyArray);
                } catch (Exception e) {
                    log.error("获取词频失败", e);
                    questCount.put("wordFrequency", new JSONArray());
                }

                if (total == 0) {
                    questCount.put("fillCount", 0);
                    questCount.put("fillPercent", 0);
                    questCount.put("nofillCount", 0);
                    questCount.put("nofillPercent", 0);
                } else {
                    Long count = jdbcTemplate.queryForObject("select count(1) from personal_quest_content where quest_id=? and quest_content_id=?  and answer_text IS NOT NULL and quest_type='输入框'", Long.class, questId, questContent.getId());
                    count = count == null ? 0 : count;
                    questCount.put("fillCount", count);
                    questCount.put("fillPercent", count * 100 / total);

                    long nofillCount = total - count;
                    questCount.put("nofillCount", nofillCount);
                    questCount.put("nofillPercent", nofillCount * 100 / total);
                }
            } else if (StringUtils.equals(questCount.getString("type"), "评分")) {
                //统计personal_quest_content中answer_text的1-5的个数
                Long count1 = jdbcTemplate.queryForObject("select count(1) from personal_quest_content where quest_id=? and quest_content_id=?  and answer_text='1' ", Long.class, questId, questContent.getId());
                count1 = count1 == null ? 0 : count1;
                questCount.put("count1", count1);

                Long count2 = jdbcTemplate.queryForObject("select count(1) from personal_quest_content where quest_id=? and quest_content_id=?  and answer_text='2'", Long.class, questId, questContent.getId());
                count2 = count2 == null ? 0 : count2;
                questCount.put("count2", count2);

                Long count3 = jdbcTemplate.queryForObject("select count(1) from personal_quest_content where quest_id=? and quest_content_id=?  and answer_text='3'", Long.class, questId, questContent.getId());
                count3 = count3 == null ? 0 : count3;
                questCount.put("count3", count3);

                Long count4 = jdbcTemplate.queryForObject("select count(1) from personal_quest_content where quest_id=? and quest_content_id=?  and answer_text='4'", Long.class, questId, questContent.getId());
                count4 = count4 == null ? 0 : count4;
                questCount.put("count4", count4);

                Long count5 = jdbcTemplate.queryForObject("select count(1) from personal_quest_content where quest_id=? and quest_content_id=?  and answer_text='5'", Long.class, questId, questContent.getId());
                count5 = count5 == null ? 0 : count5;
                questCount.put("count5", count5);

                // 计算总数和总分
                long totalCount = count1 + count2 + count3 + count4 + count5;
                long totalScore = count1 + count2 * 2 + count3 * 3 + count4 * 4 + count5 * 5;
                questCount.put("total", totalCount);

                // 计算平均分
                double average = totalCount > 0 ? (double) totalScore / totalCount : 0;
                // 保留两位小数
                BigDecimal averageScore = BigDecimal.valueOf(average).setScale(2, BigDecimal.ROUND_HALF_UP);
                questCount.put("average", averageScore);

            }

            listArray.add(questCount);
        }

        stat.put("questList", listArray);

        return stat;
    }


}
