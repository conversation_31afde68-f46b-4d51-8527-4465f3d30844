package org.jeecg.modules.quest.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.quest.entity.PersonalQuestionnaire;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 问卷答题
 * @Author: jeecg-boot
 * @Date:   2024-11-13
 * @Version: V1.0
 */
public interface PersonalQuestionnaireMapper extends BaseMapper<PersonalQuestionnaire> {

    public List<PersonalQuestionnaire> getByQuestId(@Param("questId") String questId);

    public List<PersonalQuestionnaire> getByQuestIdAndBizId(@Param("questId") String questId, @Param("bizId") String bizId);

    PersonalQuestionnaire queryByQuestAndCard(@Param("questId") String questId, @Param("account") String account);

}
