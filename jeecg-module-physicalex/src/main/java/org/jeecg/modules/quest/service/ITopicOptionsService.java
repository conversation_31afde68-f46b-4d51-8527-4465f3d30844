package org.jeecg.modules.quest.service;

import org.jeecg.modules.quest.entity.TopicOptions;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 问卷题目选项
 * @Author: jeecg-boot
 * @Date:   2024-11-19
 * @Version: V1.0
 */
public interface ITopicOptionsService extends IService<TopicOptions> {

    List<TopicOptions> listByQuestContentId(String questContentId);
}
