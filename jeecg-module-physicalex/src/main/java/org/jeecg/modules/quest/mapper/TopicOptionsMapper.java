package org.jeecg.modules.quest.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.quest.entity.TopicOptions;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 问卷题目选项
 * @Author: jeecg-boot
 * @Date:   2024-11-19
 * @Version: V1.0
 */
public interface TopicOptionsMapper extends BaseMapper<TopicOptions> {

    public List<TopicOptions> listByQuestContentId(@Param("questContentId") String questContentId);
}
