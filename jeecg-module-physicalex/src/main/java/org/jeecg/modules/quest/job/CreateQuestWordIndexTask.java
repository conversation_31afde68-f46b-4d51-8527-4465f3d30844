package org.jeecg.modules.quest.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.quest.service.IPersonalQuestContentService;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CreateQuestWordIndexTask implements Job {

    @Autowired
    private IPersonalQuestContentService personalQuestContentService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        personalQuestContentService.createLuceneIndex();
    }
}
