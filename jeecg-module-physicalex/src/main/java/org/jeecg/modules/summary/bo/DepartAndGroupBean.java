package org.jeecg.modules.summary.bo;

import lombok.Data;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.jeecg.modules.system.entity.SysDepart;

import java.util.List;

@Data
public class DepartAndGroupBean {
    private SysDepart depart;
    private List<CustomerRegItemGroup> groupList;
    private List<CustomerRegItemGroup> summarizingGroupList;
    private boolean summaryFlag;
    private CustomerRegDepartSummary departSummary;
}
