package org.jeecg.modules.summary.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.github.difflib.DiffUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.summary.entity.CustomerRegSummaryHistory;
import org.jeecg.modules.summary.service.ICustomerRegSummaryHistoryService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 总检记录表
 * @Author: jeecg-boot
 * @Date: 2024-09-10
 * @Version: V1.0
 */
@Api(tags = "总检记录表")
@RestController
@RequestMapping("/summary/customerRegSummaryHistory")
@Slf4j
public class CustomerRegSummaryHistoryController extends JeecgController<CustomerRegSummaryHistory, ICustomerRegSummaryHistoryService> {
    @Autowired
    private ICustomerRegSummaryHistoryService customerRegSummaryHistoryService;

    /**
     * 分页列表查询
     *
     * @param customerRegSummaryHistory
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "总检记录表-分页列表查询")
    @ApiOperation(value = "总检记录表-分页列表查询", notes = "总检记录表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerRegSummaryHistory>> queryPageList(CustomerRegSummaryHistory customerRegSummaryHistory,
                                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                  HttpServletRequest req) {
        QueryWrapper<CustomerRegSummaryHistory> queryWrapper = QueryGenerator.initQueryWrapper(customerRegSummaryHistory, req.getParameterMap());
        Page<CustomerRegSummaryHistory> page = new Page<CustomerRegSummaryHistory>(pageNo, pageSize);
        IPage<CustomerRegSummaryHistory> pageList = customerRegSummaryHistoryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "总检记录表-分页列表查询", notes = "总检记录表-分页列表查询")
    @GetMapping(value = "/listBySummaryId")
    public Result<IPage<CustomerRegSummaryHistory>> queryPageList(String summaryId,
                                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                  HttpServletRequest req) {
        QueryWrapper<CustomerRegSummaryHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("summary_id", summaryId);
        queryWrapper.orderByDesc("create_time");
        Page<CustomerRegSummaryHistory> page = new Page<>(pageNo, pageSize);
        IPage<CustomerRegSummaryHistory> pageList = customerRegSummaryHistoryService.page(page, queryWrapper);


        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param customerRegSummaryHistory
     * @return
     */
    @AutoLog(value = "总检记录表-添加")
    @ApiOperation(value = "总检记录表-添加", notes = "总检记录表-添加")
    @RequiresPermissions("summary:customer_reg_summary_history:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CustomerRegSummaryHistory customerRegSummaryHistory) {
        customerRegSummaryHistoryService.save(customerRegSummaryHistory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param customerRegSummaryHistory
     * @return
     */
    @AutoLog(value = "总检记录表-编辑")
    @ApiOperation(value = "总检记录表-编辑", notes = "总检记录表-编辑")
    @RequiresPermissions("summary:customer_reg_summary_history:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomerRegSummaryHistory customerRegSummaryHistory) {
        customerRegSummaryHistoryService.updateById(customerRegSummaryHistory);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "总检记录表-通过id删除")
    @ApiOperation(value = "总检记录表-通过id删除", notes = "总检记录表-通过id删除")
    @RequiresPermissions("summary:customer_reg_summary_history:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegSummaryHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "总检记录表-批量删除")
    @ApiOperation(value = "总检记录表-批量删除", notes = "总检记录表-批量删除")
    @RequiresPermissions("summary:customer_reg_summary_history:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegSummaryHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "总检记录表-通过id查询")
    @ApiOperation(value = "总检记录表-通过id查询", notes = "总检记录表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegSummaryHistory> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegSummaryHistory customerRegSummaryHistory = customerRegSummaryHistoryService.getById(id);
        if (customerRegSummaryHistory == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegSummaryHistory);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegSummaryHistory
     */
    @RequiresPermissions("summary:customer_reg_summary_history:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegSummaryHistory customerRegSummaryHistory) {
        return super.exportXls(request, customerRegSummaryHistory, CustomerRegSummaryHistory.class, "总检记录表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("summary:customer_reg_summary_history:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegSummaryHistory.class);
    }

}
