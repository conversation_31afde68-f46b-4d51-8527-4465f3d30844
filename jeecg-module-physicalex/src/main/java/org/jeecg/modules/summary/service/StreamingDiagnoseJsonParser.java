package org.jeecg.modules.summary.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.jeecg.modules.summary.dto.Diagnose;

public class StreamingDiagnoseJsonParser {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final StringBuilder buffer = new StringBuilder();
    private boolean arrayStarted = false;

    public void append(String partialJson, DiagnoseConsumer consumer) throws Exception {
        buffer.append(partialJson);
        trimLeftWhitespace();

        if (!arrayStarted && !buffer.isEmpty()) {
            if (buffer.charAt(0) == '[') {
                arrayStarted = true;
                buffer.deleteCharAt(0);
                trimLeftWhitespace();
            } else {
                // 不包含数组左边界，[，继续等待
                return;
            }
        }

        while (true) {
            trimLeftWhitespace();

            if (!buffer.isEmpty() && buffer.charAt(0) == ']') {
                buffer.deleteCharAt(0);
                arrayStarted = false;
                break;
            }
            int objStart = buffer.indexOf("{");
            if (objStart < 0) break;

            int objEnd = findMatchingBrace(buffer, objStart);
            if (objEnd < 0) break; // 还没有完整对象

            int tail = objEnd + 1;
            while (tail < buffer.length() && Character.isWhitespace(buffer.charAt(tail))) tail++;
            if (tail < buffer.length() && buffer.charAt(tail) == ',') tail++;

            String objJson = buffer.substring(objStart, objEnd + 1);
            Diagnose diag = objectMapper.readValue(objJson, Diagnose.class);
            consumer.onDiagnose(diag);

            // 关键：每处理1次对象，删除buffer中的该对象内容
            buffer.delete(0, tail);
        }
    }


    private void trimLeftWhitespace() {
        while (!buffer.isEmpty() && Character.isWhitespace(buffer.charAt(0))) buffer.deleteCharAt(0);
    }

    private int findMatchingBrace(StringBuilder sb, int start) {
        int depth = 0;
        for (int i = start; i < sb.length(); i++) {
            char c = sb.charAt(i);
            if (c == '{') depth++;
            else if (c == '}') {
                depth--;
                if (depth == 0) return i;
            }
        }
        return -1;
    }
}
