package org.jeecg.modules.summary.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.TaskService;
import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.summary.entity.ItemResultChangeApply;
import org.jeecg.modules.summary.entity.SummaryAuditRecord;
import org.jeecg.modules.summary.mapper.ItemResultChangeApplyMapper;
import org.jeecg.modules.summary.mapper.SummaryAuditRecordMapper;
import org.jeecg.modules.summary.service.ISummaryAuditRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Map;

/**
 * @Description: 总检审核记录
 * @Author: jeecg-boot
 * @Date: 2024-05-27
 * @Version: V1.0
 */
@Service
public class SummaryAuditRecordServiceImpl extends ServiceImpl<SummaryAuditRecordMapper, SummaryAuditRecord> implements ISummaryAuditRecordService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private TaskService taskService;
    @Autowired
    private ItemResultChangeApplyMapper itemResultChangeApplyMapper;

    @Override
    public String revokeAudit(SummaryAuditRecord summaryAuditRecord) throws Exception {
        String summaryId = summaryAuditRecord.getSummaryId();
        String customerRegId = summaryAuditRecord.getCustomerRegId();
        String lastSummaryStatus = null;
        try {
            lastSummaryStatus = jdbcTemplate.queryForObject("select summary_status from summary_audit_record where summary_id=? order by create_time desc limit 1", String.class, summaryId);
        } catch (Exception ignored) {
        }
        lastSummaryStatus = StringUtils.isNotBlank(lastSummaryStatus) ? lastSummaryStatus : ExConstants.SUMMARY_STATUS_已总检;
        jdbcTemplate.update("update customer_reg_summary set status = ? where id =?", lastSummaryStatus, summaryId);
        //记录撤销审核记录
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        summaryAuditRecord.setSummaryId(summaryId);
        summaryAuditRecord.setAuditResult("撤销");
        summaryAuditRecord.setCreator(sysUser.getRealname());
        save(summaryAuditRecord);

        UpdateWrapper<CustomerReg> regUpdateWrapper = new UpdateWrapper<>();
        regUpdateWrapper.set("summary_status", lastSummaryStatus);
        regUpdateWrapper.set("e_report_status", ExConstants.REPORT_STATE_待生成);
        regUpdateWrapper.set("e_report_url", null);
        regUpdateWrapper.eq("id", customerRegId);

        customerRegMapper.update(null, regUpdateWrapper);

        return lastSummaryStatus;
    }

    @Override
    public void revokeAuditByProcess(SummaryAuditRecord auditRecord) throws Exception {

        String taskId = auditRecord.getTaskId();
        if (StringUtils.isBlank(taskId)) {
            throw new RuntimeException("taskId不能为空");
        }
        Map<String, Object> variables = taskService.getVariables(taskId);
        String businessType = (String) variables.get("businessType");

        if (StringUtils.equals(businessType, ExConstants.PROCESS_BUSINESS_TYPE_体检数据修改)) {
            //如果是体检数据修改流程
            ItemResultChangeApply changeApply = itemResultChangeApplyMapper.selectById(auditRecord.getBusinessKey());
            if (StringUtils.equals(auditRecord.getAgreeFlag(), "1")) {
                auditRecord.setApplyer(changeApply.getCreator());
                auditRecord.setApplyBy(changeApply.getCreateBy());
                auditRecord.setBusinessType(businessType);
                revokeAudit(auditRecord);

                variables.put("revokeStatus", "已撤销");
            } else {
                LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                variables.put("confirmer", loginUser.getRealname());
                variables.put("confirmBy", loginUser.getUsername());
                variables.put("applyStatus", ExConstants.RECORD_CHANGE_APPLY_STATUS_REFUSED);

                variables.put("revokeStatus", "拒绝撤销");
                String applyer = changeApply.getCreateBy();
                variables.put("msgReceiver", applyer);
                String msgCount = "您的体检数据修改申请未通过，总检审核人拒绝撤销审核，具体原因：" + auditRecord.getRejectReason();
                variables.put("msgContent", msgCount);
            }
        }
        taskService.complete(taskId, variables);
    }
}
