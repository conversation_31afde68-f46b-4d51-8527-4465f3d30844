package org.jeecg.modules.summary.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.summary.entity.AccountReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 账号关联的报告
 * @Author: jeecg-boot
 * @Date:   2025-03-11
 * @Version: V1.0
 */
public interface AccountReportMapper extends BaseMapper<AccountReport> {
   Page<AccountReport> pageAccountReport(Page<AccountReport> page, @Param("accountId") String accountId);

   Page<AccountReport> pageAccountReportByIdCard(Page<AccountReport> page, @Param("idCard") String idCard);
}
