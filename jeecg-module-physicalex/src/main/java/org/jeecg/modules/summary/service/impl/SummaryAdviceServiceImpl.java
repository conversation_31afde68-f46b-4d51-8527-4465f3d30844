package org.jeecg.modules.summary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.document.*;
import org.apache.lucene.index.*;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.*;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.jeecg.common.util.ListsUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.JsonExtractorAndParser;
import org.jeecg.excommons.utils.LuceneQueryStringUtil;
import org.jeecg.excommons.utils.TextUtils;
import org.jeecg.modules.ai.bo.AIMessage;
import org.jeecg.modules.ai.bo.WordExplain;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.ai.service.DashScopeService;
import org.jeecg.modules.ai.entity.AiSetting;
import org.jeecg.modules.ai.entity.AiMod;
import org.jeecg.modules.ai.mapper.AiSettingMapper;
import org.jeecg.modules.ai.model.ApplicationResult;
import org.jeecg.modules.ai.model.Message;
import org.jeecg.modules.ai.config.DashScopeConfig;
import org.jeecg.excommons.utils.ProxyOkHttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.jeecg.modules.summary.entity.AdviceBean;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.SummaryAdvice;
import org.jeecg.modules.summary.mapper.SummaryAdviceMapper;
import org.jeecg.modules.summary.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description: 总检建议
 * @Author: jeecg-boot
 * @Date: 2024-05-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class SummaryAdviceServiceImpl extends ServiceImpl<SummaryAdviceMapper, SummaryAdvice> implements ISummaryAdviceService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SummaryAdviceMapper summaryAdviceMapper;
    @Autowired
    private Analyzer hanlpAnalyzer;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private ICustomerRegDepartSummaryService departSummaryService;

    @Value("${biz.lucene.path}")
    private String lucenePath;
    @Autowired
    private AIService aiService;
    @Autowired
    private LuceneIndexManager luceneIndexManager;
    @Autowired
    private SummaryAdviceDocumentBuilder documentBuilder;
    @Autowired
    private SummaryAdviceQueryBuilder queryBuilder;
    @Autowired
    private LuceneBatchUpdateService batchUpdateService;
    @Autowired
    private LuceneOperationStatsService operationStatsService;
    @Autowired
    private AiSettingMapper aiSettingMapper;
    @Autowired
    private ProxyOkHttpUtil proxyOkHttpUtil;
    @Autowired
    private DashScopeConfig dashScopeConfig;
    @Autowired
    private DashScopeService dashScopeService;

    private final Object lock = new Object();

    @Override
    public Integer getNextSortNum(String departmentId) {
        Integer maxSort = 0;
        try {
            maxSort = jdbcTemplate.queryForObject("select max(sort) from summary_advice where department_id = ?", Integer.class, departmentId);
        } catch (Exception ignored) {
        }
        return maxSort == null ? 1 : maxSort + 1;
    }

    @Override
    public void addAdvice(SummaryAdvice summaryAdvice) {
        //检查是否有重复的关键字
        String existId = null;
        List<String> existIds = jdbcTemplate.queryForList("SELECT id FROM summary_advice WHERE keywords = ?", String.class, summaryAdvice.getKeywords());
        existId = !existIds.isEmpty() ? existIds.get(0) : null;
        if (existId != null) {
            summaryAdvice.setId(existId);
            summaryAdviceMapper.updateById(summaryAdvice);
        } else {
            summaryAdvice.setIndexTime(new Date());
            summaryAdviceMapper.insert(summaryAdvice);
        }
    }

    @Override
    public void createLuceneIndex4SummaryAdvice(SummaryAdvice summaryAdvice) {
        if (summaryAdvice == null) {
            log.warn("SummaryAdvice为空，无法创建索引");
            return;
        }

        if (!documentBuilder.isValidForIndexing(summaryAdvice)) {
            log.warn("SummaryAdvice数据不完整，无法创建索引: {}", summaryAdvice.getId());
            return;
        }

        try {
            Document document = documentBuilder.buildDocument(summaryAdvice);
            List<Document> docs = new ArrayList<>();
            docs.add(document);

            boolean success = luceneIndexManager.addDocuments(docs);
            if (success) {
                // 更新数据库索引时间
                String updateSql = "UPDATE summary_advice SET index_time = ? WHERE id = ?";
                jdbcTemplate.update(updateSql, new Date(), summaryAdvice.getId());
                log.debug("成功创建索引: {}", summaryAdvice.getId());
            } else {
                log.error("创建索引失败: {}", summaryAdvice.getId());
            }
        } catch (Exception e) {
            log.error("创建索引异常: {}", summaryAdvice.getId(), e);
        }
    }

    @Override
    public List<SummaryAdvice> listAdviceFromDB(String keyword, String gender, Integer age, String marriage, String departmentId) {
        List<SummaryAdvice> summaryAdviceList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT * FROM summary_advice WHERE 1=1");

        List<Object> params = new ArrayList<>();

        /*if (StringUtils.isNotBlank(keyword)) {
            sql.append(" AND keywords LIKE ?");
            params.add("%" + keyword + "%");
        }*/
        if (StringUtils.isNotBlank(keyword)) {
            sql.append(" AND keywords = ?");
            params.add(keyword);
        }
        if (StringUtils.isNotBlank(gender)) {
            sql.append(" AND (sex_limit='不限' or sex_limit = ?)");
            params.add(gender);
        }
        if (age != null) {
            sql.append(" AND min_age <= ? AND max_age >= ?");
            params.add(age);
            params.add(age);
        }
        if (StringUtils.isNotBlank(marriage)) {
            sql.append(" AND (marriage_limit = '不限' or marriage_limit = ?");
            params.add(marriage);
        }
        /*if (StringUtils.isNotBlank(departmentId)) {
            sql.append(" AND department_id = ?");
            params.add(departmentId);
        }*/

        sql.append(" order by sort desc");

        try {
            summaryAdviceList = jdbcTemplate.query(sql.toString(), params.toArray(), (rs, rowNum) -> {
                SummaryAdvice summaryAdvice = new SummaryAdvice();
                summaryAdvice.setId(rs.getString("id"));
                summaryAdvice.setDepartmentId(rs.getString("department_id"));
                summaryAdvice.setKeywords(rs.getString("keywords"));
                summaryAdvice.setJobCategory(rs.getString("job_category"));
                summaryAdvice.setRiskFactor(rs.getString("risk_factor"));
                summaryAdvice.setSexLimit(rs.getString("sex_limit"));
                summaryAdvice.setMarriageLimit(rs.getString("marriage_limit"));
                summaryAdvice.setMinAge(rs.getInt("min_age"));
                summaryAdvice.setMaxAge(rs.getInt("max_age"));
                summaryAdvice.setDiseaseSeverity(rs.getString("disease_severity"));
                summaryAdvice.setDiseaseType(rs.getString("disease_type"));
                summaryAdvice.setAdviceContent(rs.getString("advice_content"));
                summaryAdvice.setIcdCode(rs.getString("idc_code"));
                summaryAdvice.setDepartmentAdvice(rs.getString("department_advice"));
                summaryAdvice.setTeamAdvice(rs.getString("team_advice"));
                summaryAdvice.setMedicalAdvice(rs.getString("medical_advice"));
                summaryAdvice.setDietAdvice(rs.getString("diet_advice"));
                summaryAdvice.setHealthAdvice(rs.getString("health_advice"));
                return summaryAdvice;
            });
        } catch (Exception e) {
            log.error("查询数据库失败", e);
        }

        return summaryAdviceList;
    }

    @Override
    public void createLuceneIndex() {
        try {
            List<SummaryAdvice> summaryAdviceList = summaryAdviceMapper.listNeedCreateLuceneIndex();
            if (summaryAdviceList.isEmpty()) {
                log.info("没有需要创建索引的数据");
                return;
            }

            log.info("开始创建索引，数据量: {}", summaryAdviceList.size());

            // 构建文档列表
            List<Document> docs = new ArrayList<>();
            List<SummaryAdvice> validAdviceList = new ArrayList<>();

            for (SummaryAdvice summaryAdvice : summaryAdviceList) {
                if (documentBuilder.isValidForIndexing(summaryAdvice)) {
                    Document document = documentBuilder.buildDocument(summaryAdvice);
                    docs.add(document);
                    validAdviceList.add(summaryAdvice);
                } else {
                    log.warn("跳过无效数据: {}", summaryAdvice.getId());
                }
            }

            if (docs.isEmpty()) {
                log.warn("没有有效的数据可以创建索引");
                return;
            }

            // 使用索引管理器添加文档
            boolean success = luceneIndexManager.addDocuments(docs);
            if (success) {
                // 使用优化的批量更新服务
                batchUpdateService.recommendedBatchUpdate(validAdviceList);
                log.info("成功创建索引，处理数据量: {}", validAdviceList.size());
                // 记录索引操作统计
                operationStatsService.recordIndexOperation(true);
            } else {
                log.error("创建索引失败");
                operationStatsService.recordIndexOperation(false);
            }
        } catch (Exception e) {
            log.error("创建索引异常", e);
        }
    }

    @Override
    public void updateLuceneIndexNessary(String id) {
        if (StringUtils.isBlank(id)) {
            log.warn("ID为空，无法更新索引");
            return;
        }

        try {
            SummaryAdvice summaryAdvice = getById(id);
            if (summaryAdvice == null) {
                log.warn("找不到ID为{}的数据，无法更新索引", id);
                return;
            }

            if (!documentBuilder.isValidForIndexing(summaryAdvice)) {
                log.warn("数据不完整，无法更新索引: {}", id);
                return;
            }

            Document document = documentBuilder.buildDocument(summaryAdvice);
            Term term = new Term("id", id);

            boolean success = luceneIndexManager.updateDocument(term, document);
            if (success) {
                jdbcTemplate.update("UPDATE summary_advice SET index_time = ? WHERE id = ?", new Date(), id);
                log.debug("成功更新索引: {}", id);
            } else {
                log.error("更新索引失败: {}", id);
            }
        } catch (Exception e) {
            log.error("更新索引异常: {}", id, e);
        }
    }

    @Override
    public void updateLuceneIndex(SummaryAdvice summaryAdvice) {
        if (summaryAdvice == null || StringUtils.isBlank(summaryAdvice.getId())) {
            log.warn("SummaryAdvice为空或ID为空，无法更新索引");
            return;
        }

        if (!documentBuilder.isValidForIndexing(summaryAdvice)) {
            log.warn("数据不完整，无法更新索引: {}", summaryAdvice.getId());
            return;
        }

        String id = summaryAdvice.getId();

        try {
            // 检查索引中是否存在该文档
            Query existsQuery = queryBuilder.buildExistsQuery(id);
            boolean exists = luceneIndexManager.documentExists(existsQuery);

            if (!exists) {
                // 如果不存在，创建新索引
                createLuceneIndex4SummaryAdvice(summaryAdvice);
                return;
            }

            // 更新现有索引
            Document document = documentBuilder.buildDocument(summaryAdvice);
            Term term = new Term("id", id);

            boolean success = luceneIndexManager.updateDocument(term, document);
            if (success) {
                jdbcTemplate.update("UPDATE summary_advice SET index_time = ? WHERE id = ?", new Date(), id);
                log.debug("成功更新索引: {}", id);
            } else {
                log.error("更新索引失败: {}", id);
            }
        } catch (Exception e) {
            log.error("更新索引异常: {}", id, e);
        }
    }

    @Override
    public void updateLuceneIndexNessary() {
        try {
            List<SummaryAdvice> needUpdateList = summaryAdviceMapper.listNessaryUpdateLuceneIndex();
            if (needUpdateList.isEmpty()) {
                log.info("没有需要更新的索引数据");
                return;
            }

            log.info("开始批量更新索引，数据量: {}", needUpdateList.size());

            // 批量更新索引
            int successCount = 0;
            List<SummaryAdvice> successList = new ArrayList<>();

            for (SummaryAdvice summaryAdvice : needUpdateList) {
                if (!documentBuilder.isValidForIndexing(summaryAdvice)) {
                    log.warn("跳过无效数据: {}", summaryAdvice.getId());
                    continue;
                }

                Document document = documentBuilder.buildDocument(summaryAdvice);
                Term term = new Term("id", summaryAdvice.getId());

                boolean success = luceneIndexManager.updateDocument(term, document);
                if (success) {
                    successCount++;
                    successList.add(summaryAdvice);
                } else {
                    log.error("更新索引失败: {}", summaryAdvice.getId());
                }
            }

            // 使用优化的批量更新服务
            if (!successList.isEmpty()) {
                batchUpdateService.recommendedBatchUpdate(successList);
            }

            log.info("批量更新索引完成，成功: {}, 总数: {}", successCount, needUpdateList.size());

        } catch (Exception e) {
            log.error("批量更新索引异常", e);
        }
    }

    @Override
    public void updateLuceneIndexAll() {
        try {
            List<SummaryAdvice> allAdviceList = summaryAdviceMapper.listAll();
            if (allAdviceList.isEmpty()) {
                log.info("没有数据需要重建索引");
                return;
            }

            log.info("开始重建所有索引，数据量: {}", allAdviceList.size());

            // 构建文档列表
            List<Document> documents = new ArrayList<>();
            List<SummaryAdvice> validAdviceList = new ArrayList<>();

            for (SummaryAdvice summaryAdvice : allAdviceList) {
                if (documentBuilder.isValidForIndexing(summaryAdvice)) {
                    Document document = documentBuilder.buildDocument(summaryAdvice);
                    documents.add(document);
                    validAdviceList.add(summaryAdvice);
                } else {
                    log.warn("跳过无效数据: {}", summaryAdvice.getId());
                }
            }

            if (documents.isEmpty()) {
                log.warn("没有有效的数据可以重建索引");
                return;
            }

            // 重建索引
            boolean success = luceneIndexManager.rebuildIndex(documents);
            if (success) {
                // 使用优化的批量更新服务
                batchUpdateService.recommendedBatchUpdate(validAdviceList);
                log.info("成功重建索引，处理数据量: {}", validAdviceList.size());
            } else {
                log.error("重建索引失败");
            }
        } catch (Exception e) {
            log.error("重建索引异常", e);
        }
    }

    @Override
    public void removeLuceneIndex(String id) {
        if (StringUtils.isBlank(id)) {
            log.warn("ID为空，无法删除索引");
            return;
        }

        try {
            Term term = new Term("id", id);
            boolean success = luceneIndexManager.deleteDocuments(term);
            if (success) {
                log.debug("成功删除索引: {}", id);
            } else {
                log.error("删除索引失败: {}", id);
            }
        } catch (Exception e) {
            log.error("删除索引异常: {}", id, e);
        }
    }

    @Override
    public List<SummaryAdvice> listAdviceFromLucene(Boolean isExactlyMatch, String keyword, String gender, Integer age, String marriage, String departmentId) {
        List<SummaryAdvice> summaryAdviceList = new ArrayList<>();

        // 验证查询参数
        if (!queryBuilder.validateQueryParams(keyword, gender, age, marriage, departmentId)) {
            return summaryAdviceList;
        }

        // 检查索引健康状态
        if (!luceneIndexManager.isIndexHealthy()) {
            log.warn("索引不健康，尝试修复...");
            if (!luceneIndexManager.repairIndex()) {
                log.error("索引修复失败，回退到数据库查询");
                return listAdviceFromDB(keyword, gender, age, marriage, departmentId);
            }
        }

        try {
            // 预处理关键字
            String processedKeyword = queryBuilder.preprocessKeyword(keyword);
            String normalDepartSummary = sysSettingService.getValueByCode("departSummaryNormalWord");

            // 构建查询
            Query mainQuery = queryBuilder.buildMainQuery(isExactlyMatch, processedKeyword, gender, age, marriage, departmentId, normalDepartSummary);

            // 执行搜索
            TopDocs topDocs = luceneIndexManager.search(mainQuery, 20);
            if (topDocs == null) {
                log.warn("搜索失败，回退到数据库查询");
                return listAdviceFromDB(keyword, gender, age, marriage, departmentId);
            }

            // 处理搜索结果
            for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
                Document doc = luceneIndexManager.getDocument(scoreDoc.doc);
                if (doc != null) {
                    SummaryAdvice summaryAdvice = documentBuilder.buildSummaryAdvice(doc);
                    if (summaryAdvice != null) {
                        summaryAdvice.setMatchScore(scoreDoc.score);
                        summaryAdviceList.add(summaryAdvice);
                    }
                }
            }

            log.debug("Lucene搜索完成，关键字: {}, 结果数量: {}", keyword, summaryAdviceList.size());
            // 记录搜索操作统计
            operationStatsService.recordSearchOperation(true);

        } catch (Exception e) {
            log.error("Lucene查询失败，回退到数据库查询", e);
            operationStatsService.recordSearchOperation(false);
            return listAdviceFromDB(keyword, gender, age, marriage, departmentId);
        }

        return summaryAdviceList;
    }

    @Override
    public List<AdviceBean> generateAdviceByRegId(String customerRegId) throws Exception {
        //先获取登记信息
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            return new ArrayList<>();
        }
        String departSummaryAbnormalOnly = sysSettingService.getValueByCode("depart_summary_abnormal_only");
        departSummaryAbnormalOnly = org.apache.commons.lang3.StringUtils.trimToNull(departSummaryAbnormalOnly);
        //再获取科室小结
        //List<CustomerRegDepartSummary> departSummaryList = regDepartSummaryMapper.listByCustomerReg(customerRegId, departSummaryAbnormalOnly);
        List<String> abnormalTextList = departSummaryService.generateAbnormalSummaryList(customerRegId);
        if (abnormalTextList.isEmpty()) {
            return new ArrayList<>();
        }
        //根据科室小结生成建议
        return generateAdviceByDepartSummaryList(false, true, abnormalTextList, customerReg);
    }

    @Override
    public List<AdviceBean> generateAdviceByDepartSummaryList(Boolean aiSummary, Boolean isExactlyMatch, List<String> keywordList, CustomerReg customerReg) throws Exception {

        if (keywordList == null || keywordList.isEmpty()) {
            return new ArrayList<>();
        }

        if (customerReg == null) {
            return new ArrayList<>();
        }

        List<AdviceBean> summaryAdviceList = new ArrayList<>();

        if (aiSummary) {
            String useAi = sysSettingService.getValueByCode("ai_enabled");


            if (!StringUtils.equals(useAi, "1")) {
                throw new RuntimeException("AI总检未启用！");
            }

            try {
                summaryAdviceList = aiService.generateAdvice(keywordList, customerReg);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            String summaryAdviceAdoptLimitScore = sysSettingService.getValueByCode("summaryAdviceAdoptLimitScore");
            if (StringUtils.isBlank(summaryAdviceAdoptLimitScore)) {
                summaryAdviceAdoptLimitScore = "20";
            }

            int i = 0;
            List<AdviceBean> adviceBeanList = new ArrayList<>();
            List<String> detailKeywords = new ArrayList<>();
            for (String keyword : keywordList) {
                if (StringUtils.isBlank(keyword)) {
                    continue;
                }
                detailKeywords.addAll(TextUtils.extractAbnormalPhrases(keyword));
            }

            for (String keyword : detailKeywords) {
                if (StringUtils.isBlank(keyword)) {
                    continue;
                }
                //将keyword中所有包含 未见异常或未见明显异常的 词去掉
                //List<SummaryAdvice> adviceList = listAdviceFromLucene(isExactlyMatch, keyword, customerReg.getGender(), customerReg.getAge(), customerReg.getMarriageStatus(), null);
                List<SummaryAdvice> adviceList = listAdviceFromDB(keyword, customerReg.getGender(), customerReg.getAge(), customerReg.getMarriageStatus(), null);

                if (adviceList.isEmpty()) {
                    //构建一个空的AdviceBean
                    AdviceBean emptyAdviceBean = new AdviceBean();
                    emptyAdviceBean.setName(keyword);
                    emptyAdviceBean.setContent("");
                    emptyAdviceBean.setSeq(i++);
                    emptyAdviceBean.setSource("知识库建议");
                    adviceBeanList.add(emptyAdviceBean);
                    continue;
                }
                SummaryAdvice mostMatchedAdvice = adviceList.get(0);
               /* if (mostMatchedAdvice.getMatchScore() < Integer.parseInt(summaryAdviceAdoptLimitScore)) {
                    continue;
                }*/
                AdviceBean adviceBean = new AdviceBean();
                adviceBean.setName(mostMatchedAdvice.getKeywords());
                adviceBean.setContent(mostMatchedAdvice.getAdviceContent());
                adviceBean.setSeq(i++);
                adviceBean.setSource("知识库建议");
                adviceBeanList.add(adviceBean);
            }
            //排除adviceBeanList中的name相同的建议
            List<String> nameList = new ArrayList<>();
            summaryAdviceList = new ArrayList<>();
            for (AdviceBean adviceBean : adviceBeanList) {
                if (!nameList.contains(adviceBean.getName())) {
                    nameList.add(adviceBean.getName());
                    summaryAdviceList.add(adviceBean);
                }
            }
        }

        return summaryAdviceList;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateKeywordsExplain() {
        try {
            List<SummaryAdvice> summaryAdvices = this.list(new LambdaQueryWrapper<SummaryAdvice>().isNull(SummaryAdvice::getKeywordsExplain));
            if (CollectionUtils.isNotEmpty(summaryAdvices)) {

                for (List<SummaryAdvice> adviceList : ListsUtils.subList(summaryAdvices, 100)) {
                    List<String> keyWords = adviceList.stream().map(SummaryAdvice::getKeywords).distinct().toList();

                    List<WordExplain> wordExplainList = aiService.explainWord(keyWords);
                    Map<String, String> wordExplainMap = wordExplainList.stream().collect(Collectors.toMap(WordExplain::getWord, WordExplain::getExplain));

                    for (SummaryAdvice summaryAdvice : adviceList) {
                        String explain = wordExplainMap.get(summaryAdvice.getKeywords());
                        if (StringUtils.isNotBlank(explain)) {
                            summaryAdvice.setKeywordsExplain(explain);
                        }
                    }

                    saveOrUpdateBatch(adviceList);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getAIReply(String prompt) throws Exception {
        try {
            long start = System.currentTimeMillis();
            AIMessage aiMessage = new AIMessage();
            aiMessage.setContent(prompt);
            aiMessage.setRole("user");

            String resultStr = aiService.execAi4ModuleFunc(ExConstants.AI_MODULE_选项, ExConstants.AI_FUNC_聊天, null, Collections.singletonList(aiMessage));
            long end = System.currentTimeMillis();
            log.info("收到的AI建议,耗时：" + (end - start) + "ms," + resultStr);
            return resultStr;
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    @Override
    public String generateAdviceDictionaryFromAI() throws Exception {
        log.info("开始从AI模型获取总检建议字典");

        // 1. 查询advice_content为空的记录
        List<SummaryAdvice> emptyAdviceList = summaryAdviceMapper.selectList(new LambdaQueryWrapper<SummaryAdvice>().isNull(SummaryAdvice::getAdviceContent).or().eq(SummaryAdvice::getAdviceContent, ""));

        if (emptyAdviceList.isEmpty()) {
            log.info("没有需要生成建议内容的记录");
            return "没有需要生成建议内容的记录";
        }

        log.info("找到{}条需要生成建议内容的记录", emptyAdviceList.size());

        // 2. 获取AI配置
        List<AiSetting> aiSettings = aiSettingMapper.getAiSettingByModule(ExConstants.AI_MODULE_总检, ExConstants.AI_FUNC_总检建议字典);

        if (aiSettings.isEmpty()) {
            throw new Exception("未配置AI模型设置！");
        }
        //筛选出dashscope模型
        List<AiSetting> aiSettingsFilted = aiSettings.stream().filter(setting -> org.apache.commons.lang3.StringUtils.equals(setting.getAiMod().getCode(), ExConstants.AI_MOD_DASHSCOPE)).toList();
        if (aiSettingsFilted.isEmpty()) {
            throw new Exception("未配置AI模型设置！");
        }

        AiSetting aiSetting = aiSettingsFilted.get(0);

        AiMod aiMod = aiSetting.getAiMod();

        if (!StringUtils.equals(aiMod.getCode(), ExConstants.AI_MOD_DASHSCOPE)) {
            throw new Exception("当前仅支持阿里云百炼模型");
        }

        String config = aiSetting.getConfig();
        if (StringUtils.isBlank(config)) {
            throw new Exception("AI模型配置为空！");
        }

        JSONObject configObj = JSONObject.parseObject(config);
        if (configObj == null) {
            throw new Exception("AI模型配置格式错误！");
        }

        String apiKey = configObj.getString("apiKey");
        String appId = configObj.getString("appId");
        if (StringUtils.isBlank(apiKey) || StringUtils.isBlank(appId)) {
            throw new Exception("未配置阿里云百炼API Key或App ID");
        }

        // 3. 分批处理，每10个为一组
        int batchSize = 30;
        int totalBatches = (int) Math.ceil((double) emptyAdviceList.size() / batchSize);
        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < totalBatches; i++) {
            int startIndex = i * batchSize;
            int endIndex = Math.min(startIndex + batchSize, emptyAdviceList.size());
            List<SummaryAdvice> batch = emptyAdviceList.subList(startIndex, endIndex);

            log.info("处理第{}/{}批，包含{}条记录", i + 1, totalBatches, batch.size());

            try {
                // 4. 拼接keywords作为用户消息
                List<String> keywords = batch.stream().map(SummaryAdvice::getKeywords).filter(StringUtils::isNotBlank).collect(Collectors.toList());

                if (keywords.isEmpty()) {
                    log.warn("第{}批没有有效的关键词，跳过", i + 1);
                    continue;
                }

                String userPrompt = String.join("，", keywords);

                // 5. 调用AI模型
                String aiResponse = callDashScopeAI(apiKey, appId, userPrompt, aiSetting.getSysPrompt());

                // 6. 解析AI响应并更新数据库
                int batchSuccessCount = processAIResponse(aiResponse, batch);
                successCount += batchSuccessCount;
                failCount += (batch.size() - batchSuccessCount);

                log.info("第{}批处理完成，成功{}条，失败{}条", i + 1, batchSuccessCount, batch.size() - batchSuccessCount);

                // 避免频繁调用AI接口，添加延迟
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("处理第{}批时发生异常", i + 1, e);
                failCount += batch.size();
            }
        }

        String result = String.format("AI建议字典生成完成！总计处理%d条记录，成功%d条，失败%d条", emptyAdviceList.size(), successCount, failCount);
        log.info(result);
        return result;
    }

    /**
     * 调用阿里云百炼AI模型
     */
    private String callDashScopeAI(String apiKey, String appId, String userPrompt, String sysPrompt) throws Exception {
        try {
            // 使用DashScopeService统一调用
            ApplicationResult result = dashScopeService.doCall(appId, apiKey, null, userPrompt);

            if (result == null || result.getOutput() == null) {
                throw new Exception("AI模型返回结果为空");
            }

            String responseText = result.getOutput().getText();
            if (StringUtils.isBlank(responseText)) {
                throw new Exception("AI模型返回内容为空");
            }

            log.debug("AI模型响应: {}", responseText);
            return responseText;

        } catch (Exception e) {
            log.error("调用阿里云百炼AI模型失败", e);
            throw new Exception("调用AI模型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理AI响应并更新数据库
     */
    private int processAIResponse(String aiResponse, List<SummaryAdvice> batch) {
        int successCount = 0;

        try {
            // 提取JSON结构
            String jsonStr = JsonExtractorAndParser.extractAndParseJson(aiResponse);
            if (StringUtils.isBlank(jsonStr)) {
                log.warn("无法从AI响应中提取JSON结构: {}", aiResponse);
                return 0;
            }

            // 解析JSON数组
            JSONArray jsonArray = JSONArray.parseArray(jsonStr);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("AI响应JSON数组为空: {}", jsonStr);
                return 0;
            }

            // 创建关键词到建议的映射
            Map<String, String> keywordToAdviceMap = new HashMap<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);
                if (item != null) {
                    String name = item.getString("name");
                    String introduce = item.getString("introduce");
                    String advice = item.getString("advice");

                    if (StringUtils.isNotBlank(name) && (StringUtils.isNotBlank(introduce) || StringUtils.isNotBlank(advice))) {
                        // 组合简要科普和通用健康建议
                        StringBuilder fullAdvice = new StringBuilder();
                        if (StringUtils.isNotBlank(introduce)) {
                            fullAdvice.append(introduce);
                        }
                        if (StringUtils.isNotBlank(advice)) {
                            if (!fullAdvice.isEmpty()) {
                                fullAdvice.append("\n");
                            }
                            fullAdvice.append(advice);
                        }

                        //找到匹配的建议对象
                        SummaryAdvice matchedAdvice = batch.stream().filter(a -> StringUtils.equals(a.getKeywords(), name)).findFirst().orElse(null);

                        if (matchedAdvice != null) {
                            matchedAdvice.setAdviceContent(fullAdvice.toString());
                            matchedAdvice.setCreateType("AI");
                            matchedAdvice.setIndexTime(new Date());
                            // 更新Lucene索引
                            updateLuceneIndex(matchedAdvice);
                            summaryAdviceMapper.updateById(matchedAdvice);
                            successCount++;
                        } else {
                            //插入新的建议
                            SummaryAdvice newAdvice = new SummaryAdvice();
                            newAdvice.setKeywords(name);
                            newAdvice.setAdviceContent(fullAdvice.toString());
                            newAdvice.setIndexTime(new Date());
                            newAdvice.setCreateType("AI");
                            summaryAdviceMapper.insert(newAdvice);
                            // 更新Lucene索引
                            updateLuceneIndex(newAdvice);
                            successCount++;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理AI响应时发生异常", e);
        }

        return successCount;
    }

    /**
     * 从AI响应中提取JSON结构
     */
    private String extractJsonFromResponse(String response) {
        if (StringUtils.isBlank(response)) {
            return null;
        }

        // 查找JSON数组的开始和结束位置
        int startIndex = response.indexOf('[');
        int endIndex = response.lastIndexOf(']');

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }

        // 如果没有找到数组，尝试查找对象
        startIndex = response.indexOf('{');
        endIndex = response.lastIndexOf('}');

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            String jsonStr = response.substring(startIndex, endIndex + 1);
            // 如果是单个对象，包装成数组
            return "[" + jsonStr + "]";
        }

        return null;
    }

    /**
     * 查找匹配的建议内容
     */
    private String findMatchingAdvice(String keyword, Map<String, String> keywordToAdviceMap) {
        // 精确匹配
        String advice = keywordToAdviceMap.get(keyword);
        if (StringUtils.isNotBlank(advice)) {
            return advice;
        }

        // 模糊匹配
        for (Map.Entry<String, String> entry : keywordToAdviceMap.entrySet()) {
            String key = entry.getKey();
            if (keyword.contains(key) || key.contains(keyword)) {
                return entry.getValue();
            }
        }

        return null;
    }

    @Override
    public long countAllIndexedDocuments() {
        try {
            // 使用通配符查询获取所有文档数量
            Query allDocsQuery = new MatchAllDocsQuery();
            TopDocs topDocs = luceneIndexManager.search(allDocsQuery, Integer.MAX_VALUE);
            return topDocs != null ? topDocs.totalHits : 0;
        } catch (Exception e) {
            log.warn("Failed to count indexed documents", e);
            return -1;
        }
    }



}
