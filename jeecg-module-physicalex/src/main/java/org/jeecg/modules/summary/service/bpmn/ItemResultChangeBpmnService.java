package org.jeecg.modules.summary.service.bpmn;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.ItemResultChangeApply;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.mapper.ItemResultChangeApplyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component(value = "itemResultChangeBpmnService")
@Slf4j
public class ItemResultChangeBpmnService {

    @Autowired
    private ItemResultChangeApplyMapper itemResultChangeApplyMapper;
    @Autowired
    private ISysBaseAPI sysBaseApi;

    public void sendMsg(DelegateExecution execution) {
        // 获取流程变量
        String businessKey = execution.getProcessBusinessKey();
        if (StringUtils.isNotBlank(businessKey)) {
           //发送系统消息
            try {
                String confirmBy = (String) execution.getVariable("confirmBy");
                String confirmer = (String) execution.getVariable("confirmer");
                String applyStatus = (String) execution.getVariable("applyStatus");

                UpdateWrapper<ItemResultChangeApply> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("status", applyStatus);
                updateWrapper.set("confirmer", confirmer);
                updateWrapper.set("confirm_by", confirmBy);
                updateWrapper.set("confirm_time", new Date());
                updateWrapper.eq("id", businessKey);
                itemResultChangeApplyMapper.update(null, updateWrapper);

                String msgReceiver =(String) execution.getVariable("msgReceiver");
                String msgContent  = (String) execution.getVariable("msgContent");
                MessageDTO md = new MessageDTO();
                md.setToAll(false);
                md.setTitle("报告数据修改申请结果");
                md.setContent(msgContent);
                md.setToUser(msgReceiver);
                sysBaseApi.sendSysAnnouncement(md);
            } catch (Exception e) {
                log.error("发送报告数据修改申请结果消息出错", e.getMessage());
            }
        } else {
            throw new RuntimeException("businessKey不能为空");
        }
    }

}
