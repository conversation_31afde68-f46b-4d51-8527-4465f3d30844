package org.jeecg.modules.summary.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.utils.DictTranslateUtil;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.OrgInfo;
import org.jeecg.modules.basicinfo.mapper.CompanyMapper;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.service.IOrgInfoService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.occu.entity.ZyConclusion;
import org.jeecg.modules.occu.entity.ZyConclusionDetail;
import org.jeecg.modules.occu.entity.ZyRiskFactorDisease;
import org.jeecg.modules.occu.mapper.ZyRiskFactorDiseaseMapper;
import org.jeecg.modules.occu.mapper.ZyRiskFactorItemgroupMapper;
import org.jeecg.modules.occu.service.IZyConclusionDetailService;
import org.jeecg.modules.occu.service.IZyConclusionService;
import org.jeecg.modules.reg.entity.*;
import org.jeecg.modules.reg.mapper.CompanyRegMapper;
import org.jeecg.modules.reg.mapper.CompanyTeamItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.summary.bo.teamreport.*;
import org.jeecg.modules.summary.entity.CompanyRegSummary;
import org.jeecg.modules.summary.mapper.CompanyRegSummaryMapper;
import org.jeecg.modules.summary.mapper.TeamReportMapper;
import org.jeecg.modules.summary.service.ISummaryAdviceService;
import org.jeecg.modules.summary.service.ITeamReportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TeamReportServiceImpl implements ITeamReportService {
    @Autowired
    private TeamReportMapper teamReportMapper;
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private CompanyRegMapper companyRegMapper;
    @Autowired
    private CompanyRegSummaryMapper companyRegSummaryMapper;
    @Autowired
    private CompanyTeamItemGroupMapper companyTeamItemGroupMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    private ISummaryAdviceService summaryAdviceService;
    @Autowired
    private AIService aiService;
    @Autowired
    private IZyConclusionService zyConclusionService;
    @Autowired
    private IZyConclusionDetailService zyConclusionDetailService;
    @Autowired
    private ZyRiskFactorItemgroupMapper zyRiskFactorItemgroupMapper;
    @Autowired
    private ZyRiskFactorDiseaseMapper zyRiskFactorDiseaseMapper;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private IOrgInfoService orgInfoService;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private DictTranslateUtil dictTranslateUtil;

    @Override
    public TeamReportVO getTeamReportStat(String companyRegId,String companyDeptId) {
        TeamReportVO reportVO = new TeamReportVO();
        reportVO.setCompany(companyMapper.getTeamExamCompany(companyRegId));
        CompanyReg companyReg = companyRegMapper.selectById(companyRegId);
        reportVO.setCompanyReg(companyReg);
        //团体使用套餐所含项目情况
        List<CompanyTeam> companyTeamItemGroups = companyTeamItemGroupMapper.selectByCompanyRegId(companyRegId);
        if (CollectionUtils.isNotEmpty(companyTeamItemGroups)) {
            for (CompanyTeam group : companyTeamItemGroups) {
                String teamItemNames = group.getCompanyTeamItemGroupList().stream().map(CompanyTeamItemGroup::getItemGroupName).collect(Collectors.joining("  "));
                group.setCompanyTeamItemGroupNames(teamItemNames);
            }
        }
        reportVO.setCompanyTeams(companyTeamItemGroups);

        //团体参检人数统计
        List<CustomerReg> customerRegs = customerRegMapper.getCustomerRegsByCompanyRegId(companyRegId,companyDeptId);
        TeamReportVO.CompanyTeamPersonStat teamPersonStat = new TeamReportVO.CompanyTeamPersonStat();
        teamPersonStat.setCheckedTotal(customerRegs.size());
        String maleTotal = customerRegs.stream().filter(item -> StringUtils.equals(item.getGender(), "男")).count() + "";
        String femaleTotal = customerRegs.stream().filter(item -> StringUtils.equals(item.getGender(), "女")).count() + "";
        teamPersonStat.setCheckedMaleTotal((Integer.valueOf(maleTotal)));
        teamPersonStat.setCheckedFemaleTotal((Integer.valueOf(femaleTotal)));
        DecimalFormat df = new DecimalFormat("#.##"); // 保留两位小数
        teamPersonStat.setCheckedMalePercentage(df.format(Double.parseDouble(maleTotal) / Integer.valueOf(customerRegs.size()).doubleValue()));
        teamPersonStat.setCheckedFemalePercentage(df.format(Double.parseDouble(femaleTotal) / Integer.valueOf(customerRegs.size()).doubleValue()));
        teamPersonStat.setMaleToFemaleRatio(df.format(Double.parseDouble(maleTotal) / Double.parseDouble(femaleTotal)));
        reportVO.setCompanyTeamPersonStat(teamPersonStat);
        //团检人员男女年龄分布
        TeamReportVO.AgeGroupsByGender ageGroupsByGender = new TeamReportVO.AgeGroupsByGender();
        Map<String, List<CustomerReg>> ageGroups = Maps.newLinkedHashMap();
        ageGroups.put("0-30", Lists.newArrayList());
        ageGroups.put("31-40", Lists.newArrayList());
        ageGroups.put("41-50", Lists.newArrayList());
        ageGroups.put("51-60", Lists.newArrayList());
        ageGroups.put("60以上", Lists.newArrayList());
        for (CustomerReg customerReg : customerRegs) {
            int age = customerReg.getAge();
            if (age <= 30) {
                ageGroups.get("0-30").add(customerReg);
            } else if (age <= 40) {
                ageGroups.get("31-40").add(customerReg);
            } else if (age <= 50) {
                ageGroups.get("41-50").add(customerReg);
            } else if (age <= 60) {
                ageGroups.get("51-60").add(customerReg);
            } else {
                ageGroups.get("60以上").add(customerReg);
            }
        }

        List<ValueAndLabel> ageGroupsByGenders = Lists.newArrayList();
        ageGroups.forEach((key, regs) -> {
            String maleNum = String.valueOf(regs.stream().filter(i -> StringUtils.equals(i.getGender(), "男")).count());
            String femaleNum = String.valueOf(regs.stream().filter(i -> StringUtils.equals(i.getGender(), "女")).count());
            ageGroupsByGenders.add(new ValueAndLabel(key, String.valueOf(regs.size()), maleNum, femaleNum));
            if (StringUtils.equals(key, "0-30")) {
                ageGroupsByGender.set_1_GroupTotal(String.valueOf(regs.size()));
                ageGroupsByGender.set_1_GroupMaleTotal(maleNum);
                ageGroupsByGender.set_1_GroupFemaleTotal(femaleNum);
            } else if (StringUtils.equals(key, "31-40")) {
                ageGroupsByGender.set_2_GroupTotal(String.valueOf(regs.size()));
                ageGroupsByGender.set_2_GroupMaleTotal(maleNum);
                ageGroupsByGender.set_2_GroupFemaleTotal(femaleNum);
            } else if (StringUtils.equals(key, "41-50")) {
                ageGroupsByGender.set_3_GroupTotal(String.valueOf(regs.size()));
                ageGroupsByGender.set_3_GroupMaleTotal(maleNum);
                ageGroupsByGender.set_3_GroupFemaleTotal(femaleNum);
            } else if (StringUtils.equals(key, "51-60")) {
                ageGroupsByGender.set_4_GroupTotal(String.valueOf(regs.size()));
                ageGroupsByGender.set_4_GroupMaleTotal(maleNum);
                ageGroupsByGender.set_4_GroupFemaleTotal(femaleNum);
            } else if (StringUtils.equals(key, "60以上")) {
                ageGroupsByGender.set_5_GroupTotal(String.valueOf(regs.size()));
                ageGroupsByGender.set_5_GroupMaleTotal(maleNum);
                ageGroupsByGender.set_5_GroupFemaleTotal(femaleNum);
            }

        });

        ageGroupsByGender.setAgeGroups(ageGroupsByGenders);
//        reportVO.setAgeGroupChart(this.getAgeGroupStat(companyRegId));
        reportVO.setAgeGroupChart(ageGroupsByGender);
        //异常结果汇总
        List<CompanyAbnormalItemResultVO> abnormalItemVOList = Lists.newArrayList();
        List<CompanyAbnormalItemResultVO> abnormalItems = teamReportMapper.getAbnormalItemsByCompanyRegId(companyRegId,companyDeptId);
        if (CollectionUtils.isNotEmpty(abnormalItems)) {
            List<String> itemGroupIds = abnormalItems.stream().map(CompanyAbnormalItemResultVO::getItemGroupId).collect(Collectors.toList());
            Map<String, List<ItemGroup>> itemGroupMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(itemGroupIds)) {
                itemGroupMap = itemGroupMapper.selectBatchIds(itemGroupIds).stream().collect(Collectors.groupingBy(ItemGroup::getId));
            }
            Map<String, List<CompanyAbnormalItemResultVO>> abnormalItemGroupByExamNo = abnormalItems.stream().filter(i->StringUtils.isNotBlank(i.getExamNo())).collect(Collectors.groupingBy(CompanyAbnormalItemResultVO::getExamNo));

            /*List<String> abnormalDescs = abnormalItems.stream().map(CompanyAbnormalItemVO::getAbnormalDesc).distinct().collect(Collectors.toList());
            List<SummaryAdvice> summaryAdvices = summaryAdviceService.list(new LambdaQueryWrapper<SummaryAdvice>().in(SummaryAdvice::getKeywords, abnormalDescs));
            Map<String, List<SummaryAdvice>> adviceMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(summaryAdvices)) {
                adviceMap = summaryAdvices.stream().collect(Collectors.groupingBy(SummaryAdvice::getKeywords));
            }*/
         /*   //获取ai解释
            List<SummaryAdvice> updateAdvices = Lists.newArrayList();
            Map<String, List<SummaryAdvice>> aiAdviceMap = Maps.newHashMap();
            try {
                //通过ai获取解释
                //去除数据库查到的
                abnormalDescs.removeAll(adviceMap.keySet());
                List<WordExplain> wordExplainList = aiService.explainWord(abnormalDescs);
                List<SummaryAdvice> aiAdvices = wordExplainList.stream().map(item -> {
                    SummaryAdvice summaryAdvice = new SummaryAdvice();
                    summaryAdvice.setKeywords(item.getWord());
                    summaryAdvice.setKeywordsExplain(item.getExplain());
                    summaryAdvice.setAdviceContent(item.getAdvice());
                    return summaryAdvice;
                }).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(aiAdvices)) {
                    aiAdviceMap = aiAdvices.stream().filter(a -> org.apache.commons.lang.StringUtils.isNotBlank(a.getKeywords())).collect(Collectors.groupingBy(SummaryAdvice::getKeywords));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }*/

            Map<String, List<CompanyAbnormalItemResultVO>> resultMap = abnormalItems.stream().collect(Collectors.groupingBy(CompanyAbnormalItemResultVO::getAbnormalDesc));
            for (String abnormalDesc : resultMap.keySet()) {
                List<CompanyAbnormalItemResultVO> abnormalList = resultMap.get(abnormalDesc);
                CompanyAbnormalItemResultVO abnormalItemVO = abnormalList.get(0);
                Long count = abnormalList.stream().map(CompanyAbnormalItemResultVO::getCustomerRegId).distinct().count();
                abnormalItemVO.setTotal(count.intValue());
                if (CollectionUtils.isNotEmpty(itemGroupMap.get(abnormalItemVO.getItemGroupId())))
                    abnormalItemVO.setSexLimit(CollectionUtils.isNotEmpty(itemGroupMap.get(abnormalItemVO.getItemGroupId())) ? itemGroupMap.get(abnormalItemVO.getItemGroupId()).get(0).getSexLimit() : "不限");
                List<String> nameList = Lists.newArrayList();
                abnormalList.stream().map(CompanyAbnormalItemResultVO::getExamNo).distinct().forEach(item -> {
                    nameList.add(abnormalItemGroupByExamNo.get(item).get(0).getName());
                });
                abnormalItemVO.setNameList(nameList.stream().collect(Collectors.joining(",")));
                //查询名词解释和建议
               /*  if (CollectionUtils.isNotEmpty(adviceMap.get(abnormalDesc)) && StringUtils.isNotBlank(adviceMap.get(abnormalDesc).get(0).getAdviceContent())) {
                    abnormalItemVO.setAdvice(adviceMap.get(abnormalDesc).get(0).getAdviceContent());
                    abnormalItemVO.setExplain(adviceMap.get(abnormalDesc).get(0).getKeywordsExplain());
                   if (StringUtils.isNotBlank(adviceMap.get(abnormalDesc).get(0).getKeywordsExplain())) {
                        abnormalItemVO.setExplain(adviceMap.get(abnormalDesc).get(0).getKeywordsExplain());
                    } else {
                        List<SummaryAdvice> aiAdvices = aiAdviceMap.get(abnormalDesc);
                        if (CollectionUtils.isNotEmpty(aiAdvices)) {
                            abnormalItemVO.setExplain(aiAdvices.get(0).getKeywordsExplain());

                            SummaryAdvice summaryAdvice = summaryAdvices.get(0);
                            summaryAdvice.setKeywordsExplain(aiAdvices.get(0).getKeywordsExplain());
                            summaryAdvice.setIndexTime(new Date());
                            updateAdvices.add(summaryAdvice);
                        }
                    }
                }*/
            /*    else {
                    List<SummaryAdvice> aiAdvices = aiAdviceMap.get(abnormalDesc);
                    if (CollectionUtils.isNotEmpty(aiAdvices)) {
                        abnormalItemVO.setAdvice(aiAdvices.get(0).getAdviceContent());
                        abnormalItemVO.setExplain(aiAdvices.get(0).getKeywordsExplain());
                        SummaryAdvice summaryAdvice = aiAdvices.get(0);
                        summaryAdvice.setIndexTime(new Date());
                        summaryAdvice.setCreateTime(new Date());
                        updateAdvices.add(summaryAdvice);
                    }
                }*/

//                    abnormalItemVO.setAdvice("认识肥胖症的危害；认识到单纯性肥胖是一种生活方式疾病；认识治疗肥胖症是一 个自我控制的过\n" +
//                            "程。改变生活方式，矫正饮食、行为习惯。");
//                    abnormalItemVO.setExplain("体重超重或肥胖是指体内脂肪积聚过多和（或）分布异常、体重增加，是\n" +
//                            "由于遗传或环境因素共同作用的结果。1999 年，世界卫生组织己正式宣布肥胖为一种疾病。");
                abnormalItemVOList.add(abnormalItemVO);
            }
//        summaryAdviceService.saveBatch(insertAdvices);
//            summaryAdviceService.saveOrUpdateBatch(updateAdvices);

            //团检人员异常结果汇总
            reportVO.setAbnormalItemVOS(abnormalItemVOList);
            //男女性专科分类通过item_group表的性别限制作为区分标志
            //男性专科异常结果汇总
            reportVO.setMaleAbnormalItms(abnormalItemVOList.stream().filter(i -> StringUtils.equals(i.getSexLimit(), "男")).collect(Collectors.toList()));
            //女性专科异常结果汇总
            reportVO.setFemaleAbnormalItms(abnormalItemVOList.stream().filter(i -> StringUtils.equals(i.getSexLimit(), "女")).collect(Collectors.toList()));

            //疾病人员名单
            List<CompanyAbnormalItemResultVO> abnormalItemsByPerson = Lists.newArrayList();
            abnormalItemGroupByExamNo.forEach((eaxmNo, rets) -> {
                CompanyAbnormalItemResultVO abnormalItemVO = new CompanyAbnormalItemResultVO();
                abnormalItemVO.setName(rets.get(0).getName());
                abnormalItemVO.setGender(rets.get(0).getGender());
                abnormalItemVO.setAge(rets.get(0).getAge());
                abnormalItemVO.setAbnormalItemList(rets.stream().map(i -> "[" + i.getAbnormalDesc() + "]").collect(Collectors.joining()));
                abnormalItemsByPerson.add(abnormalItemVO);
            });
            reportVO.setAbnormalItemListByPerson(abnormalItemsByPerson);

            reportVO.setDepartAbnormalChart(this.getDepartAbnormalStat(companyRegId,companyDeptId));
            reportVO.setTop10tAbnormalConditionChart(this.getTop10tAbnormalConditionStat(companyRegId,companyDeptId));
            reportVO.setB_DepartmentAbnormalChart(this.getB_DepartmentAbnormalStat(companyRegId,companyDeptId));
            reportVO.setAbnormalGeneralSummary(this.getAbnormalGeneralSummaryStat(companyRegId, companyReg,companyDeptId));
            reportVO.setAbnormalConditionList(this.getAbnormalConditionListStat(companyRegId));
            reportVO.setAbnormalItemAndNameList(this.getAbnormalItemAndNameListStat(companyRegId,companyDeptId));
        }
        return reportVO;
    }

    @Override
    public ZyTeamReportVO getZyTeamReportStat(String companyRegId, String companyDeptId) {
        ZyTeamReportVO reportVO = new ZyTeamReportVO();
        //查询团检医院
        OrgInfo defultOrgInfo = orgInfoService.getDefultOrgInfo();
        defultOrgInfo.setQualityPictureList(Arrays.asList(StringUtils.split(defultOrgInfo.getQualityPicture(),",")));
        reportVO.setOrgInfo(defultOrgInfo);
        reportVO.setCompany(companyMapper.getTeamExamCompany(companyRegId));
        CompanyReg companyReg = companyRegMapper.selectById(companyRegId);
        reportVO.setCompanyReg(companyReg);
        //团体使用套餐所含项目情况
        List<CompanyTeam> companyTeamItemGroups = companyTeamItemGroupMapper.selectByCompanyRegId(companyRegId);
        if (CollectionUtils.isNotEmpty(companyTeamItemGroups)) {
            for (CompanyTeam group : companyTeamItemGroups) {
                String teamItemNames = group.getCompanyTeamItemGroupList().stream().map(CompanyTeamItemGroup::getItemGroupName).collect(Collectors.joining("  "));
                group.setCompanyTeamItemGroupNames(teamItemNames);
            }
        }
        reportVO.setCompanyTeams(companyTeamItemGroups);

        //团体参检人数统计
        List<CustomerReg> customerRegs = customerRegMapper.getCustomerRegsByCompanyRegId(companyRegId, companyDeptId);
        if (CollectionUtils.isEmpty(customerRegs)) {
            return null;
        }
        customerRegs.forEach(customerReg -> {
            dictTranslateUtil.translate(customerReg);
            customerReg.setRiskFactor(customerReg.getRiskFactor_dictText());
            customerReg.setWorkType(customerReg.getWorkType_dictText());
            customerReg.setWorkShop(customerReg.getWorkShop_dictText());
            customerReg.setJobStatus(customerReg.getJobStatus_dictText());
        });

        //团检下所有人员customerRegId
        List<String> customerRegIds = customerRegs.stream().map(CustomerReg::getId).distinct().toList();
        //获取团检下所有人员的风险因素
        Set<String> riskFactorSet = Sets.newHashSet();
        customerRegs.stream().forEach(r->{
            if (StringUtils.isNotBlank(r.getRiskFactor())) {
                Set<String> riskList = Arrays.asList(StringUtils.split(r.getRiskFactor(), ",")).stream().sorted().collect(Collectors.toSet());
                riskFactorSet.addAll(riskList);
                r.setRiskFactor(StringUtils.join(riskList,","));
            }
        });
        //根据风险因素查询岗位类别、职业病和职业病禁忌症
        if (CollectionUtils.isNotEmpty(riskFactorSet)) {
            List<ZyRiskFactorDisease> riskDiseases = zyRiskFactorDiseaseMapper.getZyRiskDiseaseByRiskFactors(riskFactorSet);
            reportVO.setRiskFactorDiseases(riskDiseases);
        }
        //根据风险因素分组查询检查人数和必检项目
        Map<String, List<CustomerReg>> customerRegGroupByRiskFactor = customerRegs.stream().collect(Collectors.groupingBy(i -> i.getJobStatus() + ";" + i.getRiskFactor()));
        List<ZyCompanyRisk2CheckItem> risk2CheckItems=Lists.newArrayList();
        customerRegGroupByRiskFactor.forEach((risk,regs)->{
            String[] risk2workType = StringUtils.split(risk,";");
            if (risk2workType.length==2){
                ZyCompanyRisk2CheckItem risk2CheckItem = new ZyCompanyRisk2CheckItem();
                risk2CheckItem.setRiskFactors(risk2workType[1]);
                risk2CheckItem.setWorkType(risk2workType[0]);
                risk2CheckItem.setActualCheckTotal(regs.size());
                risk2CheckItem.setShouldCheckTotal(regs.size());
                List<String> itemGroupNames = zyRiskFactorItemgroupMapper.getItemGroupByRiskFactors(Arrays.asList(StringUtils.split(risk2workType[1], ",")));
                risk2CheckItem.setNeedCheckItemGroups(StringUtils.join(itemGroupNames,","));
                risk2CheckItems.add(risk2CheckItem);
            }

        });
        reportVO.setRiskMustItem(risk2CheckItems);
        //总体数据
        ZyTeamReportVO.ZyCompanyOverview zyCompanyOverview = new ZyTeamReportVO.ZyCompanyOverview();
        zyCompanyOverview.setJobStatus(customerRegs.stream().map(CustomerReg::getJobStatus).distinct().collect(Collectors.joining(",")));
        zyCompanyOverview.setActualCheckPersonNumber(customerRegs.size());
        zyCompanyOverview.setExamStartTime(customerRegs.stream().min(Comparator.comparing(CustomerReg::getRegTime)).get().getRegTime());
        zyCompanyOverview.setExamEndTime(customerRegs.stream().max(Comparator.comparing(CustomerReg::getRegTime)).get().getRegTime());
        Set<String> riskSet = Sets.newHashSet();
        for (String riskFactor : customerRegs.stream().filter(i -> StringUtils.isNotBlank(i.getRiskFactor())).map(CustomerReg::getRiskFactor).toList()) {
            riskSet.addAll(Arrays.asList(StringUtils.split(riskFactor, ",")));
        }
        zyCompanyOverview.setRiskFactors(StringUtils.join(riskSet, ","));

         //查询职业检医生
        String summaryDoctor = sysSettingService.getValueByCode("zySummaryDoctor");
        String auditDoctor = sysSettingService.getValueByCode("zyAuditDoctor");
        String reportDoctor = sysSettingService.getValueByCode("zyReportDoctor");
        zyCompanyOverview.setSummaryDoctor(summaryDoctor);
        zyCompanyOverview.setAuditDoctor(auditDoctor);
        zyCompanyOverview.setReportDoctor(reportDoctor);
        reportVO.setZyCompanyOverview(zyCompanyOverview);

        List<CompanyAbnormalItemResultVO> allItemResults = teamReportMapper.getAllItemResultByCompanyRegId(companyRegId, companyDeptId);
        List<CompanyAbnormalItemResultVO> abnormalItems = allItemResults.stream().filter(i -> Objects.equals(i.getAbnormalFlag(), "1")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allItemResults)) {
            //查询职业检明细结论
            List<ZyConclusionDetail> zyConclusionDetails = zyConclusionDetailService.list(new LambdaQueryWrapper<ZyConclusionDetail>().in(ZyConclusionDetail::getCustomerRegId, customerRegIds));
            Map<String, List<ZyConclusionDetail>> zyConDetailsGroupByRegId=Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(zyConclusionDetails)){
                 zyConDetailsGroupByRegId = zyConclusionDetails.stream().collect(Collectors.groupingBy(ZyConclusionDetail::getCustomerRegId));
                Map<String, List<ZyConclusionDetail>> zyConDetailGroupByRisk = zyConclusionDetails.stream().collect(Collectors.groupingBy(i->i.getWorkType()+";"+i.getRiskFactor()));
                List< ZyTeamReportVO.ZyRiskPersonNumberOverview> riskPersonNumberOverviews=Lists.newArrayList();
                zyConDetailGroupByRisk.forEach((riskFactor,conclusionDetails)->{
                    String[] risk2WorkType = StringUtils.split(riskFactor,";");
                    ZyTeamReportVO.ZyRiskPersonNumberOverview zyRiskPersonNumberOverview = new ZyTeamReportVO.ZyRiskPersonNumberOverview();
                    if (risk2WorkType.length==2) {
                        zyRiskPersonNumberOverview.setRiskFactor(risk2WorkType[1]);
                        zyRiskPersonNumberOverview.setJobStatus(risk2WorkType[0]);
                    }
                    long suspectedOccuDiseaseNum = conclusionDetails.stream().filter(i -> StringUtils.equals(i.getConclusion(), "疑似职业病")).count();
                    long occuContraindicationNum = conclusionDetails.stream().filter(i -> StringUtils.equals(i.getConclusion(), "职业禁忌证")).count();
                    long reCheckNum = conclusionDetails.stream().filter(i -> StringUtils.equals(i.getConclusion(), "复查")).count();
                    long noAbnormalNum = conclusionDetails.stream().filter(i -> StringUtils.equals(i.getConclusion(), "目前未见异常")).count();
                    long otherAbnormalNum = conclusionDetails.stream().filter(i -> StringUtils.equals(i.getConclusion(), "其他疾病或异常")).count();
                    zyRiskPersonNumberOverview.setNoAbnormalNum(noAbnormalNum);
                    zyRiskPersonNumberOverview.setOtherAbnormalNum(otherAbnormalNum);
                    zyRiskPersonNumberOverview.setSuspectedOccuDiseaseNum(suspectedOccuDiseaseNum);
                    zyRiskPersonNumberOverview.setOccuContraindicationNum(occuContraindicationNum);
                    zyRiskPersonNumberOverview.setReCheckNum(reCheckNum);
                    riskPersonNumberOverviews.add(zyRiskPersonNumberOverview);
                });
              reportVO.setZyRiskPersonNumberOverviews(riskPersonNumberOverviews);
            }

          //异常结果汇总
            List<ZyCompanyAbnormalResult> zyCompanyAbnormalResults = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(abnormalItems)) {
                Map<String, List<CompanyAbnormalItemResultVO>> abnormalItemGroupByExamNo = abnormalItems.stream().collect(Collectors.groupingBy(CompanyAbnormalItemResultVO::getExamNo));
                Map<String, List<CompanyAbnormalItemResultVO>> resultMap = abnormalItems.stream().collect(Collectors.groupingBy(CompanyAbnormalItemResultVO::getAbnormalDesc));
                for (String abnormalDesc : resultMap.keySet()) {
                    ZyCompanyAbnormalResult zyCompanyAbnormalResult = new ZyCompanyAbnormalResult();
                    zyCompanyAbnormalResult.setAbnormalFlagDesc(abnormalDesc);
                    List<CompanyAbnormalItemResultVO> abnormalList = resultMap.get(abnormalDesc);
                    zyCompanyAbnormalResult.setAbnormalPersonList(abnormalList);
                    zyCompanyAbnormalResults.add(zyCompanyAbnormalResult);
                }
                //团检人员异常结果汇总
                reportVO.setAbnormalItemPersonList(zyCompanyAbnormalResults);
            }
            //查询职业检总结结论，并按照customerRegId分组
            Map<String, ZyConclusion> zyConclusionGroupByRegId = zyConclusionService.list(new LambdaQueryWrapper<ZyConclusion>().in(ZyConclusion::getCustomerRegId, customerRegIds)).stream().collect(Collectors.toMap(ZyConclusion::getCustomerRegId, group -> group, (group1, group2) -> group1));
            Map<String, List<CompanyAbnormalItemResultVO>> allItemResultGroupByRegId = allItemResults.stream().collect(Collectors.groupingBy(CompanyAbnormalItemResultVO::getCustomerRegId));
            Map<String, List<CustomerRegItemGroup>> itemGroupsGroupByRegId = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().in(CustomerRegItemGroup::getCustomerRegId, customerRegIds)).stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getCustomerRegId));
            //组装每一个的检查数据
            for (CustomerReg reg:customerRegs){
                    reg.setZyConclusion(zyConclusionGroupByRegId.get(reg.getId()));
                    reg.setZyConclusionDetails(zyConDetailsGroupByRegId.get(reg.getId()));
                    reg.setItemResults(allItemResultGroupByRegId.get(reg.getId()));
                    reg.setGroupList(itemGroupsGroupByRegId.get(reg.getId()));
            }
            //人员检查一览表
            List<CompanyAbnormalItemResultVO> personList = Lists.newArrayList();
            customerRegs.forEach(reg->{
                CompanyAbnormalItemResultVO abnormalItemVO=new CompanyAbnormalItemResultVO();
                if (CollectionUtils.isNotEmpty(reg.getItemResults())) {
                     abnormalItemVO = reg.getItemResults().get(0);
                     abnormalItemVO.setRiskFactor(reg.getRiskFactor_dictText());
                     abnormalItemVO.setWorkShop(reg.getWorkShop_dictText());
                     abnormalItemVO.setWorkType(reg.getWorkType_dictText());
                     abnormalItemVO.setJobStatus(reg.getJobStatus_dictText());
                    abnormalItemVO.setAbnormalItemList(reg.getItemResults().stream().filter(i->StringUtils.equals(i.getAbnormalFlag(),"1")).map(CompanyAbnormalItemResultVO::getAbnormalDesc).collect(Collectors.joining(",")));
                }else{
                    BeanUtils.copyProperties(reg,abnormalItemVO);
                }
                abnormalItemVO.setZyConclusion(reg.getZyConclusion());
                personList.add(abnormalItemVO);
            });
            reportVO.setAllPersonList(personList);

            //查询疑似职业病的人员
            List<CompanyAbnormalItemResultVO> occuDiseasePensonList = Lists.newArrayList();
            List<CustomerReg> occuDiseaseRegs = customerRegs.stream().filter(i -> CollectionUtils.isNotEmpty(i.getZyConclusionDetails())&&i.getZyConclusionDetails().stream().anyMatch(cd -> StringUtils.equals(cd.getConclusion(), "疑似职业病"))).toList();
            occuDiseaseRegs.forEach(reg->{
                CompanyAbnormalItemResultVO abnormalItemVO=new CompanyAbnormalItemResultVO();
                if (CollectionUtils.isNotEmpty(reg.getItemResults())) {
                    abnormalItemVO = reg.getItemResults().get(0);
                    abnormalItemVO.setAbnormalItemList(reg.getItemResults().stream().filter(i->StringUtils.equals(i.getAbnormalFlag(),"1")).map(CompanyAbnormalItemResultVO::getAbnormalDesc).collect(Collectors.joining(",")));
                }else{
                    BeanUtils.copyProperties(reg,abnormalItemVO);
                }
                abnormalItemVO.setZyConclusion(reg.getZyConclusion());
                occuDiseasePensonList.add(abnormalItemVO);
            });
            reportVO.setOccuDiseasePersonList(occuDiseasePensonList);
            //查询职业禁忌症的人员
            List<CompanyAbnormalItemResultVO> occuContradicationPersonList = Lists.newArrayList();
            List<CustomerReg> occuContradicationRegs = customerRegs.stream().filter(i -> CollectionUtils.isNotEmpty(i.getZyConclusionDetails())&&i.getZyConclusionDetails().stream().anyMatch(cd -> StringUtils.equals(cd.getConclusion(), "职业禁忌证"))).toList();
            occuContradicationRegs.forEach(reg->{
                CompanyAbnormalItemResultVO abnormalItemVO=new CompanyAbnormalItemResultVO();
                if (CollectionUtils.isNotEmpty(reg.getItemResults())) {
                    abnormalItemVO = reg.getItemResults().get(0);
                    abnormalItemVO.setAbnormalItemList(reg.getItemResults().stream().filter(i->StringUtils.equals(i.getAbnormalFlag(),"1")).map(CompanyAbnormalItemResultVO::getAbnormalDesc).collect(Collectors.joining(",")));
                }else{
                    BeanUtils.copyProperties(reg,abnormalItemVO);
                }
                abnormalItemVO.setZyConclusion(reg.getZyConclusion());
                occuContradicationPersonList.add(abnormalItemVO);
            });
            reportVO.setOccuContradicationPersonList(occuContradicationPersonList);

            //查询复查的人员
            List<CompanyAbnormalItemResultVO> recheckPersonList = Lists.newArrayList();
            List<CustomerReg> recheckRegs = customerRegs.stream().filter(i -> CollectionUtils.isNotEmpty(i.getZyConclusionDetails())&&i.getZyConclusionDetails().stream().anyMatch(cd -> StringUtils.equals(cd.getConclusion(), "复查"))).toList();
            recheckRegs.forEach(reg->{
                CompanyAbnormalItemResultVO abnormalItemVO=new CompanyAbnormalItemResultVO();
                if (CollectionUtils.isNotEmpty(reg.getItemResults())) {
                    abnormalItemVO = reg.getItemResults().get(0);
                    abnormalItemVO.setAbnormalItemList(reg.getItemResults().stream().filter(i->StringUtils.equals(i.getAbnormalFlag(),"1")).map(CompanyAbnormalItemResultVO::getAbnormalDesc).collect(Collectors.joining(",")));
                }else{
                    BeanUtils.copyProperties(reg,abnormalItemVO);
                }
                abnormalItemVO.setZyConclusion(reg.getZyConclusion());
                recheckPersonList.add(abnormalItemVO);
            });
            reportVO.setRecheckPersonList(recheckPersonList);

            //查询有未检项目人员
            List<CompanyAbnormalItemResultVO> haveNoCheckGroupPersonList = Lists.newArrayList();
            List<CustomerReg> haveNoCheckGroupRegs = customerRegs.stream().filter(i -> CollectionUtils.isNotEmpty(i.getGroupList())&& i.getGroupList().stream().anyMatch(g -> StringUtils.equals(g.getCheckStatus(), "未检"))).toList();
            haveNoCheckGroupRegs.forEach(reg->{
                CompanyAbnormalItemResultVO abnormalItemVO=new CompanyAbnormalItemResultVO();
                if (CollectionUtils.isNotEmpty(reg.getItemResults())) {
                    abnormalItemVO = reg.getItemResults().get(0);
                    abnormalItemVO.setAbnormalItemList(reg.getItemResults().stream().filter(i->StringUtils.equals(i.getAbnormalFlag(),"1")).map(CompanyAbnormalItemResultVO::getAbnormalDesc).collect(Collectors.joining(",")));
                }else{
                    BeanUtils.copyProperties(reg,abnormalItemVO);
                }
                abnormalItemVO.setZyConclusion(reg.getZyConclusion());
                haveNoCheckGroupPersonList.add(abnormalItemVO);
            });
            reportVO.setRecheckPersonList(haveNoCheckGroupPersonList);

            //查询没有异常的人员
            List<CompanyAbnormalItemResultVO> noAbnormalPersonList = Lists.newArrayList();
            List<CustomerReg> noAbnormalRegs = customerRegs.stream().filter(i ->CollectionUtils.isNotEmpty(i.getItemResults())&& i.getItemResults().stream().allMatch( r-> StringUtils.equals(r.getAbnormalFlag(), "0"))).toList();
            noAbnormalRegs.forEach(reg->{
                CompanyAbnormalItemResultVO abnormalItemVO=new CompanyAbnormalItemResultVO();
                if (CollectionUtils.isNotEmpty(reg.getItemResults())) {
                    abnormalItemVO = reg.getItemResults().get(0);
//                    abnormalItemVO.setAbnormalItemList(reg.getItemResults().stream().map(CompanyAbnormalItemResultVO::getAbnormalDesc).collect(Collectors.joining()));
                }else{
                    BeanUtils.copyProperties(reg,abnormalItemVO);
                }
                abnormalItemVO.setZyConclusion(reg.getZyConclusion());
                noAbnormalPersonList.add(abnormalItemVO);
            });
            reportVO.setRecheckPersonList(noAbnormalPersonList);
            //查询其他异常的人员
            List<CompanyAbnormalItemResultVO> otherAbnormalPersonList = Lists.newArrayList();
            List<CustomerReg> otherAbnormalRegs = customerRegs.stream().filter(i -> CollectionUtils.isNotEmpty(i.getZyConclusionDetails())&&i.getZyConclusionDetails().stream().allMatch(cd -> StringUtils.equals(cd.getConclusion(), "其他疾病或异常"))).toList();
            otherAbnormalRegs.forEach(reg->{
                CompanyAbnormalItemResultVO abnormalItemVO=new CompanyAbnormalItemResultVO();
                if (CollectionUtils.isNotEmpty(reg.getItemResults())) {
                    abnormalItemVO = reg.getItemResults().get(0);
                    abnormalItemVO.setAbnormalItemList(reg.getItemResults().stream().filter(i->StringUtils.equals(i.getAbnormalFlag(),"1")).map(CompanyAbnormalItemResultVO::getAbnormalDesc).collect(Collectors.joining(",")));
                }else{
                    BeanUtils.copyProperties(reg,abnormalItemVO);
                }
                abnormalItemVO.setZyConclusion(reg.getZyConclusion());
                otherAbnormalPersonList.add(abnormalItemVO);
            });
            reportVO.setRecheckPersonList(otherAbnormalPersonList);

        }
        return reportVO;
    }

    @Override
    public List<ValueAndLabel> getAgeGroupStat(String companyRegId) {
        Map<String, BigDecimal> ageGroupStat = teamReportMapper.getAgeGroupStat(companyRegId);
        List<ValueAndLabel> ageGroupStatList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(ageGroupStat)) {
            ageGroupStat.forEach((key, item) -> {
                switch (key) {
                    case "a":
                        key = "<25岁";
                        break;
                    case "b":
                        key = "25~30岁";
                        break;
                    case "c":
                        key = "31~40岁";
                        break;
                    case "d":
                        key = "41~50岁";
                        break;
                    case "e":
                        key = "51~60岁";
                        break;
                    default:
                        key = ">60岁";
                        break;
                }

                ageGroupStatList.add(new ValueAndLabel(item.toString(), key));
            });
        }
        return ageGroupStatList;


    }

    @Override
    public List<ValueAndLabel> getDepartAbnormalStat(String companyRegId,String companyDeptId) {
        return CollectionUtils.isNotEmpty(teamReportMapper.getDepartAbnormalStat(companyRegId,companyDeptId)) ? teamReportMapper.getDepartAbnormalStat(companyRegId,companyDeptId) : Lists.newArrayList();
    }

    @Override
    public List<ValueAndLabel> getTop10tAbnormalConditionStat(String companyRegId,String companyDeptId) {
        return CollectionUtils.isNotEmpty(teamReportMapper.getTop10tAbnormalConditionStat(companyRegId,companyDeptId)) ? teamReportMapper.getTop10tAbnormalConditionStat(companyRegId,companyDeptId) : Lists.newArrayList();
    }

    @Override
    public List<ValueAndLabel> getB_DepartmentAbnormalStat(String companyRegId,String companyDeptId) {
        return CollectionUtils.isNotEmpty(teamReportMapper.getB_DepartmentAbnormalStat(companyRegId,companyDeptId)) ? teamReportMapper.getB_DepartmentAbnormalStat(companyRegId,companyDeptId) : Lists.newArrayList();
    }

    @Override
    public AbnormalGeneralSummary getAbnormalGeneralSummaryStat(String companyRegId, CompanyReg companyReg,String companyDeptId) {
        AbnormalGeneralSummary checkedGeneralStat = teamReportMapper.getCheckedGeneralStat(companyRegId,companyDeptId);
        AbnormalGeneralSummary genderGeneralStat = teamReportMapper.getGenderGeneralStat(companyRegId,companyDeptId);
        if (Objects.nonNull(checkedGeneralStat) && Objects.nonNull(genderGeneralStat)) {

//                checkedGeneralStat.setPersonTotal(genderGeneralStat.personTotal);
            checkedGeneralStat.setPersonTotal(companyReg.getPersonCount());
            checkedGeneralStat.setMaleTotal(genderGeneralStat.getMaleTotal());
            checkedGeneralStat.setMalePercent(genderGeneralStat.getMalePercent());
            checkedGeneralStat.setFemaleTotal(genderGeneralStat.getFemaleTotal());
            checkedGeneralStat.setFemalePercent(genderGeneralStat.getFemalePercent());
            checkedGeneralStat.setMaleToFemaleRatio(checkedGeneralStat.getMaleCheckedTotal() + ":" + checkedGeneralStat.getFemaleCheckedTotal());
            //按性别统计人数
            List<ValueAndLabel> checkedPersonTotalByGender = Lists.newArrayList();
            checkedPersonTotalByGender.add(new ValueAndLabel(String.valueOf(checkedGeneralStat.getMaleCheckedTotal()), "男"));
            checkedPersonTotalByGender.add(new ValueAndLabel(String.valueOf(checkedGeneralStat.getFemaleCheckedTotal()), "女"));
            checkedGeneralStat.setCheckedPersonTotalByGender(checkedPersonTotalByGender);
            //按性别统计人数比例
            List<ValueAndLabel> checkedPersonPercentageByGender = Lists.newArrayList();
            checkedPersonPercentageByGender.add(new ValueAndLabel(String.valueOf(checkedGeneralStat.getMaleCheckedPercent()), "男"));
            checkedPersonPercentageByGender.add(new ValueAndLabel(String.valueOf(checkedGeneralStat.getFemaleCheckedPercent()), "女"));
            checkedGeneralStat.setCheckedPersonPercentageByGender(checkedPersonPercentageByGender);
            //按检查状态统计人数
            List<ValueAndLabel> setPersonTotalStatByCheckState = Lists.newArrayList();
            setPersonTotalStatByCheckState.add(new ValueAndLabel(String.valueOf(checkedGeneralStat.getPersonTotal()), "应到人数"));
            setPersonTotalStatByCheckState.add(new ValueAndLabel(String.valueOf(checkedGeneralStat.getCheckedTotal()), "实到人数"));
            setPersonTotalStatByCheckState.add(new ValueAndLabel(String.valueOf(checkedGeneralStat.getPersonTotal() - checkedGeneralStat.getCheckedTotal()), "未检人数"));
            checkedGeneralStat.setPersonTotalStatByCheckState(setPersonTotalStatByCheckState);
        }

        return checkedGeneralStat;
    }

    @Override
    public List<AbnormalCondition> getAbnormalConditionListStat(String companyRegId) {
        //        return  teamReportMapper.getAbnormalConditionListStat(companyRegId);
        CompanyRegSummary companyRegSummary = companyRegSummaryMapper.selectOne(new LambdaQueryWrapper<CompanyRegSummary>().eq(CompanyRegSummary::getCompanyRegId, companyRegId));
        if (Objects.nonNull(companyRegSummary)) {
            String abnormalSummary = companyRegSummary.getAbnormalSummary();
            Integer checkedTotal = companyRegSummary.getCheckedTotal();
            CompanyAbnormalSummary summary = JSONObject.parseObject(abnormalSummary, CompanyAbnormalSummary.class);
            List<CompanyAbnormalSummary.AbnormalInfo> abnormalInfos = CollectionUtils.isNotEmpty(summary.getAbnormalInfos()) ? summary.getAbnormalInfos() : Lists.newArrayList();
            return abnormalInfos.stream().map(item -> {
                AbnormalCondition abnormalCondition = new AbnormalCondition();
                BeanUtils.copyProperties(item, abnormalCondition);

                BigDecimal percentage = new BigDecimal(item.getTotal()).divide(new BigDecimal(checkedTotal), 2, RoundingMode.HALF_UP);
                String formattedPercentage = String.format(percentage.toString(), percentage.multiply(new BigDecimal(100)));

                abnormalCondition.setPercent(formattedPercentage + "%");
                return abnormalCondition;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();

    }

    @Override
    public List<TeamReportVO.AbnormalItemAndNameVO> getAbnormalItemAndNameListStat(String companyRegId,String companyDeptId) {
        List<AbnormalItemAndName> abnormalItemAndNameList = teamReportMapper.getAbnormalItemAndNameListStat(companyRegId,companyDeptId);
        List<TeamReportVO.AbnormalItemAndNameVO> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(abnormalItemAndNameList)) {
            Map<String, List<AbnormalItemAndName>> map = abnormalItemAndNameList.stream().collect(Collectors.groupingBy(i -> i.getDepartmentName()));
            map.forEach((k, v) -> {
                TeamReportVO.AbnormalItemAndNameVO vo = new TeamReportVO.AbnormalItemAndNameVO();
                vo.setDepartmentName(k);
                vo.setDepartmentNo(v.get(0).departmentNo);
                List<TeamReportVO.AbnormalItemAndNameVO.AbnormalItemVO> itemVOS = v.stream().map(item -> {
                    TeamReportVO.AbnormalItemAndNameVO.AbnormalItemVO itemVO = new TeamReportVO.AbnormalItemAndNameVO.AbnormalItemVO();
                    itemVO.setAbnormalDesc(item.getAbnormalDesc());
                    itemVO.setNameList(item.getNameList());
                    itemVO.setTotal(item.getTotal());
                    itemVO.setPercentage(item.getPercentage());
                    return itemVO;

                }).collect(Collectors.toList());
                vo.setAbnormalItem(itemVOS);
                list.add(vo);
            });
        }
        return list;
    }
}
