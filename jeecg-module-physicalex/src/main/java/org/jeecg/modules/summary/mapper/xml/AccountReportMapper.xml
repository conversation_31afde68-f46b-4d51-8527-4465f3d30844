<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.summary.mapper.AccountReportMapper">

    <select id="pageAccountReport" resultType="org.jeecg.modules.summary.entity.AccountReport">
        select a.*,c.name,c.exam_no,c.gender,c.age,c.id_card,c.summary_audit_time as report_time from account_report a join customer_reg c on a.customer_reg_id = c.id where a.account_id = #{accountId} order by a.create_time desc
    </select>
    <select id="pageAccountReportByIdCard" resultType="org.jeecg.modules.summary.entity.AccountReport">
        select c.id as customer_reg_id, c.name,c.exam_no,c.gender,c.age,c.id_card,c.summary_audit_time as report_time from customer_reg c  where c.id_card = #{idCard} and c.summary_status='审核通过' order by c.create_time desc
    </select>
</mapper>