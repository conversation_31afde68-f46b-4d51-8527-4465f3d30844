package org.jeecg.modules.summary.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.summary.entity.CustomerRegSummaryExtra;
import org.jeecg.modules.summary.service.ICustomerRegSummaryExtraService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 总检扩展表
 * @Author: jeecg-boot
 * @Date:   2025-06-03
 * @Version: V1.0
 */
@Api(tags="总检扩展表")
@RestController
@RequestMapping("/summary/customerRegSummaryExtra")
@Slf4j
public class CustomerRegSummaryExtraController extends JeecgController<CustomerRegSummaryExtra, ICustomerRegSummaryExtraService> {
	@Autowired
	private ICustomerRegSummaryExtraService customerRegSummaryExtraService;
	
	/**
	 * 分页列表查询
	 *
	 * @param customerRegSummaryExtra
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "总检扩展表-分页列表查询")
	@ApiOperation(value="总检扩展表-分页列表查询", notes="总检扩展表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CustomerRegSummaryExtra>> queryPageList(CustomerRegSummaryExtra customerRegSummaryExtra,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CustomerRegSummaryExtra> queryWrapper = QueryGenerator.initQueryWrapper(customerRegSummaryExtra, req.getParameterMap());
		Page<CustomerRegSummaryExtra> page = new Page<CustomerRegSummaryExtra>(pageNo, pageSize);
		IPage<CustomerRegSummaryExtra> pageList = customerRegSummaryExtraService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param customerRegSummaryExtra
	 * @return
	 */
	@AutoLog(value = "总检扩展表-添加")
	@ApiOperation(value="总检扩展表-添加", notes="总检扩展表-添加")
	@RequiresPermissions("summary:customer_reg_summary_extra:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CustomerRegSummaryExtra customerRegSummaryExtra) {
		customerRegSummaryExtraService.save(customerRegSummaryExtra);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param customerRegSummaryExtra
	 * @return
	 */
	@AutoLog(value = "总检扩展表-编辑")
	@ApiOperation(value="总检扩展表-编辑", notes="总检扩展表-编辑")
	@RequiresPermissions("summary:customer_reg_summary_extra:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CustomerRegSummaryExtra customerRegSummaryExtra) {
		customerRegSummaryExtraService.updateById(customerRegSummaryExtra);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "总检扩展表-通过id删除")
	@ApiOperation(value="总检扩展表-通过id删除", notes="总检扩展表-通过id删除")
	@RequiresPermissions("summary:customer_reg_summary_extra:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		customerRegSummaryExtraService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "总检扩展表-批量删除")
	@ApiOperation(value="总检扩展表-批量删除", notes="总检扩展表-批量删除")
	@RequiresPermissions("summary:customer_reg_summary_extra:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.customerRegSummaryExtraService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "总检扩展表-通过id查询")
	@ApiOperation(value="总检扩展表-通过id查询", notes="总检扩展表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CustomerRegSummaryExtra> queryById(@RequestParam(name="id",required=true) String id) {
		CustomerRegSummaryExtra customerRegSummaryExtra = customerRegSummaryExtraService.getById(id);
		if(customerRegSummaryExtra==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(customerRegSummaryExtra);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param customerRegSummaryExtra
    */
    @RequiresPermissions("summary:customer_reg_summary_extra:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegSummaryExtra customerRegSummaryExtra) {
        return super.exportXls(request, customerRegSummaryExtra, CustomerRegSummaryExtra.class, "总检扩展表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("summary:customer_reg_summary_extra:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegSummaryExtra.class);
    }

}
