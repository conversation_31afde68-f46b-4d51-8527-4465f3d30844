package org.jeecg.modules.summary.bo.teamreport;


import lombok.Data;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.basicinfo.entity.OrgInfo;
import org.jeecg.modules.occu.entity.ZyRiskFactorDisease;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CompanyTeam;

import java.util.Date;
import java.util.List;


@Data
public class ZyTeamReportVO {
    public OrgInfo orgInfo;
    public Company company;
    public CompanyReg companyReg;
    public List<CompanyTeam> companyTeams;
    public ZyCompanyOverview zyCompanyOverview;
    public List<ZyRiskPersonNumberOverview> zyRiskPersonNumberOverviews;
    List<ZyCompanyRisk2CheckItem> riskMustItem;
    public List<ZyRiskFactorDisease> riskFactorDiseases;
    public List<ZyCompanyAbnormalResult> abnormalItemPersonList;
    public List<CompanyAbnormalItemResultVO> allPersonList;
    public List<CompanyAbnormalItemResultVO> occuDiseasePersonList;
    public List<CompanyAbnormalItemResultVO> occuContradicationPersonList;
    public List<CompanyAbnormalItemResultVO> recheckPersonList;
    public List<CompanyAbnormalItemResultVO> recheckResultPersonList;
    public List<CompanyAbnormalItemResultVO> haveUncheckGroupPersonList;
    public List<CompanyAbnormalItemResultVO> noAbnormalGroupPersonList;
    public List<CompanyAbnormalItemResultVO> otherAbnormalPersonList;




    @Data
    public static class ZyCompanyOverview{
        public Date examStartTime;
        public Date examEndTime;
        public String jobStatus;
        public String riskFactors;
        public Integer actualCheckPersonNumber;
        public Integer reCheckPersonNumber;
        public String jobStatusDistribute;
        public String summaryDoctor;
        public String auditDoctor;
        public String reportDoctor;

    }
    @Data
    public static class ZyRiskPersonNumberOverview {
        public String jobStatus;
        public String riskFactor;
        public Long suspectedOccuDiseaseNum;
        public Long occuContraindicationNum;
        public Long reCheckNum;
        public Long noAbnormalNum;
        public Long otherAbnormalNum;

    }
}
