package org.jeecg.modules.summary.bo;

import lombok.Data;
import org.jeecg.modules.basicinfo.entity.OrgInfo;
import org.jeecg.modules.occu.entity.ZyConclusion;
import org.jeecg.modules.occu.entity.ZyConclusionDetail;
import org.jeecg.modules.occu.entity.ZyInquiry;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.summary.entity.CustomerRegSummary;

import java.util.List;
import java.util.Map;


@Data
public class ReportBean {
    private CustomerReg customerReg;
    private CustomerRegSummary summaryAdvice;
    private List<CustomerRegDepartSummary> departSummaryList;
    private List<GroupBean> groupBeanList;
    private List<DepartAndGroupBean> departAndGroupList;
    private List<DepartAndGroupBean> abnormalDepartAndGroupList;
    private List<TextBean> abnormalTextList;
    private List<TextBean> adviceTextList;
    private List<TextBean> departSummaryTextList;
    private List<CustomerRegItemResult> keyGroupResultList;
    private List<CustomerRegItemResult> historyGroupResultList;
    private List<TextBean> reportImgList;
    private List<ZyConclusionDetail> zyConclusionDetailList;
    private ZyConclusion zyConclusion;
    private boolean showHistory;
    private boolean showKeyGroup;
    private OrgInfo orgInfo;

    private Map<String, List<CustomerRegItemGroup>> groupByFunctionMap;
    private Map<String, List<TextBean>> groupByFunctionPicMap;
    private Map<String, List<CustomerRegItemGroup>> groupByClassCodeMap;

    private CheckStat checkStat;
    private AddMinusStat addMinusStat;

    private ZyInquiry zyInquiry;
}
