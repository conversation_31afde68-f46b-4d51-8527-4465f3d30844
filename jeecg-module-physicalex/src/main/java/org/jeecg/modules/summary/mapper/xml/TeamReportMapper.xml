<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.summary.mapper.TeamReportMapper">


    <select id="getAgeGroupStat" resultType="java.util.Map">
        select
            ROUND(count(distinct (case when c.age BETWEEN 0 and 24 then c.exam_no else null end)) * 100.0/count(distinct c.exam_no),2) a,
            ROUND(count(distinct (case when c.age BETWEEN 25 and 30   then c.exam_no else null end))* 100.0 /count(distinct c.exam_no),2) b,
            ROUND(count(distinct (case when c.age BETWEEN 31 and 40  then c.exam_no else null end)) * 100.0 /count(distinct c.exam_no),2) c,
            ROUND(count(distinct (case when c.age BETWEEN 41 and 50 then c.exam_no else null end))* 100.0 /count(distinct c.exam_no),2) d,
            ROUND(count(distinct (case when c.age BETWEEN 51 and 60 then c.exam_no else null end)) * 100.0 /count(distinct c.exam_no),2) e,
            ROUND(count(distinct (case when c.age > 60  then c.exam_no else null end))* 100.0 /count(distinct c.exam_no),2) f
        from customer_reg_item_group g join customer_reg c on g.customer_reg_id =c.id
        where c.company_reg_id=#{companyRegId}
    </select>
    <select id="getDepartAbnormalStat" resultType="org.jeecg.modules.summary.bo.teamreport.ValueAndLabel">
        SELECT
            g.department_name label,
            ROUND(COUNT(DISTINCT CASE WHEN r.abnormal_flag = 1 THEN r.customer_reg_id END) * 100.0 / (
                SELECT COUNT(DISTINCT r2.customer_reg_id)
                FROM customer_reg_item_result r2
                         JOIN customer_reg c2 ON r2.customer_reg_id = c2.id
                WHERE c2.company_reg_id = #{companyRegId}
            ),2) AS 'value'
        FROM
            customer_reg_item_result r
                JOIN customer_reg c ON r.customer_reg_id = c.id
                JOIN customer_reg_item_group g ON r.customer_reg_id = g.customer_reg_id
        WHERE
            c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
        GROUP BY
            g.department_id, g.department_name
    </select>
    <select id="getTop10tAbnormalConditionStat" resultType="org.jeecg.modules.summary.bo.teamreport.ValueAndLabel">
        SELECT
            CONCAT(r.item_name, IFNULL(r.abnormal_flag_desc, ''),COUNT(DISTINCT r.customer_reg_id),'人') AS label,
            COUNT(DISTINCT r.customer_reg_id) AS 'value'
        FROM
            customer_reg_item_result r
                JOIN customer_reg c ON r.customer_reg_id = c.id
                JOIN customer_reg_item_group g ON r.customer_reg_id = g.customer_reg_id AND c.id = g.customer_reg_id
        WHERE
            r.abnormal_flag = 1
          AND c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
        GROUP BY
            CONCAT(r.item_name, IFNULL(r.abnormal_flag_desc, ''))
        ORDER BY
            'value' DESC
            LIMIT 10

    </select>
    <select id="getB_DepartmentAbnormalStat" resultType="org.jeecg.modules.summary.bo.teamreport.ValueAndLabel">
        SELECT
            CONCAT(r.item_name, IFNULL(r.abnormal_flag_desc, ''),COUNT(DISTINCT r.customer_reg_id),'人') AS label,
            COUNT(DISTINCT r.customer_reg_id) AS 'value'
        FROM
            customer_reg_item_result r
                JOIN customer_reg c ON r.customer_reg_id = c.id
                JOIN customer_reg_item_group g ON r.customer_reg_id = g.customer_reg_id AND c.id = g.customer_reg_id
        WHERE
            r.abnormal_flag = 1
          and g.department_name='超声医学科'
          AND c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
        GROUP BY
            CONCAT(r.item_name, IFNULL(r.abnormal_flag_desc, ''))
    </select>
    <select id="getGenderGeneralStat"
            resultType="org.jeecg.modules.summary.bo.teamreport.AbnormalGeneralSummary">
        select
            count(distinct c.exam_no ) personTotal,
            count(distinct (case when c.gender='男' then c.exam_no else null end)) maleTotal,
            CONCAT(ROUND(count(distinct (case when c.gender='男' then c.exam_no else null end))/count(distinct c.exam_no ) * 100, 2), '%') malePercent,
            count(distinct (case when c.gender='女' then c.exam_no else null end)) femaleTotal,
            CONCAT(ROUND(count(distinct (case when c.gender='女' then c.exam_no else null end))/count(distinct c.exam_no ) * 100, 2), '%') femalePercent
        from customer_reg_item_group g join customer_reg c on g.customer_reg_id =c.id
        where c.company_reg_id= #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
    </select>
    <select id="getCheckedGeneralStat"
            resultType="org.jeecg.modules.summary.bo.teamreport.AbnormalGeneralSummary">
        SELECT
            SUM( CASE WHEN a.check_status = '已检' THEN 1 ELSE 0 END ) AS checkedTotal,
            CONCAT(ROUND(SUM( CASE WHEN a.check_status = '已检' THEN 1 ELSE 0 END )/(
                SELECT
                    count( DISTINCT c.exam_no ) personTotal
                FROM
                    customer_reg_item_group g
                        JOIN customer_reg c ON g.customer_reg_id = c.id
                WHERE
                    c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
            ) * 100, 2), '%') checkedPercent,
            SUM( CASE WHEN a.check_status = '已检' AND a.gender = '男' THEN 1 ELSE 0 END ) AS maleCheckedTotal,
            CONCAT(ROUND(SUM( CASE WHEN a.check_status = '已检' AND a.gender = '男' THEN 1 ELSE 0 END )/ SUM( CASE WHEN a.check_status = '已检' THEN 1 ELSE 0 END ) * 100, 2), '%') maleCheckedPercent,
            SUM( CASE WHEN a.check_status = '已检' AND a.gender = '女' THEN 1 ELSE 0 END ) AS femaleCheckedTotal,
            CONCAT(ROUND(SUM( CASE WHEN a.check_status = '已检' AND a.gender = '女' THEN 1 ELSE 0 END )/ SUM( CASE WHEN a.check_status = '已检' THEN 1 ELSE 0 END ) * 100, 2), '%') femaleCheckedPercent,
            (
                SELECT
                    count( CASE WHEN a.count = 0 THEN a.id ELSE NULL END )
                FROM
                    (
                        SELECT
                            cc.id,
                            sum( CASE WHEN rr.abnormal_flag = 1 THEN 1 ELSE 0 END ) count
                        FROM
                            customer_reg_item_result rr
                            JOIN customer_reg cc ON rr.customer_reg_id = cc.id
                        WHERE
                            cc.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and cc.company_dept_id=#{companyDeptId}
        </if>
                        GROUP BY
                            cc.id
                    ) a
            ) allNormalToTal,
            CONCAT(ROUND((
                             SELECT
                                 count( CASE WHEN a.count = 0 THEN a.id ELSE NULL END )
                             FROM
                                 (
                                     SELECT
                                         cc.id,
                                         sum( CASE WHEN rr.abnormal_flag = 1 THEN 1 ELSE 0 END ) count
                                     FROM
                                         customer_reg_item_result rr
                                         JOIN customer_reg cc ON rr.customer_reg_id = cc.id
                                     WHERE
                                         cc.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
                                     GROUP BY
                                         cc.id
                                 ) a
                         )/ SUM( CASE WHEN a.check_status = '已检' THEN 1 ELSE 0 END ) * 100, 2), '%') allNormalPercent,
            (
                SELECT
                    count( CASE WHEN a.count != 0 THEN a.id ELSE NULL END )
                FROM
                    (
                        SELECT
                            cc.id,
                            sum( CASE WHEN rr.abnormal_flag = 1 THEN 1 ELSE 0 END ) count
                        FROM
                            customer_reg_item_result rr
                            JOIN customer_reg cc ON rr.customer_reg_id = cc.id
                        WHERE
                            cc.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and cc.company_dept_id=#{companyDeptId}
        </if>
                        GROUP BY
                            cc.id
                    ) a
            ) notAllNormalToTal,
            CONCAT(ROUND((
                             SELECT
                                 count( CASE WHEN a.count != 0 THEN a.id ELSE NULL END )
                             FROM
                                 (
                                     SELECT
                                         cc.id,
                                         sum( CASE WHEN rr.abnormal_flag = 1 THEN 1 ELSE 0 END ) count
                                     FROM
                                         customer_reg_item_result rr
                                         JOIN customer_reg cc ON rr.customer_reg_id = cc.id
                                     WHERE
                                         cc.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and cc.company_dept_id=#{companyDeptId}
        </if>
                                     GROUP BY
                                         cc.id
                                 ) a
                         )/ SUM( CASE WHEN a.check_status = '已检' THEN 1 ELSE 0 END ) * 100, 2), '%') notAllNormalPercent
        FROM
            (
                SELECT
                    cc.gender,
                    CASE

                        WHEN SUM( CASE WHEN gg.check_status != '未检' THEN 1 ELSE 0 END ) > 0 THEN
                            '已检' ELSE '未检'
                        END AS check_status
                FROM
                    `customer_reg_item_group` gg
                        JOIN customer_reg cc ON cc.id = gg.customer_reg_id
                WHERE
                        gg.customer_reg_id IN ( SELECT id FROM `customer_reg` WHERE `company_reg_id` = #{companyRegId}  <if test="companyDeptId !=null and companyDeptId!='' ">
        and .company_dept_id=#{companyDeptId}
    </if>)
                GROUP BY
                    gg.exam_no
            ) AS a

    </select>
    <select id="getAbnormalConditionListStat"
            resultType="org.jeecg.modules.summary.bo.teamreport.AbnormalCondition">
        SELECT DISTINCT
            CONCAT(
                    r.item_name,
                    IFNULL( r.abnormal_flag_desc, '' )) 'desc',
                count( DISTINCT r.customer_reg_id ) total,
            CONCAT(ROUND(count( DISTINCT r.customer_reg_id ) /(
                SELECT
                    count( DISTINCT r.customer_reg_id )
                FROM
                    customer_reg_item_result r
                        JOIN customer_reg c ON r.customer_reg_id = c.id
                WHERE
                    c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
            ) * 100, 2), '%') percent,
            GROUP_CONCAT( DISTINCT c.NAME ) nameList,
            '大家都到北京附加符号和菲菲业务员' advice
        FROM
            customer_reg_item_result r
                JOIN customer_reg c ON r.customer_reg_id = c.id
                JOIN customer_reg_item_group g ON r.customer_reg_id = g.customer_reg_id
                AND c.id = g.customer_reg_id
        WHERE
            r.abnormal_flag = 1
          AND c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
        GROUP BY
            CONCAT(
                    r.item_name,
                    IFNULL( r.abnormal_flag_desc, '' ))
    </select>


    <select id="getAbnormalItemAndNameListStat"
            resultType="org.jeecg.modules.summary.bo.teamreport.AbnormalItemAndName">
        SELECT
            g.department_name,
            1 department_no,
            CONCAT(
                    r.item_name,
                    IFNULL( r.abnormal_flag_desc, '' )) abnormalDesc,
            GROUP_CONCAT( DISTINCT c.NAME ) nameList,
            count( DISTINCT r.customer_reg_id ) total,
            CONCAT(ROUND(count( DISTINCT r.customer_reg_id ) /(
                SELECT
                    count( DISTINCT r.customer_reg_id )
                FROM
                    customer_reg_item_result r
                        JOIN customer_reg c ON r.customer_reg_id = c.id
                WHERE
                    c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
            ) * 100, 2), '%') percentage
        FROM
            customer_reg_item_result r
                JOIN customer_reg c ON r.customer_reg_id = c.id
                JOIN customer_reg_item_group g ON r.customer_reg_id = g.customer_reg_id
                AND c.id = g.customer_reg_id
        WHERE
            r.abnormal_flag = 1
          AND c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
        GROUP BY
            g.department_name,
            CONCAT(
                    r.item_name,
                    IFNULL( r.abnormal_flag_desc, '' ))

    </select>
    <select id="getAbnormalItemsByCompanyRegId"
            resultType="org.jeecg.modules.summary.bo.teamreport.CompanyAbnormalItemResultVO">
        SELECT  r.id,
                r.exam_no,
                r.customer_reg_id,
                r.item_group_id,
                r.item_group_name,
                r.check_department_code,
                r.check_department_name,
                r.value,
                r.value_ref_range,
                r.abnormal_flag,
                r.abnormal_flag_desc,
                r.doctor_id,
                r.doctor_name,
                r.check_conclusion,
                r.abandon_Flag,
                c.name,
                c.gender,
                c.age,
                CONCAT(
                        r.item_name,
                        IFNULL( r.abnormal_flag_desc, '' )) abnormalDesc

        FROM
            customer_reg_item_result r
                JOIN customer_reg c ON r.customer_reg_id = c.id
        WHERE
            r.abnormal_flag = 1
          AND c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
    </select>
    <select id="getAllItemResultByCompanyRegId"
            resultType="org.jeecg.modules.summary.bo.teamreport.CompanyAbnormalItemResultVO">
        SELECT  r.id,
        r.exam_no,
        r.customer_reg_id,
        r.item_group_id,
        r.item_group_name,
        r.check_department_code,
        r.check_department_name,
        r.value,
        r.value_ref_range,
        r.abnormal_flag,
        r.abnormal_flag_desc,
        r.doctor_id,
        r.doctor_name,
        r.check_conclusion,
        r.abandon_Flag,
        c.name,
        c.gender,
        c.age,
        c.id_card,
        c.risk_factor,
        c.risk_months,
        c.risk_years,
        c.work_type,
        c.work_shop,
        c.job_status,
        CONCAT(
        r.item_name,
        IFNULL( r.abnormal_flag_desc, '' )) abnormalDesc

        FROM
        customer_reg_item_result r
        JOIN customer_reg c ON r.customer_reg_id = c.id
        WHERE
         c.company_reg_id = #{companyRegId}
        <if test="companyDeptId !=null and companyDeptId!='' ">
            and c.company_dept_id=#{companyDeptId}
        </if>
    </select>

</mapper>
