package org.jeecg.modules.summary.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Lucene Index Health Check and Monitoring Service
 */
@Service
@Slf4j
public class LuceneIndexHealthService {

    @Autowired
    private LuceneIndexManager luceneIndexManager;

    @Autowired
    private LuceneOperationStatsService operationStatsService;

    // Health check status
    private volatile long lastHealthCheckTime = 0;
    private volatile boolean lastHealthStatus = true;

    /**
     * Scheduled health check - every 5 minutes
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void scheduledHealthCheck() {
        try {
            boolean isHealthy = performHealthCheck();
            lastHealthCheckTime = System.currentTimeMillis();
            
            if (!isHealthy && lastHealthStatus) {
                // Health status changed from normal to abnormal
                log.error("Lucene index health check failed, attempting repair...");
                attemptRepair();
            } else if (isHealthy && !lastHealthStatus) {
                // Health status recovered from abnormal to normal
                log.info("Lucene index health status has recovered");
            }
            
            lastHealthStatus = isHealthy;
            
        } catch (Exception e) {
            log.error("Scheduled health check exception", e);
            lastHealthStatus = false;
        }
    }

    /**
     * Perform health check
     */
    public boolean performHealthCheck() {
        try {
            // Check index manager status
            boolean managerHealthy = luceneIndexManager.checkIndexHealth();
            
            if (!managerHealthy) {
                log.warn("Index manager health check failed");
                return false;
            }

            // Perform simple search test
            boolean searchHealthy = testSearchFunctionality();
            
            if (!searchHealthy) {
                log.warn("Search functionality test failed");
                return false;
            }

            log.debug("Lucene index health check passed");
            return true;
            
        } catch (Exception e) {
            log.error("Health check exception", e);
            return false;
        }
    }

    /**
     * Test search functionality
     */
    private boolean testSearchFunctionality() {
        try {
            // Perform a simple search test using LuceneIndexManager directly
            org.apache.lucene.search.Query testQuery = new org.apache.lucene.search.MatchAllDocsQuery();
            org.apache.lucene.search.TopDocs result = luceneIndexManager.search(testQuery, 1);
            return result != null;
        } catch (Exception e) {
            log.error("Search functionality test failed", e);
            return false;
        }
    }

    /**
     * Attempt to repair index
     */
    public boolean attemptRepair() {
        try {
            log.info("Starting attempt to repair Lucene index...");
            
            // Try to repair index
            boolean repaired = luceneIndexManager.repairIndex();
            
            if (!repaired) {
                log.warn("Index repair failed, manual intervention may be required");
                // Note: Index rebuild should be triggered manually to avoid circular dependency
                repaired = false;
            }
            
            if (repaired) {
                log.info("Index repair successful");
            } else {
                log.error("Index repair failed");
            }
            
            return repaired;
            
        } catch (Exception e) {
            log.error("Index repair exception", e);
            return false;
        }
    }

    /**
     * Record search operation (delegated to stats service)
     */
    public void recordSearchOperation(boolean success) {
        operationStatsService.recordSearchOperation(success);
    }

    /**
     * Record index operation (delegated to stats service)
     */
    public void recordIndexOperation(boolean success) {
        operationStatsService.recordIndexOperation(success);
    }

    /**
     * Get enhanced health status report
     */
    public HealthReport getHealthReport() {
        HealthReport report = new HealthReport();
        report.setHealthy(lastHealthStatus);
        report.setLastCheckTime(lastHealthCheckTime);

        // Get statistics from stats service
        LuceneOperationStatsService.OperationStats stats = operationStatsService.getAllStats();
        LuceneOperationStatsService.SearchStats searchStats = stats.getSearchStats();
        LuceneOperationStatsService.IndexStats indexStats = stats.getIndexStats();

        report.setTotalSearchCount(searchStats.getTotalCount());
        report.setFailedSearchCount(searchStats.getFailedCount());
        report.setSearchSuccessRate(searchStats.getSuccessRate());

        report.setTotalIndexCount(indexStats.getTotalCount());
        report.setFailedIndexCount(indexStats.getFailedCount());
        report.setIndexSuccessRate(indexStats.getSuccessRate());

        // Add real-time index information
        try {
            report.setIndexExists(checkIndexExists());
            report.setIndexDocumentCount(getIndexDocumentCount());
            report.setIndexSizeInfo(getIndexSizeInfo());
        } catch (Exception e) {
            log.warn("Failed to get index information", e);
        }

        return report;
    }

    /**
     * Check if index exists
     */
    private boolean checkIndexExists() {
        return luceneIndexManager.checkIndexHealth();
    }

    /**
     * Get index document count
     */
    private long getIndexDocumentCount() {
        try {
            // 直接使用LuceneIndexManager查询文档数量，避免循环依赖
            org.apache.lucene.search.Query allDocsQuery = new org.apache.lucene.search.MatchAllDocsQuery();
            org.apache.lucene.search.TopDocs topDocs = luceneIndexManager.search(allDocsQuery, Integer.MAX_VALUE);
            return topDocs != null ? topDocs.totalHits : 0;
        } catch (Exception e) {
            log.warn("Failed to get document count", e);
            return -1;
        }
    }

    /**
     * Get index size information
     */
    private String getIndexSizeInfo() {
        try {
            java.io.File indexDir = new java.io.File(getIndexPath());
            if (indexDir.exists() && indexDir.isDirectory()) {
                long totalSize = calculateDirectorySize(indexDir);
                return formatFileSize(totalSize);
            }
            return "Unknown";
        } catch (Exception e) {
            log.warn("Failed to get index size", e);
            return "Unknown";
        }
    }

    /**
     * Get index path from LuceneIndexManager
     */
    private String getIndexPath() {
        // 这里需要从配置或LuceneIndexManager获取索引路径
        // 暂时返回默认路径，实际应该从配置中获取
        return System.getProperty("java.io.tmpdir") + "/lucene-index";
    }

    /**
     * Calculate directory size
     */
    private long calculateDirectorySize(java.io.File directory) {
        long size = 0;
        if (directory.isDirectory()) {
            java.io.File[] files = directory.listFiles();
            if (files != null) {
                for (java.io.File file : files) {
                    if (file.isDirectory()) {
                        size += calculateDirectorySize(file);
                    } else {
                        size += file.length();
                    }
                }
            }
        }
        return size;
    }

    /**
     * Format file size
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * Reset monitoring metrics (delegated to stats service)
     */
    public void resetMetrics() {
        operationStatsService.resetMetrics();
        log.info("Monitoring metrics have been reset");
    }

    /**
     * Enhanced Health Report class
     */
    public static class HealthReport {
        private boolean healthy;
        private long lastCheckTime;
        private long totalSearchCount;
        private long failedSearchCount;
        private long totalIndexCount;
        private long failedIndexCount;
        private double searchSuccessRate;
        private double indexSuccessRate;

        // New fields for enhanced reporting
        private boolean indexExists;
        private long indexDocumentCount;
        private String indexSizeInfo;

        // Getters and Setters
        public boolean isHealthy() { return healthy; }
        public void setHealthy(boolean healthy) { this.healthy = healthy; }
        
        public long getLastCheckTime() { return lastCheckTime; }
        public void setLastCheckTime(long lastCheckTime) { this.lastCheckTime = lastCheckTime; }
        
        public long getTotalSearchCount() { return totalSearchCount; }
        public void setTotalSearchCount(long totalSearchCount) { this.totalSearchCount = totalSearchCount; }
        
        public long getFailedSearchCount() { return failedSearchCount; }
        public void setFailedSearchCount(long failedSearchCount) { this.failedSearchCount = failedSearchCount; }
        
        public long getTotalIndexCount() { return totalIndexCount; }
        public void setTotalIndexCount(long totalIndexCount) { this.totalIndexCount = totalIndexCount; }
        
        public long getFailedIndexCount() { return failedIndexCount; }
        public void setFailedIndexCount(long failedIndexCount) { this.failedIndexCount = failedIndexCount; }
        
        public double getSearchSuccessRate() { return searchSuccessRate; }
        public void setSearchSuccessRate(double searchSuccessRate) { this.searchSuccessRate = searchSuccessRate; }
        
        public double getIndexSuccessRate() { return indexSuccessRate; }
        public void setIndexSuccessRate(double indexSuccessRate) { this.indexSuccessRate = indexSuccessRate; }

        public boolean isIndexExists() { return indexExists; }
        public void setIndexExists(boolean indexExists) { this.indexExists = indexExists; }

        public long getIndexDocumentCount() { return indexDocumentCount; }
        public void setIndexDocumentCount(long indexDocumentCount) { this.indexDocumentCount = indexDocumentCount; }

        public String getIndexSizeInfo() { return indexSizeInfo; }
        public void setIndexSizeInfo(String indexSizeInfo) { this.indexSizeInfo = indexSizeInfo; }

        @Override
        public String toString() {
            return String.format("HealthReport{healthy=%s, searchSuccessRate=%.2f%%, indexSuccessRate=%.2f%%, " +
                    "totalSearch=%d, failedSearch=%d, totalIndex=%d, failedIndex=%d, " +
                    "indexExists=%s, documentCount=%d, indexSize=%s}",
                    healthy, searchSuccessRate * 100, indexSuccessRate * 100,
                    totalSearchCount, failedSearchCount, totalIndexCount, failedIndexCount,
                    indexExists, indexDocumentCount, indexSizeInfo);
        }
    }
}
