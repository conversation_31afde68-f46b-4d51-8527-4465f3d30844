package org.jeecg.modules.summary.service.impl;

import org.jeecg.modules.summary.entity.ReportActionRecord;
import org.jeecg.modules.summary.mapper.ReportActionRecordMapper;
import org.jeecg.modules.summary.service.IReportActionRecordService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 报告操作记录
 * @Author: jeecg-boot
 * @Date:   2024-11-18
 * @Version: V1.0
 */
@Service
public class ReportActionRecordServiceImpl extends ServiceImpl<ReportActionRecordMapper, ReportActionRecord> implements IReportActionRecordService {

}
