package org.jeecg.modules.summary.service.impl;

import org.jeecg.modules.summary.entity.CustomerRegSummaryHistory;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryHistoryMapper;
import org.jeecg.modules.summary.service.ICustomerRegSummaryHistoryService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 总检记录表
 * @Author: jeecg-boot
 * @Date:   2024-09-10
 * @Version: V1.0
 */
@Service
public class CustomerRegSummaryHistoryServiceImpl extends ServiceImpl<CustomerRegSummaryHistoryMapper, CustomerRegSummaryHistory> implements ICustomerRegSummaryHistoryService {

}
