package org.jeecg.modules.summary.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.jeecg.modules.summary.entity.AccountReport;
import org.jeecg.modules.summary.service.IAccountReportService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 账号关联的报告
 * @Author: jeecg-boot
 * @Date: 2025-03-11
 * @Version: V1.0
 */
@Api(tags = "账号关联的报告")
@RestController
@RequestMapping("/summary/accountReport")
@Slf4j
public class AccountReportController extends JeecgController<AccountReport, IAccountReportService> {
    @Autowired
    private IAccountReportService accountReportService;
    @Autowired
    private ISmsRecordsService smsRecordsService;

    //queryReportByAccountId
    @GetMapping(value = "/queryReportByAccountId")
    public Result<?> queryReportByAccountId(String accountId, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<AccountReport> page = new Page<>(pageNo, pageSize);
        accountReportService.queryReportByAccountId(page, accountId);
        return Result.OK(page);
    }

    //generateAccountReport
    @GetMapping(value = "/generateAccountReport")
    public Result<?> generateAccountReport(String accountId, String idCard, String phone, String smsCode) {
        try {
            smsRecordsService.checkSmsCode(phone, smsCode, "report_query");
        } catch (Exception e) {
            log.error("验证短信失败：", e);
            return Result.error("验证短信失败: " + e.getMessage());
        }
        String tip = accountReportService.generateAccountReport(accountId, idCard);
        return Result.OK(tip);
    }

    //getPhoneByIdCard
    @GetMapping(value = "/getPhoneByIdCard")
    public Result<?> getPhoneByIdCard(String idCard) {
        String phone = accountReportService.getPhoneByIdCard(idCard);
        if (StringUtils.isBlank(phone)) {
            return Result.error("未找相关体检记录");
        }
        return Result.OK(phone);
    }

    //pageReportByIdcard
    @GetMapping(value = "/pageReportByIdcard")
    public Result<?> pageReportByIdcard(String idCard, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<AccountReport> page = new Page<>(pageNo, pageSize);
        accountReportService.pageReportByIdcard(page, idCard);
        return Result.OK(page);
    }

    //statReportByIdcard
    @GetMapping(value = "/statReportByIdcard")
    public Result<?> statReportByIdcard(String idCard) {
        Map<String, Object> stat = accountReportService.statReportByIdcard(idCard);
        return Result.OK(stat);
    }


}
