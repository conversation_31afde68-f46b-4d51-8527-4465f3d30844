package org.jeecg.modules.summary.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 总检建议
 * @Author: jeecg-boot
 * @Date:   2024-05-19
 * @Version: V1.0
 */
@Data
@TableName("summary_advice")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="summary_advice对象", description="总检建议")
public class SummaryAdvice implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**排序号*/
	@Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.String sort;
	/**危害因素*/
	@Excel(name = "危害因素", width = 15)
    @ApiModelProperty(value = "危害因素")
    private java.lang.String riskFactor;
	/**科室ID*/
	@Excel(name = "科室ID", width = 15)
    @ApiModelProperty(value = "科室ID")
    private java.lang.String departmentId;
	/**适用性别*/
	@Excel(name = "适用性别", width = 15)
    @ApiModelProperty(value = "适用性别")
    private java.lang.String sexLimit;
	/**关键字*/
	@Excel(name = "关键字", width = 15)
    @ApiModelProperty(value = "关键字")
    private java.lang.String keywords;
	/**助记码*/
	@Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private java.lang.String helpChar;
	/**科室名称*/
	@Excel(name = "科室名称", width = 15)
    @ApiModelProperty(value = "科室名称")
    private java.lang.String departmentName;
	/**适用婚别*/
	@Excel(name = "适用婚别", width = 15)
    @ApiModelProperty(value = "适用婚别")
    private java.lang.String marriageLimit;
	/**最小年龄*/
	@Excel(name = "最小年龄", width = 15)
    @ApiModelProperty(value = "最小年龄")
    private java.lang.Integer minAge;
	/**最大年龄*/
	@Excel(name = "最大年龄", width = 15)
    @ApiModelProperty(value = "最大年龄")
    private java.lang.Integer maxAge;
	/**岗位*/
	@Excel(name = "岗位", width = 15)
    @ApiModelProperty(value = "岗位")
    private java.lang.String jobCategory;
	/**疾病程度*/
	@Excel(name = "疾病程度", width = 15)
    @ApiModelProperty(value = "疾病程度")
    private java.lang.String diseaseSeverity;
	/**疾病类型*/
	@Excel(name = "疾病类型", width = 15)
    @ApiModelProperty(value = "疾病类型")
    private java.lang.String diseaseType;
	/**总检建议*/
	@Excel(name = "总检建议", width = 15)
    @ApiModelProperty(value = "总检建议")
    private java.lang.String adviceContent;
	/**团报隐藏*/
	@Excel(name = "团报隐藏", width = 15)
    @ApiModelProperty(value = "团报隐藏")
    private java.lang.Integer teamHiddenFlag;
	/**疾病*/
	@Excel(name = "疾病", width = 15)
    @ApiModelProperty(value = "疾病")
    private java.lang.String icdCode;
	/**女性病统计*/
	@Excel(name = "女性病统计", width = 15)
    @ApiModelProperty(value = "女性病统计")
    private java.lang.String wmIll;
	/**随访周期*/
	@Excel(name = "随访周期", width = 15)
    @ApiModelProperty(value = "随访周期")
    private java.lang.Integer followUpPeriod;
	/**专科建议*/
	@Excel(name = "专科建议", width = 15)
    @ApiModelProperty(value = "专科建议")
    private java.lang.String departmentAdvice;
	/**团体建议*/
	@Excel(name = "团体建议", width = 15)
    @ApiModelProperty(value = "团体建议")
    private java.lang.String teamAdvice;
	/**就医指导*/
	@Excel(name = "就医指导", width = 15)
    @ApiModelProperty(value = "就医指导")
    private java.lang.String medicalAdvice;
	/**饮食指导*/
	@Excel(name = "饮食指导", width = 15)
    @ApiModelProperty(value = "饮食指导")
    private java.lang.String dietAdvice;
	/**健康指导*/
	@Excel(name = "健康指导", width = 15)
    @ApiModelProperty(value = "健康指导")
    private java.lang.String healthAdvice;
	/**复查周期*/
	@Excel(name = "复查周期", width = 15)
    @ApiModelProperty(value = "复查周期")
    private java.lang.Integer reviewPeriod;
	/**阳性状态*/
	@Excel(name = "阳性状态", width = 15)
    @ApiModelProperty(value = "阳性状态")
    private java.lang.Integer positiveFlag;
	/**生活相关*/
	@Excel(name = "生活相关", width = 15)
    @ApiModelProperty(value = "生活相关")
    private java.lang.Integer lifeRelatedFlag;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;

    private java.lang.String updateBy;

    private java.util.Date updateTime;

    private java.util.Date indexTime;

    private String keywordsExplain;

    private String createType;

    @TableField(exist = false)
    private Float matchScore;
}
