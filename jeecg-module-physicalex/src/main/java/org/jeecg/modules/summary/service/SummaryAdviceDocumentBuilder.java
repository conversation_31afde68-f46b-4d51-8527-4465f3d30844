package org.jeecg.modules.summary.service;

import org.apache.commons.lang.StringUtils;
import org.apache.lucene.document.*;
import org.apache.lucene.index.IndexableField;
import org.jeecg.modules.summary.entity.SummaryAdvice;
import org.springframework.stereotype.Component;

/**
 * Summary Advice Document Builder
 * Unified handling of SummaryAdvice to Lucene Document conversion
 */
@Component
public class SummaryAdviceDocumentBuilder {

    /**
     * Convert SummaryAdvice to Lucene Document
     */
    public Document buildDocument(SummaryAdvice summaryAdvice) {
        if (summaryAdvice == null) {
            throw new IllegalArgumentException("SummaryAdvice cannot be null");
        }

        Document document = new Document();
        
        // ID field - for unique identification
        document.add(new StringField("id", 
            StringUtils.trimToEmpty(summaryAdvice.getId()), Field.Store.YES));
        
        // Department ID
        document.add(new StringField("departmentId", 
            StringUtils.trimToEmpty(summaryAdvice.getDepartmentId()), Field.Store.YES));
        
        // Keywords - searchable text field
        if (StringUtils.isNotBlank(summaryAdvice.getKeywords())) {
            document.add(new TextField("keywords", 
                summaryAdvice.getKeywords(), Field.Store.YES));
        }
        
        // Job category
        document.add(new StringField("jobCategory", 
            StringUtils.trimToEmpty(summaryAdvice.getJobCategory()), Field.Store.YES));
        
        // Risk factor
        document.add(new StringField("riskFactor", 
            StringUtils.trimToEmpty(summaryAdvice.getRiskFactor()), Field.Store.YES));
        
        // Gender restriction
        String sexLimit = summaryAdvice.getSexLimit();
        if (StringUtils.isBlank(sexLimit)) {
            sexLimit = "不限";
        }
        document.add(new StringField("sexLimit", sexLimit, Field.Store.YES));
        
        // Marriage status restriction
        String marriageLimit = summaryAdvice.getMarriageLimit();
        if (StringUtils.isBlank(marriageLimit)) {
            marriageLimit = "不限";
        }
        document.add(new StringField("marriageLimit", marriageLimit, Field.Store.YES));
        
        // Age range
        Integer minAge = summaryAdvice.getMinAge();
        Integer maxAge = summaryAdvice.getMaxAge();
        document.add(new IntPoint("minAge", minAge != null ? minAge : 0));
        document.add(new IntPoint("maxAge", maxAge != null ? maxAge : 120));
        
        // Store age range for display
        document.add(new StoredField("minAgeStored", minAge != null ? minAge : 0));
        document.add(new StoredField("maxAgeStored", maxAge != null ? maxAge : 120));
        
        // Disease severity
        document.add(new StringField("diseaseSeverity", 
            StringUtils.trimToEmpty(summaryAdvice.getDiseaseSeverity()), Field.Store.YES));
        
        // Disease type
        document.add(new StringField("diseaseType", 
            StringUtils.trimToEmpty(summaryAdvice.getDiseaseType()), Field.Store.YES));
        
        // Advice content - searchable text field
        if (StringUtils.isNotBlank(summaryAdvice.getAdviceContent())) {
            document.add(new TextField("adviceContent", 
                summaryAdvice.getAdviceContent(), Field.Store.YES));
        }
        
        // ICD code
        document.add(new StringField("icdCode", 
            StringUtils.trimToEmpty(summaryAdvice.getIcdCode()), Field.Store.YES));
        
        // Department advice - searchable text field
        if (StringUtils.isNotBlank(summaryAdvice.getDepartmentAdvice())) {
            document.add(new TextField("departmentAdvice", 
                summaryAdvice.getDepartmentAdvice(), Field.Store.YES));
        }
        
        // Team advice - searchable text field
        if (StringUtils.isNotBlank(summaryAdvice.getTeamAdvice())) {
            document.add(new TextField("teamAdvice", 
                summaryAdvice.getTeamAdvice(), Field.Store.YES));
        }
        
        // Medical advice - searchable text field
        if (StringUtils.isNotBlank(summaryAdvice.getMedicalAdvice())) {
            document.add(new TextField("medicalAdvice", 
                summaryAdvice.getMedicalAdvice(), Field.Store.YES));
        }
        
        // Diet advice - searchable text field
        if (StringUtils.isNotBlank(summaryAdvice.getDietAdvice())) {
            document.add(new TextField("dietAdvice", 
                summaryAdvice.getDietAdvice(), Field.Store.YES));
        }
        
        // Health advice - searchable text field
        if (StringUtils.isNotBlank(summaryAdvice.getHealthAdvice())) {
            document.add(new TextField("healthAdvice", 
                summaryAdvice.getHealthAdvice(), Field.Store.YES));
        }
        
        return document;
    }

    /**
     * Convert Lucene Document to SummaryAdvice
     */
    public SummaryAdvice buildSummaryAdvice(Document document) {
        if (document == null) {
            return null;
        }

        SummaryAdvice summaryAdvice = new SummaryAdvice();
        
        summaryAdvice.setId(document.get("id"));
        summaryAdvice.setDepartmentId(document.get("departmentId"));
        summaryAdvice.setKeywords(document.get("keywords"));
        summaryAdvice.setJobCategory(document.get("jobCategory"));
        summaryAdvice.setRiskFactor(document.get("riskFactor"));
        summaryAdvice.setSexLimit(document.get("sexLimit"));
        summaryAdvice.setMarriageLimit(document.get("marriageLimit"));
        summaryAdvice.setDiseaseSeverity(document.get("diseaseSeverity"));
        summaryAdvice.setDiseaseType(document.get("diseaseType"));
        summaryAdvice.setAdviceContent(document.get("adviceContent"));
        summaryAdvice.setIcdCode(document.get("icdCode"));
        summaryAdvice.setDepartmentAdvice(document.get("departmentAdvice"));
        summaryAdvice.setTeamAdvice(document.get("teamAdvice"));
        summaryAdvice.setMedicalAdvice(document.get("medicalAdvice"));
        summaryAdvice.setDietAdvice(document.get("dietAdvice"));
        summaryAdvice.setHealthAdvice(document.get("healthAdvice"));
        
        // Handle age fields
        IndexableField minAgeField = document.getField("minAgeStored");
        IndexableField maxAgeField = document.getField("maxAgeStored");
        
        if (minAgeField != null) {
            summaryAdvice.setMinAge(minAgeField.numericValue().intValue());
        }
        if (maxAgeField != null) {
            summaryAdvice.setMaxAge(maxAgeField.numericValue().intValue());
        }
        
        return summaryAdvice;
    }

    /**
     * Validate if SummaryAdvice can be indexed
     */
    public boolean isValidForIndexing(SummaryAdvice summaryAdvice) {
        if (summaryAdvice == null) {
            return false;
        }
        
        // At least need ID and keywords
        return StringUtils.isNotBlank(summaryAdvice.getId()) && 
               StringUtils.isNotBlank(summaryAdvice.getKeywords());
    }
}
