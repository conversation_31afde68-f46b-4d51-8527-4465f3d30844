package org.jeecg.modules.summary.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.summary.service.ISummaryAdviceService;
import org.jeecg.modules.summary.service.LuceneIndexHealthService;
import org.jeecg.modules.summary.service.LuceneIndexManager;
import org.jeecg.modules.summary.service.LuceneIndexTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Lucene Index Management Controller
 * Provides API interfaces for index management and monitoring
 */
@Api(tags = "Lucene Index Management")
@RestController
@RequestMapping("/summary/lucene")
@Slf4j
public class LuceneIndexManagementController {

    @Autowired
    private ISummaryAdviceService summaryAdviceService;

    @Autowired
    private LuceneIndexManager luceneIndexManager;

    @Autowired
    private LuceneIndexHealthService healthService;

    @Autowired
    private LuceneIndexTaskService indexTaskService;

    @ApiOperation(value = "Create Index", notes = "Create index for data without index")
    @PostMapping("/createIndex")
    public Result<?> createIndex() {
        try {
            String taskId = indexTaskService.startCreateTask();
            return Result.OK("索引创建任务已启动", taskId);
        } catch (Exception e) {
            log.error("Failed to start create task", e);
            return Result.error("索引创建任务启动失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Update Necessary Index", notes = "Update indexes that need updating")
    @PostMapping("/updateNecessaryIndex")
    public Result<?> updateNecessaryIndex() {
        try {
            String taskId = indexTaskService.startUpdateTask();
            return Result.OK("索引更新任务已启动", taskId);
        } catch (Exception e) {
            log.error("Failed to start update task", e);
            return Result.error("索引更新任务启动失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Rebuild All Index", notes = "Clear and rebuild all indexes")
    @PostMapping("/rebuildAllIndex")
    public Result<?> rebuildAllIndex() {
        try {
            // 异步执行重建任务
            String taskId = indexTaskService.startRebuildTask();
            return Result.OK("重建索引任务启动成功", taskId);
        } catch (Exception e) {
            log.error("重建索引任务启动失败", e);
            return Result.error("重建索引任务启动失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Get Task Status", notes = "Get current task execution status")
    @GetMapping("/taskStatus")
    public Result<?> getTaskStatus(String taskId) {
        try {
            return Result.OK("已获取任务状态", indexTaskService.getTaskStatus());
        } catch (Exception e) {
            log.error("Failed to get task status", e);
            return Result.error("获取任务状态失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Delete Specified Index", notes = "Delete index by specified ID")
    @DeleteMapping("/deleteIndex/{id}")
    public Result<?> deleteIndex(@PathVariable String id) {
        try {
            summaryAdviceService.removeLuceneIndex(id);
            return Result.OK("索引已成功删除");
        } catch (Exception e) {
            log.error("Failed to delete index", e);
            return Result.error("Failed to delete index: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Check Index Health", notes = "Check Lucene index health status")
    @GetMapping("/healthCheck")
    public Result<?> healthCheck() {
        try {
            boolean isHealthy = healthService.performHealthCheck();
            LuceneIndexHealthService.HealthReport report = healthService.getHealthReport();

            if (isHealthy) {
                return Result.OK("索引状态正常", report);
            } else {
                return Result.error("索引状态异常", report);
            }
        } catch (Exception e) {
            log.error("Health check failed", e);
            return Result.error("Health check failed: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Repair Index", notes = "Attempt to repair damaged index")
    @PostMapping("/repairIndex")
    public Result<?> repairIndex() {
        try {
            boolean success = healthService.attemptRepair();
            if (success) {
                return Result.OK("索引修复成功");
            } else {
                return Result.error("索引修复失败");
            }
        } catch (Exception e) {
            log.error("Index repair exception", e);
            return Result.error("Index repair exception: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Get Health Report", notes = "Get detailed index health report")
    @GetMapping("/healthReport")
    public Result<LuceneIndexHealthService.HealthReport> getHealthReport() {
        try {
            LuceneIndexHealthService.HealthReport report = healthService.getHealthReport();
            return Result.OK(report);
        } catch (Exception e) {
            log.error("Failed to get health report", e);
            return Result.error("获取健康状态异常: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Reset Monitoring Metrics", notes = "Reset index operation monitoring metrics")
    @PostMapping("/resetMetrics")
    public Result<?> resetMetrics() {
        try {
            healthService.resetMetrics();
            return Result.OK("统计指标已重置");
        } catch (Exception e) {
            log.error("Failed to reset monitoring metrics", e);
            return Result.error("统计指标重置失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Check Index Manager Status", notes = "Check Lucene index manager status")
    @GetMapping("/managerStatus")
    public Result<?> getManagerStatus() {
        try {
            boolean isHealthy = luceneIndexManager.isIndexHealthy();
            boolean checkResult = luceneIndexManager.checkIndexHealth();

            return Result.OK("索引管理器状态", new Object() {
                public boolean isHealthy() {
                    return isHealthy;
                }

                public boolean checkResult() {
                    return checkResult;
                }

                public String status() {
                    return isHealthy && checkResult ? "Normal" : "Abnormal";
                }
            });
        } catch (Exception e) {
            log.error("Failed to get manager status", e);
            return Result.error("Failed to get manager status: " + e.getMessage());
        }
    }

    @ApiOperation(value = "Force Health Check", notes = "Immediately execute a health check")
    @PostMapping("/forceHealthCheck")
    public Result<?> forceHealthCheck() {
        try {
            healthService.scheduledHealthCheck();
            return Result.OK("健康检查已执行");
        } catch (Exception e) {
            log.error("Force health check failed", e);
            return Result.error("强制健康检查失败: " + e.getMessage());
        }
    }
}
