package org.jeecg.modules.summary.service.impl;

import org.jeecg.modules.summary.entity.CustomerRegSummaryDetail;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryDetailMapper;
import org.jeecg.modules.summary.service.ICustomerRegSummaryDetailService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 总检建议详细表
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Service
public class CustomerRegSummaryDetailServiceImpl extends ServiceImpl<CustomerRegSummaryDetailMapper, CustomerRegSummaryDetail> implements ICustomerRegSummaryDetailService {

}
