package org.jeecg.modules.summary.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 报告召回
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
@Data
@TableName("report_callback")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="report_callback对象", description="报告召回")
public class ReportCallback implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**体检号*/
	@Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;
	/**发起人账号*/
	@Excel(name = "发起人账号", width = 15)
    @ApiModelProperty(value = "发起人账号")
    private java.lang.String applyBy;
	/**发起人*/
	@Excel(name = "发起人", width = 15)
    @ApiModelProperty(value = "发起人")
    private java.lang.String applyer;
	/**召回理由*/
	@Excel(name = "召回理由", width = 15, dicCode = "report_callback_resaon")
	@Dict(dicCode = "report_callback_resaon")
    @ApiModelProperty(value = "召回理由")
    private java.lang.String callbackReason;
	/**召回结果*/
	@Excel(name = "召回结果", width = 15, dicCode = "report_callback_result")
	@Dict(dicCode = "report_callback_result")
    @ApiModelProperty(value = "召回结果")
    private java.lang.String result;
	/**召回操作人账号*/
	@Excel(name = "召回操作人账号", width = 15)
    @ApiModelProperty(value = "召回操作人账号")
    private java.lang.String callbackBy;
	/**召回操作人*/
	@Excel(name = "召回操作人", width = 15)
    @ApiModelProperty(value = "召回操作人")
    private java.lang.String callbackOperator;
	/**召回时间*/
	@Excel(name = "召回时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "召回时间")
    private java.util.Date callbackTime;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**总检记录*/
	@Excel(name = "总检记录", width = 15)
    @ApiModelProperty(value = "总检记录")
    private java.lang.String summaryId;
	/**登记记录ID*/
	@Excel(name = "登记记录ID", width = 15)
    @ApiModelProperty(value = "登记记录ID")
    private java.lang.String customerRegId;
    /**流程实例ID*/
    @Excel(name = "流程实例ID", width = 15)
    @ApiModelProperty(value = "流程实例ID")
    private String processInsId;
    /**流程任务ID*/
    @Excel(name = "流程任务ID", width = 15)
    @ApiModelProperty(value = "流程任务ID")
    private String taskId;
    /**业务ID*/
    @Excel(name = "业务ID", width = 15)
    @ApiModelProperty(value = "业务ID")
    private String bussinessKey;
    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String bussinessType;
    @TableField(exist = false)
    private String agreeFlag;
}
