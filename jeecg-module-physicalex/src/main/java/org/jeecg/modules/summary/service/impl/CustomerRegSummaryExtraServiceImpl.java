package org.jeecg.modules.summary.service.impl;

import org.jeecg.modules.summary.entity.CustomerRegSummaryExtra;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryExtraMapper;
import org.jeecg.modules.summary.service.ICustomerRegSummaryExtraService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 总检扩展表
 * @Author: jeecg-boot
 * @Date:   2025-06-03
 * @Version: V1.0
 */
@Service
public class CustomerRegSummaryExtraServiceImpl extends ServiceImpl<CustomerRegSummaryExtraMapper, CustomerRegSummaryExtra> implements ICustomerRegSummaryExtraService {

}
