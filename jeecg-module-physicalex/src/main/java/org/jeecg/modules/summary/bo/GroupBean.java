package org.jeecg.modules.summary.bo;

import lombok.Data;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

import java.util.List;

@Data
public class GroupBean {
    private String groupId;
    private String groupName;
    private String departmentId;
    private String departmentName;
    private String checkConclusion;
    private String checkStatus;
    private String checkStatusDesc;
    private String abandonFlag;
    private String checkTime;
    private String checkDate;
    private String reportDoctorCode;
    private String reportDoctorName;
    private String auditDoctorCode;
    private String auditDoctorName;
    private String checkDoctorCode;
    private String checkDoctorName;
    private String checkPartCode;
    private String checkPartName;
    private List<String> reportPics;
    private List<String> pic;
    private List<CustomerRegItemResult> itemResultList;
    private String classCode;
    private List<TextBean> reportPicBeanList;
}
