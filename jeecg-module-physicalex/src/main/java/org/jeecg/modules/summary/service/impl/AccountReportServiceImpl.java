package org.jeecg.modules.summary.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.mapper.CustomerMapper;
import org.jeecg.modules.summary.entity.AccountReport;
import org.jeecg.modules.summary.mapper.AccountReportMapper;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.service.IAccountReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 账号关联的报告
 * @Author: jeecg-boot
 * @Date: 2025-03-11
 * @Version: V1.0
 */
@Service
public class AccountReportServiceImpl extends ServiceImpl<AccountReportMapper, AccountReport> implements IAccountReportService {
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private AccountReportMapper accountReportMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private CustomerRegSummaryMapper customerRegSummaryMapper;

    @Override
    public String getPhoneByIdCard(String idCard) {
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Customer::getIdCard, idCard);
        queryWrapper.last("limit 1");
        Customer customer = customerMapper.selectOne(queryWrapper);
        if (customer != null) {
            return customer.getPhone();
        }
        return null;
    }

    @Override
    public void pageReportByIdcard(Page<AccountReport> page, String idCard) {
        accountReportMapper.pageAccountReportByIdCard(page, idCard);
    }

    @Override
    public Map<String, Object> statReportByIdcard(String idCard) {
        //统计报告数量
        Integer reportCount = jdbcTemplate.queryForObject("select count(1) from customer_reg where id_card = ? and summary_status=?", Integer.class, idCard, ExConstants.SUMMARY_STATUS_审核通过);
        //获取最近一次报告时间
        Date lastReportTime = jdbcTemplate.queryForObject("select max(summary_time) from customer_reg where id_card = ? and summary_status=?", Date.class, idCard, ExConstants.SUMMARY_STATUS_审核通过);

        Map<String, Object> stat = new HashMap<>();
        stat.put("reportCount", reportCount);
        if (lastReportTime != null) {
            stat.put("lastReportTime", DateUtil.formatDateTime(lastReportTime));
        }
        return stat;
    }

    @Override
    public void queryReportByAccountId(Page<AccountReport> page, String accountId) {

        accountReportMapper.pageAccountReport(page, accountId);
    }

    @Override
    public String generateAccountReport(String accountId, String idCard) {
        List<AccountReport> accountReportList = customerRegSummaryMapper.getAccountReportByIdcard(idCard);
        int count = 0;
        if (CollectionUtils.isNotEmpty(accountReportList)) {
            for (AccountReport accountReport : accountReportList) {
                accountReport.setAccountId(accountId);
                accountReport.setCreateTime(new Date());
                //查询accountReport是否已存在
                Integer existCount = jdbcTemplate.queryForObject("select count(1) from account_report where account_id = ? and summary_id = ?", Integer.class, accountId, accountReport.getSummaryId());
                if (existCount > 0) {
                    continue;
                }

                accountReportMapper.insert(accountReport);
                count++;
            }
        }
        return "本次查询到" + count + "条报告";
    }
}
