package org.jeecg.modules.summary.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@TableName("company_reg_summary")
@ApiModel(value = "CompanyRegSummary对象", description = "")
@Data
public class CompanyRegSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("团检登记ID")
    private String companyRegId;

    @ApiModelProperty("团检异常情况详述")
    private String abnormalSummary;

    @ApiModelProperty("总人数")
    private Integer total;

    @ApiModelProperty("参检人数")
    private Integer checkedTotal;

    @ApiModelProperty("创建人账号")
    private String createBy;

    @ApiModelProperty("创建人")
    private String creatorName;

    @ApiModelProperty("创建日期")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人账号")
    private String updateBy;

    @ApiModelProperty("更新人")
    private String updator;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCompanyRegId() {
        return companyRegId;
    }

    public void setCompanyRegId(String companyRegId) {
        this.companyRegId = companyRegId;
    }

    public String getAbnormalSummary() {
        return abnormalSummary;
    }

    public void setAbnormalSummary(String abnormalSummary) {
        this.abnormalSummary = abnormalSummary;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getCheckedTotal() {
        return checkedTotal;
    }

    public void setCheckedTotal(Integer checkedTotal) {
        this.checkedTotal = checkedTotal;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CompanyRegSummary{" +
            "id = " + id +
            ", companyRegId = " + companyRegId +
            ", abnormalSummary = " + abnormalSummary +
            ", total = " + total +
            ", checkedTotal = " + checkedTotal +
            ", createBy = " + createBy +
            ", creatorName = " + creatorName +
            ", createTime = " + createTime +
            ", updateBy = " + updateBy +
            ", updator = " + updator +
            ", updateTime = " + updateTime +
        "}";
    }
}
