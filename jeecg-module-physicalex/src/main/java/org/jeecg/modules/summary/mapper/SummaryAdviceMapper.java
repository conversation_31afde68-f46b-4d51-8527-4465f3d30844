package org.jeecg.modules.summary.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.SummaryAdvice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 总检建议
 * @Author: jeecg-boot
 * @Date:   2024-05-19
 * @Version: V1.0
 */
public interface SummaryAdviceMapper extends BaseMapper<SummaryAdvice> {

    List<SummaryAdvice> listNeedCreateLuceneIndex();

    List<SummaryAdvice> listNessaryUpdateLuceneIndex();

    List<SummaryAdvice> listAllUpdateLuceneIndex();

    List<SummaryAdvice> listAll();

    /**
     * 获取总记录数
     */
    long getTotalCount();

    /**
     * 分页查询数据
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 数据列表
     */
    List<SummaryAdvice> listByPage(@Param("offset") int offset, @Param("limit") int limit);

}
