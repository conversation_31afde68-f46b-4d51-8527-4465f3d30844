package org.jeecg.modules.summary.service;

import org.jeecg.modules.summary.dto.ItemResultChangeApplyDto;
import org.jeecg.modules.summary.entity.ItemResultChangeApply;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 体检结果修改申请
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
public interface IItemResultChangeApplyService extends IService<ItemResultChangeApply> {
   List<ItemResultChangeApply> getLatestChangeApplyList(String customerRegId, String departmentId);

   void saveItemResultChangeApply(ItemResultChangeApply itemResultChangeApply) throws Exception;

   ItemResultChangeApplyDto getItemResultChangeApplyDto(String id);

   void handleItemResultChangeApply(String taskId,String agreeFlag,String remark) throws Exception;

}
