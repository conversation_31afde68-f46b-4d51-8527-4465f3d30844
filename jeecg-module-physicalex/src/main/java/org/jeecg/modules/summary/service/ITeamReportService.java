package org.jeecg.modules.summary.service;


import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.summary.bo.teamreport.*;

import java.util.List;

public interface ITeamReportService {

    public TeamReportVO getTeamReportStat(String companyRegId,String companyDeptId);

    public ZyTeamReportVO getZyTeamReportStat(String companyRegId, String companyDeptId);

    public List<ValueAndLabel> getAgeGroupStat(String companyRegId);

    public List<ValueAndLabel> getDepartAbnormalStat(String companyRegId,String companyDeptId);

    public List<ValueAndLabel> getTop10tAbnormalConditionStat(String companyRegId,String companyDeptId);

    public List<ValueAndLabel> getB_DepartmentAbnormalStat(String companyRegId,String companyDeptId);

    public AbnormalGeneralSummary getAbnormalGeneralSummaryStat(String companyRegId, CompanyReg companyReg,String companyDeptId);

    public List<AbnormalCondition> getAbnormalConditionListStat(String companyRegId);

    public List<TeamReportVO.AbnormalItemAndNameVO> getAbnormalItemAndNameListStat(String companyRegId,String companyDeptId);

}
