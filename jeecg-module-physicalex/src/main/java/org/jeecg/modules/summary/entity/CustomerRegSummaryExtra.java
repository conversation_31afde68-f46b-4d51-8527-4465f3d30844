package org.jeecg.modules.summary.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 总检扩展表
 * @Author: jeecg-boot
 * @Date:   2025-06-03
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_summary_extra")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer_reg_summary_extra对象", description="总检扩展表")
public class CustomerRegSummaryExtra implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**登记记录ID*/
	@Excel(name = "登记记录ID", width = 15)
    @ApiModelProperty(value = "登记记录ID")
    private java.lang.String customerRegId;
	/**总检建议ID*/
	@Excel(name = "总检建议ID", width = 15)
    @ApiModelProperty(value = "总检建议ID")
    private java.lang.String summaryId;
	/**异常汇总结构化数据*/
	@Excel(name = "异常汇总结构化数据", width = 15)
    @ApiModelProperty(value = "异常汇总结构化数据")
    private java.lang.String abnormalStructContent;
	/**建议结构化数据*/
	@Excel(name = "建议结构化数据", width = 15)
    @ApiModelProperty(value = "建议结构化数据")
    private java.lang.String summaryStructContent;
	/**诊断结构化数据*/
	@Excel(name = "诊断结构化数据", width = 15)
    @ApiModelProperty(value = "诊断结构化数据")
    private java.lang.String diagnoseStructContent;
}
