package org.jeecg.modules.summary.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.summary.entity.ReportCallback;
import org.jeecg.modules.summary.service.IReportCallbackService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 报告召回
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
@Api(tags="报告召回")
@RestController
@RequestMapping("/summary/reportCallback")
@Slf4j
public class ReportCallbackController extends JeecgController<ReportCallback, IReportCallbackService> {
	@Autowired
	private IReportCallbackService reportCallbackService;
	
	/**
	 * 分页列表查询
	 *
	 * @param reportCallback
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "报告召回-分页列表查询")
	@ApiOperation(value="报告召回-分页列表查询", notes="报告召回-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ReportCallback>> queryPageList(ReportCallback reportCallback,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ReportCallback> queryWrapper = QueryGenerator.initQueryWrapper(reportCallback, req.getParameterMap());
		Page<ReportCallback> page = new Page<ReportCallback>(pageNo, pageSize);
		IPage<ReportCallback> pageList = reportCallbackService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param reportCallback
	 * @return
	 */
	@AutoLog(value = "报告召回-添加")
	@ApiOperation(value="报告召回-添加", notes="报告召回-添加")
	@RequiresPermissions("summary:report_callback:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ReportCallback reportCallback) {
		reportCallbackService.save(reportCallback);
		return Result.OK("添加成功！");
	}

	 @AutoLog(value = "报告召回-报告召回")
	 @ApiOperation(value="报告召回-报告召回", notes="报告召回-报告召回")
	 //@RequiresPermissions("summary:report_callback:add")
	 @PostMapping(value = "/callbackReport")
	 public Result<String> callbackReport(@RequestBody ReportCallback reportCallback) {
         try {
             reportCallbackService.callbackReport(reportCallback);
			 return Result.OK("操作成功！");
         } catch (Exception e) {
			 return Result.error(e.getMessage());
         }
	 }


	
	/**
	 *  编辑
	 *
	 * @param reportCallback
	 * @return
	 */
	@AutoLog(value = "报告召回-编辑")
	@ApiOperation(value="报告召回-编辑", notes="报告召回-编辑")
	@RequiresPermissions("summary:report_callback:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ReportCallback reportCallback) {
		reportCallbackService.updateById(reportCallback);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "报告召回-通过id删除")
	@ApiOperation(value="报告召回-通过id删除", notes="报告召回-通过id删除")
	@RequiresPermissions("summary:report_callback:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		reportCallbackService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "报告召回-批量删除")
	@ApiOperation(value="报告召回-批量删除", notes="报告召回-批量删除")
	@RequiresPermissions("summary:report_callback:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.reportCallbackService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "报告召回-通过id查询")
	@ApiOperation(value="报告召回-通过id查询", notes="报告召回-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ReportCallback> queryById(@RequestParam(name="id",required=true) String id) {
		ReportCallback reportCallback = reportCallbackService.getById(id);
		if(reportCallback==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(reportCallback);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param reportCallback
    */
    @RequiresPermissions("summary:report_callback:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ReportCallback reportCallback) {
        return super.exportXls(request, reportCallback, ReportCallback.class, "报告召回");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("summary:report_callback:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ReportCallback.class);
    }

}
