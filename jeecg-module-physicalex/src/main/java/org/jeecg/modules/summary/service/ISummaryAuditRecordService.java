package org.jeecg.modules.summary.service;

import org.jeecg.modules.summary.entity.SummaryAuditRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 总检审核记录
 * @Author: jeecg-boot
 * @Date:   2024-05-27
 * @Version: V1.0
 */
public interface ISummaryAuditRecordService extends IService<SummaryAuditRecord> {

    String revokeAudit(SummaryAuditRecord auditRecord) throws Exception;

    void revokeAuditByProcess(SummaryAuditRecord auditRecord) throws Exception;
}
