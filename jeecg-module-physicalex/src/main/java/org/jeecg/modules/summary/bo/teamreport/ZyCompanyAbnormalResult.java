package org.jeecg.modules.summary.bo.teamreport;

import lombok.Data;

import java.util.List;

@Data
public class ZyCompanyAbnormalResult {
    private String examNo;
    private String customerRegId;
    private String itemGroupId;
    private String itemGroupName;
    private String valueRefRange;
    private String abnormalFlag;
    private String abnormalFlagDesc;
    private String checkConclusion;
    private Integer abandonFlag;
    private String explain;
    private String advice;
    private List<CompanyAbnormalItemResultVO> abnormalPersonList;
    private List<CompanyAbnormalItemResultVO> allPersonList;
}
