package org.jeecg.modules.summary.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.CustomerRegSummaryHistory;
import org.jeecg.modules.summary.entity.SummaryAuditRecord;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryHistoryMapper;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.jeecg.modules.summary.service.ISummaryAuditRecordService;
import org.jeecg.modules.summary.service.SystemUserUtilService;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.appointment.mapper.CustomerOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: 总检审核记录
 * @Author: jeecg-boot
 * @Date: 2024-05-27
 * @Version: V1.0
 */
@Api(tags = "总检审核记录")
@RestController
@RequestMapping("/summary/summaryAuditRecord")
@Slf4j
public class SummaryAuditRecordController extends JeecgController<SummaryAuditRecord, ISummaryAuditRecordService> {
    @Autowired
    private ISummaryAuditRecordService summaryAuditRecordService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SystemUserUtilService systemUserUtilService;
    @Autowired
    private ICustomerRegSummaryService customerRegSummaryService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private CustomerRegSummaryHistoryMapper customerRegSummaryHistoryMapper;
    @Autowired
    private CustomerOrderMapper customerOrderMapper;

    /**
     * 分页列表查询
     *
     * @param summaryAuditRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "总检审核记录-分页列表查询")
    @ApiOperation(value = "总检审核记录-分页列表查询", notes = "总检审核记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<SummaryAuditRecord>> queryPageList(SummaryAuditRecord summaryAuditRecord, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<SummaryAuditRecord> queryWrapper = QueryGenerator.initQueryWrapper(summaryAuditRecord, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        Page<SummaryAuditRecord> page = new Page<SummaryAuditRecord>(pageNo, pageSize);
        IPage<SummaryAuditRecord> pageList = summaryAuditRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    //revokeSummaryStatus
    @ApiOperation(value = "总检审核记录-撤销审核", notes = "总检审核记录-撤销审核")
    @PostMapping(value = "/revokeSummaryStatus")
    @RequiresPermissions("summary:summary_audit_record:revoke")
    public Result<String> revokeSummaryStatus(@RequestBody SummaryAuditRecord summaryAuditRecord) throws Exception {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        summaryAuditRecord.setApplyer(loginUser.getRealname());
        summaryAuditRecord.setApplyBy(loginUser.getUsername());
        String lastSummaryStatus = summaryAuditRecordService.revokeAudit(summaryAuditRecord);
        return Result.OK("撤销成功！", lastSummaryStatus);
    }

    @ApiOperation(value = "总检审核记录-撤销审核", notes = "总检审核记录-撤销审核")
    @PostMapping(value = "/revokeAuditByProcess")
    @RequiresPermissions("summary:summary_audit_record:revoke")
    public Result<String> revokeAuditByProcess(@RequestBody SummaryAuditRecord summaryAuditRecord) throws Exception {

        summaryAuditRecordService.revokeAuditByProcess(summaryAuditRecord);
        return Result.OK("操作成功！");
    }


    /**
     * 添加
     *
     * @param summaryAuditRecord
     * @return
     */
    @AutoLog(value = "总检审核记录-添加")
    @ApiOperation(value = "总检审核记录-添加", notes = "总检审核记录-添加")
    @RequiresPermissions("summary:summary_audit_record:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody SummaryAuditRecord summaryAuditRecord) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        summaryAuditRecord.setCreator(sysUser.getRealname());
        summaryAuditRecordService.save(summaryAuditRecord);
        CustomerRegSummaryHistory history = new CustomerRegSummaryHistory();
        history.setCreator(sysUser.getRealname());
        history.setCreateBy(sysUser.getUsername());
        history.setCustomerRegId(summaryAuditRecord.getCustomerRegId());
        history.setSummaryId(summaryAuditRecord.getSummaryId());
        history.setType("总检审核");
        history.setSummaryType("总检审核");
        history.setRemark(summaryAuditRecord.getRejectReason());
        customerRegSummaryHistoryMapper.insert(history);

        //更新总检的状态
        String auditResult = summaryAuditRecord.getAuditResult();
        if (StringUtils.isNotBlank(auditResult) && StringUtils.isNotBlank(summaryAuditRecord.getSummaryId())) {
            UpdateWrapper<CustomerRegSummary> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", summaryAuditRecord.getSummaryId());
            updateWrapper.set("status", auditResult);
            updateWrapper.set("auditor", sysUser.getRealname());
            updateWrapper.set("auditor_name", sysUser.getRealname());
            updateWrapper.set("audite_by", sysUser.getUsername());
            updateWrapper.set("confirm_time", new Date());
            updateWrapper.set("audit_time", new Date());
            updateWrapper.set("auditor_sign_pic", systemUserUtilService.getSignPicByUsername(sysUser.getUsername()));
            customerRegSummaryService.update(updateWrapper);

            UpdateWrapper<CustomerReg> regUpdateWrapper = new UpdateWrapper<>();
            regUpdateWrapper.set("summary_status", auditResult);
            regUpdateWrapper.set("summary_audit_time", new Date());
            regUpdateWrapper.eq("id", summaryAuditRecord.getCustomerRegId());
            customerRegMapper.update(null, regUpdateWrapper);
            //更新套餐订单状态
            LambdaUpdateWrapper<CustomerOrder> orderUpdateWrapper = new LambdaUpdateWrapper<>();
            orderUpdateWrapper.set(CustomerOrder::getStatus,ExConstants.ORDER_STATUS_REPORTED);
            orderUpdateWrapper.eq(CustomerOrder::getCustomerRegId, summaryAuditRecord.getCustomerRegId());
            customerOrderMapper.update(null, orderUpdateWrapper);
        } else {
            return Result.error("总检状态更新失败！");
        }

        return Result.OK("操作成功！", summaryAuditRecord);
    }

    /**
     * 编辑
     *
     * @param summaryAuditRecord
     * @return
     */
    @AutoLog(value = "总检审核记录-编辑")
    @ApiOperation(value = "总检审核记录-编辑", notes = "总检审核记录-编辑")
    @RequiresPermissions("summary:summary_audit_record:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody SummaryAuditRecord summaryAuditRecord) {
        summaryAuditRecordService.updateById(summaryAuditRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "总检审核记录-通过id删除")
    @ApiOperation(value = "总检审核记录-通过id删除", notes = "总检审核记录-通过id删除")
    @RequiresPermissions("summary:summary_audit_record:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        summaryAuditRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "总检审核记录-批量删除")
    @ApiOperation(value = "总检审核记录-批量删除", notes = "总检审核记录-批量删除")
    @RequiresPermissions("summary:summary_audit_record:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.summaryAuditRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "总检审核记录-通过id查询")
    @ApiOperation(value = "总检审核记录-通过id查询", notes = "总检审核记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<SummaryAuditRecord> queryById(@RequestParam(name = "id", required = true) String id) {
        SummaryAuditRecord summaryAuditRecord = summaryAuditRecordService.getById(id);
        if (summaryAuditRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(summaryAuditRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param summaryAuditRecord
     */
    @RequiresPermissions("summary:summary_audit_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SummaryAuditRecord summaryAuditRecord) {
        return super.exportXls(request, summaryAuditRecord, SummaryAuditRecord.class, "总检审核记录");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("summary:summary_audit_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SummaryAuditRecord.class);
    }

}
