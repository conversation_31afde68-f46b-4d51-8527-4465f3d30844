package org.jeecg.modules.summary.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.index.Term;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.*;
import org.apache.lucene.document.IntPoint;
import org.jeecg.excommons.utils.LuceneQueryStringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Summary Advice Query Builder
 * Unified handling of search query construction
 */
@Component
@Slf4j
public class SummaryAdviceQueryBuilder {

    @Autowired
    private Analyzer hanlpAnalyzer;

    /**
     * Build main query
     */
    public Query buildMainQuery(Boolean isExactlyMatch, String keyword, String gender,
                               Integer age, String marriage, String departmentId,
                               String normalDepartSummary) {

        if (StringUtils.isBlank(keyword)) {
            throw new IllegalArgumentException("Keyword cannot be empty");
        }

        try {
            BooleanQuery.Builder mainQuery = new BooleanQuery.Builder();

            // Exclude normal department summary
            if (StringUtils.isNotBlank(normalDepartSummary)) {
                Query normalDepartSummaryQuery = new TermQuery(new Term("keywords", normalDepartSummary));
                mainQuery.add(new BooleanClause(normalDepartSummaryQuery, BooleanClause.Occur.MUST_NOT));
            }

            // Build keyword query
            Query keywordQuery = buildKeywordQuery(isExactlyMatch, keyword);
            mainQuery.add(new BooleanClause(keywordQuery, BooleanClause.Occur.MUST));

            // Add gender restriction
            if (StringUtils.isNotBlank(gender)) {
                Query genderQuery = buildGenderQuery(gender);
                mainQuery.add(new BooleanClause(genderQuery, BooleanClause.Occur.MUST));
            }

            // Add marriage status restriction
            if (StringUtils.isNotBlank(marriage)) {
                Query marriageQuery = buildMarriageQuery(marriage);
                mainQuery.add(new BooleanClause(marriageQuery, BooleanClause.Occur.MUST));
            }

            // Add age restriction
            if (age != null) {
                Query ageQuery = buildAgeQuery(age);
                mainQuery.add(new BooleanClause(ageQuery, BooleanClause.Occur.MUST));
            }

            // Add department restriction
            if (StringUtils.isNotBlank(departmentId)) {
                Query departmentQuery = new TermQuery(new Term("departmentId", departmentId));
                mainQuery.add(new BooleanClause(departmentQuery, BooleanClause.Occur.MUST));
            }

            return mainQuery.build();

        } catch (Exception e) {
            log.error("Failed to build query", e);
            throw new RuntimeException("Failed to build query", e);
        }
    }

    /**
     * Build keyword query
     */
    private Query buildKeywordQuery(Boolean isExactlyMatch, String keyword) throws ParseException {
        // Clean keyword
        String cleanKeyword = LuceneQueryStringUtil.handleIllegalChars(keyword);
        cleanKeyword = cleanKeyword.replace("*", "");

        BooleanQuery.Builder keywordQueryBuilder = new BooleanQuery.Builder();

        if (isExactlyMatch != null && isExactlyMatch) {
            // Exact match
            Query exactQuery = new TermQuery(new Term("keywords", cleanKeyword));
            keywordQueryBuilder.add(new BooleanClause(exactQuery, BooleanClause.Occur.MUST));
        } else {
            // Fuzzy match
            Query exactQuery = new TermQuery(new Term("keywords", cleanKeyword));
            keywordQueryBuilder.add(new BooleanClause(exactQuery, BooleanClause.Occur.SHOULD));

            // Use analyzer for tokenized search
            QueryParser keywordsParser = new QueryParser("keywords", hanlpAnalyzer);
            Query analyzedQuery = keywordsParser.parse(cleanKeyword);
            keywordQueryBuilder.add(new BooleanClause(analyzedQuery, BooleanClause.Occur.SHOULD));
        }

        return keywordQueryBuilder.build();
    }

    /**
     * Build gender query
     */
    private Query buildGenderQuery(String gender) {
        BooleanQuery.Builder genderQueryBuilder = new BooleanQuery.Builder();

        Query genderEqQuery = new TermQuery(new Term("sexLimit", gender));
        Query genderUnlimitedQuery = new TermQuery(new Term("sexLimit", "不限"));

        genderQueryBuilder.add(new BooleanClause(genderEqQuery, BooleanClause.Occur.SHOULD));
        genderQueryBuilder.add(new BooleanClause(genderUnlimitedQuery, BooleanClause.Occur.SHOULD));

        return genderQueryBuilder.build();
    }

    /**
     * Build marriage query
     */
    private Query buildMarriageQuery(String marriage) {
        BooleanQuery.Builder marriageQueryBuilder = new BooleanQuery.Builder();

        Query marriageEqQuery = new TermQuery(new Term("marriageLimit", marriage));
        Query marriageUnlimitedQuery = new TermQuery(new Term("marriageLimit", "不限"));

        marriageQueryBuilder.add(new BooleanClause(marriageEqQuery, BooleanClause.Occur.SHOULD));
        marriageQueryBuilder.add(new BooleanClause(marriageUnlimitedQuery, BooleanClause.Occur.SHOULD));

        return marriageQueryBuilder.build();
    }

    /**
     * Build age query
     */
    private Query buildAgeQuery(Integer age) {
        BooleanQuery.Builder ageQueryBuilder = new BooleanQuery.Builder();

        Query minAgeQuery = IntPoint.newRangeQuery("minAge", Integer.MIN_VALUE, age);
        Query maxAgeQuery = IntPoint.newRangeQuery("maxAge", age, Integer.MAX_VALUE);

        ageQueryBuilder.add(new BooleanClause(minAgeQuery, BooleanClause.Occur.MUST));
        ageQueryBuilder.add(new BooleanClause(maxAgeQuery, BooleanClause.Occur.MUST));

        return ageQueryBuilder.build();
    }

    /**
     * Build ID query
     */
    public Query buildIdQuery(String id) {
        if (StringUtils.isBlank(id)) {
            throw new IllegalArgumentException("ID cannot be empty");
        }
        return new TermQuery(new Term("id", id));
    }

    /**
     * Build existence query
     */
    public Query buildExistsQuery(String id) {
        try {
            QueryParser queryParser = new QueryParser("id", hanlpAnalyzer);
            return queryParser.parse(id);
        } catch (ParseException e) {
            log.error("Failed to build existence query", e);
            return new TermQuery(new Term("id", id));
        }
    }

    /**
     * Validate query parameters
     */
    public boolean validateQueryParams(String keyword, String gender, Integer age,
                                     String marriage, String departmentId) {
        // At least need keyword
        if (StringUtils.isBlank(keyword)) {
            log.warn("Query parameter validation failed: keyword is empty");
            return false;
        }

        // Age range check
        if (age != null && (age < 0 || age > 150)) {
            log.warn("Query parameter validation failed: age out of reasonable range {}", age);
            return false;
        }

        return true;
    }

    /**
     * Clean and preprocess keyword
     */
    public String preprocessKeyword(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return keyword;
        }

        // Use utility class to handle illegal characters
        String processed = LuceneQueryStringUtil.handleIllegalChars(keyword);

        // Remove wildcards
        processed = processed.replace("*", "").replace("?", "");

        // Remove extra spaces
        processed = processed.trim().replaceAll("\\s+", " ");

        return processed;
    }
}
