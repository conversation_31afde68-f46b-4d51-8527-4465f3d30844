package org.jeecg.modules.summary.bo.teamreport;

import lombok.Data;
import org.jeecg.modules.occu.entity.ZyConclusion;

import java.util.List;

@Data
public class CompanyAbnormalItemResultVO {
    private String examNo;
    private String customerRegId;
    private String itemGroupId;
    private String itemGroupName;
    private String checkDepartmentCode;
    private String checkDepartmentName;
    private String value;
    private String valueRefRange;
    private String abnormalFlag;
    private String abnormalFlagDesc;
    private String doctorId;
    private String doctorName;
    private String checkConclusion;
    private Integer abandonFlag;
    private String name;
    private String gender;
    private String idCard;
    private String riskFactor;
    private String jobStatus;
    private String workType;
    private String workShop;
    private String riskMonths;
    private String riskYears;
    private Integer age;
    private String abnormalDesc;
    private Integer total;
    private String sexLimit;
    private String nameList;
    private String abnormalItemList;
    private String explain;
    private String advice;
    private List<CompanyAbnormalItemResultVO> companyAbnormalItemList;
    private ZyConclusion zyConclusion;
}
