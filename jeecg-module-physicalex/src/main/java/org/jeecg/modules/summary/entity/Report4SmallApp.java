package org.jeecg.modules.summary.entity;

import lombok.Data;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;

import java.util.Date;
import java.util.List;

@Data
public class Report4SmallApp {

    private String name;

    private String relationType;

    private Integer abnormalGroupCount;

    private Integer abnormalItemCount;

    private String examOrg;

    private Date reportTime;

    private String reportTimeStr;

    private String customerId;

    private String customerRegId;

    private CustomerReg customerReg;

    private CustomerRegSummary summaryAdvice;

    private List<CustomerRegItemGroup> groupList;

}
