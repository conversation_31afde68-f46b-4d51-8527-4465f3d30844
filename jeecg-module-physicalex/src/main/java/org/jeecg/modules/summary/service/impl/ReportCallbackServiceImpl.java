package org.jeecg.modules.summary.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.ItemResultChangeApply;
import org.jeecg.modules.summary.entity.ReportCallback;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.mapper.ItemResultChangeApplyMapper;
import org.jeecg.modules.summary.mapper.ReportCallbackMapper;
import org.jeecg.modules.summary.service.IReportCallbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.Map;

/**
 * @Description: 报告召回
 * @Author: jeecg-boot
 * @Date: 2024-09-24
 * @Version: V1.0
 */
@Service
public class ReportCallbackServiceImpl extends ServiceImpl<ReportCallbackMapper, ReportCallback> implements IReportCallbackService {

    @Autowired
    private TaskService taskService;
    @Autowired
    private ItemResultChangeApplyMapper itemResultChangeApplyMapper;
    @Autowired
    private CustomerRegSummaryMapper customerRegSummaryMapper;

    @Override
    public void callbackReport(ReportCallback reportCallback) throws Exception {
        if (StringUtils.isBlank(reportCallback.getTaskId())) {
            throw new RuntimeException("任务ID不能为空");
        }

        String bussinessKey = reportCallback.getBussinessKey();
        String bussinessType = reportCallback.getBussinessType();
        Map<String, Object> variables = taskService.getVariables(reportCallback.getTaskId());
        if (StringUtils.equals(bussinessType, ExConstants.PROCESS_BUSINESS_TYPE_体检数据修改)) {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            ItemResultChangeApply changeApply = itemResultChangeApplyMapper.selectById(bussinessKey);

            //如果状态为已召回，则更新customerRegSummary表报告的状态
            if (StringUtils.equals(reportCallback.getResult(), "已召回")) {
                //customerRegSummaryMapper.updateReportStatus(changeApply.getCustomerRegSummaryId(),"已召回");
                UpdateWrapper<CustomerRegSummary> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("report_print_status", ExConstants.REPORT_STATUS_已召回);
                updateWrapper.eq("id", changeApply.getSummaryId());
                customerRegSummaryMapper.update(null, updateWrapper);
                variables.put("callbackStatus", "已召回");
            } else {

                variables.put("confirmer", loginUser.getRealname());
                variables.put("confirmBy", loginUser.getUsername());
                variables.put("applyStatus", ExConstants.RECORD_CHANGE_APPLY_STATUS_REFUSED);

                variables.put("callbackStatus", "未召回");
                String msgReceiver = changeApply.getCreateBy();
                String msgContent = "您的体检数据修改申请未通过，报告未召回，具体原因:" + reportCallback.getRemark();
                variables.put("msgReceiver", msgReceiver);
                variables.put("msgContent", msgContent);
            }
            reportCallback.setCallbackOperator(loginUser.getRealname());
            reportCallback.setCallbackBy(loginUser.getUsername());
            reportCallback.setCallbackTime(new Date());
            reportCallback.setCallbackReason(changeApply.getApplyReason());
            reportCallback.setApplyer(changeApply.getCreator());
            reportCallback.setApplyBy(changeApply.getCreateBy());
            reportCallback.setCustomerRegId(changeApply.getCustomerRegId());
            reportCallback.setExamNo(changeApply.getExamNo());
            reportCallback.setSummaryId(changeApply.getSummaryId());

        }
        save(reportCallback);
        //完成任务
        taskService.complete(reportCallback.getTaskId(), variables);
    }
}
