package org.jeecg.modules.summary.service;

import org.jeecg.modules.summary.entity.ReportCallback;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 报告召回
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
public interface IReportCallbackService extends IService<ReportCallback> {

    void callbackReport(ReportCallback reportCallback) throws Exception;
}
