package org.jeecg.modules.summary.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 总检记录表
 * @Author: jeecg-boot
 * @Date:   2024-09-10
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_summary_history")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer_reg_summary_history对象", description="总检记录表")
public class CustomerRegSummaryHistory implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    private String creator;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**登记记录ID*/
	@Excel(name = "登记记录ID", width = 15)
    @ApiModelProperty(value = "登记记录ID")
    private java.lang.String customerRegId;
	/**总检记录ID*/
	@Excel(name = "总检记录ID", width = 15)
    @ApiModelProperty(value = "总检记录ID")
    private java.lang.String summaryId;
	/**汇总变更前*/
	@Excel(name = "汇总变更前", width = 15)
    @ApiModelProperty(value = "汇总变更前")
    private java.lang.String summaryOrigin;
    /**汇总变更后*/
    @Excel(name = "汇总变更后", width = 15)
    @ApiModelProperty(value = "汇总变更后")
    private java.lang.String summaryAfter;
	/**建议变更前*/
	@Excel(name = "建议变更前", width = 15)
    @ApiModelProperty(value = "建议变更前")
    private java.lang.String adviceOrigin;
    /**建议变更后*/
    @Excel(name = "建议变更后", width = 15)
    @ApiModelProperty(value = "建议变更后")
    private java.lang.String adviceAfter;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型")
    private java.lang.String type;
	/**总检类型*/
	@Excel(name = "总检类型", width = 15)
    @ApiModelProperty(value = "总检类型")
    private java.lang.String summaryType;

    private String remark;
}
