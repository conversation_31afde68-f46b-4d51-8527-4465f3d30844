package org.jeecg.modules.summary.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoInitailSummaryTask implements Job {

    @Autowired
    private ICustomerRegSummaryService summaryService;

    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                summaryService.doAutoInitialSummary();
            } catch (Exception e) {
                log.error("自动初检任务失败：", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("自动初检任务正在运行");
        }
    }
}