import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '登记记录ID',
    align: "center",
    dataIndex: 'customerRegId'
  },
  {
    title: '总检建议ID',
    align: "center",
    dataIndex: 'summaryId'
  },
  {
    title: '异常汇总结构化数据',
    align: "center",
    dataIndex: 'abnormalStructContent'
  },
  {
    title: '建议结构化数据',
    align: "center",
    dataIndex: 'summaryStructContent'
  },
  {
    title: '诊断结构化数据',
    align: "center",
    dataIndex: 'diagnoseStructContent'
  },
];

// 高级查询数据
export const superQuerySchema = {
  customerRegId: {title: '登记记录ID',order: 0,view: 'text', type: 'string',},
  summaryId: {title: '总检建议ID',order: 1,view: 'text', type: 'string',},
  abnormalStructContent: {title: '异常汇总结构化数据',order: 2,view: 'text', type: 'string',},
  summaryStructContent: {title: '建议结构化数据',order: 3,view: 'text', type: 'string',},
  diagnoseStructContent: {title: '诊断结构化数据',order: 4,view: 'text', type: 'string',},
};
