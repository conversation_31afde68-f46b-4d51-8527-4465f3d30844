package org.jeecg.modules.summary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.summary.entity.AdviceBean;
import org.jeecg.modules.summary.entity.SummaryAdvice;

import java.util.List;

/**
 * @Description: 总检建议
 * @Author: jeecg-boot
 * @Date: 2024-05-19
 * @Version: V1.0
 */
public interface ISummaryAdviceService extends IService<SummaryAdvice> {

    Integer getNextSortNum(String departmentId);

    void addAdvice(SummaryAdvice summaryAdvice);

    void createLuceneIndex();

    void updateLuceneIndexNessary(String id);

    void updateLuceneIndex(SummaryAdvice summaryAdvice);

    void updateLuceneIndexNessary();

    void updateLuceneIndexAll();

    void removeLuceneIndex(String id);

    void createLuceneIndex4SummaryAdvice(SummaryAdvice summaryAdvice);

    List<SummaryAdvice> listAdviceFromDB(String keyword, String gender, Integer age, String marriage, String departmentId);

    List<SummaryAdvice> listAdviceFromLucene(Boolean isExactlyMatch, String keyword, String gender, Integer age, String marriage, String departmentId);

    List<AdviceBean> generateAdviceByRegId(String customerRegId) throws Exception;

    List<AdviceBean> generateAdviceByDepartSummaryList(Boolean aiSummary, Boolean isExactlyMatch, List<String> abnormalTextList, CustomerReg reg) throws Exception;

    void updateKeywordsExplain();

    String getAIReply(String prompt) throws Exception;

    /**
     * 从AI模型获取总检建议字典
     * 针对advice_content为空的记录，批量调用AI生成建议内容
     * @return 处理结果信息
     * @throws Exception 异常
     */
    String generateAdviceDictionaryFromAI() throws Exception;

    /**
     * Count all indexed documents
     */
    long countAllIndexedDocuments();



}
