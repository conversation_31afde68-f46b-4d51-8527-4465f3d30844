package org.jeecg.modules.summary.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.summary.entity.AccountReport;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * @Description: 账号关联的报告
 * @Author: jeecg-boot
 * @Date: 2025-03-11
 * @Version: V1.0
 */
public interface IAccountReportService extends IService<AccountReport> {

    void queryReportByAccountId(Page<AccountReport> page, String accountId);

    String generateAccountReport(String accountId, String idCard);

    String getPhoneByIdCard(String idCard);

    void pageReportByIdcard(Page<AccountReport> page, String idCard);

    Map<String,Object> statReportByIdcard(String idCard);
}
