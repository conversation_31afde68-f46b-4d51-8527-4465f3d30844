package org.jeecg.modules.summary.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.summary.entity.ReportActionRecord;
import org.jeecg.modules.summary.service.IReportActionRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 报告操作记录
 * @Author: jeecg-boot
 * @Date:   2024-11-18
 * @Version: V1.0
 */
@Api(tags="报告操作记录")
@RestController
@RequestMapping("/summary/reportActionRecord")
@Slf4j
public class ReportActionRecordController extends JeecgController<ReportActionRecord, IReportActionRecordService> {
	@Autowired
	private IReportActionRecordService reportActionRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param reportActionRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "报告操作记录-分页列表查询")
	@ApiOperation(value="报告操作记录-分页列表查询", notes="报告操作记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ReportActionRecord>> queryPageList(ReportActionRecord reportActionRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ReportActionRecord> queryWrapper = QueryGenerator.initQueryWrapper(reportActionRecord, req.getParameterMap());
		Page<ReportActionRecord> page = new Page<ReportActionRecord>(pageNo, pageSize);
		IPage<ReportActionRecord> pageList = reportActionRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param reportActionRecord
	 * @return
	 */
	@AutoLog(value = "报告操作记录-添加")
	@ApiOperation(value="报告操作记录-添加", notes="报告操作记录-添加")
	@RequiresPermissions("summary:report_action_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ReportActionRecord reportActionRecord) {
		reportActionRecordService.save(reportActionRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param reportActionRecord
	 * @return
	 */
	@AutoLog(value = "报告操作记录-编辑")
	@ApiOperation(value="报告操作记录-编辑", notes="报告操作记录-编辑")
	@RequiresPermissions("summary:report_action_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ReportActionRecord reportActionRecord) {
		reportActionRecordService.updateById(reportActionRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "报告操作记录-通过id删除")
	@ApiOperation(value="报告操作记录-通过id删除", notes="报告操作记录-通过id删除")
	@RequiresPermissions("summary:report_action_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		reportActionRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "报告操作记录-批量删除")
	@ApiOperation(value="报告操作记录-批量删除", notes="报告操作记录-批量删除")
	@RequiresPermissions("summary:report_action_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.reportActionRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "报告操作记录-通过id查询")
	@ApiOperation(value="报告操作记录-通过id查询", notes="报告操作记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ReportActionRecord> queryById(@RequestParam(name="id",required=true) String id) {
		ReportActionRecord reportActionRecord = reportActionRecordService.getById(id);
		if(reportActionRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(reportActionRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param reportActionRecord
    */
    @RequiresPermissions("summary:report_action_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ReportActionRecord reportActionRecord) {
        return super.exportXls(request, reportActionRecord, ReportActionRecord.class, "报告操作记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("summary:report_action_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ReportActionRecord.class);
    }

}
