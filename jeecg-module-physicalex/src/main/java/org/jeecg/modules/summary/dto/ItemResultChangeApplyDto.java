package org.jeecg.modules.summary.dto;

import lombok.Data;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.ItemResultChangeApply;
@Data
public class ItemResultChangeApplyDto {
    private ItemResultChangeApply changeApply;
    private CustomerReg customerReg;
    private CustomerRegSummary customerRegSummary;
}
