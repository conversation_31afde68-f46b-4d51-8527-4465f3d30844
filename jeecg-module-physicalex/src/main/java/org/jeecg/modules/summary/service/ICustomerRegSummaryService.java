package org.jeecg.modules.summary.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang.StringUtils;
import org.jeecg.excommons.BatchResult;
import org.jeecg.modules.ai.bo.AIMessage;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.summary.bo.AbnormalSummaryAndAdvice;
import org.jeecg.modules.summary.bo.ReportBean;
import org.jeecg.modules.summary.entity.AdviceBean;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-05-20
 * @Version: V1.0
 */
public interface ICustomerRegSummaryService extends IService<CustomerRegSummary> {

    void pageCustomerReg(Page<CustomerReg> page, String examCatory, String qualified, String name, String gender, String idCard, String phone, String dateType, String dateStart, String dateEnd, String examNo, String examCardNo, String checkState, String companyRegId, String teamId, String printStatus, String doctorType, String doctor, String filterStatus, String status, String summaryStatus, String preSummaryMethod, String initailSummaryMethod, String eReportStatus, String paperReportStatus, String sortOrder, String daysFromReg);

    String getStatusByReg(String customerRegId);

    CustomerRegSummary getByRegId(String customerRegId);

    CustomerRegSummary getOrGenerateByRedId(String customerRegId);

    CustomerRegSummary getSummaryById(String id);

    CustomerRegSummary saveOrUpdateCustomerSummary(CustomerRegSummary customerRegSummary) throws Exception;

    CustomerReg getRegById(String regId);

    void saveHealthCardResult(String customerRegId, String summaryId, String healthCardResult);

    void updateReportPrintTimes(String summaryId);

    boolean isSummaryAudited(String customerRegId);

    BatchResult<CustomerReg> preSummaryByIds(List<String> ids, String preSummaryType);

    BatchResult<CustomerReg> unPreSummaryByIds(List<String> ids);

    BatchResult<CustomerReg> preSummaryByCondition(QueryWrapper<CustomerReg> queryWrapper, String preSummaryType);

    BatchResult<CustomerReg> unPreSummaryByCondition(QueryWrapper<CustomerReg> queryWrapper);

    void doAutoPreSummary();

    void doAutoInitialSummary();

    AbnormalSummaryAndAdvice generateAbnormalSummaryAndAdvice(CustomerReg customerReg, Boolean useAI) throws Exception;

    List<AdviceBean> generateAdviceByDepartSummary(Boolean aiSummary, String abnormalText, String customerRegId) throws Exception;

    void generateAiSummaryAsync(List<AIMessage> message, SseEmitter emitter, AtomicBoolean completed, String modId, String clientId) throws Exception;
}
