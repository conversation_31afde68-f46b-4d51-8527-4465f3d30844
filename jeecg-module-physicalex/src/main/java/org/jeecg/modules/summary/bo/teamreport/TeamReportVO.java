package org.jeecg.modules.summary.bo.teamreport;


import lombok.Data;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CompanyTeam;

import java.util.List;


@Data
public class TeamReportVO {
    public Company company;
    public CompanyReg companyReg;
    public List<CompanyTeam> companyTeams;
    public CompanyTeamPersonStat companyTeamPersonStat;
    public AgeGroupsByGender ageGroupChart;
    public List<CompanyAbnormalItemResultVO> abnormalItemVOS;
    public List<CompanyAbnormalItemResultVO> femaleAbnormalItms;
    public List<CompanyAbnormalItemResultVO> maleAbnormalItms;
    public List<CompanyAbnormalItemResultVO> abnormalItemListByPerson;

    public List<ValueAndLabel> departAbnormalChart;
    public List<ValueAndLabel> top10tAbnormalConditionChart;
    public List<ValueAndLabel> b_DepartmentAbnormalChart;
    public AbnormalGeneralSummary abnormalGeneralSummary;
    public List<AbnormalCondition> abnormalConditionList;
    public List<AbnormalItemAndNameVO> abnormalItemAndNameList;

    @Data
    public static class AgeGroupsByGender {
        public String _1_GroupTotal;
        public String _1_GroupMaleTotal;
        public String _1_GroupFemaleTotal;
        public String _2_GroupTotal;
        public String _2_GroupMaleTotal;
        public String _2_GroupFemaleTotal;
        public String _3_GroupTotal;
        public String _3_GroupMaleTotal;
        public String _3_GroupFemaleTotal;
        public String _4_GroupTotal;
        public String _4_GroupMaleTotal;
        public String _4_GroupFemaleTotal;
        public String _5_GroupTotal;
        public String _5_GroupMaleTotal;
        public String _5_GroupFemaleTotal;
        public List<ValueAndLabel> ageGroups;

    }

    @Data
    public static class AbnormalItemAndNameVO {
        public String departmentNo;
        public String departmentName;
        public List<AbnormalItemVO> abnormalItem;
        @Data
        public static class AbnormalItemVO {
            public String abnormalDesc;
            public String nameList;
            public int total;
            public String percentage;

        }
    }
    @Data
    public static class CompanyTeamPersonStat {
        public Integer checkedTotal;
        public Integer checkedMaleTotal;
        public Integer checkedFemaleTotal;
        public String checkedMalePercentage;
        public String checkedFemalePercentage;
        public String maleToFemaleRatio;
        public List<ValueAndLabel> checkedPersonTotalByGender;
        public List<ValueAndLabel> checkedPersonPercentageByGender;



    }
}
