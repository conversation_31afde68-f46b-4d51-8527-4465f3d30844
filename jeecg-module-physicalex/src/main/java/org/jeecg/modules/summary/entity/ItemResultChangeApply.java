package org.jeecg.modules.summary.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 体检结果修改申请
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
@Data
@TableName("item_result_change_apply")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="item_result_change_apply对象", description="体检结果修改申请")
public class ItemResultChangeApply implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**体检号*/
	@Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;
	/**检客姓名*/
	@Excel(name = "检客姓名", width = 15)
    @ApiModelProperty(value = "检客姓名")
    private java.lang.String customerName;
	/**申请原因*/
	@Excel(name = "申请原因", width = 15, dicCode = "item_result_change_reason")
	@Dict(dicCode = "item_result_change_reason")
    @ApiModelProperty(value = "申请原因")
    private java.lang.String applyReason;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private java.lang.String status;
	/**关联组合ID*/
	@Excel(name = "关联组合ID", width = 15)
    @ApiModelProperty(value = "关联组合ID")
    private java.lang.String customerRegItemGroupId;
	/**申请科室ID*/
	@Excel(name = "申请科室ID", width = 15)
    @ApiModelProperty(value = "申请科室ID")
    private java.lang.String applyDepartmentId;
	/**申请人*/
	@Excel(name = "申请人", width = 15)
    @ApiModelProperty(value = "申请人")
    private java.lang.String creator;
	/**申请科室*/
	@Excel(name = "申请科室", width = 15)
    @ApiModelProperty(value = "申请科室")
    private java.lang.String applyDepartment;
	/**总检ID*/
	@Excel(name = "总检ID", width = 15)
    @ApiModelProperty(value = "总检ID")
    private java.lang.String summaryId;
	/**确认者*/
	@Excel(name = "确认者", width = 15)
    @ApiModelProperty(value = "确认者")
    private java.lang.String confirmer;
	/**确认者账号*/
	@Excel(name = "确认者账号", width = 15)
    @ApiModelProperty(value = "确认者账号")
    private java.lang.String confirmBy;
	/**确认时间*/
	@Excel(name = "确认时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "确认时间")
    private java.util.Date confirmTime;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**登记记录ID*/
	@Excel(name = "登记记录ID", width = 15)
    @ApiModelProperty(value = "登记记录ID")
    private java.lang.String customerRegId;
}
