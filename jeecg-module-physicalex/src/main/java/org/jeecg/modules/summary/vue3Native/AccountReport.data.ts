import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '账号ID',
    align: "center",
    dataIndex: 'accountId'
  },
  {
    title: '登记记录ID',
    align: "center",
    dataIndex: 'customerRegId'
  },
  {
    title: '总检记录ID',
    align: "center",
    dataIndex: 'summaryId'
  },
];

// 高级查询数据
export const superQuerySchema = {
  accountId: {title: '账号ID',order: 0,view: 'text', type: 'string',},
  customerRegId: {title: '登记记录ID',order: 1,view: 'text', type: 'string',},
  summaryId: {title: '总检记录ID',order: 2,view: 'text', type: 'string',},
};
