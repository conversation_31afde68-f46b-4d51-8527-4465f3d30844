package org.jeecg.modules.summary.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.TaskService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.bpmn.service.CamundaService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.summary.dto.ItemResultChangeApplyDto;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.ItemResultChangeApply;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.mapper.ItemResultChangeApplyMapper;
import org.jeecg.modules.summary.service.IItemResultChangeApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 体检结果修改申请
 * @Author: jeecg-boot
 * @Date: 2024-09-24
 * @Version: V1.0
 */
@Service
public class ItemResultChangeApplyServiceImpl extends ServiceImpl<ItemResultChangeApplyMapper, ItemResultChangeApply> implements IItemResultChangeApplyService {
    @Autowired
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private CamundaService camundaService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private ItemResultChangeApplyMapper itemResultChangeApplyMapper;
    @Autowired
    private TaskService taskService;


    @Override
    public List<ItemResultChangeApply> getLatestChangeApplyList(String customerRegId, String departmentId) {
        QueryWrapper<ItemResultChangeApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_reg_id", customerRegId);
        queryWrapper.eq("apply_department_id", departmentId);
        queryWrapper.ne("status", ExConstants.RECORD_CHANGE_APPLY_STATUS_COMPLISHED);
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveItemResultChangeApply(ItemResultChangeApply itemResultChangeApply) throws Exception {
        QueryWrapper<ItemResultChangeApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_reg_id", itemResultChangeApply.getCustomerRegId());
        queryWrapper.eq("apply_department_id", itemResultChangeApply.getApplyDepartmentId());
        queryWrapper.ne("status", ExConstants.RECORD_CHANGE_APPLY_STATUS_COMPLISHED);
        long existCount = count(queryWrapper);
        if (existCount > 0) {
            throw new Exception("有未完成的申请！");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        itemResultChangeApply.setCreator(sysUser.getRealname());
        itemResultChangeApply.setStatus(ExConstants.RECORD_CHANGE_APPLY_STATUS_APPLIED);

        CustomerRegSummary customerRegSummary = customerRegSummaryMapper.getByRegId(itemResultChangeApply.getCustomerRegId());
        if (customerRegSummary == null) {
            throw new Exception("未找到对应的总检记录！");
        }
        itemResultChangeApply.setSummaryId(customerRegSummary.getId());
        save(itemResultChangeApply);

        String changeDataProcessKey = sysSettingService.getValueByCode("process_key_change_data");
        Map<String, Object> processVariables = new HashMap<>();
        processVariables.put("itemResultChangeApplyId", itemResultChangeApply.getId());
        processVariables.put("customerRegId", itemResultChangeApply.getCustomerRegId());
        processVariables.put("customerName", itemResultChangeApply.getCustomerName());
        processVariables.put("summaryId", itemResultChangeApply.getSummaryId());
        processVariables.put("applyBy", itemResultChangeApply.getCreateBy());
        processVariables.put("applyer", itemResultChangeApply.getCreator());
        processVariables.put("applyDepartmentId", itemResultChangeApply.getApplyDepartmentId());
        processVariables.put("reportPrintTimes", customerRegSummary.getReportPrintTimes());
        processVariables.put("reportPrintStatus", customerRegSummary.getReportPrintStatus());

        processVariables.put("summaryAuditBy", customerRegSummary.getAuditeBy());
        processVariables.put("summaryChiefDoctor", customerRegSummary.getChiefDoctorUsername());
        processVariables.put("summaryInitailDoctor", customerRegSummary.getInitailDoctorUsername());
        processVariables.put("summaryDoctor", StringUtils.isNotBlank(customerRegSummary.getChiefDoctorUsername()) ? customerRegSummary.getChiefDoctorUsername() : customerRegSummary.getInitailDoctorUsername());

        processVariables.put("summaryStatus", customerRegSummary.getStatus());
        StringBuilder taskDesc = new StringBuilder();
        taskDesc.append("状态：").append(itemResultChangeApply.getStatus()).append("\n");
        taskDesc.append("体检人员：").append(itemResultChangeApply.getCustomerName()).append(" ");
        taskDesc.append("体检号：").append(itemResultChangeApply.getExamNo()).append("\n");
        taskDesc.append("申请人：").append(itemResultChangeApply.getCreator()).append(" ");
        taskDesc.append("申请科室：").append(itemResultChangeApply.getApplyDepartment()).append("\n");
        taskDesc.append("申请时间：").append(DateUtil.format(itemResultChangeApply.getCreateTime(), "YY-MM-dd HH:mm:ss")).append("\n");
        taskDesc.append("申请原因：").append(itemResultChangeApply.getApplyReason());

        processVariables.put("taskDesc", taskDesc.toString());


        camundaService.startProcessInstance(changeDataProcessKey, itemResultChangeApply.getId(), processVariables);
    }

    @Override
    public ItemResultChangeApplyDto getItemResultChangeApplyDto(String id) {
        ItemResultChangeApply itemResultChangeApply = getById(id);
        if (itemResultChangeApply != null) {
            ItemResultChangeApplyDto itemResultChangeApplyDto = new ItemResultChangeApplyDto();
            itemResultChangeApplyDto.setChangeApply(itemResultChangeApply);
            CustomerRegSummary customerRegSummary = customerRegSummaryMapper.getByRegId(itemResultChangeApply.getCustomerRegId());
            itemResultChangeApplyDto.setCustomerRegSummary(customerRegSummary);
            CustomerReg customerReg = customerRegMapper.selectById(itemResultChangeApply.getCustomerRegId());
            itemResultChangeApplyDto.setCustomerReg(customerReg);
            return itemResultChangeApplyDto;
        }
        return null;
    }

    @Override
    public void handleItemResultChangeApply(String taskId, String agreeFlag, String remark) throws Exception {
        if (StringUtils.isBlank(taskId)) {
            throw new RuntimeException("taskId不能为空");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, Object> variables = taskService.getVariables(taskId);
        String businessType = (String) variables.get("businessType");
        if (StringUtils.equals(businessType, ExConstants.PROCESS_BUSINESS_TYPE_体检数据修改)) {
            //如果是体检数据修改流程
            ItemResultChangeApply changeApply = itemResultChangeApplyMapper.selectById((String) variables.get("businessKey"));
            if (StringUtils.equals(agreeFlag, "1")) {
                //同意
                variables.put("cheifDoctorAgree", "同意");
                variables.put("msgReceiver", changeApply.getCreateBy());
                variables.put("msgContent", "您的体检数据修改申请已通过，总检医生已同意修改！");

                variables.put("confirmer", loginUser.getRealname());
                variables.put("confirmBy", loginUser.getUsername());
                variables.put("applyStatus", ExConstants.RECORD_CHANGE_APPLY_STATUS_AGREED);
            } else {
                //拒绝
                variables.put("confirmer", loginUser.getRealname());
                variables.put("confirmBy", loginUser.getUsername());
                variables.put("applyStatus", ExConstants.RECORD_CHANGE_APPLY_STATUS_REFUSED);

                variables.put("cheifDoctorAgree", "不同意");
                variables.put("msgReceiver", changeApply.getCreateBy());
                variables.put("msgContent", "您的体检数据修改申请未通过，总检医生拒绝修改，具体原因：" + remark);
            }
        }
        taskService.complete(taskId, variables);
    }
}
