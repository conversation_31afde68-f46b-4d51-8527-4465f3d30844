package org.jeecg.modules.summary.service.bpmn;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.ItemResultChangeApply;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.mapper.ItemResultChangeApplyMapper;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component(value = "itemResultChangeBean")
@Slf4j
public class ItemResultChangeBean {

    @Autowired
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Autowired
    private ItemResultChangeApplyMapper itemResultChangeApplyMapper;
    @Autowired
    private ISysBaseAPI sysBaseApi;
    @Autowired
    private ISysUserService sysUserService;

    public void getAuditBy(DelegateTask delegateTask) {
        // 获取流程变量
        String summaryId = (String) delegateTask.getVariable("summaryId");
        if (StringUtils.isNotBlank(summaryId)) {
            // 根据summaryId查询审核人
            CustomerRegSummary summary = customerRegSummaryMapper.selectById(summaryId);
            String auditBy = summary.getAuditeBy();
            delegateTask.setVariable("auditBy", auditBy);
        } else {
            throw new RuntimeException("summaryId不能为空");
        }
    }

    public void getSummaryBy(DelegateTask delegateTask) {
        // 获取流程变量
        String summaryId = (String) delegateTask.getVariable("summaryId");
        if (StringUtils.isNotBlank(summaryId)) {
            // 根据summaryId查询审核人
            CustomerRegSummary summary = customerRegSummaryMapper.selectById(summaryId);
            String summaryBy = summary.getCreateBy();
            if (StringUtils.isNotBlank(summaryBy)) {
                delegateTask.setVariable("summaryBy", summaryBy);
            } else {
                delegateTask.setVariable("summaryBy", summary.getPreAuditBy());
            }

        } else {
            throw new RuntimeException("summaryId不能为空");
        }
    }

    public void lockSummary(DelegateExecution execution) {
        // 获取流程变量
        String businessKey = execution.getProcessBusinessKey();
        if (StringUtils.isNotBlank(businessKey)) {
            // 根据businessKey查询审核人
            ItemResultChangeApply apply = itemResultChangeApplyMapper.selectById(businessKey);
            String summaryId = apply.getSummaryId();
            UpdateWrapper<CustomerRegSummary> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("lock_flag", "1");
            updateWrapper.set("lock_time", new Date());
            updateWrapper.set("lock_reason", apply.getApplyReason());
            updateWrapper.eq("id", summaryId);
            customerRegSummaryMapper.update(null, updateWrapper);

          /*  UpdateWrapper<ItemResultChangeApply> applyUpdateWrapper = new UpdateWrapper<>();
            applyUpdateWrapper.set("status", ExConstants.RECORD_CHANGE_APPLY_STATUS_AGREED);
            applyUpdateWrapper.set("lock_time", new Date());*/

        } else {
            throw new RuntimeException("businessKey不能为空");
        }
    }

    public void sendMsg(DelegateExecution execution) {
        // 获取流程变量
        String businessKey = execution.getProcessBusinessKey();
        if (StringUtils.isNotBlank(businessKey)) {
            //发送系统消息
            try {
                String confirmBy = (String) execution.getVariable("confirmBy");
                String confirmer = (String) execution.getVariable("confirmer");
                String applyStatus = (String) execution.getVariable("applyStatus");

                UpdateWrapper<ItemResultChangeApply> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("status", applyStatus);
                updateWrapper.set("confirmer", confirmer);
                updateWrapper.set("confirm_by", confirmBy);
                updateWrapper.set("confirm_time", new Date());
                updateWrapper.eq("id", businessKey);
                itemResultChangeApplyMapper.update(null, updateWrapper);

                String msgReceiver = (String) execution.getVariable("msgReceiver");
                String msgContent = (String) execution.getVariable("msgContent");
              /*  SysUser user = sysUserService.getUserByName(msgReceiver);
                if (user != null) {
                    MessageDTO md = new MessageDTO();
                    md.setToAll(false);
                    md.setTitle("报告数据修改申请结果");
                    md.setContent(msgContent);
                    md.setToUser(user.getId());
                    md.setFromUser("system");
                    sysBaseApi.sendSysAnnouncement(md);
                }*/

                if(StringUtils.isNotBlank(msgReceiver)){
                    MessageDTO md = new MessageDTO();
                    md.setToAll(false);
                    md.setTitle("报告数据修改申请结果");
                    md.setContent(msgContent);
                    md.setToUser(msgReceiver);
                    sysBaseApi.sendSysAnnouncement(md);
                }
            } catch (Exception e) {
                log.error("发送报告数据修改申请结果消息出错", e.getMessage());
            }
        } else {
            throw new RuntimeException("businessKey不能为空");
        }
    }

}
