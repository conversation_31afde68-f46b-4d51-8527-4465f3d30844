package org.jeecg.modules.summary.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 账号关联的报告
 * @Author: jeecg-boot
 * @Date: 2025-03-11
 * @Version: V1.0
 */
@Data
@TableName("account_report")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "account_report对象", description = "账号关联的报告")
public class AccountReport implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 账号ID
     */
    @Excel(name = "账号ID", width = 15)
    @ApiModelProperty(value = "账号ID")
    private java.lang.String accountId;
    /**
     * 登记记录ID
     */
    @Excel(name = "登记记录ID", width = 15)
    @ApiModelProperty(value = "登记记录ID")
    private java.lang.String customerRegId;
    /**
     * 总检记录ID
     */
    @Excel(name = "总检记录ID", width = 15)
    @ApiModelProperty(value = "总检记录ID")
    private java.lang.String summaryId;

    @TableField(exist = false)
    private Integer abnormalCount;
    @TableField(exist = false)
    private String name;
    @TableField(exist = false)
    private String idCard;
    @TableField(exist = false)
    private String gender;
    @TableField(exist = false)
    private String age;
    @TableField(exist = false)
    private String examNo;
    @TableField(exist = false)
    private String reportTime;
}
