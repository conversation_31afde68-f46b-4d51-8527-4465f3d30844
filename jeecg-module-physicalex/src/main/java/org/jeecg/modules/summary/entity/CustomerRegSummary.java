package org.jeecg.modules.summary.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date:   2024-05-20
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_summary")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer_reg_summary对象", description="总检")
public class CustomerRegSummary implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**体检登记ID*/
	@Excel(name = "体检登记ID", width = 15)
    @ApiModelProperty(value = "体检登记ID")
    private java.lang.String customerRegId;
    /**体检号*/
    @Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;
	/**总检结论*/
	@Excel(name = "总检结论", width = 15)
    @ApiModelProperty(value = "总检结论")
    private java.lang.String characterSummary;
    @ApiModelProperty(value = "总检结论JSON")
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class)
    private List<AdviceBean> summaryJson;
	/**总检诊断*/
	@Excel(name = "总检诊断", width = 15)
    @ApiModelProperty(value = "总检诊断")
    private java.lang.String diagnosisSummary;
	/**建议*/
	@Excel(name = "建议", width = 15)
    @ApiModelProperty(value = "建议")
    private java.lang.String advice;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**总检医生ID*/
    @ApiModelProperty(value = "总检医生ID")
    private java.lang.String createBy;
	/**总检医生*/
	@Excel(name = "总检医生", width = 15)
    @ApiModelProperty(value = "总检医生")
    private java.lang.String creatorName;
    /**总检医生签名*/
    @Excel(name = "总检医生签名", width = 15)
    @ApiModelProperty(value = "总检医生签名")
    @TableField(exist = false)
    private java.lang.String creatorSignPic;
    /**更新人账号*/
    @Excel(name = "更新人账号", width = 15)
    @ApiModelProperty(value = "更新人账号")
    private java.lang.String updateBy;
    /**更新人*/
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private java.lang.String updator;
    /**更新时间*/
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**总检时间*/
	@Excel(name = "总检时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "总检时间")
    private java.util.Date confirmTime;
	/**审核医生ID*/
	@Excel(name = "审核医生ID", width = 15)
    @ApiModelProperty(value = "审核医生ID")
    private java.lang.String auditeBy;
	/**审核医生*/
	@Excel(name = "审核医生", width = 15)
    @ApiModelProperty(value = "审核医生")
    private java.lang.String auditorName;
    /**审核医生*/
    @Excel(name = "审核医生签名", width = 15)
    @ApiModelProperty(value = "审核医生签名")
    @TableField(exist = false)
    private java.lang.String auditorSignPic;
    /**预审核医生*/
    @Excel(name = "预审核医生", width = 15)
    @ApiModelProperty(value = "预审核医生")
    private java.lang.String preAuditor;
    /**预审核医生账号*/
    @Excel(name = "预审核医生账号", width = 15)
    @ApiModelProperty(value = "预审核医生账号")
    private java.lang.String preAuditBy;
    /**初检医生签名图片*/
    @Excel(name = "初检医生签名图片", width = 15)
    @ApiModelProperty(value = "初检医生签名图片")
    @TableField(exist = false)
    private java.lang.String preAuditorSignPic;
    /**预审核时间*/
    @Excel(name = "预审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预审核时间")
    private java.util.Date preAuditTime;
    /**报告打印时间*/
    @Excel(name = "报告打印时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报告打印时间")
    private java.util.Date reportPrintTime;
    /**报告打印次数*/
    @Excel(name = "报告打印次数", width = 15)
    @ApiModelProperty(value = "报告打印次数")
    private Integer reportPrintTimes;
	/**his系统医生ID*/
	@Excel(name = "his系统医生ID", width = 15)
    @ApiModelProperty(value = "his系统医生ID")
    private java.lang.String hisDoctorId;
	/**总检状态*/
	@Excel(name = "总检状态", width = 15)
    @ApiModelProperty(value = "总检状态")
    private java.lang.String status;
    /**健康证结果*/
    @Excel(name = "健康证结果", width = 15)
    @ApiModelProperty(value = "健康证结果")
    private String healthCardResult;
    /**主检时间*/
    @Excel(name = "主检时间", width = 15)
    @ApiModelProperty(value = "主检时间")
    private Date chiefTime;
    /**审核时间*/
    @Excel(name = "审核时间", width = 15)
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;
    /**初检时间*/
    @Excel(name = "初检时间", width = 15)
    @ApiModelProperty(value = "初检时间")
    private Date initailTime;
    /**报告状态*/
    @Excel(name = "报告状态", width = 15)
    @ApiModelProperty(value = "报告状态")
    private String reportPrintStatus;
    /**主检医生*/
    @Excel(name = "主检医生", width = 15)
    @ApiModelProperty(value = "主检医生")
    private String chiefDoctor;
    /**主检医生账号*/
    @Excel(name = "主检医生账号", width = 15)
    @ApiModelProperty(value = "主检医生账号")
    private String chiefDoctorUsername;
    /**初检医生*/
    @Excel(name = "初检医生", width = 15)
    @ApiModelProperty(value = "初检医生")
    private String initailDoctor;
    /**初检医生账号*/
    @Excel(name = "初检医生账号", width = 15)
    @ApiModelProperty(value = "初检医生账号")
    private String initailDoctorUsername;
    /**锁定状态*/
    @Excel(name = "锁定状态", width = 15)
    @ApiModelProperty(value = "锁定状态")
    private String lockFlag;
    /**锁定原因*/
    @Excel(name = "锁定原因", width = 15)
    @ApiModelProperty(value = "锁定原因")
    private String lockReason;
    /**锁定时间*/
    @Excel(name = "锁定时间", width = 15)
    @ApiModelProperty(value = "锁定时间")
    private Date lockTime;
    /**解锁时间*/
    @Excel(name = "解锁时间", width = 15)
    @ApiModelProperty(value = "解锁时间")
    private Date unlockTime;

    @TableField(exist = false)
    private List<CustomerRegDepartSummary> departSummaryList;
    @TableField(exist = false)
    private List<String> abnormalSummaryList;
    @TableField(exist = false)
    private String departSummaryText;
    @TableField(exist = false)
    private String auditDate;
    @TableField(exist = false)
    private String type;


    @TableField(exist = false)
    private String adviceSummary;
    @TableField(exist = false)
    private Integer abnormalCount;
    @TableField(exist = false)
    private String formatChiefTime;
    @TableField(exist = false)
    private String adviceText;
    @TableField(exist = false)
    private JSONArray abnormalSummaryData;
    @TableField(exist = false)
    private JSONArray diagnosisList;
    @TableField(exist = false)
    private JSONArray adviceStructuredData;

}
