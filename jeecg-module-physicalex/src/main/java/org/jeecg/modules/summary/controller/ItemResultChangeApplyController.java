package org.jeecg.modules.summary.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.bpmn.service.CamundaService;
import org.jeecg.modules.summary.dto.ItemResultChangeApplyDto;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.ItemResultChangeApply;
import org.jeecg.modules.summary.entity.SummaryAuditRecord;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.jeecg.modules.summary.service.IItemResultChangeApplyService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 体检结果修改申请
 * @Author: jeecg-boot
 * @Date: 2024-09-24
 * @Version: V1.0
 */
@Api(tags = "体检结果修改申请")
@RestController
@RequestMapping("/summary/itemResultChangeApply")
@Slf4j
public class ItemResultChangeApplyController extends JeecgController<ItemResultChangeApply, IItemResultChangeApplyService> {
    @Autowired
    private IItemResultChangeApplyService itemResultChangeApplyService;
    @Autowired
    private ICustomerRegSummaryService customerRegSummaryService;

    @ApiOperation(value = "体检结果修改申请-通过id查询", notes = "体检结果修改申请-通过id查询")
    @GetMapping(value = "/getDetail")
    public Result<?> getDetail(@RequestParam(name = "id", required = true) String id) {
        ItemResultChangeApplyDto itemResultChangeApply = itemResultChangeApplyService.getItemResultChangeApplyDto(id);
        if (itemResultChangeApply == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(itemResultChangeApply);
    }

    //getLatestChangeApplyList
    @ApiOperation(value = "获取最新的修改申请列表", notes = "获取最新的修改申请列表")
    @GetMapping(value = "/getLatestChangeApplyList")
    public Result<List<ItemResultChangeApply>> getLatestChangeApplyList(@RequestParam(name = "customerRegId", required = true) String customerRegId, @RequestParam(name = "departmentId", required = true) String departmentId) {
        List<ItemResultChangeApply> list = itemResultChangeApplyService.getLatestChangeApplyList(customerRegId, departmentId);
        return Result.OK(list);
    }

    /**
     * 分页列表查询
     *
     * @param itemResultChangeApply
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检结果修改申请-分页列表查询")
    @ApiOperation(value = "体检结果修改申请-分页列表查询", notes = "体检结果修改申请-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ItemResultChangeApply>> queryPageList(ItemResultChangeApply itemResultChangeApply, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ItemResultChangeApply> queryWrapper = QueryGenerator.initQueryWrapper(itemResultChangeApply, req.getParameterMap());
        Page<ItemResultChangeApply> page = new Page<ItemResultChangeApply>(pageNo, pageSize);
        IPage<ItemResultChangeApply> pageList = itemResultChangeApplyService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "总检审核记录-主检医生审核", notes = "总检审核记录-撤销审核")
    @GetMapping(value = "/doctorAudit")
    //@RequiresPermissions("summary:summary_audit_record:revoke")
    public Result<String> revokeAuditByProcess(String taskId, String agreeFlag, String remark) throws Exception {

        itemResultChangeApplyService.handleItemResultChangeApply(taskId, agreeFlag, remark);
        return Result.OK("操作成功！");
    }

    /**
     * 添加
     *
     * @param itemResultChangeApply
     * @return
     */
    @AutoLog(value = "体检结果修改申请-添加")
    @ApiOperation(value = "体检结果修改申请-添加", notes = "体检结果修改申请-添加")
    //@RequiresPermissions("summary:item_result_change_apply:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ItemResultChangeApply itemResultChangeApply) {

        try {
            itemResultChangeApplyService.saveItemResultChangeApply(itemResultChangeApply);
            return Result.OK("添加成功！");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param itemResultChangeApply
     * @return
     */
    @AutoLog(value = "体检结果修改申请-编辑")
    @ApiOperation(value = "体检结果修改申请-编辑", notes = "体检结果修改申请-编辑")
    //@RequiresPermissions("summary:item_result_change_apply:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ItemResultChangeApply itemResultChangeApply) {
        itemResultChangeApplyService.updateById(itemResultChangeApply);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检结果修改申请-通过id删除")
    @ApiOperation(value = "体检结果修改申请-通过id删除", notes = "体检结果修改申请-通过id删除")
    //@RequiresPermissions("summary:item_result_change_apply:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        itemResultChangeApplyService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检结果修改申请-批量删除")
    @ApiOperation(value = "体检结果修改申请-批量删除", notes = "体检结果修改申请-批量删除")
    //@RequiresPermissions("summary:item_result_change_apply:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.itemResultChangeApplyService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检结果修改申请-通过id查询")
    @ApiOperation(value = "体检结果修改申请-通过id查询", notes = "体检结果修改申请-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ItemResultChangeApply> queryById(@RequestParam(name = "id", required = true) String id) {
        ItemResultChangeApply itemResultChangeApply = itemResultChangeApplyService.getById(id);
        if (itemResultChangeApply == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(itemResultChangeApply);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param itemResultChangeApply
     */
    @RequiresPermissions("summary:item_result_change_apply:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItemResultChangeApply itemResultChangeApply) {
        return super.exportXls(request, itemResultChangeApply, ItemResultChangeApply.class, "体检结果修改申请");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("summary:item_result_change_apply:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ItemResultChangeApply.class);
    }

}
