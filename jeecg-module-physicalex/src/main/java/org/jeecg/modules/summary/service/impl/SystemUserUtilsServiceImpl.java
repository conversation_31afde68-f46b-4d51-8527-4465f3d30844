package org.jeecg.modules.summary.service.impl;

import org.jeecg.modules.summary.service.SystemUserUtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
@CacheConfig(cacheNames = "systemUserUtils")
@Service
public class SystemUserUtilsServiceImpl implements SystemUserUtilService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Cacheable(key = "#username", unless = "#result == null")
    @Override
    public String getSignPicByUsername(String username) {
        List<String> signPics = jdbcTemplate.queryForList("select sign_pic from sys_user where username = ?", String.class, username);

        return signPics.isEmpty() ? null : signPics.get(0);
    }

    @Cacheable(key = "#username", unless = "#result == null")
    @Override
    public String getRealnameByUsername(String username) {
        List<String> realnames = jdbcTemplate.queryForList("select realname from sys_user where username = ?", String.class, username);
        return realnames.isEmpty() ? null : realnames.get(0);
    }
    @Cacheable(key = "#userId", unless = "#result == null")
    @Override
    public String getRealnameByUserId(String userId) {
        List<String> realnames = jdbcTemplate.queryForList("select realname from sys_user where id = ?", String.class, userId);
        return realnames.isEmpty() ? null : realnames.get(0);
    }
    @Cacheable(key = "#userId", unless = "#result == null")
    @Override
    public String getUsernameByUserId(String userId) {
        List<String> usernames = jdbcTemplate.queryForList("select username from sys_user where id = ?", String.class, userId);
        return usernames.isEmpty() ? null : usernames.get(0);
    }
}
