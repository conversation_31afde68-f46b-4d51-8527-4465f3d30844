package org.jeecg.modules.summary.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.*;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Lucene Index Manager
 * Unified management of index creation, update, deletion and query operations
 * Solve resource leakage, concurrency safety, exception handling and other issues
 */
@Component
@Slf4j
public class LuceneIndexManager {

    @Value("${biz.lucene.path}")
    private String lucenePath;

    @Autowired
    private Analyzer hanlpAnalyzer;

    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private volatile boolean indexHealthy = true;

    @PostConstruct
    public void init() {
        try {
            ensureIndexDirectoryExists();
            checkIndexHealth();
            log.info("Lucene Index Manager initialized successfully, index path: {}", lucenePath);
        } catch (Exception e) {
            log.error("Lucene Index Manager initialization failed", e);
            indexHealthy = false;
        }
    }

    /**
     * Ensure index directory exists
     */
    private void ensureIndexDirectoryExists() throws IOException {
        Path indexPath = Paths.get(lucenePath);
        if (!Files.exists(indexPath)) {
            Files.createDirectories(indexPath);
            log.info("Created index directory: {}", lucenePath);
        }
    }

    /**
     * Check index health status
     */
    public boolean checkIndexHealth() {
        lock.readLock().lock();
        try (Directory directory = FSDirectory.open(Paths.get(lucenePath))) {
            if (DirectoryReader.indexExists(directory)) {
                try (IndexReader reader = DirectoryReader.open(directory)) {
                    // Simple check if index is readable
                    reader.numDocs();
                    indexHealthy = true;
                    return true;
                }
            } else {
                // Index does not exist, but this is not an error
                indexHealthy = true;
                return true;
            }
        } catch (Exception e) {
            log.error("Index health check failed", e);
            indexHealthy = false;
            return false;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Get optimized IndexWriter configuration
     */
    private IndexWriterConfig getOptimizedIndexWriterConfig(IndexWriterConfig.OpenMode openMode) {
        IndexWriterConfig config = new IndexWriterConfig(hanlpAnalyzer);
        config.setOpenMode(openMode);

        // Optimize memory usage
        config.setRAMBufferSizeMB(256.0); // Set RAM buffer size to 256MB
        config.setMaxBufferedDocs(1000);  // Set max buffered documents

        // Set merge policy
        config.setUseCompoundFile(true);  // Use compound file format to reduce file handles

        return config;
    }

    /**
     * Add documents to index
     */
    public boolean addDocuments(List<Document> documents) {
        if (!indexHealthy && !checkIndexHealth()) {
            log.error("Index is unhealthy, cannot add documents");
            return false;
        }

        lock.writeLock().lock();
        try (Directory directory = FSDirectory.open(Paths.get(lucenePath));
             IndexWriter writer = new IndexWriter(directory,
                 getOptimizedIndexWriterConfig(IndexWriterConfig.OpenMode.CREATE_OR_APPEND))) {

            writer.addDocuments(documents);
            writer.commit();
            log.debug("Successfully added {} documents to index", documents.size());
            return true;

        } catch (Exception e) {
            log.error("Failed to add documents to index", e);
            indexHealthy = false;
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Update document
     */
    public boolean updateDocument(Term term, Document document) {
        if (!indexHealthy && !checkIndexHealth()) {
            log.error("Index is unhealthy, cannot update document");
            return false;
        }

        lock.writeLock().lock();
        try (Directory directory = FSDirectory.open(Paths.get(lucenePath));
             IndexWriter writer = new IndexWriter(directory,
                 getOptimizedIndexWriterConfig(IndexWriterConfig.OpenMode.CREATE_OR_APPEND))) {

            writer.updateDocument(term, document);
            writer.commit();
            log.debug("Successfully updated document: {}", term);
            return true;

        } catch (Exception e) {
            log.error("Failed to update document", e);
            indexHealthy = false;
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Delete documents
     */
    public boolean deleteDocuments(Term term) {
        if (!indexHealthy && !checkIndexHealth()) {
            log.error("Index is unhealthy, cannot delete document");
            return false;
        }

        lock.writeLock().lock();
        try (Directory directory = FSDirectory.open(Paths.get(lucenePath));
             IndexWriter writer = new IndexWriter(directory,
                 getOptimizedIndexWriterConfig(IndexWriterConfig.OpenMode.CREATE_OR_APPEND))) {

            writer.deleteDocuments(term);
            writer.commit();
            log.debug("Successfully deleted document: {}", term);
            return true;

        } catch (Exception e) {
            log.error("Failed to delete document", e);
            indexHealthy = false;
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Rebuild all indexes
     */
    public boolean rebuildIndex(List<Document> documents) {
        lock.writeLock().lock();
        try (Directory directory = FSDirectory.open(Paths.get(lucenePath));
             IndexWriter writer = new IndexWriter(directory,
                 getOptimizedIndexWriterConfig(IndexWriterConfig.OpenMode.CREATE))) {

            writer.deleteAll();
            if (documents != null && !documents.isEmpty()) {
                writer.addDocuments(documents);
            }
            writer.commit();
            indexHealthy = true;
            log.info("Successfully rebuilt index, document count: {}", documents != null ? documents.size() : 0);
            return true;

        } catch (Exception e) {
            log.error("Failed to rebuild index", e);
            indexHealthy = false;
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Search documents
     */
    public TopDocs search(Query query, int maxResults) {
        if (!indexHealthy && !checkIndexHealth()) {
            log.error("Index is unhealthy, cannot execute search");
            return null;
        }

        lock.readLock().lock();
        try (Directory directory = FSDirectory.open(Paths.get(lucenePath))) {
            if (!DirectoryReader.indexExists(directory)) {
                log.warn("Index does not exist, returning empty result");
                return null;
            }

            try (IndexReader reader = DirectoryReader.open(directory)) {
                IndexSearcher searcher = new IndexSearcher(reader);
                return searcher.search(query, maxResults);
            }

        } catch (Exception e) {
            log.error("Search failed", e);
            indexHealthy = false;
            return null;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Get document
     */
    public Document getDocument(int docId) {
        if (!indexHealthy && !checkIndexHealth()) {
            log.error("Index is unhealthy, cannot get document");
            return null;
        }

        lock.readLock().lock();
        try (Directory directory = FSDirectory.open(Paths.get(lucenePath));
             IndexReader reader = DirectoryReader.open(directory)) {

            IndexSearcher searcher = new IndexSearcher(reader);
            return searcher.doc(docId);

        } catch (Exception e) {
            log.error("Failed to get document", e);
            indexHealthy = false;
            return null;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Check if document exists
     */
    public boolean documentExists(Query query) {
        TopDocs topDocs = search(query, 1);
        return topDocs != null && topDocs.totalHits > 0;
    }

    /**
     * Get index health status
     */
    public boolean isIndexHealthy() {
        return indexHealthy;
    }

    /**
     * Repair index if possible
     */
    public boolean repairIndex() {
        log.info("Attempting to repair index...");
        lock.writeLock().lock();
        try {
            ensureIndexDirectoryExists();
            return checkIndexHealth();
        } catch (Exception e) {
            log.error("Failed to repair index", e);
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }
}
