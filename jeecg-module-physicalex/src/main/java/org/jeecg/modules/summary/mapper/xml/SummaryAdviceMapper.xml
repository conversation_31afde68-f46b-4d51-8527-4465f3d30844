<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.summary.mapper.SummaryAdviceMapper">

    <select id="listNeedCreateLuceneIndex" resultType="org.jeecg.modules.summary.entity.SummaryAdvice">
        SELECT
        *
        FROM
        summary_advice
        WHERE
        index_time is null  and advice_content!= ''
    </select>
    <select id="listNessaryUpdateLuceneIndex" resultType="org.jeecg.modules.summary.entity.SummaryAdvice">
        SELECT
       *
        FROM
        summary_advice
        WHERE
            index_time is not null and advice_content!= ''
            and update_time > index_time
    </select>
    <select id="listAllUpdateLuceneIndex" resultType="org.jeecg.modules.summary.entity.SummaryAdvice">
        SELECT
        *
        FROM
        summary_advice
        WHERE
            index_time is not null and advice_content is not null and advice_content!= ''
    </select>
    <select id="listAll" resultType="org.jeecg.modules.summary.entity.SummaryAdvice">
        SELECT
        *
        FROM
        summary_advice where keywords is not null and advice_content is not null and advice_content != ''
    </select>

    <select id="getTotalCount" resultType="long">
        SELECT
        COUNT(*)
        FROM
        summary_advice
    </select>

    <select id="listByPage" resultType="org.jeecg.modules.summary.entity.SummaryAdvice">
        SELECT
        *
        FROM
        summary_advice
        ORDER BY id
        LIMIT #{offset}, #{limit}
    </select>

</mapper>