package org.jeecg.modules.summary.mapper;


import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.summary.bo.teamreport.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TeamReportMapper {

    Map<String, BigDecimal> getAgeGroupStat(@Param("companyRegId") String companyRegId);
    List<ValueAndLabel> getDepartAbnormalStat(@Param("companyRegId") String companyRegId,@Param("companyDeptId") String companyDeptId);
    List<ValueAndLabel> getTop10tAbnormalConditionStat(@Param("companyRegId") String companyRegId,@Param("companyDeptId") String companyDeptId);

    List<ValueAndLabel> getB_DepartmentAbnormalStat(@Param("companyRegId") String companyRegId,@Param("companyDeptId") String companyDeptId);
    AbnormalGeneralSummary getGenderGeneralStat(@Param("companyRegId") String companyRegId,@Param("companyDeptId") String companyDeptId);

    AbnormalGeneralSummary getCheckedGeneralStat(@Param("companyRegId") String companyRegId,@Param("companyDeptId") String companyDeptId);

    List<AbnormalCondition> getAbnormalConditionListStat(@Param("companyRegId") String companyRegId,@Param("companyDeptId") String companyDeptId);

    List<AbnormalItemAndName> getAbnormalItemAndNameListStat(@Param("companyRegId") String companyRegId,@Param("companyDeptId") String companyDeptId);

    List<CompanyAbnormalItemResultVO> getAbnormalItemsByCompanyRegId(@Param("companyRegId") String companyRegId, @Param("companyDeptId") String companyDeptId);

    List<CompanyAbnormalItemResultVO> getAllItemResultByCompanyRegId(@Param("companyRegId") String companyRegId, @Param("companyDeptId") String companyDeptId);


}
