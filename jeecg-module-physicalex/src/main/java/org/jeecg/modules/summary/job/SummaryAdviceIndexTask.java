package org.jeecg.modules.summary.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.summary.service.ISummaryAdviceService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class SummaryAdviceIndexTask implements Job {

    @Autowired
    private ISummaryAdviceService summaryAdviceService;

    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                summaryAdviceService.generateAdviceDictionaryFromAI();
                summaryAdviceService.updateLuceneIndexNessary();
            } catch (Exception e) {
                log.error("总检建议创建索引任务失败：", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("总检建议创建索引任务正在运行");
        }
    }
}