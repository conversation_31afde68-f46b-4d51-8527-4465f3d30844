package org.jeecg.modules.summary.bo.teamreport;

import lombok.Data;

import java.util.List;

@Data
public class AbnormalGeneralSummary {
    public int personTotal;
    public int maleTotal;
    public String malePercent;
    public int femaleTotal;
    public String femalePercent;
    public int checkedTotal;
    public String checkedPercent;
    public int maleCheckedTotal;
    public String maleCheckedPercent;
    public int femaleCheckedTotal;
    public String femaleCheckedPercent;
    public String maleToFemaleRatio;
    public int allNormalTotal;
    public String allNormalPercent;
    public int notAllNormalTotal;
    public String notAllNormalPercent;

    public List<ValueAndLabel> checkedPersonTotalByGender;
    public List<ValueAndLabel> checkedPersonPercentageByGender;
    public List<ValueAndLabel> personTotalStatByCheckState;
}
