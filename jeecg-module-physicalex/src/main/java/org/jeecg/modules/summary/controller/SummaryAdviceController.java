package org.jeecg.modules.summary.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import groovy.lang.GroovyShell;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.summary.entity.SummaryAdvice;
import org.jeecg.modules.summary.service.ISummaryAdviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 总检建议
 * @Author: jeecg-boot
 * @Date: 2024-05-19
 * @Version: V1.0
 */
@Api(tags = "总检建议")
@RestController
@RequestMapping("/summary/summaryAdvice")
@Slf4j
public class SummaryAdviceController extends JeecgController<SummaryAdvice, ISummaryAdviceService> {
    @Autowired
    private ISummaryAdviceService summaryAdviceService;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private JdbcTemplate jdbcTemplate;


    /**
     * 分页查询适合体检登记的建议
     *
     * @param req
     * @return
     */
    //@AutoLog(value = "总检建议-分页列表查询")
    @ApiOperation(value = "总检建议-分页查询适合体检登记的建议", notes = "总检建议-分页查询适合体检登记的建议")
    @GetMapping(value = "/listAdviceWithLimit")
    public Result<?> list4CustomerReg(HttpServletRequest req) {
        String gender = req.getParameter("gender");
        Integer age = oConvertUtils.getInt(req.getParameter("age"));
        String marriage = req.getParameter("marriage");
        String departmentId = req.getParameter("departmentId");
        String keyword = req.getParameter("keywords");

        List<SummaryAdvice> adviceList = new ArrayList<>();
        if (StringUtils.isNotBlank(keyword)) {
            String summaryAdviceAdoptLimitScore = sysSettingService.getValueByCode("summaryAdviceAdoptLimitScore");
            if (org.apache.commons.lang.StringUtils.isBlank(summaryAdviceAdoptLimitScore)) {
                summaryAdviceAdoptLimitScore = "20";
            }
            // adviceList = summaryAdviceService.listAdviceFromDB( keyword, gender, age, marriage, departmentId);
            adviceList = summaryAdviceService.listAdviceFromLucene(false, keyword, gender, age, marriage, departmentId);
            String finalSummaryAdviceAdoptLimitScore = summaryAdviceAdoptLimitScore;
            adviceList.removeIf(advice -> advice.getMatchScore() == null || advice.getMatchScore() < Integer.parseInt(finalSummaryAdviceAdoptLimitScore));
        }

        return Result.OK(adviceList);
    }


    /**
     * 分页列表查询
     *
     * @param summaryAdvice
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "总检建议-分页列表查询")
    @ApiOperation(value = "总检建议-分页列表查询", notes = "总检建议-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<SummaryAdvice>> queryPageList(SummaryAdvice summaryAdvice, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<SummaryAdvice> queryWrapper = QueryGenerator.initQueryWrapper(summaryAdvice, req.getParameterMap());
        Page<SummaryAdvice> page = new Page<SummaryAdvice>(pageNo, pageSize);
        IPage<SummaryAdvice> pageList = summaryAdviceService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    //updateLuceneIndex
    @GetMapping(value = "/updateLuceneIndex")
    public Result<?> updateLuceneIndex() {
        summaryAdviceService.updateLuceneIndexAll();
        return Result.OK("更新成功！");
    }

    /**
     * 添加
     *
     * @param summaryAdvice
     * @return
     */
    @AutoLog(value = "总检建议-添加")
    @ApiOperation(value = "总检建议-添加", notes = "总检建议-添加")
    //@RequiresPermissions("summary:summary_advice:add")
    @PostMapping(value = "/addAdvice")
    public Result<String> saveOrUpdate(@RequestBody SummaryAdvice summaryAdvice) {
        //根据关键字查询ICD-10编码
        String icdCode = null;
        try {
            icdCode = jdbcTemplate.queryForObject("select code from icd where name = ?", String.class, summaryAdvice.getKeywords());
        } catch (Exception e) {
        }
        summaryAdvice.setIcdCode(icdCode);
        summaryAdviceService.save(summaryAdvice);
        summaryAdviceService.createLuceneIndex4SummaryAdvice(summaryAdvice);
        return Result.OK("操作成功！");
    }

    /**
     * 添加
     *
     * @param summaryAdvice
     * @return
     */
    @AutoLog(value = "总检建议-添加")
    @ApiOperation(value = "总检建议-添加", notes = "总检建议-添加")
    @RequiresPermissions("summary:summary_advice:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody SummaryAdvice summaryAdvice) {
        //根据关键字查询ICD-10编码
        String icdCode = null;
        try {
            icdCode = jdbcTemplate.queryForObject("select code from icd where name = ?", String.class, summaryAdvice.getKeywords());
        } catch (Exception e) {
        }
        summaryAdvice.setIcdCode(icdCode);
        summaryAdviceService.save(summaryAdvice);
        summaryAdviceService.createLuceneIndex4SummaryAdvice(summaryAdvice);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param summaryAdvice
     * @return
     */
    @AutoLog(value = "总检建议-编辑")
    @ApiOperation(value = "总检建议-编辑", notes = "总检建议-编辑")
    @RequiresPermissions("summary:summary_advice:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody SummaryAdvice summaryAdvice) {
        //根据关键字查询ICD-10编码
        String icdCode = null;
        try {
            icdCode = jdbcTemplate.queryForObject("select code from icd where name = ?", String.class, summaryAdvice.getKeywords());
        } catch (Exception e) {
        }
        summaryAdvice.setIcdCode(icdCode);
        summaryAdviceService.updateById(summaryAdvice);
        summaryAdviceService.addAdvice(summaryAdvice);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "总检建议-通过id删除")
    @ApiOperation(value = "总检建议-通过id删除", notes = "总检建议-通过id删除")
    @RequiresPermissions("summary:summary_advice:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        summaryAdviceService.removeById(id);
        summaryAdviceService.removeLuceneIndex(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "总检建议-批量删除")
    @ApiOperation(value = "总检建议-批量删除", notes = "总检建议-批量删除")
    @RequiresPermissions("summary:summary_advice:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.summaryAdviceService.removeByIds(Arrays.asList(ids.split(",")));
        List<String> idList = Arrays.asList(ids.split(","));
        idList.forEach(id -> {
            summaryAdviceService.removeLuceneIndex(id);
        });
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "总检建议-通过id查询")
    @ApiOperation(value = "总检建议-通过id查询", notes = "总检建议-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<SummaryAdvice> queryById(@RequestParam(name = "id", required = true) String id) {
        SummaryAdvice summaryAdvice = summaryAdviceService.getById(id);
        if (summaryAdvice == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(summaryAdvice);
    }

    /**
     * 从AI模型获取总检建议字典
     *
     * @return
     */
    @AutoLog(value = "总检建议-从AI获取建议字典")
    @ApiOperation(value = "总检建议-从AI获取建议字典", notes = "针对advice_content为空的记录，批量调用AI生成建议内容")
    @PostMapping(value = "/generateAdviceDictionaryFromAI")
    public Result<String> generateAdviceDictionaryFromAI() {
        try {
            String result = summaryAdviceService.generateAdviceDictionaryFromAI();
            return Result.OK(result);
        } catch (Exception e) {
            log.error("从AI获取建议字典失败", e);
            return Result.error("从AI获取建议字典失败: " + e.getMessage());
        }
    }

    /**
     * 导出excel
     *
     * @param request
     * @param summaryAdvice
     */
    @RequiresPermissions("summary:summary_advice:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SummaryAdvice summaryAdvice) {
        return super.exportXls(request, summaryAdvice, SummaryAdvice.class, "总检建议");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("summary:summary_advice:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SummaryAdvice.class);
    }

    @PostMapping(value = "/testGroovyExpression")
    public Result<?> testGroovyExpression(@RequestBody JSONObject info) {
        String expression = info.getString("expression");
        // 检查expression是否为空
        if (expression == null || expression.trim().isEmpty()) {
            return Result.error("Expression cannot be null or empty");
        }

        // 检查expression是否包含可能导致安全问题的字符或字符串
        // 这只是一个基本的示例，你可能需要根据你的具体需求来扩展这个检查
        if (expression.contains("System.exit") || expression.contains("Runtime.getRuntime")) {
            return Result.error("Expression contains potentially unsafe code");
        }
        GroovyShell shell = new GroovyShell();
        try {
            //检查expression是否合法及安全性
            shell.setVariable("departConclusion", 1);

            Object result = shell.evaluate(expression);
            log.info("测试Groovy表达式成功！结果为：" + result);
            if (result == null) {
                return Result.error("表达式求值结果为空，请检查表达式是否正确！");
            }
            if (result instanceof Boolean) {

                return Result.OK("测试Groovy表达式成功！");

            } else {
                return Result.error("表达式求值结果不是bool值,请检查表达式是否正确！");
            }
        } catch (Exception e) {
            log.error("测试Groovy表达式失败！", e);
            return Result.error("测试Groovy表达式失败！");
        }
    }

    //getNextSortNum

    @GetMapping(value = "/getNextSortNum")
    public Result<?> getNextSortNum(String departmentId) {
        Integer nextSortNum = summaryAdviceService.getNextSortNum(departmentId);
        return Result.OK(nextSortNum);
    }

}
