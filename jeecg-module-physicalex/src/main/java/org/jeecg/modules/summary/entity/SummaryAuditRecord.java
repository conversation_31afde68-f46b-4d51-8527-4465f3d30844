package org.jeecg.modules.summary.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 总检审核记录
 * @Author: jeecg-boot
 * @Date: 2024-05-27
 * @Version: V1.0
 */
@Data
@TableName("summary_audit_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "summary_audit_record对象", description = "总检审核记录")
public class SummaryAuditRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15, dicCode = "audit_status")
    @Dict(dicCode = "audit_status")
    @ApiModelProperty(value = "状态")
    private java.lang.String auditResult;
    /**
     * 驳回理由
     */
    @Excel(name = "驳回理由", width = 15)
    @ApiModelProperty(value = "驳回理由")
    private java.lang.String rejectReason;
    /**
     * 审核日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核日期")
    private java.util.Date createTime;
    /**
     * 审核人
     */
    @Excel(name = "审核人", width = 15)
    @ApiModelProperty(value = "审核人")
    private java.lang.String creator;
    /**
     * 总检ID
     */
    @Excel(name = "总检ID", width = 15)
    @ApiModelProperty(value = "总检ID")
    private java.lang.String summaryId;
    /**
     * 登记ID
     */
    @Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
    /**
     * 当时状态
     */
    @Excel(name = "当时状态", width = 15)
    @ApiModelProperty(value = "当时状态")
    private java.lang.String summaryStatus;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 流程ID
     */
    @Excel(name = "流程ID", width = 15)
    @ApiModelProperty(value = "流程ID")
    private String processInsId;
    /**
     * 任务ID
     */
    @Excel(name = "任务ID", width = 15)
    @ApiModelProperty(value = "任务ID")
    private String taskId;
    /**
     * 申请人
     */
    @Excel(name = "申请人", width = 15)
    @ApiModelProperty(value = "申请人")
    private String applyer;
    /**
     * 申请人账号
     */
    @Excel(name = "申请人账号", width = 15)
    @ApiModelProperty(value = "申请人账号")
    private String applyBy;
    /**
     * 业务ID
     */
    @Excel(name = "业务ID", width = 15)
    @ApiModelProperty(value = "业务ID")
    private String businessKey;
    /**
     * 业务类型
     */
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @TableField(exist = false)
    private String agreeFlag;
}
