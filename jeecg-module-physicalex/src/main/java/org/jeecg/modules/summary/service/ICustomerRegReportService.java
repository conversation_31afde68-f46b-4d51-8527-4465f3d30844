package org.jeecg.modules.summary.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.summary.bo.AbnormalSummaryAndAdvice;
import org.jeecg.modules.summary.bo.ReportBean;
import org.jeecg.modules.summary.entity.AccountReport;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.Report4SmallApp;

import java.util.List;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date:   2024-05-20
 * @Version: V1.0
 */
public interface ICustomerRegReportService {

    ReportBean getReportBean(String customerRegId);

    Report4SmallApp getReport4SmallApp(String customerRegId);

    Report4SmallApp getReport4SmallAppByRegId(String customerRegId) throws Exception;

    List<Report4SmallApp> getReportsByOpenId(String openId);

    CustomerReg getEReportByRegId(String customerRegId);

    String getEReportUrlByRegId(String customerRegId) throws Exception;

}
