<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.interfacecheck.mapper.InterfaceTraceLogMapper">


    <select id="pageList" resultType="org.jeecg.modules.interfacecheck.entity.InterfaceTraceLog">
        SELECT
        l.*,
        r.NAME
        FROM
        interface_trace_log l
        JOIN customer_reg r ON l.exam_no = r.exam_no
        <where>
            <if test="name != null and name !=''">
                and r.NAME like concat('%',#{name},'%')
            </if>
            <if test="examNo !=null and examNo !=''">
                and l.exam_no like concat('%',#{examNo},'%')
            </if>
            <if test="description !=null and description !=''">
                and l.description like concat('%',#{description},'%')
            </if>
            <if test="okFlag !=null and okFlag !=''">
                and l.ok_flag =#{okFlag}
            </if>
            <if test="type !=null and type !=''">
                and l.type =#{type}
            </if>
            <if test="startTime !=null and startTime !=''">
                and l.create_time &gt;= #{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                and l.create_time &lt;= #{endTime}
            </if>
        </where>
        order by l.create_time desc
    </select>
</mapper>