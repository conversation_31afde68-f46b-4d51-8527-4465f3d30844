package org.jeecg.modules.interfacecheck.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: interface_trace_log
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
@Data
@TableName("interface_trace_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="interface_trace_log对象", description="interface_trace_log")
public class InterfaceTraceLog implements Serializable {
        private static final long serialVersionUID = 1L;

        /**id*/
        @TableId
        @ApiModelProperty(value = "id")
        private String id;
        /**体检号*/
        @ApiModelProperty(value = "体检号")
        private String examNo;
        /**类型（查询、更新状态、报告回推）*/
        @ApiModelProperty(value = "类型（查询、更新状态、报告回推）")
        private String type;
        /**创建时间*/
        @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "创建时间")
        private Date createTime;
        /**业务流描述*/
        @ApiModelProperty(value = "业务流描述")
        private String description;
        /**响应时长*/
        @ApiModelProperty(value = "响应时长")
        private Long costTime;
        /**请求体*/
        @ApiModelProperty(value = "请求体")
        private String reqBody;
        /**响应体*/
        @ApiModelProperty(value = "响应体")
        private String respBody;
        /**报错信息*/
        @ApiModelProperty(value = "报错信息")
        private String errorMsg;
        /**请求路径*/
        @ApiModelProperty(value = "请求路径")
        private String reqUrl;
        /**接口标志*/
        @ApiModelProperty(value = "接口状态")
        private String okFlag;

        @TableField(exist = false)
        @ApiModelProperty(value = "体检人姓名")
        private String name;

        /**接口标志*/
        @ApiModelProperty(value = "响应耗时")
        private Long respCostTime;
        /**接口标志*/
        @ApiModelProperty(value = "响应状态")
        private String respStatus;

        /**错误处理人*/
        @ApiModelProperty(value = "错误处理人")
        private String handleBy;
        /**错误处理时间*/
        @ApiModelProperty(value = "错误处理时间")
        private Date handleTime;
        /**处理结果*/
        @ApiModelProperty(value = "处理结果")
        private String handleResult;
}
