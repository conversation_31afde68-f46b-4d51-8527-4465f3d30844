package org.jeecg.modules.interfacecheck.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.interfacecheck.entity.InterfaceTraceLog;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.util.List;

/**
 * @Description: interface_trace_log
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
public interface IInterfaceTraceLogService extends IService<InterfaceTraceLog> {

    void pageList(Page<InterfaceTraceLog> page,  String name, String examNo, String okFlag, String description,String type,String startTime,String endTime);

    void handleBatch(List<InterfaceTraceLog> list) throws Exception;

    void cleanUp();
}
