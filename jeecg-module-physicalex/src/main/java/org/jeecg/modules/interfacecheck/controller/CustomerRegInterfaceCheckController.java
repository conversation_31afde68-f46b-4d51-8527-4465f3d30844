package org.jeecg.modules.interfacecheck.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.bo.DepartStat;
import org.jeecg.modules.interfacecheck.entity.InterfaceResult;
import org.jeecg.modules.interfacecheck.service.ICustomerRegInterfaeCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 工作站客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Api(tags = "接口数据排查")
@RestController
@RequestMapping("/interfaceCheck/regCheck")
@Slf4j
public class CustomerRegInterfaceCheckController extends JeecgController<CustomerReg, ICustomerRegService> {
    @Autowired
    private ICustomerRegInterfaeCheckService customerRegInterfaeCheckService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    /**
     *
     * @return
     */
    @RequestMapping(value = "/regGroupsGroupByDepart", method = RequestMethod.GET)
    public Result<?> regGroupsGroupByDepart(String customerRegId) {
        List<DepartStat> departStats = customerRegInterfaeCheckService.regGroupsGroupByDepart(customerRegId);
        return Result.ok(departStats);
    }

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "接口数据排查-分页列表查询", notes = "接口数据排查-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerReg>> queryPageList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String gender = StringUtils.trimToNull(req.getParameter("gender"));
        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String phone = StringUtils.trimToNull(req.getParameter("phone"));
        String regDateStart = StringUtils.trimToNull(req.getParameter("regDateStart"));
        String regDateEnd = StringUtils.trimToNull(req.getParameter("regDateEnd"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String examCardNo = StringUtils.trimToNull(req.getParameter("examCardNo"));
        String interfaceSyncStatus = StringUtils.trimToNull(req.getParameter("interfaceSyncStatus"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String itemGroupId = StringUtils.trimToNull(req.getParameter("itemGroupId"));
        String itemId = StringUtils.trimToNull(req.getParameter("itemId"));
        String ignoreDepartment = StringUtils.trimToNull(req.getParameter("ignoreDepartment"));

        String departmentId = StringUtils.trimToNull(req.getParameter("departmentId"));

        if (!StringUtils.equals(ignoreDepartment,"1")&&StringUtils.isBlank(departmentId)) {
            return Result.OK(page);
        }
        List<String> departmentIds = null;
        if (StringUtils.isNotBlank(departmentId)) {
            departmentIds = Arrays.asList(StringUtils.split(departmentId, ","));
        }


        customerRegInterfaeCheckService.pageCustomerReg(page, departmentIds, name, gender, idCard, phone, regDateStart, regDateEnd, examNo, examCardNo, interfaceSyncStatus, companyRegId, teamId, itemGroupId, itemId);
        return Result.OK(page);
    }


    @ApiOperation(value = "接口数据排查-根据登记id获取大项列表", notes = "接口数据排查-根据登记id获取大项列表")
    @GetMapping(value = "/getGroupsByRegIdAndDepartId")
    public Result<List<CustomerRegItemGroup>> getGroupsByRegId(String regId,String departmentId) {
        List<CustomerRegItemGroup> groups = customerRegInterfaeCheckService.getGroupsByRegIdAndDepartId(regId,departmentId);
        return Result.OK(groups);
    }
    @ApiOperation(value = "接口数据排查-根据大项id查询接口返回详情", notes = "接口数据排查-根据大项id查询接口返回详情")
    @GetMapping(value = "/getInterfaceResultByRegGroupId")
    public Result<InterfaceResult> getInterfaceResultByRegGroupId(String regGroupId) {
        InterfaceResult interfaceResult = customerRegItemGroupService.getInterfaceResultByRegGroupId(regGroupId);
        return Result.OK(interfaceResult);
    }
}
