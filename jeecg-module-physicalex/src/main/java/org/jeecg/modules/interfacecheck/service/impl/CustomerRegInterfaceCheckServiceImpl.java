package org.jeecg.modules.interfacecheck.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.station.bo.DepartStat;
import org.jeecg.modules.station.bo.StatusStat;
import org.jeecg.modules.interfacecheck.mapper.CustomerRegInterfaceCheckMapper;
import org.jeecg.modules.interfacecheck.service.ICustomerRegInterfaeCheckService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Service
public class CustomerRegInterfaceCheckServiceImpl extends ServiceImpl<CustomerRegMapper, CustomerReg> implements ICustomerRegInterfaeCheckService {
    @Autowired
    private CustomerRegInterfaceCheckMapper customerRegInterfaceCheckMapper;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private CommonAPI commonAPI;
    @Autowired
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;

    @Override
    public void pageCustomerReg(Page<CustomerReg> page, List<String> departmentIds, String name, String gender, String idCard, String phone, String regDateStart, String regDateEnd, String examNo, String examCardNo, String interfaceSyncStatus, String companyRegId, String teamId, String itemGroupId, String itemId) {
        customerRegInterfaceCheckMapper.pageCustomerReg(page, departmentIds, name, gender, idCard, phone, regDateStart, regDateEnd, examNo, examCardNo, interfaceSyncStatus, companyRegId, teamId, itemGroupId, itemId);

        if (page.getRecords().isEmpty()) {
            return;
        }
        List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("interfaceSyncStatus");

        page.getRecords().forEach(customerReg -> {
            List<StatusStat> interfaceStatusStatList = customerRegItemGroupMapper.statCustomerRegItemGroupInterfaceStatus(customerReg.getId(), departmentIds);

            interfaceStatusStatList.forEach(statusStat -> {
                String valueToSearch = statusStat.getStatus(); // 替换为你要搜索的值
                Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> valueToSearch.equals(dictModel.getValue())).findFirst();

                if (optionalDictModel.isPresent()) {
                    String color = optionalDictModel.get().getColor();
                    statusStat.setColor(color);
                }
            });

            customerReg.setInterfaceStatusStatList(interfaceStatusStatList);
        });
    }


    @Override
    public List<CustomerRegItemGroup> getGroupsByRegIdAndDepartId(String regId, String departmentId) {
        return customerRegItemGroupMapper.getGroupsByRegIdAndDepartId(regId,departmentId,null);
    }


    @Override
    public List<DepartStat> regGroupsGroupByDepart(String customerRegId) {

        List<DepartStat> list = customerRegItemGroupMapper.regGroupsGroupByDepart(customerRegId);
        for (DepartStat departStat : list) {
            SysDepart depart = sysDepartMapper.selectById(departStat.getDepartmentId());
            departStat.setDepart(depart);
        }
        //按照depart的guideOrder排序
        list.sort(Comparator.comparingInt(o -> o.getDepart().getGuideSort()));

        return list;
    }
}
