package org.jeecg.modules.interfacecheck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.interfacecheck.entity.InterfaceTraceLog;
import org.jeecg.modules.reg.entity.CustomerReg;

/**
 * @Description: interface_trace_log
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
public interface InterfaceTraceLogMapper extends BaseMapper<InterfaceTraceLog> {

    Page<InterfaceTraceLog> pageList(Page<InterfaceTraceLog> page, @Param("name") String name, @Param("examNo") String examNo, @Param("okFlag")String okFlag, @Param("description")String description, @Param("type")String type, @Param("startTime")String startTime, @Param("endTime")String endTime);

}
