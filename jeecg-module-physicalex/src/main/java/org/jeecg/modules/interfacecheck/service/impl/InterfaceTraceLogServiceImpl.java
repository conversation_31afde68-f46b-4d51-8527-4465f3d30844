package org.jeecg.modules.interfacecheck.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.interfacecheck.entity.InterfaceTraceLog;
import org.jeecg.modules.interfacecheck.mapper.InterfaceTraceLogMapper;
import org.jeecg.modules.interfacecheck.service.IInterfaceTraceLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

/**
 * @Description: interface_trace_log
 * @Author: jeecg-boot
 * @Date: 2025-02-10
 * @Version: V1.0
 */
@Service
public class InterfaceTraceLogServiceImpl extends ServiceImpl<InterfaceTraceLogMapper, InterfaceTraceLog> implements IInterfaceTraceLogService {
    @Autowired
    private InterfaceTraceLogMapper interfaceTraceLogMapper;

    @Override
    public void pageList(Page<InterfaceTraceLog> page, String name, String examNo, String okFlag, String description, String type, String startTime, String endTime) {
        interfaceTraceLogMapper.pageList(page, name, examNo, okFlag, description, type, startTime, endTime);
    }

    @Override
    public void handleBatch(List<InterfaceTraceLog> list) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //将有handleResult的数据进行处理
        for (InterfaceTraceLog log : list) {
            if (StringUtils.isBlank(log.getHandleResult())) {
                continue;
            }
            //处理逻辑
            log.setHandleBy(sysUser.getUsername());
            log.setHandleTime(new Date());
            log.setOkFlag("已处理");
            interfaceTraceLogMapper.updateById(log);
        }
    }

    @Override
    public void cleanUp() {
        //将3个月前的okFlag等于正常的数据删除
        LambdaUpdateWrapper<InterfaceTraceLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.le(InterfaceTraceLog::getCreateTime, new Date(System.currentTimeMillis() - 90L * 24 * 60 * 60 * 1000));
        updateWrapper.eq(InterfaceTraceLog::getOkFlag, "正常");
        interfaceTraceLogMapper.delete(updateWrapper);
    }
}
