package org.jeecg.modules.interfacecheck.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.interfacecheck.entity.InterfaceTraceLog;
import org.jeecg.modules.interfacecheck.service.IInterfaceTraceLogService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: interface_trace_log
 * @Author: jeecg-boot
 * @Date: 2025-02-10
 * @Version: V1.0
 */
@Api(tags = "interface_trace_log")
@RestController
@RequestMapping("/interfaceCheck/interfaceTraceLog")
@Slf4j
public class InterfaceTraceLogController extends JeecgController<InterfaceTraceLog, IInterfaceTraceLogService> {
    @Autowired
    private IInterfaceTraceLogService interfaceTraceLogService;

    //批量处理
    @RequestMapping(value = "/handleBatch", method = RequestMethod.POST)
    @RequiresPermissions("station:interface_trace_log:handleBatch")
    public Result<?> handleBatch(@RequestBody JSONArray data) {

        List<InterfaceTraceLog> logList = data.toJavaList(InterfaceTraceLog.class);
        try {
            interfaceTraceLogService.handleBatch(logList);
            return Result.OK("处理成功!");
        } catch (Exception e) {
            return Result.error("处理失败!" + e.getMessage());
        }
    }

    /**
     * 分页列表查询
     *
     * @param interfaceTraceLog
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "interface_trace_log-分页列表查询")
    @ApiOperation(value = "interface_trace_log-分页列表查询", notes = "interface_trace_log-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InterfaceTraceLog>> queryPageList(InterfaceTraceLog interfaceTraceLog, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String description = StringUtils.trimToNull(req.getParameter("description"));
        String type = StringUtils.trimToNull(req.getParameter("type"));
        String okFlag = StringUtils.trimToNull(req.getParameter("okFlag"));
        String createTimeStart = StringUtils.trimToNull(req.getParameter("createTimeStart"));
        String createTimeEnd = StringUtils.trimToNull(req.getParameter("createTimeEnd"));

        //如果没有查询条件，就不用查询了
        if (StringUtils.isBlank(name) && StringUtils.isBlank(examNo) && StringUtils.isBlank(description) && StringUtils.isBlank(type) && StringUtils.isBlank(okFlag) && StringUtils.isBlank(createTimeStart) && StringUtils.isBlank(createTimeEnd)) {
            return Result.OK(new Page<InterfaceTraceLog>(pageNo, pageSize));
        }

        Page<InterfaceTraceLog> page = new Page<InterfaceTraceLog>(pageNo, pageSize);
        interfaceTraceLogService.pageList(page, name, examNo, okFlag, description, type, createTimeStart, createTimeEnd);
        return Result.OK(page);
    }

    /**
     * 添加
     *
     * @param interfaceTraceLog
     * @return
     */
    @AutoLog(value = "interface_trace_log-添加")
    @ApiOperation(value = "interface_trace_log-添加", notes = "interface_trace_log-添加")
    @RequiresPermissions("datasync:interface_trace_log:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InterfaceTraceLog interfaceTraceLog) {
        interfaceTraceLogService.save(interfaceTraceLog);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param interfaceTraceLog
     * @return
     */
    @AutoLog(value = "interface_trace_log-编辑")
    @ApiOperation(value = "interface_trace_log-编辑", notes = "interface_trace_log-编辑")
    @RequiresPermissions("datasync:interface_trace_log:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InterfaceTraceLog interfaceTraceLog) {
        interfaceTraceLogService.updateById(interfaceTraceLog);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "interface_trace_log-通过id删除")
    @ApiOperation(value = "interface_trace_log-通过id删除", notes = "interface_trace_log-通过id删除")
    @RequiresPermissions("datasync:interface_trace_log:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        interfaceTraceLogService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "interface_trace_log-批量删除")
    @ApiOperation(value = "interface_trace_log-批量删除", notes = "interface_trace_log-批量删除")
    @RequiresPermissions("datasync:interface_trace_log:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.interfaceTraceLogService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "interface_trace_log-通过id查询")
    @ApiOperation(value = "interface_trace_log-通过id查询", notes = "interface_trace_log-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InterfaceTraceLog> queryById(@RequestParam(name = "id", required = true) String id) {
        InterfaceTraceLog interfaceTraceLog = interfaceTraceLogService.getById(id);
        if (interfaceTraceLog == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(interfaceTraceLog);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param interfaceTraceLog
     */
    @RequiresPermissions("datasync:interface_trace_log:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InterfaceTraceLog interfaceTraceLog) {
        return super.exportXls(request, interfaceTraceLog, InterfaceTraceLog.class, "interface_trace_log");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("datasync:interface_trace_log:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InterfaceTraceLog.class);
    }

}
