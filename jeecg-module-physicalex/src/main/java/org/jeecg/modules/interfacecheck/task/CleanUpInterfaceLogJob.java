package org.jeecg.modules.interfacecheck.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.interfacecheck.service.IInterfaceTraceLogService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class CleanUpInterfaceLogJob implements Job {

    @Autowired
    private IInterfaceTraceLogService interfaceTraceLogService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                interfaceTraceLogService.cleanUp();
            } catch (Exception e) {
                log.error("清理接口日志异常！", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("清理接口日志正在执行中!");
        }
    }
}