package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: occu_sync_record
 * @Author: jeecg-boot
 * @Date:   2025-05-28
 * @Version: V1.0
 */
@Data
@TableName("occu_sync_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="occu_sync_record对象", description="occu_sync_record")
public class OccuSyncRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**上传时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上传时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**接口名称*/
	@Excel(name = "接口名称", width = 15)
    @ApiModelProperty(value = "接口名称")
    private java.lang.String interfaceName;
	/**接口地址*/
	@Excel(name = "接口地址", width = 15)
    @ApiModelProperty(value = "接口地址")
    private java.lang.String interfaceUrl;
	/**业务主键*/
	@Excel(name = "业务主键", width = 15)
    @ApiModelProperty(value = "业务主键")
    private java.lang.String bizId;
	/**业务数据更新时间*/
	@Excel(name = "业务数据更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "业务数据更新时间")
    private java.util.Date bizLastTime;
	/**结果状态*/
	@Excel(name = "结果状态", width = 15)
    @ApiModelProperty(value = "结果状态")
    private java.lang.String resultStatus;
	/**结果说明*/
	@Excel(name = "结果说明", width = 15)
    @ApiModelProperty(value = "结果说明")
    private java.lang.String resultMsg;
	/**耗时(毫秒)*/
	@Excel(name = "耗时(毫秒)", width = 15)
    @ApiModelProperty(value = "耗时(毫秒)")
    private java.lang.Integer costMs;
	/**错误类型*/
	@Excel(name = "错误类型", width = 15)
    @ApiModelProperty(value = "错误类型")
    private java.lang.String errorType;
	/**错误详情*/
	@Excel(name = "错误详情", width = 15)
    @ApiModelProperty(value = "错误详情")
    private java.lang.String errorDetail;
	/**异常堆栈*/
	@Excel(name = "异常堆栈", width = 15)
    @ApiModelProperty(value = "异常堆栈")
    private java.lang.String stackTrace;
}
