package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyInquiryDiseaseHistory;
import org.jeecg.modules.occu.mapper.ZyInquiryDiseaseHistoryMapper;
import org.jeecg.modules.occu.service.IZyInquiryDiseaseHistoryService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 职业问诊既往病史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquiryDiseaseHistoryServiceImpl extends ServiceImpl<ZyInquiryDiseaseHistoryMapper, ZyInquiryDiseaseHistory> implements IZyInquiryDiseaseHistoryService {
	
	@Autowired
	private ZyInquiryDiseaseHistoryMapper zyInquiryDiseaseHistoryMapper;
	
	@Override
	public List<ZyInquiryDiseaseHistory> selectByMainId(String mainId) {
		return zyInquiryDiseaseHistoryMapper.selectByMainId(mainId);
	}
}
