import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '接口名称',
    align: "center",
    dataIndex: 'interfaceName'
  },
  {
    title: '接口地址',
    align: "center",
    dataIndex: 'interfaceUrl'
  },
  {
    title: '业务主键',
    align: "center",
    dataIndex: 'bizId'
  },
  {
    title: '业务数据更新时间',
    align: "center",
    dataIndex: 'bizLastTime'
  },
  {
    title: '结果状态',
    align: "center",
    dataIndex: 'resultStatus'
  },
  {
    title: '结果说明',
    align: "center",
    dataIndex: 'resultMsg'
  },
  {
    title: '耗时(毫秒)',
    align: "center",
    dataIndex: 'costMs'
  },
  {
    title: '错误类型',
    align: "center",
    dataIndex: 'errorType'
  },
  {
    title: '错误详情',
    align: "center",
    dataIndex: 'errorDetail'
  },
  {
    title: '异常堆栈',
    align: "center",
    dataIndex: 'stackTrace'
  },
];

// 高级查询数据
export const superQuerySchema = {
  interfaceName: {title: '接口名称',order: 0,view: 'text', type: 'string',},
  interfaceUrl: {title: '接口地址',order: 1,view: 'text', type: 'string',},
  bizId: {title: '业务主键',order: 2,view: 'text', type: 'string',},
  bizLastTime: {title: '业务数据更新时间',order: 3,view: 'datetime', type: 'string',},
  resultStatus: {title: '结果状态',order: 4,view: 'text', type: 'string',},
  resultMsg: {title: '结果说明',order: 5,view: 'text', type: 'string',},
  costMs: {title: '耗时(毫秒)',order: 6,view: 'number', type: 'number',},
  errorType: {title: '错误类型',order: 7,view: 'text', type: 'string',},
  errorDetail: {title: '错误详情',order: 8,view: 'text', type: 'string',},
  stackTrace: {title: '异常堆栈',order: 9,view: 'textarea', type: 'string',},
};
