package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyInquiryMaritalStatus;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 职业问诊婚姻状况
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface IZyInquiryMaritalStatusService extends IService<ZyInquiryMaritalStatus> {

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId
   * @return List<ZyInquiryMaritalStatus>
   */
	public List<ZyInquiryMaritalStatus> selectByMainId(String mainId);
}
