<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.occu.mapper.ZyInquirySymptomMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  zy_inquiry_symptom 
		WHERE
			 inquiry_id = #{mainId} 
	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.occu.entity.ZyInquirySymptom">
		SELECT *
		FROM  zy_inquiry_symptom
		WHERE
			 inquiry_id = #{mainId} 
	</select>
	<select id="selectSymptomListByInquiryId" resultType="org.jeecg.modules.occu.entity.ZyInquirySymptom">
		SELECT
			s.id AS id,
			s.create_time AS createTime,
			s.create_by AS createBy,
			s.update_by AS updateBy,
			s.update_time AS updateTime,
			s.severity AS severity,
			s.remark AS remark,
			s.inquiry_id AS inquiryId,
			s.check_date checkDate,
			s.check_doctor AS checkDoctor,
			d.dict_text AS symptom,
			d.system AS system
		FROM
			zy_inquiry_symptom s right JOIN zy_symptom_dict d ON s.symptom = d.id
		and s.inquiry_id = #{inquiryId}
	</select>
</mapper>
