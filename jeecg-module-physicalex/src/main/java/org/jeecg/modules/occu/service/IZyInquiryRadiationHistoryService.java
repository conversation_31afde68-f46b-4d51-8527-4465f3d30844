package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyInquiryRadiationHistory;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 职业问诊放射史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface IZyInquiryRadiationHistoryService extends IService<ZyInquiryRadiationHistory> {

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId
   * @return List<ZyInquiryRadiationHistory>
   */
	public List<ZyInquiryRadiationHistory> selectByMainId(String mainId);
}
