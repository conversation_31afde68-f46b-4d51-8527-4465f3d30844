package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.ZySymptom;
import org.jeecg.modules.occu.service.IZySymptomService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 职业病症状
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Api(tags="职业病症状")
@RestController
@RequestMapping("/occu/zySymptom")
@Slf4j
public class ZySymptomController extends JeecgController<ZySymptom, IZySymptomService> {
	@Autowired
	private IZySymptomService zySymptomService;
	
	/**
	 * 分页列表查询
	 *
	 * @param zySymptom
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "职业病症状-分页列表查询")
	@ApiOperation(value="职业病症状-分页列表查询", notes="职业病症状-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ZySymptom>> queryPageList(ZySymptom zySymptom,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ZySymptom> queryWrapper = QueryGenerator.initQueryWrapper(zySymptom, req.getParameterMap());
		Page<ZySymptom> page = new Page<ZySymptom>(pageNo, pageSize);
		IPage<ZySymptom> pageList = zySymptomService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param zySymptom
	 * @return
	 */
	@AutoLog(value = "职业病症状-添加")
	@ApiOperation(value="职业病症状-添加", notes="职业病症状-添加")
	@RequiresPermissions("occu:zy_symptom:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZySymptom zySymptom) {
		zySymptomService.save(zySymptom);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zySymptom
	 * @return
	 */
	@AutoLog(value = "职业病症状-编辑")
	@ApiOperation(value="职业病症状-编辑", notes="职业病症状-编辑")
	@RequiresPermissions("occu:zy_symptom:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZySymptom zySymptom) {
		zySymptomService.updateById(zySymptom);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "职业病症状-通过id删除")
	@ApiOperation(value="职业病症状-通过id删除", notes="职业病症状-通过id删除")
	@RequiresPermissions("occu:zy_symptom:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zySymptomService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "职业病症状-批量删除")
	@ApiOperation(value="职业病症状-批量删除", notes="职业病症状-批量删除")
	@RequiresPermissions("occu:zy_symptom:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.zySymptomService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "职业病症状-通过id查询")
	@ApiOperation(value="职业病症状-通过id查询", notes="职业病症状-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZySymptom> queryById(@RequestParam(name="id",required=true) String id) {
		ZySymptom zySymptom = zySymptomService.getById(id);
		if(zySymptom==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zySymptom);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zySymptom
    */
    @RequiresPermissions("occu:zy_symptom:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZySymptom zySymptom) {
        return super.exportXls(request, zySymptom, ZySymptom.class, "职业病症状");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_symptom:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZySymptom.class);
    }

}
