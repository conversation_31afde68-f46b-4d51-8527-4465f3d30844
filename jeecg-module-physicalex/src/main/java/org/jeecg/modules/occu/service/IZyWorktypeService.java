package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.entity.ZyWorktypeRiskFactor;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 工种
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
public interface IZyWorktypeService extends IService<ZyWorktype> {

    /**
     * 保存工种及其关联的危害因素
     * @param zyWorktype 工种信息
     * @param zyWorktypeRiskFactorList 关联的危害因素列表
     */
    public void saveMain(ZyWorktype zyWorktype, List<ZyWorktypeRiskFactor> zyWorktypeRiskFactorList);

    /**
     * 更新工种及其关联的危害因素
     * @param zyWorktype 工种信息
     * @param zyWorktypeRiskFactorList 关联的危害因素列表
     */
    public void updateMain(ZyWorktype zyWorktype, List<ZyWorktypeRiskFactor> zyWorktypeRiskFactorList);

    /**
     * 删除工种及其关联的危害因素
     * @param id 工种ID
     */
    public void delMain(String id);
}
