package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyInquiryMaritalStatus;
import org.jeecg.modules.occu.mapper.ZyInquiryMaritalStatusMapper;
import org.jeecg.modules.occu.service.IZyInquiryMaritalStatusService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 职业问诊婚姻状况
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquiryMaritalStatusServiceImpl extends ServiceImpl<ZyInquiryMaritalStatusMapper, ZyInquiryMaritalStatus> implements IZyInquiryMaritalStatusService {
	
	@Autowired
	private ZyInquiryMaritalStatusMapper zyInquiryMaritalStatusMapper;
	
	@Override
	public List<ZyInquiryMaritalStatus> selectByMainId(String mainId) {
		return zyInquiryMaritalStatusMapper.selectByMainId(mainId);
	}
}
