package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: zy_risk_factor_disease
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Data
@TableName("zy_risk_factor_disease")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="zy_risk_factor_disease对象", description="zy_risk_factor_disease")
public class ZyRiskFactorDisease implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**zy_risk_factor的id*/
	@Excel(name = "zy_risk_factor的id", width = 15)
    @ApiModelProperty(value = "zy_risk_factor的id")
    private java.lang.String factorId;
	/**风险因素*/
	@Excel(name = "风险因素", width = 15)
    @ApiModelProperty(value = "风险因素")
    private java.lang.String factorName;
	/**岗位阶段*/
	@Excel(name = "岗位阶段", width = 15)
    @ApiModelProperty(value = "岗位阶段")
    private java.lang.String jobStatus;
	/**禁忌症*/
	@Excel(name = "禁忌症", width = 15)
    @ApiModelProperty(value = "禁忌症")
    private java.lang.String occuContraindication;
	/**职业病*/
	@Excel(name = "职业病", width = 15)
    @ApiModelProperty(value = "职业病")
    private java.lang.String occuDisease;
	/**体检周期*/
	@Excel(name = "体检周期", width = 15)
    @ApiModelProperty(value = "体检周期")
    private java.lang.String examCycle;
	/**参考文献*/
	@Excel(name = "参考文献", width = 15)
    @ApiModelProperty(value = "参考文献")
    private java.lang.String refArticle;
}
