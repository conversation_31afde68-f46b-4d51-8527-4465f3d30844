package org.jeecg.modules.occu.mapper;

import java.util.List;
import org.jeecg.modules.occu.entity.ZyInquirySymptom;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 职业问诊现有症状
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface ZyInquirySymptomMapper extends BaseMapper<ZyInquirySymptom> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

   /**
    * 通过主表id查询子表数据
    *
    * @param mainId 主表id
    * @return List<ZyInquirySymptom>
    */
	public List<ZyInquirySymptom> selectByMainId(@Param("mainId") String mainId);


	public List<ZyInquirySymptom> selectSymptomListByInquiryId(@Param("inquiryId") String inquiryId);

}
