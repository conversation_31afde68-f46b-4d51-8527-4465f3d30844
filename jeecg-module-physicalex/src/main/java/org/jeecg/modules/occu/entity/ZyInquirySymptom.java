package org.jeecg.modules.occu.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.aspect.annotation.Dict;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 职业问诊现有症状
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Data
@TableName("zy_inquiry_symptom")
@ApiModel(value="zy_inquiry_symptom对象", description="职业问诊现有症状")
public class ZyInquirySymptom implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**症状*/
	@Excel(name = "症状", width = 15)
//    @Dict(dicCode = "symptom")
    @Dict(dictTable = "zy_symptom_dict", dicText = "dict_text", dicCode = "id")
    @ApiModelProperty(value = "症状")
    private java.lang.String symptom;
	/**程度*/
	@Excel(name = "程度", width = 15)
    @ApiModelProperty(value = "程度")
    private java.lang.String severity;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**问诊ID*/
    @ApiModelProperty(value = "问诊ID")
    private java.lang.String inquiryId;
    /**检查日期*/
    @ApiModelProperty(value = "检查日期")
    private Date checkDate;
    /**检查医生*/
    @ApiModelProperty(value = "检查医生")
    private String checkDoctor;

    @TableField(exist = false)
    private String system;

}
