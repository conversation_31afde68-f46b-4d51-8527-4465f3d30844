<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.occu.mapper.ZyRiskFactorMapper">
    <select id="selectByIds" resultType="org.jeecg.modules.occu.entity.ZyRiskFactor">
        select * from zy_risk_factor where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByIdsOrCodes" resultType="org.jeecg.modules.occu.entity.ZyRiskFactor">
        select * from zy_risk_factor where id in
        <foreach collection="values" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        or code in
        <foreach collection="values" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>