package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 职业检结论
 * @Author: jeecg-boot
 * @Date:   2024-05-31
 * @Version: V1.0
 */
@Data
@TableName("zy_conclusion")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="zy_conclusion对象", description="职业检结论")
public class ZyConclusion implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**customerRegId*/
	@Excel(name = "customerRegId", width = 15)
    @ApiModelProperty(value = "customerRegId")
    private java.lang.String customerRegId;
	/**汇总*/
	@Excel(name = "汇总", width = 15)
    @ApiModelProperty(value = "汇总")
    private java.lang.String departSummary;
	/**总检建议*/
	@Excel(name = "总检建议", width = 15)
    @ApiModelProperty(value = "总检建议")
    private java.lang.String summaryAdvice;
	/**职业检结论*/
	@Excel(name = "职业检结论", width = 15, dictTable = "zy_conclusion_dict where dict_cate ='检查结论'", dicText = "dict_text", dicCode = "id")
	@Dict(dictTable = "zy_conclusion_dict where dict_cate ='检查结论'", dicText = "dict_text", dicCode = "id")
    @ApiModelProperty(value = "职业检结论")
    private java.lang.String conclusion;
	/**依据*/
	@Excel(name = "依据", width = 15, dictTable = "zy_conclusion_dict where dict_cate ='结论依据'", dicText = "dict_text", dicCode = "id")
	@Dict(dictTable = "zy_conclusion_dict where dict_cate ='结论依据'", dicText = "dict_text", dicCode = "id")
    @ApiModelProperty(value = "依据")
    private java.lang.String according;
	/**处理意见*/
	@Excel(name = "处理意见", width = 15)
    @ApiModelProperty(value = "处理意见")
    private java.lang.String advice;
	/**职业病*/
	@Excel(name = "职业病", width = 15, dictTable = "zy_disease_dict", dicText = "dict_text", dicCode = "id")
	@Dict(dictTable = "zy_disease_dict", dicText = "dict_text", dicCode = "id")
    @ApiModelProperty(value = "职业病")
    private java.lang.String zyDisease;
	/**职业禁忌症*/
	@Excel(name = "职业禁忌症", width = 15, dictTable = "zy_symptom_dict", dicText = "dict_text", dicCode = "id")
	@Dict(dictTable = "zy_symptom_dict", dicText = "dict_text", dicCode = "id")
    @ApiModelProperty(value = "职业禁忌症")
    private java.lang.String zySymptom;
	/**复检内容*/
	@Excel(name = "复检内容", width = 15)
    @ApiModelProperty(value = "复检内容")
    private java.lang.String recheckContent;
	/**危害因素代码*/
	@Excel(name = "危害因素代码", width = 15)
	@ApiModelProperty(value = "危害因素代码")
	private java.lang.String riskCode;
	/**危害因素*/
	@Excel(name = "危害因素", width = 15)
    @ApiModelProperty(value = "危害因素")
    private java.lang.String riskName;
	/**工种*/
	@Excel(name = "工种", width = 15)
    @ApiModelProperty(value = "工种")
    private java.lang.String workType;
	/**职业史*/
	@Excel(name = "职业史", width = 15)
    @ApiModelProperty(value = "职业史")
    private java.lang.String occupationalHistory;
	/**岗位状态*/
	@Excel(name = "岗位状态", width = 15)
    @ApiModelProperty(value = "岗位状态")
    private java.lang.String postState;
	/**岗位ID*/
	@Excel(name = "岗位ID", width = 15)
    @ApiModelProperty(value = "岗位ID")
    private java.lang.Integer postStateId;
	/**创建人账号*/
    @ApiModelProperty(value = "创建人账号")
    private java.lang.String createBy;
	/**创建人*/
	@Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "创建人")
    private java.lang.String creatorName;
	/**审核人账号*/
	@Excel(name = "审核人账号", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "审核人账号")
    private java.lang.String audteBy;
	/**审核人*/
	@Excel(name = "审核人", width = 15)
    @ApiModelProperty(value = "审核人")
    private java.lang.String audtorName;
}
