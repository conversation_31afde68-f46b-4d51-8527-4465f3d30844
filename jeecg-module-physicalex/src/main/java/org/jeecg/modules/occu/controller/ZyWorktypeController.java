package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.entity.ZyWorktypeRiskFactor;
import org.jeecg.modules.occu.service.IZyWorktypeService;
import org.jeecg.modules.occu.service.IZyWorktypeRiskFactorService;
import org.jeecg.modules.occu.vo.ZyWorktypePage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;

 /**
 * @Description: 工种
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Api(tags="工种")
@RestController
@RequestMapping("/occu/zyWorktype")
@Slf4j
public class ZyWorktypeController extends JeecgController<ZyWorktype, IZyWorktypeService> {
	@Autowired
	private IZyWorktypeService zyWorktypeService;
	@Autowired
	private IZyWorktypeRiskFactorService zyWorktypeRiskFactorService;
	
	/**
	 * 分页列表查询
	 *
	 * @param zyWorktype
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "工种-分页列表查询")
	@ApiOperation(value="工种-分页列表查询", notes="工种-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ZyWorktype>> queryPageList(ZyWorktype zyWorktype,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ZyWorktype> queryWrapper = QueryGenerator.initQueryWrapper(zyWorktype, req.getParameterMap());
		Page<ZyWorktype> page = new Page<ZyWorktype>(pageNo, pageSize);
		IPage<ZyWorktype> pageList = zyWorktypeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param zyWorktypePage
	 * @return
	 */
	@AutoLog(value = "工种-添加")
	@ApiOperation(value="工种-添加", notes="工种-添加")
	@RequiresPermissions("occu:zy_worktype:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZyWorktypePage zyWorktypePage) {
		ZyWorktype zyWorktype = new ZyWorktype();
		BeanUtils.copyProperties(zyWorktypePage, zyWorktype);
		zyWorktypeService.saveMain(zyWorktype, zyWorktypePage.getZyWorktypeRiskFactorList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zyWorktypePage
	 * @return
	 */
	@AutoLog(value = "工种-编辑")
	@ApiOperation(value="工种-编辑", notes="工种-编辑")
	@RequiresPermissions("occu:zy_worktype:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZyWorktypePage zyWorktypePage) {
		ZyWorktype zyWorktype = new ZyWorktype();
		BeanUtils.copyProperties(zyWorktypePage, zyWorktype);
		ZyWorktype zyWorktypeEntity = zyWorktypeService.getById(zyWorktype.getId());
		if(zyWorktypeEntity == null) {
			return Result.error("未找到对应数据");
		}
		zyWorktypeService.updateMain(zyWorktype, zyWorktypePage.getZyWorktypeRiskFactorList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "工种-通过id删除")
	@ApiOperation(value="工种-通过id删除", notes="工种-通过id删除")
	@RequiresPermissions("occu:zy_worktype:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zyWorktypeService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "工种-批量删除")
	@ApiOperation(value="工种-批量删除", notes="工种-批量删除")
	@RequiresPermissions("occu:zy_worktype:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		String[] idArray = ids.split(",");
		for(String id : idArray) {
			zyWorktypeService.delMain(id);
		}
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "工种-通过id查询")
	@ApiOperation(value="工种-通过id查询", notes="工种-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZyWorktype> queryById(@RequestParam(name="id",required=true) String id) {
		ZyWorktype zyWorktype = zyWorktypeService.getById(id);
		if(zyWorktype==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zyWorktype);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zyWorktype
    */
    @RequiresPermissions("occu:zy_worktype:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyWorktype zyWorktype) {
        return super.exportXls(request, zyWorktype, ZyWorktype.class, "工种");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_worktype:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyWorktype.class);
    }

    /**
     * 通过工种ID查询关联的危害因素
     *
     * @param worktypeId
     * @return
     */
    @ApiOperation(value="工种-查询关联的危害因素", notes="工种-查询关联的危害因素")
    @GetMapping(value = "/queryZyWorktypeRiskFactorByMainId")
    public Result<List<ZyWorktypeRiskFactor>> queryZyWorktypeRiskFactorByMainId(@RequestParam(name="worktypeId",required=true) String worktypeId) {
        List<ZyWorktypeRiskFactor> zyWorktypeRiskFactorList = zyWorktypeRiskFactorService.selectByWorktypeId(worktypeId);
        return Result.OK(zyWorktypeRiskFactorList);
    }

}
