package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 工种危害因素关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-08
 * @Version: V1.0
 */
@Data
@TableName("zy_worktype_risk_factor")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="zy_worktype_risk_factor对象", description="工种危害因素关联表")
public class ZyWorktypeRiskFactor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**工种ID*/
    @Excel(name = "工种ID", width = 15)
    @ApiModelProperty(value = "工种ID")
    private String worktypeId;
    
    /**危害因素ID*/
    @Excel(name = "危害因素ID", width = 15)
    @ApiModelProperty(value = "危害因素ID")
    private String riskFactorId;
    
    /**危害因素名称*/
    @Excel(name = "危害因素名称", width = 15)
    @ApiModelProperty(value = "危害因素名称")
    private String riskFactorName;
    
    /**危害因素代码*/
    @Excel(name = "危害因素代码", width = 15)
    @ApiModelProperty(value = "危害因素代码")
    private String riskFactorCode;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
