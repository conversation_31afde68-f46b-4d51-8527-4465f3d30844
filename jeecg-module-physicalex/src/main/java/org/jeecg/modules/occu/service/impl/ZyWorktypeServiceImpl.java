package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.entity.ZyWorktypeRiskFactor;
import org.jeecg.modules.occu.mapper.ZyWorktypeMapper;
import org.jeecg.modules.occu.mapper.ZyWorktypeRiskFactorMapper;
import org.jeecg.modules.occu.service.IZyWorktypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;

/**
 * @Description: 工种
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Service
public class ZyWorktypeServiceImpl extends ServiceImpl<ZyWorktypeMapper, ZyWorktype> implements IZyWorktypeService {

    @Autowired
    private ZyWorktypeMapper zyWorktypeMapper;
    @Autowired
    private ZyWorktypeRiskFactorMapper zyWorktypeRiskFactorMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMain(ZyWorktype zyWorktype, List<ZyWorktypeRiskFactor> zyWorktypeRiskFactorList) {
        zyWorktypeMapper.insert(zyWorktype);
        if(zyWorktypeRiskFactorList != null && zyWorktypeRiskFactorList.size() > 0) {
            for(ZyWorktypeRiskFactor entity : zyWorktypeRiskFactorList) {
                //外键设置
                entity.setWorktypeId(zyWorktype.getId());
                zyWorktypeRiskFactorMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMain(ZyWorktype zyWorktype, List<ZyWorktypeRiskFactor> zyWorktypeRiskFactorList) {
        zyWorktypeMapper.updateById(zyWorktype);

        //1.先删除子表数据
        zyWorktypeRiskFactorMapper.deleteByWorktypeId(zyWorktype.getId());

        //2.子表数据重新插入
        if(zyWorktypeRiskFactorList != null && zyWorktypeRiskFactorList.size() > 0) {
            for(ZyWorktypeRiskFactor entity : zyWorktypeRiskFactorList) {
                //外键设置
                entity.setWorktypeId(zyWorktype.getId());
                zyWorktypeRiskFactorMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        zyWorktypeRiskFactorMapper.deleteByWorktypeId(id);
        zyWorktypeMapper.deleteById(id);
    }
}
