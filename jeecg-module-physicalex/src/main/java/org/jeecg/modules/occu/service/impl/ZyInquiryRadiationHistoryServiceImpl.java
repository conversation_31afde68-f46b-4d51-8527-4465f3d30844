package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyInquiryRadiationHistory;
import org.jeecg.modules.occu.mapper.ZyInquiryRadiationHistoryMapper;
import org.jeecg.modules.occu.service.IZyInquiryRadiationHistoryService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 职业问诊放射史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquiryRadiationHistoryServiceImpl extends ServiceImpl<ZyInquiryRadiationHistoryMapper, ZyInquiryRadiationHistory> implements IZyInquiryRadiationHistoryService {
	
	@Autowired
	private ZyInquiryRadiationHistoryMapper zyInquiryRadiationHistoryMapper;
	
	@Override
	public List<ZyInquiryRadiationHistory> selectByMainId(String mainId) {
		return zyInquiryRadiationHistoryMapper.selectByMainId(mainId);
	}
}
