package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.OccuSyncRecord;
import org.jeecg.modules.occu.mapper.OccuSyncRecordMapper;
import org.jeecg.modules.occu.service.IOccuSyncRecordService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: occu_sync_record
 * @Author: jeecg-boot
 * @Date:   2025-05-28
 * @Version: V1.0
 */
@Service
public class OccuSyncRecordServiceImpl extends ServiceImpl<OccuSyncRecordMapper, OccuSyncRecord> implements IOccuSyncRecordService {

}
