package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 职业字典-计量单位代码
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Data
@TableName("zy_item_unit")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="zy_item_unit对象", description="职业字典-计量单位代码")
public class ZyItemUnit implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**项目分类*/
	@Excel(name = "项目分类", width = 15)
    @ApiModelProperty(value = "项目分类")
    private java.lang.String itemCategory;
	/**体检项目编码*/
	@Excel(name = "体检项目编码", width = 15)
    @ApiModelProperty(value = "体检项目编码")
    private java.lang.String itemCode;
	/**体检项目名称*/
	@Excel(name = "体检项目名称", width = 15)
    @ApiModelProperty(value = "体检项目名称")
    private java.lang.String itemName;
	/**计量单位名称*/
	@Excel(name = "计量单位名称", width = 15)
    @ApiModelProperty(value = "计量单位名称")
    private java.lang.String unitName;
	/**计量单位代码*/
	@Excel(name = "计量单位代码", width = 15)
    @ApiModelProperty(value = "计量单位代码")
    private java.lang.String unitCode;
	/**His项目代码*/
	@Excel(name = "His项目代码", width = 15)
    @ApiModelProperty(value = "His项目代码")
    private java.lang.String hisCode;
	/**His项目名称*/
	@Excel(name = "His项目名称", width = 15)
    @ApiModelProperty(value = "His项目名称")
    private java.lang.String hisName;
}
