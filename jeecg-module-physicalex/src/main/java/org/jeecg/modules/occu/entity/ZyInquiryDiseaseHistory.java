package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.aspect.annotation.Dict;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 职业问诊既往病史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Data
@TableName("zy_inquiry_disease_history")
@ApiModel(value="zy_inquiry_disease_history对象", description="职业问诊既往病史")
public class ZyInquiryDiseaseHistory implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**疾病名称*/
	@Excel(name = "疾病名称", width = 15)
    @Dict(dicCode = "code",dicText = "name",dictTable = "icd")
    @ApiModelProperty(value = "疾病名称")
    private java.lang.String disease;
	/**诊断日期*/
	@Excel(name = "诊断日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "诊断日期")
    private java.util.Date diagnoseDate;
	/**诊疗方式*/
	@Excel(name = "诊疗方式", width = 15)
    @Dict(dicCode = "treatment_means")
    @ApiModelProperty(value = "诊疗方式")
    private java.lang.String treatmentMeans;
	/**确诊单位*/
	@Excel(name = "确诊单位", width = 15)
    @ApiModelProperty(value = "确诊单位")
    private java.lang.String diagnoseCompany;
	/**是否痊愈*/
	@Excel(name = "是否痊愈", width = 15)
    @ApiModelProperty(value = "是否痊愈")
    private java.lang.String cureFlag;
	/**问诊ID*/
    @ApiModelProperty(value = "问诊ID")
    private java.lang.String inquiryId;
    /**检查日期*/
    @ApiModelProperty(value = "检查日期")
    private Date checkDate;
    /**检查医生*/
    @ApiModelProperty(value = "检查医生")
    private String checkDoctor;
}
