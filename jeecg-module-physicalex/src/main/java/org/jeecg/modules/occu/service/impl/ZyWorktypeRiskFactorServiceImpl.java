package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyWorktypeRiskFactor;
import org.jeecg.modules.occu.mapper.ZyWorktypeRiskFactorMapper;
import org.jeecg.modules.occu.service.IZyWorktypeRiskFactorService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;

/**
 * @Description: 工种危害因素关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-08
 * @Version: V1.0
 */
@Service
public class ZyWorktypeRiskFactorServiceImpl extends ServiceImpl<ZyWorktypeRiskFactorMapper, ZyWorktypeRiskFactor> implements IZyWorktypeRiskFactorService {

    @Override
    public List<ZyWorktypeRiskFactor> selectByWorktypeId(String worktypeId) {
        return baseMapper.selectByWorktypeId(worktypeId);
    }
}
