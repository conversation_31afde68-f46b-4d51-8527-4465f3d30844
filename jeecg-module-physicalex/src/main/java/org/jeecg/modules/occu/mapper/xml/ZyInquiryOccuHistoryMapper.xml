<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.occu.mapper.ZyInquiryOccuHistoryMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  zy_inquiry_occu_history 
		WHERE
			 inquiry_id = #{mainId} 
	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.occu.entity.ZyInquiryOccuHistory">
		SELECT * 
		FROM  zy_inquiry_occu_history
		WHERE
			 inquiry_id = #{mainId} 
	</select>
</mapper>
