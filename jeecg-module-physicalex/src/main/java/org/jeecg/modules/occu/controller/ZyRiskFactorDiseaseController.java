package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.ZyRiskFactorDisease;
import org.jeecg.modules.occu.service.IZyRiskFactorDiseaseService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: zy_risk_factor_disease
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Api(tags="zy_risk_factor_disease")
@RestController
@RequestMapping("/occu/zyRiskFactorDisease")
@Slf4j
public class ZyRiskFactorDiseaseController extends JeecgController<ZyRiskFactorDisease, IZyRiskFactorDiseaseService> {
	@Autowired
	private IZyRiskFactorDiseaseService zyRiskFactorDiseaseService;
	
	/**
	 * 分页列表查询
	 *
	 * @param zyRiskFactorDisease
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "zy_risk_factor_disease-分页列表查询")
	@ApiOperation(value="zy_risk_factor_disease-分页列表查询", notes="zy_risk_factor_disease-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ZyRiskFactorDisease>> queryPageList(ZyRiskFactorDisease zyRiskFactorDisease,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ZyRiskFactorDisease> queryWrapper = QueryGenerator.initQueryWrapper(zyRiskFactorDisease, req.getParameterMap());
		Page<ZyRiskFactorDisease> page = new Page<ZyRiskFactorDisease>(pageNo, pageSize);
		IPage<ZyRiskFactorDisease> pageList = zyRiskFactorDiseaseService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param zyRiskFactorDisease
	 * @return
	 */
	@AutoLog(value = "zy_risk_factor_disease-添加")
	@ApiOperation(value="zy_risk_factor_disease-添加", notes="zy_risk_factor_disease-添加")
	@RequiresPermissions("occu:zy_risk_factor_disease:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZyRiskFactorDisease zyRiskFactorDisease) {
		zyRiskFactorDiseaseService.save(zyRiskFactorDisease);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zyRiskFactorDisease
	 * @return
	 */
	@AutoLog(value = "zy_risk_factor_disease-编辑")
	@ApiOperation(value="zy_risk_factor_disease-编辑", notes="zy_risk_factor_disease-编辑")
	@RequiresPermissions("occu:zy_risk_factor_disease:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZyRiskFactorDisease zyRiskFactorDisease) {
		zyRiskFactorDiseaseService.updateById(zyRiskFactorDisease);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "zy_risk_factor_disease-通过id删除")
	@ApiOperation(value="zy_risk_factor_disease-通过id删除", notes="zy_risk_factor_disease-通过id删除")
	@RequiresPermissions("occu:zy_risk_factor_disease:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zyRiskFactorDiseaseService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "zy_risk_factor_disease-批量删除")
	@ApiOperation(value="zy_risk_factor_disease-批量删除", notes="zy_risk_factor_disease-批量删除")
	@RequiresPermissions("occu:zy_risk_factor_disease:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.zyRiskFactorDiseaseService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "zy_risk_factor_disease-通过id查询")
	@ApiOperation(value="zy_risk_factor_disease-通过id查询", notes="zy_risk_factor_disease-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZyRiskFactorDisease> queryById(@RequestParam(name="id",required=true) String id) {
		ZyRiskFactorDisease zyRiskFactorDisease = zyRiskFactorDiseaseService.getById(id);
		if(zyRiskFactorDisease==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zyRiskFactorDisease);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zyRiskFactorDisease
    */
    @RequiresPermissions("occu:zy_risk_factor_disease:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyRiskFactorDisease zyRiskFactorDisease) {
        return super.exportXls(request, zyRiskFactorDisease, ZyRiskFactorDisease.class, "zy_risk_factor_disease");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_risk_factor_disease:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyRiskFactorDisease.class);
    }

}
