package org.jeecg.modules.occu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZyConclusionDetail;

import java.util.List;

/**
 * @Description: 职业检总检详细
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
public interface ZyConclusionDetailMapper extends BaseMapper<ZyConclusionDetail> {

    List<ZyConclusionDetail> listByReg(@Param("customerRegId") String customerRegId);
}
