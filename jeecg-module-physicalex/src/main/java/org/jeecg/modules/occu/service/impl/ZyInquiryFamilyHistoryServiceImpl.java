package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyInquiryFamilyHistory;
import org.jeecg.modules.occu.mapper.ZyInquiryFamilyHistoryMapper;
import org.jeecg.modules.occu.service.IZyInquiryFamilyHistoryService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 职业问诊家族史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquiryFamilyHistoryServiceImpl extends ServiceImpl<ZyInquiryFamilyHistoryMapper, ZyInquiryFamilyHistory> implements IZyInquiryFamilyHistoryService {
	
	@Autowired
	private ZyInquiryFamilyHistoryMapper zyInquiryFamilyHistoryMapper;
	
	@Override
	public List<ZyInquiryFamilyHistory> selectByMainId(String mainId) {
		return zyInquiryFamilyHistoryMapper.selectByMainId(mainId);
	}
}
