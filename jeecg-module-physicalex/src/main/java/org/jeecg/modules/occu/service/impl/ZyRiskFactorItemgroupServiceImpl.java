package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.mapper.ZyRiskFactorItemgroupMapper;
import org.jeecg.modules.occu.service.IZyRiskFactorItemgroupService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 危害因素必检项目
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@Service
public class ZyRiskFactorItemgroupServiceImpl extends ServiceImpl<ZyRiskFactorItemgroupMapper, ZyRiskFactorItemgroup> implements IZyRiskFactorItemgroupService {
	
	@Autowired
	private ZyRiskFactorItemgroupMapper zyRiskFactorItemgroupMapper;
	
	@Override
	public List<ZyRiskFactorItemgroup> selectByMainId(String mainId) {
		return zyRiskFactorItemgroupMapper.selectByMainId(mainId);
	}
}
