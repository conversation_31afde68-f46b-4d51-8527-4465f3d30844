package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.ZyRiskFactorType;
import org.jeecg.modules.occu.service.IZyRiskFactorTypeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 危害因素分类
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Api(tags="危害因素分类")
@RestController
@RequestMapping("/occu/zyRiskFactorType")
@Slf4j
public class ZyRiskFactorTypeController extends JeecgController<ZyRiskFactorType, IZyRiskFactorTypeService> {
	@Autowired
	private IZyRiskFactorTypeService zyRiskFactorTypeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param zyRiskFactorType
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "危害因素分类-分页列表查询")
	@ApiOperation(value="危害因素分类-分页列表查询", notes="危害因素分类-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ZyRiskFactorType>> queryPageList(ZyRiskFactorType zyRiskFactorType,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ZyRiskFactorType> queryWrapper = QueryGenerator.initQueryWrapper(zyRiskFactorType, req.getParameterMap());
		Page<ZyRiskFactorType> page = new Page<ZyRiskFactorType>(pageNo, pageSize);
		IPage<ZyRiskFactorType> pageList = zyRiskFactorTypeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param zyRiskFactorType
	 * @return
	 */
	@AutoLog(value = "危害因素分类-添加")
	@ApiOperation(value="危害因素分类-添加", notes="危害因素分类-添加")
	@RequiresPermissions("occu:zy_risk_factor_type:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZyRiskFactorType zyRiskFactorType) {
		zyRiskFactorTypeService.save(zyRiskFactorType);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zyRiskFactorType
	 * @return
	 */
	@AutoLog(value = "危害因素分类-编辑")
	@ApiOperation(value="危害因素分类-编辑", notes="危害因素分类-编辑")
	@RequiresPermissions("occu:zy_risk_factor_type:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZyRiskFactorType zyRiskFactorType) {
		zyRiskFactorTypeService.updateById(zyRiskFactorType);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "危害因素分类-通过id删除")
	@ApiOperation(value="危害因素分类-通过id删除", notes="危害因素分类-通过id删除")
	@RequiresPermissions("occu:zy_risk_factor_type:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zyRiskFactorTypeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "危害因素分类-批量删除")
	@ApiOperation(value="危害因素分类-批量删除", notes="危害因素分类-批量删除")
	@RequiresPermissions("occu:zy_risk_factor_type:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.zyRiskFactorTypeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "危害因素分类-通过id查询")
	@ApiOperation(value="危害因素分类-通过id查询", notes="危害因素分类-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZyRiskFactorType> queryById(@RequestParam(name="id",required=true) String id) {
		ZyRiskFactorType zyRiskFactorType = zyRiskFactorTypeService.getById(id);
		if(zyRiskFactorType==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zyRiskFactorType);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zyRiskFactorType
    */
    @RequiresPermissions("occu:zy_risk_factor_type:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyRiskFactorType zyRiskFactorType) {
        return super.exportXls(request, zyRiskFactorType, ZyRiskFactorType.class, "危害因素分类");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_risk_factor_type:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyRiskFactorType.class);
    }

}
