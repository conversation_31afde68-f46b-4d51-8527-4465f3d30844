package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.OccuSyncRecord;
import org.jeecg.modules.occu.service.IOccuSyncRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: occu_sync_record
 * @Author: jeecg-boot
 * @Date:   2025-05-28
 * @Version: V1.0
 */
@Api(tags="occu_sync_record")
@RestController
@RequestMapping("/occu/occuSyncRecord")
@Slf4j
public class OccuSyncRecordController extends JeecgController<OccuSyncRecord, IOccuSyncRecordService> {
	@Autowired
	private IOccuSyncRecordService occuSyncRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param occuSyncRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "occu_sync_record-分页列表查询")
	@ApiOperation(value="occu_sync_record-分页列表查询", notes="occu_sync_record-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<OccuSyncRecord>> queryPageList(OccuSyncRecord occuSyncRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<OccuSyncRecord> queryWrapper = QueryGenerator.initQueryWrapper(occuSyncRecord, req.getParameterMap());
		Page<OccuSyncRecord> page = new Page<OccuSyncRecord>(pageNo, pageSize);
		IPage<OccuSyncRecord> pageList = occuSyncRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param occuSyncRecord
	 * @return
	 */
	@AutoLog(value = "occu_sync_record-添加")
	@ApiOperation(value="occu_sync_record-添加", notes="occu_sync_record-添加")
	@RequiresPermissions("occu:occu_sync_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody OccuSyncRecord occuSyncRecord) {
		occuSyncRecordService.save(occuSyncRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param occuSyncRecord
	 * @return
	 */
	@AutoLog(value = "occu_sync_record-编辑")
	@ApiOperation(value="occu_sync_record-编辑", notes="occu_sync_record-编辑")
	@RequiresPermissions("occu:occu_sync_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody OccuSyncRecord occuSyncRecord) {
		occuSyncRecordService.updateById(occuSyncRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "occu_sync_record-通过id删除")
	@ApiOperation(value="occu_sync_record-通过id删除", notes="occu_sync_record-通过id删除")
	@RequiresPermissions("occu:occu_sync_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		occuSyncRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "occu_sync_record-批量删除")
	@ApiOperation(value="occu_sync_record-批量删除", notes="occu_sync_record-批量删除")
	@RequiresPermissions("occu:occu_sync_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.occuSyncRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "occu_sync_record-通过id查询")
	@ApiOperation(value="occu_sync_record-通过id查询", notes="occu_sync_record-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<OccuSyncRecord> queryById(@RequestParam(name="id",required=true) String id) {
		OccuSyncRecord occuSyncRecord = occuSyncRecordService.getById(id);
		if(occuSyncRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(occuSyncRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param occuSyncRecord
    */
    @RequiresPermissions("occu:occu_sync_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OccuSyncRecord occuSyncRecord) {
        return super.exportXls(request, occuSyncRecord, OccuSyncRecord.class, "occu_sync_record");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:occu_sync_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OccuSyncRecord.class);
    }

}
