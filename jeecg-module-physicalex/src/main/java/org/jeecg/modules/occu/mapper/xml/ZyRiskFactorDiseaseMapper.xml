<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.occu.mapper.ZyRiskFactorDiseaseMapper">

    <select id="getZyRiskDiseaseByRiskFactors" resultType="org.jeecg.modules.occu.entity.ZyRiskFactorDisease">
        select d.* from zy_risk_factor_disease d join zy_risk_factor f on d.factor_id=f.id
        where f.name in 
        <foreach collection="riskFactors" item="riskFactor" open="(" separator="," close=")">
            #{riskFactor}
        </foreach>
    </select>
</mapper>