package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.occu.entity.ZyAdviceDict;
import org.jeecg.modules.occu.mapper.ZyAdviceDictMapper;
import org.jeecg.modules.occu.service.IZyAdviceDictService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 职业处理意见字典
 * @Author: jeecg-boot
 * @Date: 2024-12-04
 * @Version: V1.0
 */
@Service
public class ZyAdviceDictServiceImpl extends ServiceImpl<ZyAdviceDictMapper, ZyAdviceDict> implements IZyAdviceDictService {

    @Override
    public List<ZyAdviceDict> getAdviceDict() {
        QueryWrapper<ZyAdviceDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("use_count");
        return list(queryWrapper);
    }
}
