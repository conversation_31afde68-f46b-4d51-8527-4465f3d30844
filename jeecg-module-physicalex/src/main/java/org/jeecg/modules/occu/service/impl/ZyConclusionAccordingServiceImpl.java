package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyConclusionAccording;
import org.jeecg.modules.occu.mapper.ZyConclusionAccordingMapper;
import org.jeecg.modules.occu.service.IZyConclusionAccordingService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 职业结论依据字典
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Service
public class ZyConclusionAccordingServiceImpl extends ServiceImpl<ZyConclusionAccordingMapper, ZyConclusionAccording> implements IZyConclusionAccordingService {

}
