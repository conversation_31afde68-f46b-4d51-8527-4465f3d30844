package org.jeecg.modules.occu.vo;

import java.util.List;
import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.entity.ZyWorktypeRiskFactor;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 工种
 * @Author: jeecg-boot
 * @Date: 2025-01-08
 * @Version: V1.0
 */
@Data
@ApiModel(value="ZyWorktypePage对象", description="工种")
public class ZyWorktypePage {
    
    /**主键*/
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**名称*/
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;
    
    /**代码*/
    @Excel(name = "代码", width = 15)
    @ApiModelProperty(value = "代码")
    private String code;
    
    /**助记码*/
    @Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private String helpChar;
    
    /**启用*/
    @Excel(name = "启用", width = 15, replace = {"是_1","否_0"})
    @ApiModelProperty(value = "启用")
    private Integer enableFlag;
    
    /**排序*/
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sort;
    
    /**所属单位*/
    @ApiModelProperty(value = "所属单位")
    private String companyId;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**关联的危害因素列表*/
    @ExcelCollection(name="工种危害因素关联表")
    @ApiModelProperty(value = "关联的危害因素列表")
    private List<ZyWorktypeRiskFactor> zyWorktypeRiskFactorList;
}
