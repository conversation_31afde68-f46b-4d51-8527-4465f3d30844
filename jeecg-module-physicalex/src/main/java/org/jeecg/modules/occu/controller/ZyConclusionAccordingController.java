package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.ZyConclusionAccording;
import org.jeecg.modules.occu.service.IZyConclusionAccordingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 职业结论依据字典
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Api(tags="职业结论依据字典")
@RestController
@RequestMapping("/occu/zyConclusionAccording")
@Slf4j
public class ZyConclusionAccordingController extends JeecgController<ZyConclusionAccording, IZyConclusionAccordingService> {
	@Autowired
	private IZyConclusionAccordingService zyConclusionAccordingService;
	
	/**
	 * 分页列表查询
	 *
	 * @param zyConclusionAccording
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "职业结论依据字典-分页列表查询")
	@ApiOperation(value="职业结论依据字典-分页列表查询", notes="职业结论依据字典-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ZyConclusionAccording>> queryPageList(ZyConclusionAccording zyConclusionAccording,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ZyConclusionAccording> queryWrapper = QueryGenerator.initQueryWrapper(zyConclusionAccording, req.getParameterMap());
		Page<ZyConclusionAccording> page = new Page<ZyConclusionAccording>(pageNo, pageSize);
		IPage<ZyConclusionAccording> pageList = zyConclusionAccordingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param zyConclusionAccording
	 * @return
	 */
	@AutoLog(value = "职业结论依据字典-添加")
	@ApiOperation(value="职业结论依据字典-添加", notes="职业结论依据字典-添加")
	@RequiresPermissions("occu:zy_conclusion_according:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZyConclusionAccording zyConclusionAccording) {
		zyConclusionAccordingService.save(zyConclusionAccording);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zyConclusionAccording
	 * @return
	 */
	@AutoLog(value = "职业结论依据字典-编辑")
	@ApiOperation(value="职业结论依据字典-编辑", notes="职业结论依据字典-编辑")
	@RequiresPermissions("occu:zy_conclusion_according:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZyConclusionAccording zyConclusionAccording) {
		zyConclusionAccordingService.updateById(zyConclusionAccording);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "职业结论依据字典-通过id删除")
	@ApiOperation(value="职业结论依据字典-通过id删除", notes="职业结论依据字典-通过id删除")
	@RequiresPermissions("occu:zy_conclusion_according:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zyConclusionAccordingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "职业结论依据字典-批量删除")
	@ApiOperation(value="职业结论依据字典-批量删除", notes="职业结论依据字典-批量删除")
	@RequiresPermissions("occu:zy_conclusion_according:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.zyConclusionAccordingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "职业结论依据字典-通过id查询")
	@ApiOperation(value="职业结论依据字典-通过id查询", notes="职业结论依据字典-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZyConclusionAccording> queryById(@RequestParam(name="id",required=true) String id) {
		ZyConclusionAccording zyConclusionAccording = zyConclusionAccordingService.getById(id);
		if(zyConclusionAccording==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zyConclusionAccording);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zyConclusionAccording
    */
    @RequiresPermissions("occu:zy_conclusion_according:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyConclusionAccording zyConclusionAccording) {
        return super.exportXls(request, zyConclusionAccording, ZyConclusionAccording.class, "职业结论依据字典");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_conclusion_according:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyConclusionAccording.class);
    }

}
