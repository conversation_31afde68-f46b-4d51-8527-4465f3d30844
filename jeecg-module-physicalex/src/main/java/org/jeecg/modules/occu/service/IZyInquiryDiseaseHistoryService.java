package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyInquiryDiseaseHistory;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 职业问诊既往病史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface IZyInquiryDiseaseHistoryService extends IService<ZyInquiryDiseaseHistory> {

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId
   * @return List<ZyInquiryDiseaseHistory>
   */
	public List<ZyInquiryDiseaseHistory> selectByMainId(String mainId);
}
