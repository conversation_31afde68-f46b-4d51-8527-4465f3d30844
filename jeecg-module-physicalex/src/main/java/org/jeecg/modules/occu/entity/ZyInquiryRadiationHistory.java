package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.aspect.annotation.Dict;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 职业问诊放射史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Data
@TableName("zy_inquiry_radiation_history")
@ApiModel(value="zy_inquiry_radiation_history对象", description="职业问诊放射史")
public class ZyInquiryRadiationHistory implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**就职单位*/
	@Excel(name = "就职单位", width = 15)
    @ApiModelProperty(value = "就职单位")
    private java.lang.String company;
	/**接害工龄年*/
	@Excel(name = "接害工龄年", width = 15)
    @ApiModelProperty(value = "接害工龄年")
    private java.lang.Integer riskYears;
	/**接害工龄月*/
	@Excel(name = "接害工龄月", width = 15)
    @ApiModelProperty(value = "接害工龄月")
    private java.lang.Integer riskMonths;
	/**车间*/
	@Excel(name = "车间", width = 15)
    @ApiModelProperty(value = "车间")
    private java.lang.String workshop;
	/**工种*/
	@Excel(name = "工种", width = 15)
    @ApiModelProperty(value = "工种")
    private java.lang.String workName;
	/**职业照射种类*/
	@Excel(name = "职业照射种类", width = 15)
    @ApiModelProperty(value = "职业照射种类")
    private java.lang.String irradiationType;
	/**特殊情况*/
	@Excel(name = "特殊情况", width = 15)
    @ApiModelProperty(value = "特殊情况")
    private java.lang.String exceptional;
	/**放射线类型*/
	@Excel(name = "放射线类型", width = 15)
    @ApiModelProperty(value = "放射线类型")
    private java.lang.String radiationType;
	/**每日工作量*/
	@Excel(name = "每日工作量", width = 15)
    @ApiModelProperty(value = "每日工作量")
    private java.lang.String workload;
	/**累计受照剂量*/
	@Excel(name = "累计受照剂量", width = 15)
    @ApiModelProperty(value = "累计受照剂量")
    private java.lang.String cumulative;
	/**过量照射史*/
	@Excel(name = "过量照射史", width = 15)
    @ApiModelProperty(value = "过量照射史")
    private java.lang.String overdose;
	/**佩戴个人计量*/
	@Excel(name = "佩戴个人计量", width = 15)
    @ApiModelProperty(value = "佩戴个人计量")
    private java.lang.String meter;
	/**备注说明*/
	@Excel(name = "备注说明", width = 15)
    @ApiModelProperty(value = "备注说明")
    private java.lang.String remark;
	/**问诊ID*/
    @ApiModelProperty(value = "问诊ID")
    private java.lang.String inquiryId;
}
