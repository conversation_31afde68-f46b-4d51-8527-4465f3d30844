package org.jeecg.modules.occu.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 危害因素
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
public interface ZyRiskFactorMapper extends BaseMapper<ZyRiskFactor> {

    List<ZyRiskFactor> selectByIds(@Param("ids") List<String> ids);

    List<ZyRiskFactor> selectByIdsOrCodes(@Param("values") List<String> values);
}
