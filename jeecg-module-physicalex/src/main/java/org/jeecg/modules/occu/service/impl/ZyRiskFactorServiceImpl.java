package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.mapper.ZyRiskFactorItemgroupMapper;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.occu.service.IZyRiskFactorService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 危害因素
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@Service
public class ZyRiskFactorServiceImpl extends ServiceImpl<ZyRiskFactorMapper, ZyRiskFactor> implements IZyRiskFactorService {

	@Autowired
	private ZyRiskFactorMapper zyRiskFactorMapper;
	@Autowired
	private ZyRiskFactorItemgroupMapper zyRiskFactorItemgroupMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList) {
		zyRiskFactorMapper.insert(zyRiskFactor);
		if(zyRiskFactorItemgroupList!=null && zyRiskFactorItemgroupList.size()>0) {
			for(ZyRiskFactorItemgroup entity:zyRiskFactorItemgroupList) {
				//外键设置
				entity.setFactorId(zyRiskFactor.getId());
				zyRiskFactorItemgroupMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(ZyRiskFactor zyRiskFactor,List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList) {
		zyRiskFactorMapper.updateById(zyRiskFactor);
		
		//1.先删除子表数据
		zyRiskFactorItemgroupMapper.deleteByMainId(zyRiskFactor.getId());
		
		//2.子表数据重新插入
		if(zyRiskFactorItemgroupList!=null && zyRiskFactorItemgroupList.size()>0) {
			for(ZyRiskFactorItemgroup entity:zyRiskFactorItemgroupList) {
				//外键设置
				entity.setFactorId(zyRiskFactor.getId());
				zyRiskFactorItemgroupMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		zyRiskFactorItemgroupMapper.deleteByMainId(id);
		zyRiskFactorMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			zyRiskFactorItemgroupMapper.deleteByMainId(id.toString());
			zyRiskFactorMapper.deleteById(id);
		}
	}
	
}
