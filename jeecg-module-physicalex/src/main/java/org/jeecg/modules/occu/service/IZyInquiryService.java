package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyInquiry;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: 职业病问诊
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface IZyInquiryService extends IService<ZyInquiry> {

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	ZyInquiry getByRegId(String customerRegId);

	ZyInquiry getOrGenerateInquiry4Reg(CustomerReg reg) throws Exception;

	ZyInquiry getInquiryByRegId(String  regId) throws Exception;


}
