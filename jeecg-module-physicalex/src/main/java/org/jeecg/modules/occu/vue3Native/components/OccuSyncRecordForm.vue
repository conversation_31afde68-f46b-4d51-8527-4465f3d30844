<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="OccuSyncRecordForm">
          <a-row>
						<a-col :span="24">
							<a-form-item label="接口名称" v-bind="validateInfos.interfaceName" id="OccuSyncRecordForm-interfaceName" name="interfaceName">
								<a-input v-model:value="formData.interfaceName" placeholder="请输入接口名称"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="接口地址" v-bind="validateInfos.interfaceUrl" id="OccuSyncRecordForm-interfaceUrl" name="interfaceUrl">
								<a-input v-model:value="formData.interfaceUrl" placeholder="请输入接口地址"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="业务主键" v-bind="validateInfos.bizId" id="OccuSyncRecordForm-bizId" name="bizId">
								<a-input v-model:value="formData.bizId" placeholder="请输入业务主键"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="业务数据更新时间" v-bind="validateInfos.bizLastTime" id="OccuSyncRecordForm-bizLastTime" name="bizLastTime">
								<a-date-picker placeholder="请选择业务数据更新时间"  v-model:value="formData.bizLastTime" showTime value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="结果状态" v-bind="validateInfos.resultStatus" id="OccuSyncRecordForm-resultStatus" name="resultStatus">
								<a-input v-model:value="formData.resultStatus" placeholder="请输入结果状态"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="结果说明" v-bind="validateInfos.resultMsg" id="OccuSyncRecordForm-resultMsg" name="resultMsg">
								<a-input v-model:value="formData.resultMsg" placeholder="请输入结果说明"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="耗时(毫秒)" v-bind="validateInfos.costMs" id="OccuSyncRecordForm-costMs" name="costMs">
								<a-input-number v-model:value="formData.costMs" placeholder="请输入耗时(毫秒)" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="错误类型" v-bind="validateInfos.errorType" id="OccuSyncRecordForm-errorType" name="errorType">
								<a-input v-model:value="formData.errorType" placeholder="请输入错误类型"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="错误详情" v-bind="validateInfos.errorDetail" id="OccuSyncRecordForm-errorDetail" name="errorDetail">
								<a-input v-model:value="formData.errorDetail" placeholder="请输入错误详情"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="异常堆栈" v-bind="validateInfos.stackTrace" id="OccuSyncRecordForm-stackTrace" name="stackTrace">
								<a-textarea v-model:value="formData.stackTrace" :rows="4" placeholder="请输入异常堆栈" />
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../OccuSyncRecord.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    interfaceName: '',   
    interfaceUrl: '',   
    bizId: '',   
    bizLastTime: '',   
    resultStatus: '',   
    resultMsg: '',   
    costMs: undefined,
    errorType: '',   
    errorDetail: '',   
    stackTrace: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
