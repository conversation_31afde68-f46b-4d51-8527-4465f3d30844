package org.jeecg.modules.occu.mapper;

import java.util.List;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 危害因素必检项目
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
public interface ZyRiskFactorItemgroupMapper extends BaseMapper<ZyRiskFactorItemgroup> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<ZyRiskFactorItemgroup>
   */
	public List<ZyRiskFactorItemgroup> selectByMainId(@Param("mainId") String mainId);

	public List<String> getItemGroupByRiskFactors(@Param("riskFactors")List<String> riskFactors);
}
