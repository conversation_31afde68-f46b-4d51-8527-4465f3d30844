package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyConclusionDict;
import org.jeecg.modules.occu.mapper.ZyConclusionDictMapper;
import org.jeecg.modules.occu.service.IZyConclusionDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 职业检结论字典
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Service
public class ZyConclusionDictServiceImpl extends ServiceImpl<ZyConclusionDictMapper, ZyConclusionDict> implements IZyConclusionDictService {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void increaseUseCount(String id) {
        jdbcTemplate.update("update zy_conclusion_dict set use_count = use_count + 1 where id = ?", id);
    }
}
