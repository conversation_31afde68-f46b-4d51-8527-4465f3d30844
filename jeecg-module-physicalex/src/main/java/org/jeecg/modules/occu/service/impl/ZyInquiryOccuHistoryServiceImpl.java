package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyInquiryOccuHistory;
import org.jeecg.modules.occu.mapper.ZyInquiryOccuHistoryMapper;
import org.jeecg.modules.occu.service.IZyInquiryOccuHistoryService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 职业史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquiryOccuHistoryServiceImpl extends ServiceImpl<ZyInquiryOccuHistoryMapper, ZyInquiryOccuHistory> implements IZyInquiryOccuHistoryService {
	
	@Autowired
	private ZyInquiryOccuHistoryMapper zyInquiryOccuHistoryMapper;
	
	@Override
	public List<ZyInquiryOccuHistory> selectByMainId(String mainId) {
		return zyInquiryOccuHistoryMapper.selectByMainId(mainId);
	}
}
