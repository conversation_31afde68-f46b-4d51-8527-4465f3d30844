package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 危害因素
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
public interface IZyRiskFactorService extends IService<ZyRiskFactor> {

	/**
	 * 添加一对多
	 *
	 * @param zyRiskFactor
	 * @param zyRiskFactorItemgroupList
	 */
	public void saveMain(ZyRiskFactor zyRiskFactor,List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param zyRiskFactor
	 * @param zyRiskFactorItemgroupList
	 */
	public void updateMain(ZyRiskFactor zyRiskFactor,List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
