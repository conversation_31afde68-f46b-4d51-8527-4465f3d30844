package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 职业字典-危害因素必检小项
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Data
@TableName("zy_risk_factor_item")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="zy_risk_factor_item对象", description="职业字典-危害因素必检小项")
public class ZyRiskFactorItem implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**危害因素编码*/
	@Excel(name = "危害因素编码", width = 15)
    @ApiModelProperty(value = "危害因素编码")
    private java.lang.String riskFactorCode;
	/**危害因素名称*/
	@Excel(name = "危害因素名称", width = 15)
    @ApiModelProperty(value = "危害因素名称")
    private java.lang.String riskFactorName;
	/**在岗状态*/
	@Excel(name = "在岗状态", width = 15)
    @ApiModelProperty(value = "在岗状态")
    private java.lang.String postName;
	/**在岗状态编码*/
	@Excel(name = "在岗状态编码", width = 15)
    @ApiModelProperty(value = "在岗状态编码")
    private java.lang.String postCode;
	/**体检项目编码*/
	@Excel(name = "体检项目编码", width = 15)
    @ApiModelProperty(value = "体检项目编码")
    private java.lang.String itemCode;
	/**体检项目名称*/
	@Excel(name = "体检项目名称", width = 15)
    @ApiModelProperty(value = "体检项目名称")
    private java.lang.String itemName;
	/**His项目编码*/
	@Excel(name = "His项目编码", width = 15)
    @ApiModelProperty(value = "His项目编码")
    private java.lang.String hisCode;
	/**His项目名称*/
	@Excel(name = "His项目名称", width = 15)
    @ApiModelProperty(value = "His项目名称")
    private java.lang.String hisName;
	/**匹配状态*/
	@Excel(name = "匹配状态", width = 15)
    @ApiModelProperty(value = "匹配状态")
    private java.lang.String matchStatus;
}
