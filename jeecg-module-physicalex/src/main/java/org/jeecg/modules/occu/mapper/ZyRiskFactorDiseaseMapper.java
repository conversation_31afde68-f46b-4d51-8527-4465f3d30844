package org.jeecg.modules.occu.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZyRiskFactorDisease;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: zy_risk_factor_disease
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
public interface ZyRiskFactorDiseaseMapper extends BaseMapper<ZyRiskFactorDisease> {

   List<ZyRiskFactorDisease> getZyRiskDiseaseByRiskFactors(@Param("riskFactors") Set<String> riskFactors);


}
