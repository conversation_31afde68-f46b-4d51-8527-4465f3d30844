package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.ZyAdviceDict;
import org.jeecg.modules.occu.service.IZyAdviceDictService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 职业处理意见字典
 * @Author: jeecg-boot
 * @Date:   2024-12-04
 * @Version: V1.0
 */
@Api(tags="职业处理意见字典")
@RestController
@RequestMapping("/occu/zyAdviceDict")
@Slf4j
public class ZyAdviceDictController extends JeecgController<ZyAdviceDict, IZyAdviceDictService> {
	@Autowired
	private IZyAdviceDictService zyAdviceDictService;

	 @ApiOperation(value="职业处理意见字典-获取列表", notes="职业处理意见字典-获取列表")
	 @GetMapping(value = "/listZyAdvice")
	 public Result<List<ZyAdviceDict>> listZyAdvice() {
		 List<ZyAdviceDict> list = zyAdviceDictService.getAdviceDict();

		 return Result.OK(list);
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param zyAdviceDict
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "职业处理意见字典-分页列表查询")
	@ApiOperation(value="职业处理意见字典-分页列表查询", notes="职业处理意见字典-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ZyAdviceDict>> queryPageList(ZyAdviceDict zyAdviceDict,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ZyAdviceDict> queryWrapper = QueryGenerator.initQueryWrapper(zyAdviceDict, req.getParameterMap());
		Page<ZyAdviceDict> page = new Page<ZyAdviceDict>(pageNo, pageSize);
		IPage<ZyAdviceDict> pageList = zyAdviceDictService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param zyAdviceDict
	 * @return
	 */
	@AutoLog(value = "职业处理意见字典-添加")
	@ApiOperation(value="职业处理意见字典-添加", notes="职业处理意见字典-添加")
	//@RequiresPermissions("occu:zy_advice_dict:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZyAdviceDict zyAdviceDict) {
		zyAdviceDictService.save(zyAdviceDict);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zyAdviceDict
	 * @return
	 */
	@AutoLog(value = "职业处理意见字典-编辑")
	@ApiOperation(value="职业处理意见字典-编辑", notes="职业处理意见字典-编辑")
	//@RequiresPermissions("occu:zy_advice_dict:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZyAdviceDict zyAdviceDict) {
		zyAdviceDictService.updateById(zyAdviceDict);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "职业处理意见字典-通过id删除")
	@ApiOperation(value="职业处理意见字典-通过id删除", notes="职业处理意见字典-通过id删除")
	//@RequiresPermissions("occu:zy_advice_dict:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zyAdviceDictService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "职业处理意见字典-批量删除")
	@ApiOperation(value="职业处理意见字典-批量删除", notes="职业处理意见字典-批量删除")
	//@RequiresPermissions("occu:zy_advice_dict:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.zyAdviceDictService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "职业处理意见字典-通过id查询")
	@ApiOperation(value="职业处理意见字典-通过id查询", notes="职业处理意见字典-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZyAdviceDict> queryById(@RequestParam(name="id",required=true) String id) {
		ZyAdviceDict zyAdviceDict = zyAdviceDictService.getById(id);
		if(zyAdviceDict==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zyAdviceDict);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zyAdviceDict
    */
    @RequiresPermissions("occu:zy_advice_dict:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyAdviceDict zyAdviceDict) {
        return super.exportXls(request, zyAdviceDict, ZyAdviceDict.class, "职业处理意见字典");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_advice_dict:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyAdviceDict.class);
    }

}
