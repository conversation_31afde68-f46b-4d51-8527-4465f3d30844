package org.jeecg.modules.occu.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZyWorktypeRiskFactor;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 工种危害因素关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-08
 * @Version: V1.0
 */
public interface ZyWorktypeRiskFactorMapper extends BaseMapper<ZyWorktypeRiskFactor> {

    /**
     * 通过工种ID删除关联数据
     *
     * @param worktypeId 工种ID
     * @return boolean
     */
    public boolean deleteByWorktypeId(@Param("worktypeId") String worktypeId);

    /**
     * 通过工种ID查询关联数据
     *
     * @param worktypeId 工种ID
     * @return List<ZyWorktypeRiskFactor>
     */
    public List<ZyWorktypeRiskFactor> selectByWorktypeId(@Param("worktypeId") String worktypeId);
}
