package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyInquiryFamilyHistory;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 职业问诊家族史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface IZyInquiryFamilyHistoryService extends IService<ZyInquiryFamilyHistory> {

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId
   * @return List<ZyInquiryFamilyHistory>
   */
	public List<ZyInquiryFamilyHistory> selectByMainId(String mainId);
}
