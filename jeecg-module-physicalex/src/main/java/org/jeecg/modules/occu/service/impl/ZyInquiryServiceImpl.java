package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.occu.entity.*;
import org.jeecg.modules.occu.mapper.*;
import org.jeecg.modules.occu.service.IZyInquiryService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 职业病问诊
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquiryServiceImpl extends ServiceImpl<ZyInquiryMapper, ZyInquiry> implements IZyInquiryService {

    @Autowired
    private ZyInquiryMapper zyInquiryMapper;
    @Autowired
    private ZyInquiryOccuHistoryMapper zyInquiryOccuHistoryMapper;
    @Autowired
    private ZyInquiryRadiationHistoryMapper zyInquiryRadiationHistoryMapper;
    @Autowired
    private ZyInquiryFamilyHistoryMapper zyInquiryFamilyHistoryMapper;
    @Autowired
    private ZyInquiryMaritalStatusMapper zyInquiryMaritalStatusMapper;
    @Autowired
    private ZyInquirySymptomMapper zyInquirySymptomMapper;
    @Autowired
    private ZyInquiryDiseaseHistoryMapper zyInquiryDiseaseHistoryMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        zyInquiryOccuHistoryMapper.deleteByMainId(id);
        zyInquiryRadiationHistoryMapper.deleteByMainId(id);
        zyInquiryFamilyHistoryMapper.deleteByMainId(id);
        zyInquiryMaritalStatusMapper.deleteByMainId(id);
        zyInquirySymptomMapper.deleteByMainId(id);
        zyInquiryDiseaseHistoryMapper.deleteByMainId(id);
        zyInquiryMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            zyInquiryOccuHistoryMapper.deleteByMainId(id.toString());
            zyInquiryRadiationHistoryMapper.deleteByMainId(id.toString());
            zyInquiryFamilyHistoryMapper.deleteByMainId(id.toString());
            zyInquiryMaritalStatusMapper.deleteByMainId(id.toString());
            zyInquirySymptomMapper.deleteByMainId(id.toString());
            zyInquiryDiseaseHistoryMapper.deleteByMainId(id.toString());
            zyInquiryMapper.deleteById(id);
        }
    }

    @Override
    public ZyInquiry getByRegId(String customerRegId) {
        //根据customer_reg_id获取一个
        LambdaQueryWrapper<ZyInquiry> query = new LambdaQueryWrapper<ZyInquiry>();
        query.eq(ZyInquiry::getCustomerRegId, customerRegId);
        query.last("limit 1");

        return zyInquiryMapper.selectOne(query);
    }

    @Override
    public ZyInquiry getOrGenerateInquiry4Reg(CustomerReg reg) throws Exception {
        if (reg == null) {
            throw new Exception("体检登记信息为空");
        }
        LambdaQueryWrapper<ZyInquiry> query = new LambdaQueryWrapper<ZyInquiry>();
        query.eq(ZyInquiry::getCustomerRegId, reg.getId());
        query.last("limit 1");

        ZyInquiry zyInquiry = zyInquiryMapper.selectOne(query);
        if (zyInquiry == null) {
            zyInquiry = new ZyInquiry();
            zyInquiry.setCustomerRegId(reg.getId());
            zyInquiry.setName(reg.getName());
            zyInquiry.setGender(reg.getGender());
            zyInquiry.setAge(String.valueOf(reg.getAge()));
            zyInquiry.setCustomerRegId(reg.getId());

            zyInquiryMapper.insert(zyInquiry);
        }

        return zyInquiry;
    }

    @Override
    public ZyInquiry getInquiryByRegId(String regId) throws Exception {
        ZyInquiry zyInquiry = zyInquiryMapper.selectOne(new LambdaQueryWrapper<ZyInquiry>().eq(ZyInquiry::getCustomerRegId, regId).last("limit 1"));
        if (Objects.nonNull(zyInquiry)) {
            ZyInquiryOccuHistory inquiryOccuHistory = zyInquiryOccuHistoryMapper.selectOne(new LambdaQueryWrapper<ZyInquiryOccuHistory>().eq(ZyInquiryOccuHistory::getInquiryId, zyInquiry.getId()).last("limit 1"));
            zyInquiry.setZyInquiryOccuHistory(inquiryOccuHistory);
            ZyInquiryDiseaseHistory inquiryDiseaseHistory = zyInquiryDiseaseHistoryMapper.selectOne(new LambdaQueryWrapper<ZyInquiryDiseaseHistory>().eq(ZyInquiryDiseaseHistory::getInquiryId, zyInquiry.getId()).last("limit 1"));
            zyInquiry.setZyInquiryDiseaseHistory(inquiryDiseaseHistory);
            ZyInquiryMaritalStatus inquiryMaritalStatus = zyInquiryMaritalStatusMapper.selectOne(new LambdaQueryWrapper<ZyInquiryMaritalStatus>().eq(ZyInquiryMaritalStatus::getInquiryId, zyInquiry.getId()).last("limit 1"));
            zyInquiry.setZyInquiryMaritalStatus(inquiryMaritalStatus);
            ZyInquiryFamilyHistory inquiryFamilyHistory = zyInquiryFamilyHistoryMapper.selectOne(new LambdaQueryWrapper<ZyInquiryFamilyHistory>().eq(ZyInquiryFamilyHistory::getInquiryId, zyInquiry.getId()).last("limit 1"));
            zyInquiry.setZyInquiryFamilyHistory(inquiryFamilyHistory);
            List<ZyInquirySymptom> zyInquirySymptoms = zyInquirySymptomMapper.selectSymptomListByInquiryId( zyInquiry.getId());
            zyInquiry.setZyInquirySymptomList(zyInquirySymptoms);
        }
        return zyInquiry;
    }
}
