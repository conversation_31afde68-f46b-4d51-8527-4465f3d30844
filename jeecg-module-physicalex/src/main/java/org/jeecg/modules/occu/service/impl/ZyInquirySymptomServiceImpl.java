package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.occu.entity.ZyInquirySymptom;
import org.jeecg.modules.occu.mapper.ZyInquirySymptomMapper;
import org.jeecg.modules.occu.service.IZyInquirySymptomService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 职业问诊现有症状
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquirySymptomServiceImpl extends ServiceImpl<ZyInquirySymptomMapper, ZyInquirySymptom> implements IZyInquirySymptomService {
	
	@Autowired
	private ZyInquirySymptomMapper zyInquirySymptomMapper;
	
	@Override
	public List<ZyInquirySymptom> selectByMainId(String mainId) {
		return zyInquirySymptomMapper.selectByMainId(mainId);
	}
}
