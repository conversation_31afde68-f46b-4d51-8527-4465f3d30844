package org.jeecg.modules.followup.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.followup.bo.CustomerRegFollowUp;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.followup.entity.FollowUpRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 随访记录
 * @Author: jeecg-boot
 * @Date: 2024-08-19
 * @Version: V1.0
 */
public interface IFollowUpRecordService extends IService<FollowUpRecord> {

    void pageCustomerReg(Page<CustomerReg> page, String examCatory, String name, String gender, String idCard, String phone, String dateStart, String dateEnd, String examNo, String examCardNo, String companyRegId, String teamId, String fellowupStatus, String sortOrder, String followupDateStart, String followupDateEnd);

    List<CustomerReg> listCustomerReg(String examCatory, String name, String gender, String idCard, String phone, String dateStart, String dateEnd, String examNo, String examCardNo, String companyRegId, String teamId, String fellowupStatus, String sortOrder, String followupDateStart, String followupDateEnd);

    void setPersonalQuestId(String id, String personalQuestId);

    ModelAndView export(HttpServletRequest request);

    List<FollowUpRecord> listByCustomerRegId(String customerRegId,String startDate,String endDate);

}
