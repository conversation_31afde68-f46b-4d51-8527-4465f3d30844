<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.followup.mapper.FollowUpRecordMapper">

    <select id="selectCustomerReg" resultType="org.jeecg.modules.reg.entity.CustomerReg"
            parameterType="java.lang.String">
        select reg.*
        from customer_reg reg
        <where>
            reg.del_flag = 0 and summary_status = '审核通过'
            <if test="name!=null">and reg.name  like concat('%',#{name},'%')</if>
            <if test="gender!=null">and reg.gender = #{gender}</if>
            <if test="phone!=null">and reg.phone = #{phone}</if>
            <if test="idCard!=null">and reg.id_card = #{idCard}</if>
            <if test="regDateStart!=null">and reg.reg_time &gt;= #{regDateStart}</if>
            <if test="regDateEnd!=null">and reg.reg_time &lt;= #{regDateEnd}</if>
            <if test="examNo!=null">and reg.exam_no like concat('%',#{examNo},'%')</if>
            <if test="examCardNo!=null">and reg.exam_card_no = #{examCardNo}</if>
            <if test="companyRegId!=null">and reg.company_reg_id = #{companyRegId}</if>
            <if test="teamId!=null">and reg.team_id = #{teamId}</if>
            <if test="examCatory!=null">and reg.exam_category = #{examCatory}</if>
            <if test="fellowupStatus!=null">and reg.fellowup_status = #{fellowupStatus}</if>
            <if test="followupDateStart!=null and followupDateEnd!=null">
               and exists (select 1 from follow_up_record follow where follow.customer_reg_id = reg.id <if test="followupDateStart!=null"> and follow.create_time &gt;=  #{followupDateStart}</if> <if test="followupDateEnd!=null"> and follow.create_time &lt;= #{followupDateEnd}</if>)
            </if>
        </where>
        <choose>
            <when test="sortOrder!=null">
                order by reg.reg_time ${sortOrder}
            </when>
            <otherwise>
                order by reg.create_time desc
            </otherwise>
        </choose>
    </select>
</mapper>