package org.jeecg.modules.followup.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.followup.entity.FollowUpRecord;
import org.jeecg.modules.followup.service.IFollowUpRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 随访记录
 * @Author: jeecg-boot
 * @Date: 2024-08-19
 * @Version: V1.0
 */
@Api(tags = "随访记录")
@RestController
@RequestMapping("/reg/followUpRecord")
@Slf4j
public class FollowUpRecordController extends JeecgController<FollowUpRecord, IFollowUpRecordService> {
    @Autowired
    private IFollowUpRecordService followUpRecordService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "客户登记-分页列表查询", notes = "客户登记-分页列表查询")
    @GetMapping(value = "/listReg")
    public Result<IPage<CustomerReg>> listReg(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);

        String status = StringUtils.trimToNull(req.getParameter("status"));
        String qualified = StringUtils.trimToNull(req.getParameter("qualified"));
        String examCatory = StringUtils.trimToNull(req.getParameter("examCatory"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String gender = StringUtils.trimToNull(req.getParameter("gender"));
        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String phone = StringUtils.trimToNull(req.getParameter("phone"));
        String dateStart = StringUtils.trimToNull(req.getParameter("dateStart"));
        String dateEnd = StringUtils.trimToNull(req.getParameter("dateEnd"));


        String followupDateStart = StringUtils.trimToNull(req.getParameter("followupDateStart"));
        String followupDateEnd = StringUtils.trimToNull(req.getParameter("followupDateEnd"));

        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String examCardNo = StringUtils.trimToNull(req.getParameter("examCardNo"));
        String checkState = StringUtils.trimToNull(req.getParameter("checkState"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String printStatus = StringUtils.trimToNull(req.getParameter("printStatus"));
        String doctorType = StringUtils.trimToNull(req.getParameter("doctorType"));
        String doctor = StringUtils.trimToNull(req.getParameter("doctor"));
        String sortOrder = StringUtils.trimToNull(req.getParameter("sortOrder"));
        String filterStatus = StringUtils.trimToNull(req.getParameter("filterStatus"));
        String dateType = StringUtils.trimToNull(req.getParameter("dateType"));

        followUpRecordService.pageCustomerReg(page, examCatory, name, gender, idCard, phone, dateStart, dateEnd, examNo, examCardNo, companyRegId, teamId, status, sortOrder, followupDateStart, followupDateEnd);
        return Result.OK(page);
    }

    /**
     * 分页列表查询
     *
     * @param followUpRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "随访记录-分页列表查询")
    @ApiOperation(value = "随访记录-分页列表查询", notes = "随访记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<FollowUpRecord>> queryPageList(FollowUpRecord followUpRecord, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<FollowUpRecord> queryWrapper = QueryGenerator.initQueryWrapper(followUpRecord, req.getParameterMap());
        Page<FollowUpRecord> page = new Page<FollowUpRecord>(pageNo, pageSize);
        IPage<FollowUpRecord> pageList = followUpRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param followUpRecord
     * @return
     */
    @AutoLog(value = "随访记录-添加")
    @ApiOperation(value = "随访记录-添加", notes = "随访记录-添加")
    @RequiresPermissions("reg:follow_up_record:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody FollowUpRecord followUpRecord) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        followUpRecord.setCreator(sysUser.getRealname());
        followUpRecordService.save(followUpRecord);
        jdbcTemplate.update("update customer_reg set fellowup_status='已回访' where id=?", followUpRecord.getCustomerRegId());
        return Result.OK("添加成功！", followUpRecord.getId());
    }

    /**
     * 编辑
     *
     * @param followUpRecord
     * @return
     */
    @AutoLog(value = "随访记录-编辑")
    @ApiOperation(value = "随访记录-编辑", notes = "随访记录-编辑")
    @RequiresPermissions("reg:follow_up_record:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody FollowUpRecord followUpRecord) {
        followUpRecordService.updateById(followUpRecord);
        jdbcTemplate.update("update customer_reg set fellowup_status='已回访' where id=?", followUpRecord.getCustomerRegId());
        return Result.OK("编辑成功!", followUpRecord.getId());
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "随访记录-通过id删除")
    @ApiOperation(value = "随访记录-通过id删除", notes = "随访记录-通过id删除")
    @RequiresPermissions("reg:follow_up_record:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        FollowUpRecord followUpRecord = followUpRecordService.getById(id);
        if (followUpRecord == null) {
            return Result.error("未找到对应数据");
        }

        // 删除随访记录
        followUpRecordService.removeById(id);

        // 检查是否所有关联的随访记录都被删除了
        String customerRegId = followUpRecord.getCustomerRegId();
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM follow_up_record WHERE customer_reg_id = ?", Integer.class, customerRegId);

        // 如果没有关联的随访记录，更新customer_reg的fellowup_status为未回访
        if (count == 0) {
            jdbcTemplate.update("UPDATE customer_reg SET fellowup_status = '未回访' WHERE id = ?", customerRegId);
        }

        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "随访记录-批量删除")
    @ApiOperation(value = "随访记录-批量删除", notes = "随访记录-批量删除")
    @RequiresPermissions("reg:follow_up_record:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        if (idList.isEmpty()) {
            return Result.error("请选择要删除的记录！");
        }
        // 获取关联的 customerRegId
        String firstId = idList.get(0);
        FollowUpRecord followUpRecord = followUpRecordService.getById(firstId);
        this.followUpRecordService.removeByIds(idList);
        if (followUpRecord == null) {
            return Result.error("未找到对应数据");
        }
        // 检查是否所有关联的随访记录都被删除了
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM follow_up_record WHERE customer_reg_id = ?", Integer.class, followUpRecord.getCustomerRegId());
        // 如果没有关联的随访记录，更新 customer_reg 的 fellowup_status 为未回访
        if (count == 0) {
            jdbcTemplate.update("UPDATE customer_reg SET fellowup_status = '未回访' WHERE id = ?", followUpRecord.getCustomerRegId());
        }
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "随访记录-通过id查询")
    @ApiOperation(value = "随访记录-通过id查询", notes = "随访记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<FollowUpRecord> queryById(@RequestParam(name = "id", required = true) String id) {
        FollowUpRecord followUpRecord = followUpRecordService.getById(id);
        if (followUpRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(followUpRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param followUpRecord
     */
    @RequiresPermissions("reg:follow_up_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, FollowUpRecord followUpRecord) {
        return super.exportXls(request, followUpRecord, FollowUpRecord.class, "随访记录");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("reg:follow_up_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, FollowUpRecord.class);
    }

    /**
     * 导出excel
     *
     * @param request
     */
    @RequestMapping(value = "/exportCustomerRegFollowUp")
    public ModelAndView exportCustomerRegFollowUp(HttpServletRequest request) {

        return followUpRecordService.export(request);
    }

    //setPersonalQuestId
    @ApiOperation(value = "随访记录-设置个人问卷ID", notes = "随访记录-设置个人问卷ID")
    @GetMapping(value = "/setPersonalQuestId")
    public Result<?> setPersonalQuestId(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "personalQuestId", required = true) String personalQuestId) {
        followUpRecordService.setPersonalQuestId(id, personalQuestId);
        return Result.OK("设置成功!");
    }

}
