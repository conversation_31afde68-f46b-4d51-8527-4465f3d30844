package org.jeecg.modules.followup.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.followup.entity.FollowUpRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * @Description: 随访记录
 * @Author: jeecg-boot
 * @Date: 2024-08-19
 * @Version: V1.0
 */
public interface FollowUpRecordMapper extends BaseMapper<FollowUpRecord> {

    Page<CustomerReg> selectCustomerReg(Page<CustomerReg> page, @Param("examCatory") String examCatory, @Param("name") String name, @Param("gender") String gender, @Param("idCard") String idCard, @Param("phone") String phone, @Param("regDateStart") String regDateStart, @Param("regDateEnd") String regDateEnd, @Param("examNo") String examNo, @Param("examCardNo") String examCardNo, @Param("companyRegId") String companyRegId, @Param("teamId") String teamId, @Param("fellowupStatus") String fellowupStatus, @Param("sortOrder") String sortOrder, @Param("followupDateStart") String followupDateStart, @Param("followupDateEnd") String followupDateEnd);


    List<CustomerReg> selectCustomerReg(@Param("examCatory") String examCatory, @Param("name") String name, @Param("gender") String gender, @Param("idCard") String idCard, @Param("phone") String phone, @Param("regDateStart") String regDateStart, @Param("regDateEnd") String regDateEnd, @Param("examNo") String examNo, @Param("examCardNo") String examCardNo, @Param("companyRegId") String companyRegId, @Param("teamId") String teamId, @Param("fellowupStatus") String fellowupStatus, @Param("sortOrder") String sortOrder, @Param("followupDateStart") String followupDateStart, @Param("followupDateEnd") String followupDateEnd);
}
