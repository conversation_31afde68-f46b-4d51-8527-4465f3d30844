package org.jeecg.modules.followup.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.followup.entity.FollowUpRecord;
import org.jeecg.modules.quest.entity.PersonalQuestContent;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;

import java.util.List;

@Data
public class CustomerRegFollowUp {

    /**
     * 姓名
     */
    @Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
    @Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;
    @ApiModelProperty(value = "回访状态")
    private String fellowupStatus;
    @ExcelEntity(name = "随访记录")
    @ApiModelProperty(value = "随访记录")
    private FollowUpRecord followUpRecord;
    @ExcelCollection(name = "问卷内容")
    @ApiModelProperty(value = "问卷内容")
    List<PersonalQuestContent> personalQuestContentList;
}
