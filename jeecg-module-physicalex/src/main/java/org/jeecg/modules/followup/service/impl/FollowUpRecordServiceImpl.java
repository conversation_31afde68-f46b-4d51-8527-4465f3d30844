package org.jeecg.modules.followup.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.followup.bo.CustomerRegFollowUp;
import org.jeecg.modules.quest.entity.PersonalQuestContent;
import org.jeecg.modules.quest.mapper.PersonalQuestContentMapper;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.followup.entity.FollowUpRecord;
import org.jeecg.modules.followup.mapper.FollowUpRecordMapper;
import org.jeecg.modules.followup.service.IFollowUpRecordService;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 随访记录
 * @Author: jeecg-boot
 * @Date: 2024-08-19
 * @Version: V1.0
 */
@Service
public class FollowUpRecordServiceImpl extends ServiceImpl<FollowUpRecordMapper, FollowUpRecord> implements IFollowUpRecordService {
    @Autowired
    private FollowUpRecordMapper followUpRecordMapper;
    @Autowired
    private PersonalQuestContentMapper personalQuestContentMapper;

    @Override
    public void pageCustomerReg(Page<CustomerReg> page, String examCatory, String name, String gender, String idCard, String phone, String dateStart, String dateEnd, String examNo, String examCardNo, String companyRegId, String teamId, String fellowupStatus, String sortOrder, String followupDateStart, String followupDateEnd) {
        String regDateSort = "desc";
        if (StringUtils.equals(sortOrder, "降序")) {
            regDateSort = "asc";
        } else if (StringUtils.equals(sortOrder, "升序")) {
            regDateSort = "desc";
        }
        followUpRecordMapper.selectCustomerReg(page, examCatory, name, gender, idCard, phone, dateStart, dateEnd, examNo, examCardNo, companyRegId, teamId, fellowupStatus, regDateSort, followupDateStart, followupDateEnd);
    }

    @Override
    public List<CustomerReg> listCustomerReg(String examCatory, String name, String gender, String idCard, String phone, String dateStart, String dateEnd, String examNo, String examCardNo, String companyRegId, String teamId, String fellowupStatus, String sortOrder, String followupDateStart, String followupDateEnd) {
        return followUpRecordMapper.selectCustomerReg(examCatory, name, gender, idCard, phone, dateStart, dateEnd, examNo, examCardNo, companyRegId, teamId, fellowupStatus, null, followupDateStart, followupDateEnd);
    }

    @Override
    public void setPersonalQuestId(String id, String personalQuestId) {
        LambdaUpdateWrapper<FollowUpRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FollowUpRecord::getId, id);
        updateWrapper.set(FollowUpRecord::getPersonalQuestId, personalQuestId);
        followUpRecordMapper.update(null, updateWrapper);
    }


    @Override
    public ModelAndView export(HttpServletRequest request) {

        List<CustomerRegFollowUp> exportList = new ArrayList<>();
        String secondTitle = "";

        exportList = getExportData(request);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        secondTitle = "导出人:" + sysUser.getRealname();


        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "随访记录");
        mv.addObject(NormalExcelConstants.CLASS, CustomerRegFollowUp.class);
        //update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
        ExportParams exportParams = new ExportParams("随访记录", secondTitle, "随访记录");

        //update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    @Override
    public List<FollowUpRecord> listByCustomerRegId(String customerRegId, String startDate, String endDate) {

        LambdaQueryWrapper<FollowUpRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FollowUpRecord::getCustomerRegId, customerRegId);
        if (StringUtils.isNotBlank(startDate)) {
            queryWrapper.ge(FollowUpRecord::getCreateTime, startDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            queryWrapper.le(FollowUpRecord::getCreateTime, endDate);
        }
        return followUpRecordMapper.selectList(queryWrapper);
    }

    public List<CustomerRegFollowUp> getExportData(HttpServletRequest req) {
        String status = StringUtils.trimToNull(req.getParameter("status"));
        String examCatory = StringUtils.trimToNull(req.getParameter("examCatory"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String gender = StringUtils.trimToNull(req.getParameter("gender"));
        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String phone = StringUtils.trimToNull(req.getParameter("phone"));
        String dateStart = StringUtils.trimToNull(req.getParameter("dateStart"));
        String dateEnd = StringUtils.trimToNull(req.getParameter("dateEnd"));

        String followupDateStart = StringUtils.trimToNull(req.getParameter("followupDateStart"));
        String followupDateEnd = StringUtils.trimToNull(req.getParameter("followupDateEnd"));

        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String examCardNo = StringUtils.trimToNull(req.getParameter("examCardNo"));

        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));

        String sortOrder = StringUtils.trimToNull(req.getParameter("sortOrder"));


        List<CustomerReg> customerRegList = listCustomerReg(examCatory, name, gender, idCard, phone, dateStart, dateEnd, examNo, examCardNo, companyRegId, teamId, status, sortOrder, followupDateStart, followupDateEnd);

        List<CustomerRegFollowUp> customerRegFollowUpList = new ArrayList<>();
        for (CustomerReg customerReg : customerRegList) {
            List<FollowUpRecord> followUpRecordList = listByCustomerRegId(customerReg.getId(), followupDateStart, followupDateEnd);
            for (FollowUpRecord followUpRecord : followUpRecordList) {
                CustomerRegFollowUp customerRegFollowUp = new CustomerRegFollowUp();
                customerRegFollowUp.setName(customerReg.getName());
                customerRegFollowUp.setExamNo(customerReg.getExamNo());
                customerRegFollowUp.setFellowupStatus(customerReg.getFellowupStatus());
                customerRegFollowUp.setFollowUpRecord(followUpRecord);
                List<PersonalQuestContent> questContents = personalQuestContentMapper.selectByPersonalQuestId(followUpRecord.getPersonalQuestId());
                customerRegFollowUp.setPersonalQuestContentList(questContents);

                customerRegFollowUpList.add(customerRegFollowUp);
            }
        }

        return customerRegFollowUpList;
    }
}
