package org.jeecg.modules.followup.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.modules.quest.entity.PersonalQuestContent;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 随访记录
 * @Author: jeecg-boot
 * @Date:   2024-08-19
 * @Version: V1.0
 */
@Data
@TableName("follow_up_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="follow_up_record对象", description="随访记录")
public class FollowUpRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人账号*/
    @ApiModelProperty(value = "创建人账号")
    private java.lang.String createBy;
	/**创建人*/
	@Excel(name = "回访员工", width = 15)
    @ApiModelProperty(value = "回访员工")
    private java.lang.String creator;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**登记档案ID*/
	//@Excel(name = "登记档案ID", width = 15)
    @ApiModelProperty(value = "登记档案ID")
    private java.lang.String customerRegId;
    /**动作*/
    //@Excel(name = "回访方式", width = 15)
    @ApiModelProperty(value = "回访方式")
    private java.lang.String action;
    /**回访状态*/
    @Excel(name = "回访状态", width = 15)
    @ApiModelProperty(value = "回访状态")
    private String status;
    /**回访方式*/
    @Excel(name = "回访方式", width = 15)
    @ApiModelProperty(value = "回访方式")
    private java.lang.String actionMethod;
    /**内容*/
    @Excel(name = "回访备注", width = 15)
    @ApiModelProperty(value = "回访备注")
    private java.lang.String content;
	/**备注*/
	//@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;

    /**随访问卷ID*/
    //@Excel(name = "随访问卷ID", width = 15)
    @ApiModelProperty(value = "随访问卷ID")
    private String questId;
    /**随访答卷ID*/
    //@Excel(name = "随访答卷ID", width = 15)
    @ApiModelProperty(value = "随访答卷ID")
    private String personalQuestId;

}
