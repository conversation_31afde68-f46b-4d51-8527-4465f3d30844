package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.basicinfo.entity.CheckPartUsageStat;
import org.jeecg.modules.basicinfo.mapper.CheckPartUsageStatMapper;
import org.jeecg.modules.basicinfo.service.ICheckPartUsageStatService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description: 检查部位使用统计
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class CheckPartUsageStatServiceImpl extends ServiceImpl<CheckPartUsageStatMapper, CheckPartUsageStat> implements ICheckPartUsageStatService {

    @Override
    public void incrementUsage(String itemGroupId, String checkPartId) {
        try {
            // 查询是否已存在记录
            LambdaQueryWrapper<CheckPartUsageStat> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CheckPartUsageStat::getItemGroupId, itemGroupId)
                       .eq(CheckPartUsageStat::getCheckPartId, checkPartId);
            
            CheckPartUsageStat existingStat = getOne(queryWrapper);
            
            if (existingStat != null) {
                // 更新现有记录
                LambdaUpdateWrapper<CheckPartUsageStat> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(CheckPartUsageStat::getId, existingStat.getId())
                           .setSql("usage_count = usage_count + 1")
                           .set(CheckPartUsageStat::getLastUsedTime, new Date())
                           .set(CheckPartUsageStat::getUpdateTime, new Date());
                
                update(updateWrapper);
            } else {
                // 创建新记录
                CheckPartUsageStat newStat = new CheckPartUsageStat();
                newStat.setItemGroupId(itemGroupId);
                newStat.setCheckPartId(checkPartId);
                newStat.setUsageCount(1L);
                newStat.setLastUsedTime(new Date());
                newStat.setCreateTime(new Date());
                newStat.setUpdateTime(new Date());
                
                save(newStat);
            }
            
            log.debug("Incremented usage for itemGroup: {}, checkPart: {}", itemGroupId, checkPartId);
        } catch (Exception e) {
            log.error("Failed to increment usage for itemGroup: {}, checkPart: {}", itemGroupId, checkPartId, e);
        }
    }

    @Override
    public List<CheckPartUsageStat> listByItemGroupId(String itemGroupId) {
        LambdaQueryWrapper<CheckPartUsageStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CheckPartUsageStat::getItemGroupId, itemGroupId)
                   .orderByDesc(CheckPartUsageStat::getUsageCount)
                   .orderByDesc(CheckPartUsageStat::getLastUsedTime);
        
        return list(queryWrapper);
    }
}
