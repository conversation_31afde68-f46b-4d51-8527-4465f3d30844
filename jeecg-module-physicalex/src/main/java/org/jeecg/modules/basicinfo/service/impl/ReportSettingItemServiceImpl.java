package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.ReportSettingItem;
import org.jeecg.modules.basicinfo.mapper.ReportSettingItemMapper;
import org.jeecg.modules.basicinfo.service.IReportSettingItemService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 报告设置条目
 * @Author: jeecg-boot
 * @Date:   2024-07-25
 * @Version: V1.0
 */
@Service
public class ReportSettingItemServiceImpl extends ServiceImpl<ReportSettingItemMapper, ReportSettingItem> implements IReportSettingItemService {
	
	@Autowired
	private ReportSettingItemMapper reportSettingItemMapper;
	
	@Override
	public List<ReportSettingItem> selectByMainId(String mainId) {
		return reportSettingItemMapper.selectByMainId(mainId);
	}
}
