package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: itemsuit_item
 * @Author: jeecg-boot
 * @Date:   2024-02-03
 * @Version: V1.0
 */
@Data
@TableName("suit_group")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="itemsuit_item对象", description="itemsuit_item")
public class SuitGroup implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**suitId*/
    @Excel(name = "suitName", width = 15)
    @ApiModelProperty(value = "suitName")
    private java.lang.String suitName;
	/**suitId*/
	@Excel(name = "suitId", width = 15)
    @ApiModelProperty(value = "suitId")
    private java.lang.String suitId;
	/**type*/
	@Excel(name = "type", width = 15)
    @ApiModelProperty(value = "type")
    private java.lang.String type;
    /**groupName*/
    @Excel(name = "groupName", width = 15)
    @ApiModelProperty(value = "groupName")
    private java.lang.String groupName;
	/**groupId*/
	@Excel(name = "groupId", width = 15)
    @ApiModelProperty(value = "groupId")
    private java.lang.String groupId;
    @Excel(name = "departmentId", width = 15)
    @ApiModelProperty(value = "departmentId")
    private java.lang.String departmentId;
    @Excel(name = "departmentName", width = 15)
    @ApiModelProperty(value = "departmentName")
    private java.lang.String departmentName;
	/**套餐折扣*/
	@Excel(name = "套餐折扣", width = 15)
    @ApiModelProperty(value = "套餐折扣")
    private java.lang.String disRate;
	/**价格*/
	@Excel(name = "价格", width = 15)
    @ApiModelProperty(value = "价格")
    private java.math.BigDecimal price;
	/**折后价格*/
	@Excel(name = "折后价格", width = 15)
    @ApiModelProperty(value = "折后价格")
    private java.math.BigDecimal priceAfterDis;
    /**因折扣产生的差额*/
    @Excel(name = "因折扣产生的差额", width = 15)
    @ApiModelProperty(value = "因折扣产生的差额")
    private BigDecimal priceDisDiffAmount;
    /**组合的最小折扣率*/
    @Excel(name = "组合的最小折扣率", width = 15)
    @ApiModelProperty(value = "组合的最小折扣率")
    private BigDecimal minDiscountRate;

    /**检查部位ID*/
    @Excel(name = "检查部位ID", width = 15)
    @ApiModelProperty(value = "检查部位ID")
    private java.lang.String checkPartId;

    /**检查部位名称*/
    @Excel(name = "检查部位名称", width = 15)
    @ApiModelProperty(value = "检查部位名称")
    private java.lang.String checkPartName;

    /**检查部位编码*/
    @Excel(name = "检查部位编码", width = 15)
    @ApiModelProperty(value = "检查部位编码")
    private java.lang.String checkPartCode;

    /**是否需要部位选择*/
    @TableField(exist = false)
    @JSONField(deserialize = false)
    @ApiModelProperty(value = "是否需要部位选择")
    private java.lang.String hasCheckPart;

    @TableField(exist = false)
    @JSONField(deserialize = false)
    private ItemGroup group;
}
