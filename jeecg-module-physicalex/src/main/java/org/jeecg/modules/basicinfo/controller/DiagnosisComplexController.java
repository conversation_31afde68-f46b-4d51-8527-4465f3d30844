package org.jeecg.modules.basicinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplex;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplexItemRule;
import org.jeecg.modules.basicinfo.service.IDiagnosisComplexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 符合判断
 * @Author: jeecg-boot
 * @Date:   2024-04-26
 * @Version: V1.0
 */
@Api(tags="符合判断")
@RestController
@RequestMapping("/basicinfo/diagnosisComplex")
@Slf4j
public class DiagnosisComplexController extends JeecgController<DiagnosisComplex, IDiagnosisComplexService> {

	@Autowired
	private IDiagnosisComplexService diagnosisComplexService;

	/*---------------------------------主表处理-begin-------------------------------------*/

	/**
	 * 分页列表查询
	 * @param diagnosisComplex
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "符合判断-分页列表查询")
	@ApiOperation(value="符合判断-分页列表查询", notes="符合判断-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<DiagnosisComplex>> queryPageList(DiagnosisComplex diagnosisComplex,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		Page<DiagnosisComplex> page = new Page<DiagnosisComplex>(pageNo, pageSize);
        diagnosisComplexService.pageDiagnosisComplex(page, diagnosisComplex.getDepartmentId(), diagnosisComplex.getName(), diagnosisComplex.getEnableFlag());
		return Result.OK(page);
	}

	/**
     *   添加
     * @param diagnosisComplex
     * @return
     */
    @AutoLog(value = "符合判断-添加")
    @ApiOperation(value="符合判断-添加", notes="符合判断-添加")
    @RequiresPermissions("basicinfo:diagnosis_complex:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody DiagnosisComplex diagnosisComplex) {
        List<DiagnosisComplexItemRule> ruleList = diagnosisComplex.getItemRules();
        diagnosisComplexService.saveMain(diagnosisComplex,ruleList);
        return Result.OK("添加成功！");
    }
    /**
     *  编辑
     * @param diagnosisComplex
     * @return
     */
    @AutoLog(value = "符合判断-编辑")
    @ApiOperation(value="符合判断-编辑", notes="符合判断-编辑")
    @RequiresPermissions("basicinfo:diagnosis_complex:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
    public Result<String> edit(@RequestBody DiagnosisComplex diagnosisComplex) {
        List<DiagnosisComplexItemRule> ruleList = diagnosisComplex.getItemRules();
        diagnosisComplexService.updateMain(diagnosisComplex,ruleList);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     * @param id
     * @return
     */
    @AutoLog(value = "符合判断-通过id删除")
    @ApiOperation(value="符合判断-通过id删除", notes="符合判断-通过id删除")
    @RequiresPermissions("basicinfo:diagnosis_complex:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        diagnosisComplexService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @AutoLog(value = "符合判断-批量删除")
    @ApiOperation(value="符合判断-批量删除", notes="符合判断-批量删除")
    @RequiresPermissions("basicinfo:diagnosis_complex:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.diagnosisComplexService.removeBatchByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     * @return
     */
    @RequiresPermissions("basicinfo:diagnosis_complex:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DiagnosisComplex diagnosisComplex) {
        return super.exportXls(request, diagnosisComplex, DiagnosisComplex.class, "符合判断");
    }

    /**
     * 导入
     * @return
     */
    @RequiresPermissions("basicinfo:diagnosis_complex:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DiagnosisComplex.class);
    }
	/*---------------------------------主表处理-end-------------------------------------*/



}
