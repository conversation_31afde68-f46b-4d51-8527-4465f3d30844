package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.GroupChangeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 项目价格变动操作记录表
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
public interface GroupChangeRecordMapper extends BaseMapper<GroupChangeRecord> {

}
