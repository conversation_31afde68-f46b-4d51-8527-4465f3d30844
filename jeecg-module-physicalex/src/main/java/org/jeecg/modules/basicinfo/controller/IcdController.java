package org.jeecg.modules.basicinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.Icd;
import org.jeecg.modules.basicinfo.service.IIcdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: icd
 * @Author: jeecg-boot
 * @Date: 2025-05-08
 * @Version: V1.0
 */
@Api(tags = "icd")
@RestController
@RequestMapping("/basicinfo/icd")
@Slf4j
public class IcdController extends JeecgController<Icd, IIcdService> {
    @Autowired
    private IIcdService icdService;

    /**
     * 分页列表查询
     *
     * @param req
     * @return
     */
    //@AutoLog(value = "icd-分页列表查询")
    @ApiOperation(value = "icd-根据关键字查询", notes = "icd-根据关键字查询")
    @GetMapping(value = "/listByKeyword")
    public Result<IPage<Icd>> queryPageList(HttpServletRequest req) {
        String keyword = req.getParameter("keyword");
        int pageNo = Integer.parseInt(req.getParameter("pageNo"));
        int pageSize = Integer.parseInt(req.getParameter("pageSize"));
        Page<Icd> page = new Page<>(pageNo, pageSize);
        icdService.listByKeyword(page, keyword);

        return Result.OK(page);
    }


    /**
     * 分页列表查询
     *
     * @param icd
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "icd-分页列表查询")
    @ApiOperation(value = "icd-分页列表查询", notes = "icd-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Icd>> queryPageList(Icd icd, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<Icd> queryWrapper = QueryGenerator.initQueryWrapper(icd, req.getParameterMap());
        Page<Icd> page = new Page<Icd>(pageNo, pageSize);
        IPage<Icd> pageList = icdService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param icd
     * @return
     */
    @AutoLog(value = "icd-添加")
    @ApiOperation(value = "icd-添加", notes = "icd-添加")
    @RequiresPermissions("basicinfo:icd:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody Icd icd) {
        icdService.save(icd);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param icd
     * @return
     */
    @AutoLog(value = "icd-编辑")
    @ApiOperation(value = "icd-编辑", notes = "icd-编辑")
    @RequiresPermissions("basicinfo:icd:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody Icd icd) {
        icdService.updateById(icd);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "icd-通过id删除")
    @ApiOperation(value = "icd-通过id删除", notes = "icd-通过id删除")
    @RequiresPermissions("basicinfo:icd:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        icdService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "icd-批量删除")
    @ApiOperation(value = "icd-批量删除", notes = "icd-批量删除")
    @RequiresPermissions("basicinfo:icd:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.icdService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "icd-通过id查询")
    @ApiOperation(value = "icd-通过id查询", notes = "icd-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Icd> queryById(@RequestParam(name = "id", required = true) String id) {
        Icd icd = icdService.getById(id);
        if (icd == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(icd);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param icd
     */
    @RequiresPermissions("basicinfo:icd:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Icd icd) {
        return super.exportXls(request, icd, Icd.class, "icd");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:icd:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Icd.class);
    }

}
