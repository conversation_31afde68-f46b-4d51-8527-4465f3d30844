package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.basicinfo.entity.SmsNotifySetting;
import org.jeecg.modules.basicinfo.mapper.SmsNotifySettingMapper;
import org.jeecg.modules.basicinfo.service.ISmsNotifySettingService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 内部短信通知设置
 * @Author: jeecg-boot
 * @Date:   2025-03-27
 * @Version: V1.0
 */
@Service
public class SmsNotifySettingServiceImpl extends ServiceImpl<SmsNotifySettingMapper, SmsNotifySetting> implements ISmsNotifySettingService {

    @Override
    public SmsNotifySetting getByBusinessType(String businessType) {
        LambdaQueryWrapper<SmsNotifySetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SmsNotifySetting::getModule, businessType).eq(SmsNotifySetting::getEnableFlag, "1").last("limit 1");
        return this.getOne(queryWrapper);
    }
}
