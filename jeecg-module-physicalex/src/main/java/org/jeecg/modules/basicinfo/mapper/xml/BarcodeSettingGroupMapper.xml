<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.BarcodeSettingGroupMapper">

    <select id="listByBarcodeSetting" resultType="org.jeecg.modules.basicinfo.entity.BarcodeSettingGroup">
        select * from barcode_setting_group where setting_id = #{settingId}
    </select>

    <select id="listItemBySettingIdAndRegId" resultType="org.jeecg.modules.basicinfo.entity.BarcodeSettingGroup">
        select * from barcode_setting_group b join customer_reg_item_group c on b.group_id=c.item_group_id where b.setting_id=#{settingId} and c.customer_reg_id=#{customerRegId} and c.add_minus_flag!=-1 a <if test="autoCharge!='1'"> and c.pay_status='已收费'</if>
    </select>

</mapper>