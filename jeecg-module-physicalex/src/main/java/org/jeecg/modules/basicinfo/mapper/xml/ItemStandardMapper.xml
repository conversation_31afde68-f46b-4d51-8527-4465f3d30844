<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.ItemStandardMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  item_standard 
		WHERE
			 item_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.basicinfo.entity.ItemStandard">
		SELECT * 
		FROM  item_standard
		WHERE
			 item_id = #{mainId}	</select>
    <select id="selectStandardByResultSymbo" resultType="org.jeecg.modules.basicinfo.entity.ItemStandard">
		SELECT * FROM item_standard WHERE item_id = #{itemId} AND symbo = #{symbo} limit 1
	</select>
    <select id="selectDefaultStandard" resultType="org.jeecg.modules.basicinfo.entity.ItemStandard">
		SELECT * FROM item_standard WHERE item_id = #{itemId} AND default_flag = 1 limit 1
	</select>

</mapper>
