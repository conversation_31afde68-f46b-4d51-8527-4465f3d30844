package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 机构信息表
 * @Author: jeecg-boot
 * @Date: 2024-10-09
 * @Version: V1.0
 */
@ApiModel(value = "org_info对象", description = "机构信息表")
@Data
@TableName("org_info")
public class OrgInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 机构编码
     */
    @Excel(name = "机构编码", width = 15)
    @ApiModelProperty(value = "机构编码")
    private java.lang.String orgCode;
    /**
     * 机构名称
     */
    @Excel(name = "机构名称", width = 15)
    @ApiModelProperty(value = "机构名称")
    private java.lang.String orgName;
    /**
     * 营业时间
     */
    @Excel(name = "营业时间", width = 15)
    @ApiModelProperty(value = "营业时间")
    private java.lang.String businessTime;
    /**
     * 体检时间
     */
    @Excel(name = "体检时间", width = 15)
    @ApiModelProperty(value = "体检时间")
    private java.lang.String examTime;
    /**
     * 联系电话
     */
    @Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private java.lang.String phone;
    /**
     * 位置
     */
    @Excel(name = "位置", width = 15)
    @ApiModelProperty(value = "位置")
    private java.lang.String location;
    /**
     * 详细地址
     */
    @Excel(name = "详细地址", width = 15)
    @ApiModelProperty(value = "详细地址")
    private java.lang.String detailAddress;
    /**
     * 经度
     */
    @Excel(name = "经度", width = 15)
    @ApiModelProperty(value = "经度")
    private java.lang.String longitude;
    /**
     * 纬度
     */
    @Excel(name = "纬度", width = 15)
    @ApiModelProperty(value = "纬度")
    private java.lang.String latitude;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15, dicCode = "dict_item_status")
    @Dict(dicCode = "dict_item_status")
    @ApiModelProperty(value = "状态")
    private java.lang.String state;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
    /**
     * createBy
     */
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
    /**
     * updateTiem
     */
    @Excel(name = "updateTiem", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTiem")
    private java.util.Date updateTiem;
    /**
     * updateBy
     */
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;
    /**
     * 机构logo地址
     */
    @ApiModelProperty(value = "机构logo地址")
    private java.lang.String logoPicture;
    /**
     * 机构封面地址
     */
    @ApiModelProperty(value = "机构封面地址")
    private java.lang.String coverPicture;
    /**
     * 机构介绍视频
     */
    @ApiModelProperty(value = "机构介绍视频")
    private java.lang.String introduceVideo;
    /**
     * 体检说明视频
     */
    @ApiModelProperty(value = "体检说明视频")
    private java.lang.String requiremnetVideo;
    /**
     * 机构相关介绍
     */
    @ApiModelProperty(value = "机构相关介绍")
    private java.lang.String introduce;
    /**
     * 体检须知
     */
    @ApiModelProperty(value = "体检须知")
    private String examAttetion;
    /**
     * 智能客服链接
     */
    @ApiModelProperty(value = "智能客服链接")
    private String aiServiceUrl;
    /**
     * 相关资质图片
     */
    @ApiModelProperty(value = "相关资质图片")
    private String qualityPicture;

    @TableField(exist = false)
    private List<String> qualityPictureList;
}
