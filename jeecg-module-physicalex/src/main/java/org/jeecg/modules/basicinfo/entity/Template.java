package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 模版管理
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Data
@TableName("template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="template对象", description="模版管理")
public class Template implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
	/**类别*/
	@Excel(name = "类别", width = 15, dicCode = "template_type")
	@Dict(dicCode = "template_type")
    @ApiModelProperty(value = "类别")
    private java.lang.String type;
	/**体检类别*/
	@Excel(name = "体检类别", width = 15, dicCode = "examination_type")
	@Dict(dicCode = "examination_type")
    @ApiModelProperty(value = "体检类别")
    private java.lang.String examCategory;
	/**适用对象*/
	@Excel(name = "适用对象", width = 15, dicCode = "template_suit_cate")
	@Dict(dicCode = "template_suit_cate")
    @ApiModelProperty(value = "适用对象")
    private java.lang.String regType;
	/**文件*/
	@Excel(name = "文件", width = 15)
    @ApiModelProperty(value = "文件")
    private java.lang.String content;
	/**启用标志*/
	@Excel(name = "启用标志", width = 15)
    @ApiModelProperty(value = "启用标志")
    private java.lang.String enableFlag;
    /**默认*/
    @Excel(name = "默认", width = 15)
    @ApiModelProperty(value = "默认")
    private java.lang.String defaultFlag;
    /**组合ID*/
    @Excel(name = "组合ID", width = 15)
    @ApiModelProperty(value = "组合ID")
    private java.lang.String groupId;
    /**组合ID*/
    @Excel(name = "科室ID", width = 15)
    @ApiModelProperty(value = "科室ID")
    private java.lang.String departmentId;
    /**项目ID*/
    @Excel(name = "项目ID", width = 15)
    @ApiModelProperty(value = "项目ID")
    private java.lang.String keyItemId;
	/**删除标志*/
	@Excel(name = "删除标志", width = 15)
    @ApiModelProperty(value = "删除标志")
    @TableLogic
    private java.lang.String delFlag;
}
