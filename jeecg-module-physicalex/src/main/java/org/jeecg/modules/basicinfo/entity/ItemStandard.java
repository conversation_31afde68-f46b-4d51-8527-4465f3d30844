package org.jeecg.modules.basicinfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 结果参考值
 * @Author: jeecg-boot
 * @Date: 2024-01-31
 * @Version: V1.0
 */
@ApiModel(value = "item_standard对象", description = "结果参考值")
@Data
@TableName("item_standard")
public class ItemStandard implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    private java.lang.String itemId;
    /**
     * 科室
     */
    @Excel(name = "科室", width = 15)
    @ApiModelProperty(value = "科室")
    private java.lang.String departmentId;
    /**
     * 启用
     */
    @Excel(name = "启用", width = 15)
    @ApiModelProperty(value = "启用")
    private java.lang.Integer enableFlag;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private java.lang.Integer sort;
    /**
     * 助记码
     */
    @Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private java.lang.String helpChar;
    /**
     * 五笔码
     */
    @Excel(name = "五笔码", width = 15)
    @ApiModelProperty(value = "五笔码")
    private java.lang.String wubiChar;
    /**
     * 疾病分类
     */
    @Excel(name = "疾病分类", width = 15, dicCode = "disease_type")
    @ApiModelProperty(value = "疾病分类")
    private java.lang.String diseaseType;

    /**
     * 疾病程度
     */
    @Excel(name = "疾病程度", width = 15, dicCode = "disease_grade")
    @ApiModelProperty(value = "疾病程度")
    private String diseaseSeverity;
    /**
     * 危急值分类
     */
    @Excel(name = "危急值分类", width = 15, dicCode = "severity_degree")
    @ApiModelProperty(value = "危急值分类")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String severityDegree;
    /**
     * 结果异常标志
     */
    @Excel(name = "结果异常标志", width = 15, dicCode = "abnormalFlag")
    @ApiModelProperty(value = "结果异常标志")
    private java.lang.String abnormalFlag;
    /**
     * 结果标志
     */
    @Excel(name = "结果标志", width = 15, dicCode = "symbo")
    @ApiModelProperty(value = "结果标志")
    private java.lang.String symbo;
    /**
     * 标志符号
     */
    @Excel(name = "标志符号", width = 15)
    @ApiModelProperty(value = "标志符号")
    private java.lang.String symboChar;
    /**
     * 结论
     */
    @Excel(name = "结论", width = 15)
    @ApiModelProperty(value = "结论")
    private java.lang.String conclusion;
    /**
     * 参考下限
     */
    @Excel(name = "参考下限", width = 15)
    @ApiModelProperty(value = "参考下限")
    private java.lang.String minVal;
    /**
     * 参考上线
     */
    @Excel(name = "参考上线", width = 15)
    @ApiModelProperty(value = "参考上线")
    private java.lang.String maxVal;
    /**
     * 性别限制
     */
    @Excel(name = "性别限制", width = 15, dicCode = "sexLimit")
    @ApiModelProperty(value = "性别限制")
    private java.lang.String sexLlimit;
    /**
     * 最小年龄
     */
    @Excel(name = "最小年龄", width = 15)
    @ApiModelProperty(value = "最小年龄")
    private java.lang.Integer minAge;
    /**
     * 最大年龄
     */
    @Excel(name = "最大年龄", width = 15)
    @ApiModelProperty(value = "最大年龄")
    private java.lang.Integer maxAge;
    /**
     * 默认
     */
    @Excel(name = "默认", width = 15)
    @ApiModelProperty(value = "默认")
    private java.lang.Integer defaultFlag;
    /**
     * 使用频次
     */
    @Excel(name = "使用频次", width = 15)
    @ApiModelProperty(value = "使用频次")
    private java.lang.Integer frequency;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;

    /**
     * 操作符
     */
    @ApiModelProperty(value = "操作符")
    private String operator;

    /**
     * 表达式
     */
    @ApiModelProperty(value = "表达式")
    private String expression;

    @ApiModelProperty(value = "Groovy表达式")
    private String groovyExpression;

    @ApiModelProperty(value = "参考文献")
    private String baseOn;



    public boolean isApplicableTo(CustomerReg customerReg) {
        if (customerReg == null || customerReg.getGender() == null || customerReg.getAge() == null) {
            return false;
        }
        // 如果有性别限制，判断性别是否匹配
        if (this.sexLlimit != null && !StringUtils.equals(this.sexLlimit, ExConstants.LIMIT_不限) && !StringUtils.equals(this.sexLlimit, customerReg.getGender())) {
            return false;
        }

        // 如果有年龄限制，判断年龄是否在范围内
        if (this.minAge != null && customerReg.getAge() <= this.minAge) {
            return false;
        }
        if (this.maxAge != null && customerReg.getAge() >= this.maxAge) {
            return false;
        }

        // 如果没有性别和年龄限制，或者所有条件都满足，返回true
        return true;
    }

    public boolean isInRange(String value) {
        // 如果没有设置参考范围，直接返回true
        if (this.minVal == null || this.maxVal == null) {
            return true;
        }

        // 将输入值转换为double类型进行比较
        try {
            double doubleValue = Double.parseDouble(value);
            double min = Double.parseDouble(this.minVal);
            double max = Double.parseDouble(this.maxVal);

            // 判断值是否在范围内
            return doubleValue >= min && doubleValue <= max;
        } catch (NumberFormatException e) {
            // 如果输入值或参考范围无法转换为double类型，返回false
            return false;
        }
    }

    public boolean isMatched(String value) {
        if (this.operator == null || this.expression == null || value == null) {
            return false;
        }

        switch (this.operator) {
            case "等于":
                return value.equals(this.expression);
            case "不等于":
                return !value.equals(this.expression);
            case "包含":
                return value.contains(this.expression);
            case "不包含":
                return !value.contains(this.expression);
            default:
                return false;
        }
    }

}
