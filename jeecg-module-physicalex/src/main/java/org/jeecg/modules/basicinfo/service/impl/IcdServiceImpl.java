package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.basicinfo.entity.Icd;
import org.jeecg.modules.basicinfo.mapper.IcdMapper;
import org.jeecg.modules.basicinfo.service.IIcdService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: icd
 * @Author: jeecg-boot
 * @Date:   2025-05-08
 * @Version: V1.0
 */
@Service
public class IcdServiceImpl extends ServiceImpl<IcdMapper, Icd> implements IIcdService {

    @Override
    public Page<Icd> listByKeyword(Page<Icd> page, String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return page;
        }

        QueryWrapper<Icd> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("assist_code", keyword).or().like("name", keyword);
        return baseMapper.selectPage(page, queryWrapper);
    }
}
