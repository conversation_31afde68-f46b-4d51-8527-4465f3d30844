package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 体检项目
 * @Author: jeecg-boot
 * @Date:   2024-01-31
 * @Version: V1.0
 */
public interface ItemInfoMapper extends BaseMapper<ItemInfo> {

    List<ItemInfo> listItemByDepartId(@Param("departId") String departId);

    List<ItemInfo> listItemByGroupId(@Param("groupId") String groupId,@Param("sumableFlag") Integer sumableFlag);
    List<ItemInfo> listItemByGroupIds(@Param("groupIds") List<String> groupIds);

    Page<ItemInfo> listByKeyword(Page<ItemInfo> page, @Param("keyword")String keyword,@Param("departmentId")String departmentId);

    List<ItemInfo> getItemByGroupId(@Param("groupId") String groupId);
}
