package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplex;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplexItemRule;
import org.jeecg.modules.basicinfo.mapper.DiagnosisComplexItemRuleMapper;
import org.jeecg.modules.basicinfo.mapper.DiagnosisComplexMapper;
import org.jeecg.modules.basicinfo.service.IDiagnosisComplexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 符合判断
 * @Author: jeecg-boot
 * @Date: 2024-04-26
 * @Version: V1.0
 */
@Service
public class DiagnosisComplexServiceImpl extends ServiceImpl<DiagnosisComplexMapper, DiagnosisComplex> implements IDiagnosisComplexService {

    @Autowired
    private DiagnosisComplexMapper diagnosisComplexMapper;
    @Autowired
    private DiagnosisComplexItemRuleMapper diagnosisComplexItemRuleMapper;


    @Override
    public Page<DiagnosisComplex> pageDiagnosisComplex(Page<DiagnosisComplex> page, String departmentId, String name, Integer enableFlag) {
        diagnosisComplexMapper.pageDiagnosisComplex(page, departmentId, name, enableFlag);
        page.getRecords().forEach(diagnosisComplex -> {
            diagnosisComplex.setItemRules(diagnosisComplexItemRuleMapper.selectByMainId(diagnosisComplex.getId()));
        });

        return page;
    }

    @Override
    public void saveMain(DiagnosisComplex diagnosisComplex, List<DiagnosisComplexItemRule> diagnosisComplexItemRuleList) {
        if (diagnosisComplexItemRuleList != null && !diagnosisComplexItemRuleList.isEmpty()) {
            diagnosisComplex.setRulesCount(diagnosisComplexItemRuleList.size());
        }
        diagnosisComplexMapper.insert(diagnosisComplex);
        if (diagnosisComplexItemRuleList != null && !diagnosisComplexItemRuleList.isEmpty()) {
            for (DiagnosisComplexItemRule entity : diagnosisComplexItemRuleList) {
                //外键设置
                entity.setComplexId(diagnosisComplex.getId());
                diagnosisComplexItemRuleMapper.insert(entity);
            }
        }
    }

    @Override
    public void updateMain(DiagnosisComplex diagnosisComplex, List<DiagnosisComplexItemRule> diagnosisComplexItemRuleList) {

        //分析启用的规则数量
        if (diagnosisComplexItemRuleList != null && !diagnosisComplexItemRuleList.isEmpty()) {
            diagnosisComplex.setRulesCount(diagnosisComplexItemRuleList.size());
        }

        diagnosisComplexMapper.updateById(diagnosisComplex);


        //1.先删除子表数据
        diagnosisComplexItemRuleMapper.deleteByMainId(diagnosisComplex.getId());

        //2.子表数据重新插入
        if (diagnosisComplexItemRuleList != null && !diagnosisComplexItemRuleList.isEmpty()) {
            for (DiagnosisComplexItemRule entity : diagnosisComplexItemRuleList) {
                //外键设置
                entity.setComplexId(diagnosisComplex.getId());
                diagnosisComplexItemRuleMapper.insert(entity);
            }
        }
    }
}
