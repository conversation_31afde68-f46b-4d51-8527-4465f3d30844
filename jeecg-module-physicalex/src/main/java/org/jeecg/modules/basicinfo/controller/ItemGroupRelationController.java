package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.GroupRelationVO;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: item_group_relation
 * @Author: jeecg-boot
 * @Date: 2024-12-02
 * @Version: V1.0
 */
@Api(tags = "item_group_relation")
@RestController
@RequestMapping("/basicinfo/itemGroupRelation")
@Slf4j
public class ItemGroupRelationController extends JeecgController<ItemGroupRelation, IItemGroupRelationService> {
    @Autowired
    private IItemGroupRelationService itemGroupRelationService;

    /**
     * 分页列表查询
     *
     * @param itemGroupRelation
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "item_group_relation-分页列表查询")
    @ApiOperation(value = "item_group_relation-分页列表查询", notes = "item_group_relation-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ItemGroupRelation>> queryPageList(ItemGroupRelation itemGroupRelation, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ItemGroupRelation> queryWrapper = QueryGenerator.initQueryWrapper(itemGroupRelation, req.getParameterMap());
        Page<ItemGroupRelation> page = new Page<ItemGroupRelation>(pageNo, pageSize);
        IPage<ItemGroupRelation> pageList = itemGroupRelationService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "item_group_relation-根据主groupId查关联关系", notes = "item_group_relation-根据主groupId查关联关系")
    @GetMapping(value = "/getRelationGroupsByMainId")
    public Result<?> getRelationGroupsByMainId(String mainId, String mainName, HttpServletRequest req) {
        //查询附属项目
        GroupRelationVO groupRelationVO = null;
        try {
            groupRelationVO = itemGroupRelationService.getRelationGroupsByMainId(mainId);
            return Result.OK(groupRelationVO);
        } catch (Exception e) {
            return Result.error("查询失败！" + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param itemGroupRelation
     * @return
     */
    @AutoLog(value = "item_group_relation-添加")
    @ApiOperation(value = "item_group_relation-添加", notes = "item_group_relation-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ItemGroupRelation itemGroupRelation) {
        itemGroupRelationService.save(itemGroupRelation);
        return Result.OK("添加成功！");
    }

    @AutoLog(value = "item_group_relation-批量添加")
    @ApiOperation(value = "item_group_relation-批量添加", notes = "item_group_relation-批量添加")
    @PostMapping(value = "/addRelationGroupBatch")
    public Result<String> addRelationGroupBatch(@RequestBody GroupRelationVO groupRelationVO) {
        itemGroupRelationService.saveRelationGroupBatch(groupRelationVO);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param itemGroupRelation
     * @return
     */
    @AutoLog(value = "item_group_relation-编辑")
    @ApiOperation(value = "item_group_relation-编辑", notes = "item_group_relation-编辑")
    @RequiresPermissions("basicinfo:item_group_relation:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ItemGroupRelation itemGroupRelation) {
        itemGroupRelationService.updateById(itemGroupRelation);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "item_group_relation-通过id删除")
    @ApiOperation(value = "item_group_relation-通过id删除", notes = "item_group_relation-通过id删除")
    @RequiresPermissions("basicinfo:item_group_relation:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        itemGroupRelationService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "item_group_relation-批量删除")
    @ApiOperation(value = "item_group_relation-批量删除", notes = "item_group_relation-批量删除")
    @RequiresPermissions("basicinfo:item_group_relation:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.itemGroupRelationService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "item_group_relation-通过id查询")
    @ApiOperation(value = "item_group_relation-通过id查询", notes = "item_group_relation-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ItemGroupRelation> queryById(@RequestParam(name = "id", required = true) String id) {
        ItemGroupRelation itemGroupRelation = itemGroupRelationService.getById(id);
        if (itemGroupRelation == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(itemGroupRelation);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param itemGroupRelation
     */
    @RequiresPermissions("basicinfo:item_group_relation:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItemGroupRelation itemGroupRelation) {
        return super.exportXls(request, itemGroupRelation, ItemGroupRelation.class, "item_group_relation");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:item_group_relation:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ItemGroupRelation.class);
    }

    @ApiOperation(value = "查询项目依赖关系", notes = "根据项目组ID查询其依赖的项目")
    @GetMapping(value = "/getDependenciesByGroupId")
    public Result<?> getDependenciesByGroupId(@RequestParam String groupId) {
        try {
            List<ItemGroupRelation> dependencies = itemGroupRelationService.list(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupId).eq(ItemGroupRelation::getRelation, "依赖"));
            return Result.OK(dependencies);
        } catch (Exception e) {
            return Result.error("查询依赖关系失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "批量查询项目依赖关系", notes = "根据多个项目组ID批量查询依赖关系")
    @PostMapping(value = "/getDependenciesByGroupIds")
    public Result<?> getDependenciesByGroupIds(@RequestBody List<String> groupIds) {
        try {
            List<ItemGroupRelation> dependencies = itemGroupRelationService.list(new LambdaQueryWrapper<ItemGroupRelation>().in(ItemGroupRelation::getGroupId, groupIds).eq(ItemGroupRelation::getRelation, "依赖"));

            // 按groupId分组返回
            Map<String, List<ItemGroupRelation>> dependencyMap = dependencies.stream().collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));

            return Result.OK(dependencyMap);
        } catch (Exception e) {
            return Result.error("批量查询依赖关系失败：" + e.getMessage());
        }
    }

}
