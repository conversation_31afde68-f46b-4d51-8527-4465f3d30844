package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.SuitCategory;
import org.jeecg.modules.basicinfo.mapper.SuitCategoryMapper;
import org.jeecg.modules.basicinfo.service.ISuitCategoryService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 套餐类别
 * @Author: jeecg-boot
 * @Date:   2024-10-08
 * @Version: V1.0
 */
@Service
public class SuitCategoryServiceImpl extends ServiceImpl<SuitCategoryMapper, SuitCategory> implements ISuitCategoryService {

}
