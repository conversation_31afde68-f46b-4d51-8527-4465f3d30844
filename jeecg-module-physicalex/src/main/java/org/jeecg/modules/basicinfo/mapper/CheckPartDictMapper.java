package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.CheckPartDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: check_part_dict
 * @Author: jeecg-boot
 * @Date:   2025-07-04
 * @Version: V1.0
 */
public interface CheckPartDictMapper extends BaseMapper<CheckPartDict> {

}
