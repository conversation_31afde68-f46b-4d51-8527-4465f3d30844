package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.Pic;
import org.jeecg.modules.basicinfo.service.IPicService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 图片表
 * @Author: jeecg-boot
 * @Date:   2024-10-11
 * @Version: V1.0
 */
@Api(tags="图片表")
@RestController
@RequestMapping("/basicinfo/pic")
@Slf4j
public class PicController extends JeecgController<Pic, IPicService> {
	@Autowired
	private IPicService picService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pic
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "图片表-分页列表查询")
	@ApiOperation(value="图片表-分页列表查询", notes="图片表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Pic>> queryPageList(Pic pic,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Pic> queryWrapper = QueryGenerator.initQueryWrapper(pic, req.getParameterMap());
		Page<Pic> page = new Page<Pic>(pageNo, pageSize);
		IPage<Pic> pageList = picService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pic
	 * @return
	 */
	@AutoLog(value = "图片表-添加")
	@ApiOperation(value="图片表-添加", notes="图片表-添加")
	@RequiresPermissions("basicinfo:pic:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Pic pic) {
		picService.save(pic);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pic
	 * @return
	 */
	@AutoLog(value = "图片表-编辑")
	@ApiOperation(value="图片表-编辑", notes="图片表-编辑")
	@RequiresPermissions("basicinfo:pic:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Pic pic) {
		picService.updateById(pic);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "图片表-通过id删除")
	@ApiOperation(value="图片表-通过id删除", notes="图片表-通过id删除")
	@RequiresPermissions("basicinfo:pic:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		picService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "图片表-批量删除")
	@ApiOperation(value="图片表-批量删除", notes="图片表-批量删除")
	@RequiresPermissions("basicinfo:pic:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.picService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "图片表-通过id查询")
	@ApiOperation(value="图片表-通过id查询", notes="图片表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Pic> queryById(@RequestParam(name="id",required=true) String id) {
		Pic pic = picService.getById(id);
		if(pic==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pic);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pic
    */
    @RequiresPermissions("basicinfo:pic:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Pic pic) {
        return super.exportXls(request, pic, Pic.class, "图片表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:pic:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Pic.class);
    }

}
