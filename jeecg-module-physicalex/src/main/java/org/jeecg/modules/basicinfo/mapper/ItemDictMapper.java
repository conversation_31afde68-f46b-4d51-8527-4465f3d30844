package org.jeecg.modules.basicinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.ItemDict;

import java.util.List;

/**
 * @Description: 项目结果字典
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 * @Version: V1.0
 */
public interface ItemDictMapper extends BaseMapper<ItemDict> {

    List<ItemDict> listByItemId(@Param("itemId") String itemId);

    ItemDict getDefaultDict(@Param("itemId") String itemId);
}
