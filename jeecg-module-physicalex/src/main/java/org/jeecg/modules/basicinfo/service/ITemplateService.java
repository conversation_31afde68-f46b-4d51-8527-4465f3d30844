package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.Template;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 模版管理
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
public interface ITemplateService extends IService<Template> {

    String getUpdateTime(String id);

    void preDealDefaultFlag(Template template);

    Template getDefaultOfTypes(String type);

    String getDefaultIdOfType(String type);

    void setTemplateGroup(Template template);

    List<Template> listTemplate(String type, String examCategory, String regType, String defaultFlag,String groupId);

    List<String> getApplyTemplateIdsByReg(String customerRegId,Boolean reprintFlag);

    List<Template> listByRegType(String regType,String type);
}
