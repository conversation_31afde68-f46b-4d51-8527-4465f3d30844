package org.jeecg.modules.basicinfo.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.utils.InitialUtil;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.basicinfo.bo.ItemGroupLabel;
import org.jeecg.modules.basicinfo.entity.GroupChangeRecord;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemGroupInterface;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.service.IGroupChangeRecordService;
import org.jeecg.modules.basicinfo.service.IItemGroupService;
import org.jeecg.modules.basicinfo.service.IItemInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 项目组合
 * @Author: jeecg-boot
 * @Date: 2024-01-31
 * @Version: V1.0
 */
@Api(tags = "项目组合")
@RestController
@RequestMapping("/basicinfo/itemGroup")
@Slf4j
public class ItemGroupController extends JeecgController<ItemGroup, IItemGroupService> {
    @Autowired
    private IItemGroupService itemGroupService;
    @Autowired
    private IItemInfoService itemInfoService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private AIService aiService;
    @Autowired
    private IGroupChangeRecordService groupChangeRecordService;

    //检查项目缓存状态
    @ApiOperation(value = "检查项目缓存状态", notes = "检查项目缓存状态")
    @GetMapping(value = "/checkCache")
    public Result<?> checkCache() {
        return Result.OK(itemGroupService.checkGroupCacheFlag());
    }

    //获取所有组合
    @ApiOperation(value = "获取所有组合", notes = "获取所有组合")
    @GetMapping(value = "/listAllGroup")
    public Result<?> listAllGroup() {
        List<ItemGroup> list = itemGroupService.listAll();
        return Result.OK(list);
    }



    @ApiOperation(value = "按科室获取组合", notes = "按科室获取组合")
    @GetMapping(value = "/listAllByDepartment")
    public Result<?> listAllByDepartment(String name, String answerId, String suitId) {
        try {
            List<ItemGroupLabel> labels = itemGroupService.listAllByDepartment(name, answerId, suitId);
            return Result.OK(labels);
        } catch (Exception e) {
            return Result.error("查询失败");
        }
    }

    /**
     * 根据riskFactorIds查询项目组合
     *
     * @return
     */
    @ApiOperation(value = "根据riskFactorIds查询项目组合", notes = "根据riskFactorIds查询项目组合")
    @GetMapping(value = "/listByRiskFactor")
    public Result<?> listByRiskFactor(@RequestParam(name = "riskFactorIds", required = true) String riskFactorIds, @RequestParam(name = "post", required = false) String post) {
        List<String> factorCodes = Arrays.asList(riskFactorIds.split(","));
        if (factorCodes.isEmpty()) {
            return Result.OK();
        }
        List<ItemGroup> list = itemGroupService.listByRiskFactor(factorCodes, post);
        return Result.OK(list);
    }

    @ApiOperation(value = "体检项目-清除缓存", notes = "体检项目-清除缓存")
    @PostMapping(value = "/clearCache")
    public Result<?> clearCache() {
        itemGroupService.evictCached();
        return Result.OK("操作成功");
    }

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "项目组合-分页列表查询")
    @ApiOperation(value = "项目组合-分页列表查询", notes = "项目组合-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ItemGroup>> queryPageList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String keyword = req.getParameter("keyword");
        String name = req.getParameter("name");
        String helpChar = req.getParameter("helpChar");
        String feeType = req.getParameter("feeType");
        String departmentId = req.getParameter("departmentId");
        String enableFlag = req.getParameter("enableFlag");
        String chargeItemOnlyFlag = req.getParameter("chargeItemOnlyFlag");
        //QueryWrapper<ItemGroup> queryWrapper = QueryGenerator.initQueryWrapper(itemGroup, req.getParameterMap());
        Page<ItemGroup> page = new Page<ItemGroup>(pageNo, pageSize);
        IPage<ItemGroup> pageList = itemGroupService.pageItemGroup(page, name, helpChar, feeType, keyword, departmentId, enableFlag, chargeItemOnlyFlag);
        return Result.OK(pageList);
    }

    /**
     * 批量更新启用状态
     *
     * @return
     */
    @ApiOperation(value = "项目组合-批量更新启用状态", notes = "项目组合-批量更新启用状态")
    @PostMapping(value = "/batchUpdateEnableFlag")
    public Result<?> batchUpdateEnableFlag(@RequestBody JSONObject info) {
        List<String> idList = info.getJSONArray("ids").toJavaList(String.class);
        String enableFlag = info.getString("enableFlag");
        itemGroupService.batchUpdateEnableFlag(idList, enableFlag);
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("操作成功");
    }


    @ApiOperation(value = "项目组合-分页列表查询", notes = "项目组合-分页列表查询")
    @GetMapping(value = "/listRecycleBin")
    public Result<IPage<ItemGroup>> listRecycleBin(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String keyword = req.getParameter("keyword");
        String departmentId = req.getParameter("departmentId");
        Page<ItemGroup> page = new Page<ItemGroup>(pageNo, pageSize);
        IPage<ItemGroup> pageList = itemGroupService.pageRecycleBinItemGroup(page, keyword, departmentId);
        return Result.OK(pageList);
    }

    /**
     * listByKeyword
     *
     * @param keyword
     * @return
     */
    @ApiOperation(value = "项目组合-关键字查询", notes = "项目组合-关键字查询")
    @GetMapping(value = "/listByKeyword")
    public Result<?> listByKeyword(@RequestParam(name = "keyword", required = true) String keyword) {
        List<ItemGroup> list = itemGroupService.listByKeyword(keyword);
        return Result.OK(list);
    }

    // batchRecycle
    @ApiOperation(value = "项目组合-批量回收", notes = "项目组合-批量回收")
    @GetMapping(value = "/batchRecover")
    public Result<?> batchRecover(String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        itemGroupService.batchRecover(idList);
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("操作成功");
    }

    /**
     * 添加
     *
     * @param itemGroup
     * @return
     */
    @AutoLog(value = "项目组合-添加")
    @ApiOperation(value = "项目组合-添加", notes = "项目组合-添加")
    @RequiresPermissions("basicinfo:item_group:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ItemGroup itemGroup) {
        itemGroupService.setDepartmentNameAndCheckPartName(itemGroup);
        //生成首拼
        String spelling = InitialUtil.generateInitial(itemGroup.getName());
        itemGroup.setHelpChar(spelling);
        itemGroupService.save(itemGroup);
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("添加成功！", itemGroup.getId());
    }

    /**
     * 添加
     *
     * @return
     */
    @AutoLog(value = "项目组合-通过HIS项目批量添加")
    @ApiOperation(value = "项目组合-通过HIS项目批量添加", notes = "项目组合-通过HIS项目批量添加")
    @PostMapping(value = "/addBatchByHisItem")
    public Result<String> addBatchByHisItem(@RequestBody JSONArray itemGroupInterfaces) {

        Integer successCount = itemGroupService.addBathByHisItems(itemGroupInterfaces.toJavaList(ItemGroupInterface.class));
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("添加成功" + successCount + "条数据！");
    }

    /**
     * 编辑
     *
     * @param itemGroup
     * @return
     */
    @AutoLog(value = "项目组合-编辑")
    @ApiOperation(value = "项目组合-编辑", notes = "项目组合-编辑")
    @RequiresPermissions("basicinfo:item_group:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ItemGroup itemGroup) {
        itemGroupService.setDepartmentNameAndCheckPartName(itemGroup);
        ItemGroup oldGroup = itemGroupService.getById(itemGroup.getId());
        if (!Objects.equals(oldGroup.getPrice(),itemGroup.getPrice())){
            GroupChangeRecord record = new GroupChangeRecord();
            record.setGroupId(itemGroup.getId());
            record.setGroupName(itemGroup.getName());
            record.setOldPrice(oldGroup.getPrice());
            record.setNewPrice(itemGroup.getPrice());
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            record.setOperator(loginUser.getUsername());
            record.setOperatorName(loginUser.getRealname());
            record.setOperateTime(new Date());
            groupChangeRecordService.save(record);
        }
        //生成首拼
        String spelling = InitialUtil.generateInitial(itemGroup.getName());
        itemGroup.setHelpChar(spelling);
        itemGroupService.updateById(itemGroup);
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("编辑成功!", itemGroup.getId());
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "项目组合-通过id删除")
    @ApiOperation(value = "项目组合-通过id删除", notes = "项目组合-通过id删除")
    @RequiresPermissions("basicinfo:item_group:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        itemGroupService.removeById(id);
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "项目组合-批量删除")
    @ApiOperation(value = "项目组合-批量删除", notes = "项目组合-批量删除")
    @RequiresPermissions("basicinfo:item_group:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.itemGroupService.removeByIds(Arrays.asList(ids.split(",")));
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("批量删除成功!");
    }

    /**
     * 批量永久删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "项目组合-批量永久删除")
    @ApiOperation(value = "项目组合-批量删除", notes = "项目组合-批量永久删除")
    @RequiresPermissions("basicinfo:item_group:deleteBatch")
    @DeleteMapping(value = "/deleteBatchForever")
    public Result<String> deleteBatchForever(@RequestParam(name = "ids", required = true) String ids) {
        this.itemGroupService.removeForever(Arrays.asList(ids.split(",")));
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "项目组合-通过id查询")
    @ApiOperation(value = "项目组合-通过id查询", notes = "项目组合-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ItemGroup> queryById(@RequestParam(name = "id", required = true) String id) {
        ItemGroup itemGroup = itemGroupService.getById(id);
        if (itemGroup == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(itemGroup);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param itemGroup
     */
    @RequiresPermissions("basicinfo:item_group:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItemGroup itemGroup) {
        return super.exportXls(request, itemGroup, ItemGroup.class, "项目组合");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:item_group:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return super.importExcel(request, response, ItemGroup.class);
    }

    /**
     * 根据itemGroupId查询项目组合明细
     *
     * @param groupId
     * @return
     */
    @ApiOperation(value = "根据itemGroupId查询项目组合明细", notes = "根据itemGroupId查询项目组合明细")
    @GetMapping(value = "/listItemByGroupId")
    public Result<?> queryItemGroupDetailByGroupId(@RequestParam(name = "groupId", required = true) String groupId) {
        return Result.OK(itemGroupService.listItemByGroupId(groupId));
    }

    /**
     * 根据itemGroupId查询项目组合明细
     *
     * @param groupId
     * @return
     */
    @ApiOperation(value = "查询组合内和同科室下的项目", notes = "查询组合内和同科室下的项目")
    @GetMapping(value = "/listItemOfGroupAndDepart")
    public Result<?> queryItemOfGroupAndDepart(@RequestParam(name = "groupId", required = true) String groupId, @RequestParam(name = "departId", required = true) String departId) {
        JSONObject jsonObject = new JSONObject();
        List<ItemInfo> itemOfGroup = itemGroupService.listItemByGroupId(groupId);
        List<ItemInfo> itemOfDepart = itemInfoService.listItemByDepartId(departId);
        jsonObject.put("itemOfGroup", itemOfGroup);
        jsonObject.put("itemOfDepart", itemOfDepart);
        return Result.OK(jsonObject);
    }

    /**
     * 设置组合内项目
     *
     * @param info
     * @return
     */
    @ApiOperation(value = "设置组合内项目", notes = "设置组合内项目")
    @PostMapping(value = "/setItemOfGroup")
    public Result<?> setItemOfGroup(@RequestBody JSONObject info) {
        String groupId = info.getString("groupId");
        JSONArray items = info.getJSONArray("items");
        itemGroupService.setItemOfGroup(groupId, items);
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("设置成功！");
    }

    //保存顺序
    @ApiOperation(value = "保存顺序", notes = "保存顺序")
    @PostMapping(value = "/saveSort")
    public Result<?> saveSort(@RequestBody JSONObject info) {
        String groupId = info.getString("groupId");
        List<JSONObject> items = info.getJSONArray("items").toJavaList(JSONObject.class);
        itemGroupService.saveOrderOfGroup(groupId, items);
        itemGroupService.evictCached();
        itemGroupService.evictCachedGroupString();
        return Result.OK("保存成功！");
    }

    //获取下一个排序号
    @ApiOperation(value = "获取下一个排序号", notes = "获取下一个排序号")
    @GetMapping(value = "/getNextSort")
    public Result<?> getNextSort() {
        String sql = "select max(sort) from item_group";
        Integer maxSortNo = jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
            String maxSort = rs.getString(1);
            if (maxSort == null) {
                return 1;
            }

            return Integer.parseInt(maxSort);
        });
        return Result.OK(maxSortNo + 1);
    }

    //
    @ApiOperation(value = "获取组合", notes = "获取组合")
    @GetMapping(value = "/listAll")
    public Result<?> listAll(String departmentId, String name) {
        List<ItemGroup> list = itemGroupService.listAll(departmentId, name);
        return Result.OK(list);
    }

    @ApiOperation(value = "获取组合", notes = "获取组合")
    @GetMapping(value = "/updateIntroFromAI")
    public Result<?> updateIntroFromAI() {
        try {
            aiService.updateItemExplain();
            return Result.OK("获取成功");
        } catch (Exception e) {
            return Result.error("获取失败");
        }
    }

}
