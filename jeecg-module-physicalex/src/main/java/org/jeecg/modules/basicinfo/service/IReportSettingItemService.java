package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.ReportSettingItem;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 报告设置条目
 * @Author: jeecg-boot
 * @Date:   2024-07-25
 * @Version: V1.0
 */
public interface IReportSettingItemService extends IService<ReportSettingItem> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<ReportSettingItem>
	 */
	public List<ReportSettingItem> selectByMainId(String mainId);
}
