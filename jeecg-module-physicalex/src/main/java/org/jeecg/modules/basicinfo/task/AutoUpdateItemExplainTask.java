package org.jeecg.modules.basicinfo.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.summary.service.PdfGeneratorService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoUpdateItemExplainTask implements Job {

    @Autowired
    private AIService aiService;

    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                aiService.updateItemExplain();
            } catch (Exception e) {
                log.error("自动更新项目解释任务失败", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("自动更新项目解释任务正在运行");
        }
    }
}