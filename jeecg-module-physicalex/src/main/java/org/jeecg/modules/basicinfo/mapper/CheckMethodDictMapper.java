package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.CheckMethodDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: check_method_dict
 * @Author: jeecg-boot
 * @Date:   2025-06-19
 * @Version: V1.0
 */
public interface CheckMethodDictMapper extends BaseMapper<CheckMethodDict> {

}
