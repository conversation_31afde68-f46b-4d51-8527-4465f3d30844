<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.DiagnosisComplexMapper">

    <select id="pageDiagnosisComplex" resultMap="DiagnosisComplexResultMap"
            parameterType="java.util.Map">
        select * from diagnosis_complex
        <where>
            <if test="departmentId != null and departmentId != ''">
                and department_id like concat('%',#{departmentId},'%')
            </if>
            <if test="enableFlag != null">
                and enable_flag = #{enableFlag}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
        </where>
        order by create_time desc
    </select>


    <select id="getDiagnosisComplex" resultType="org.jeecg.modules.basicinfo.entity.DiagnosisComplex"
            parameterType="java.lang.String">
        select * from diagnosis_complex where id = #{id}
    </select>

    <resultMap id="DiagnosisComplexResultMap" type="org.jeecg.modules.basicinfo.entity.DiagnosisComplex">
        <id property="id" column="id"/>
        <result property="departmentId" column="department_id"/>
        <result property="departmentName" column="department_name"/>
        <result property="name" column="name"/>
        <result property="genderLimit" column="gender_limit"/>
        <result property="minAge" column="min_age"/>
        <result property="maxAge" column="max_age"/>
        <result property="marriageLimit" column="marriage_limit"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="formula" column="formula"/>
        <result property="createBy" column="create_by"/>
        <result property="creatTime" column="creat_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <!-- 对于复杂类型，需要使用association或者collection来处理 -->
        <result property="itemRules" column="item_rules"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>
</mapper>