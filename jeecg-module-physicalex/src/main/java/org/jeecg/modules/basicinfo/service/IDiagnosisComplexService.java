package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.station.bo.ComplexDiagnosticResult;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplex;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplexItemRule;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

import java.util.List;

/**
 * @Description: 符合判断
 * @Author: jeecg-boot
 * @Date:   2024-04-26
 * @Version: V1.0
 */
public interface IDiagnosisComplexService extends IService<DiagnosisComplex> {

    Page<DiagnosisComplex> pageDiagnosisComplex(Page<DiagnosisComplex> page,  String departmentId,  String name, Integer enableFlag);

    void saveMain(DiagnosisComplex diagnosisComplex, List<DiagnosisComplexItemRule> diagnosisComplexItemRuleList);

    void updateMain(DiagnosisComplex diagnosisComplex, List<DiagnosisComplexItemRule> diagnosisComplexItemRuleList);

}
