package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 内部短信通知设置
 * @Author: jeecg-boot
 * @Date:   2025-03-27
 * @Version: V1.0
 */
@Data
@TableName("sms_notify_setting")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sms_notify_setting对象", description="内部短信通知设置")
public class SmsNotifySetting implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**模块*/
	@Excel(name = "模块", width = 15, dicCode = "sms_notify_module")
	@Dict(dicCode = "sms_notify_module")
    @ApiModelProperty(value = "模块")
    private java.lang.String module;
	/**被通知手机号*/
	@Excel(name = "被通知手机号", width = 15)
    @ApiModelProperty(value = "被通知手机号")
    private java.lang.String targetPhone;
	/**内容模版*/
	@Excel(name = "内容模版", width = 15)
    @ApiModelProperty(value = "内容模版")
    private java.lang.String content;
	/**条件*/
	@Excel(name = "条件", width = 15)
    @ApiModelProperty(value = "条件")
    private java.lang.String condiiton;
	/**启用*/
    @Excel(name = "启用", width = 15,replace = {"是_1","否_0"} )
    @ApiModelProperty(value = "启用")
    private java.lang.String enableFlag;
}
