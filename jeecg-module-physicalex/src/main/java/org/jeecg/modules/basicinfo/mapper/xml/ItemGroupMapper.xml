<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.ItemGroupMapper">

    <select id="listItemByGroupId" resultType="org.jeecg.modules.basicinfo.entity.ItemInfo"
            parameterType="java.lang.String">
        select * from item_info item join itemgroup_item gi on item.id = gi.item_id where  item.enable_flag=1 and item.del_flag=0 and gi.group_id=#{groupId}
    </select>
    <select id="pageItemGroup" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup">
        select * from item_group
        <where>
            <trim prefixOverrides="AND">
                <if test="delFlag != null and delFlag !=''">and del_flag=#{delFlag}</if>
                <if test="delFlag == null or delFlag == ''.toString()">
                   and del_flag = '0'
                </if>
                <if test="keyword!=null and keyword!=''">
                 and (name like concat('%',#{keyword},'%') or help_char like concat('%',#{keyword},'%'))
                </if>
                <if test="name!=null and name!=''">
                 and (name like concat('%',#{name},'%') or help_char like concat('%',#{name},'%'))
                </if>
                <if test="helpChar!=null and helpChar!=''">
                 and (name like concat('%',#{helpChar},'%') or help_char like concat('%',#{helpChar},'%'))
                </if>
                <if test="feeType!=null and feeType!=''">
                 and fee_type like concat('%',#{feeType},'%')
                </if>
                <if test="departmentId!=null and departmentId!=''">and department_id = #{departmentId}</if>
                <if test="enableFlag!=null and enableFlag!=''"> and enable_flag=#{enableFlag}</if>
                <if test="chargeItemOnlyFlag!=null and chargeItemOnlyFlag!=''"> and charge_item_only_flag=#{chargeItemOnlyFlag}</if>
            </trim>
        </where>
        order by sort
    </select>
    <select id="listByKeyword" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup">
        select * from item_group
        <where>
            del_flag = '0' and enable_flag='1'
            <if test="keyword!=null and keyword!=''"> AND (name like concat('%',#{keyword},'%') or help_char like
                concat('%',#{keyword},'%'))
            </if>
        </where>
        limit 50
    </select>
    <select id="listByRiskFactor" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup">
        SELECT ig.*
        FROM item_group ig
        WHERE ig.del_flag = '0'
          AND ig.enable_flag = '1'
          AND EXISTS (
            SELECT 1
            FROM zy_risk_factor_itemgroup zrf
            WHERE zrf.itemgroup_id = ig.id and zrf.post = #{post}
              AND zrf.factor_id in <foreach collection="riskFactorIds" item="item" open="(" separator="," close=")">
                #{item}
                </foreach>
            )
    </select>
    <select id="listItemGroupByNames" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup">
        select * from item_group where del_flag = '0' and enable_flag='1' and name in
        <foreach collection="names" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>
    <select id="getItemGroupById" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup">
        select * from item_group where  id = #{groupId}
    </select>
    <select id="getItemGroupByHisCode" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup">
        select * from item_group where his_code = #{hisCode}
    </select>
</mapper>