package org.jeecg.modules.basicinfo.mapper;

import java.util.List;
import org.jeecg.modules.basicinfo.entity.ReportSettingItemgroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 报告分组设置-关联大项
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
public interface ReportSettingItemgroupMapper extends BaseMapper<ReportSettingItemgroup> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<ReportSettingItemgroup>
   */
	public List<ReportSettingItemgroup> selectByMainId(@Param("mainId") String mainId);
}
