package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.ComQuipment;
import org.jeecg.modules.basicinfo.service.IComQuipmentService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 串口设备管理
 * @Author: jeecg-boot
 * @Date: 2024-08-20
 * @Version: V1.0
 */
@Api(tags = "串口设备管理")
@RestController
@RequestMapping("/basicinfo/comQuipment")
@Slf4j
public class ComQuipmentController extends JeecgController<ComQuipment, IComQuipmentService> {
    @Autowired
    private IComQuipmentService comQuipmentService;

    /**
     * 查询所有启用的串口设备
     *
     * @return
     */
    //@AutoLog(value = "串口设备管理-分页列表查询")
    @ApiOperation(value = "串口设备管理-分页列表查询", notes = "串口设备管理-分页列表查询")
    @GetMapping(value = "/enabledList")
    public Result<?> getEnabledList(String tenantId) {
        QueryWrapper<ComQuipment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable_flag", "1");
        List<ComQuipment> list = comQuipmentService.list(queryWrapper);
        return Result.OK(list);
    }

    /**
     * 分页列表查询
     *
     * @param comQuipment
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "串口设备管理-分页列表查询")
    @ApiOperation(value = "串口设备管理-分页列表查询", notes = "串口设备管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ComQuipment>> queryPageList(ComQuipment comQuipment,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {
        QueryWrapper<ComQuipment> queryWrapper = QueryGenerator.initQueryWrapper(comQuipment, req.getParameterMap());
        Page<ComQuipment> page = new Page<ComQuipment>(pageNo, pageSize);
        IPage<ComQuipment> pageList = comQuipmentService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param comQuipment
     * @return
     */
    @AutoLog(value = "串口设备管理-添加")
    @ApiOperation(value = "串口设备管理-添加", notes = "串口设备管理-添加")
    @RequiresPermissions("basicinfo:com_quipment:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ComQuipment comQuipment) {
		//LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        comQuipmentService.save(comQuipment);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param comQuipment
     * @return
     */
    @AutoLog(value = "串口设备管理-编辑")
    @ApiOperation(value = "串口设备管理-编辑", notes = "串口设备管理-编辑")
    @RequiresPermissions("basicinfo:com_quipment:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ComQuipment comQuipment) {
        comQuipmentService.updateById(comQuipment);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "串口设备管理-通过id删除")
    @ApiOperation(value = "串口设备管理-通过id删除", notes = "串口设备管理-通过id删除")
    @RequiresPermissions("basicinfo:com_quipment:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        comQuipmentService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "串口设备管理-批量删除")
    @ApiOperation(value = "串口设备管理-批量删除", notes = "串口设备管理-批量删除")
    @RequiresPermissions("basicinfo:com_quipment:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.comQuipmentService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "串口设备管理-通过id查询")
    @ApiOperation(value = "串口设备管理-通过id查询", notes = "串口设备管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ComQuipment> queryById(@RequestParam(name = "id", required = true) String id) {
        ComQuipment comQuipment = comQuipmentService.getById(id);
        if (comQuipment == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(comQuipment);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param comQuipment
     */
    @RequiresPermissions("basicinfo:com_quipment:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ComQuipment comQuipment) {
        return super.exportXls(request, comQuipment, ComQuipment.class, "串口设备管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:com_quipment:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ComQuipment.class);
    }

}
