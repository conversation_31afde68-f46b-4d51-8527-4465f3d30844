package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.EquipTypeDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: equip_type_dict
 * @Author: jeecg-boot
 * @Date:   2025-05-21
 * @Version: V1.0
 */
public interface EquipTypeDictMapper extends BaseMapper<EquipTypeDict> {

}
