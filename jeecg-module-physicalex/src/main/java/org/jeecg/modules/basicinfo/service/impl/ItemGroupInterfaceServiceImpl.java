package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.ItemGroupInterface;
import org.jeecg.modules.basicinfo.mapper.ItemGroupInterfaceMapper;
import org.jeecg.modules.basicinfo.service.IItemGroupInterfaceService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: item_group_interface
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
@Service
public class ItemGroupInterfaceServiceImpl extends ServiceImpl<ItemGroupInterfaceMapper, ItemGroupInterface> implements IItemGroupInterfaceService {

}
