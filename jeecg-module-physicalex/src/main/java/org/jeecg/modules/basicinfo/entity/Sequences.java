package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: sequences
 * @Author: jeecg-boot
 * @Date:   2024-12-18
 * @Version: V1.0
 */
@Data
@TableName("sequences")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sequences对象", description="sequences")
public class Sequences implements Serializable {
    private static final long serialVersionUID = 1L;

	/**seqName*/
	@Excel(name = "seqName", width = 15)
    @ApiModelProperty(value = "seqName")
    private java.lang.String seqName;
	/**seqValue*/
	@Excel(name = "seqValue", width = 15)
    @ApiModelProperty(value = "seqValue")
    private java.lang.Long seqValue;
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
}
