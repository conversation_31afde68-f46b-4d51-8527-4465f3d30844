package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 条码设置与项目组合关联表
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Data
@TableName("barcode_setting_group")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="barcode_setting_group对象", description="条码设置与项目组合关联表")
public class BarcodeSettingGroup implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**条码设置ID*/
	@Excel(name = "条码设置ID", width = 15)
    @ApiModelProperty(value = "条码设置ID")
    private java.lang.String settingId;
	/**组合ID*/
	@Excel(name = "组合ID", width = 15)
    @ApiModelProperty(value = "组合ID")
    private java.lang.String groupId;
	/**组合名称*/
	@Excel(name = "组合名称", width = 15)
    @ApiModelProperty(value = "组合名称")
    private java.lang.String groupName;
    /**hisCode*/
    @Excel(name = "hisCode", width = 15)
    @ApiModelProperty(value = "hisCode")
    private java.lang.String hisCode;
    /**hisName*/
    @Excel(name = "hisName", width = 15)
    @ApiModelProperty(value = "hisName")
    private java.lang.String hisName;
    /**组合简称*/
    @Excel(name = "组合简称", width = 15)
    @ApiModelProperty(value = "组合简称")
    private String shortName;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;

    @TableField(exist = false)
    private String classCode;

    @ApiModelProperty("是否有效")
    private String validFlag;
}
