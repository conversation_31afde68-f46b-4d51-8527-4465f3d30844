package org.jeecg.modules.basicinfo.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import groovy.lang.GroovyShell;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.entity.ItemStandard;
import org.jeecg.modules.basicinfo.service.IItemInfoService;
import org.jeecg.modules.basicinfo.service.IItemStandardService;
import org.jeecg.modules.basicinfo.vo.ItemInfoPage;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 体检项目
 * @Author: jeecg-boot
 * @Date: 2024-01-31
 * @Version: V1.0
 */
@Api(tags = "体检项目")
@RestController
@RequestMapping("/basicinfo/itemInfo")
@Slf4j
public class ItemInfoController {
    @Autowired
    private IItemInfoService itemInfoService;
    @Autowired
    private IItemStandardService itemStandardService;

    //listByKeyword
    @ApiOperation(value = "体检项目-关键字查询", notes = "体检项目-关键字查询")
    @GetMapping(value = "/listByKeyword")
    public Result<IPage<ItemInfo>> listByKeyword(String keyword,String departmentId, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Page<ItemInfo> page = new Page<ItemInfo>(pageNo, pageSize);
        IPage<ItemInfo> pageList = itemInfoService.listByKeyword(page, keyword,departmentId);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询
     *
     * @param itemInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检项目-分页列表查询")
    @ApiOperation(value = "体检项目-分页列表查询", notes = "体检项目-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ItemInfo>> queryPageList(ItemInfo itemInfo, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ItemInfo> queryWrapper = QueryGenerator.initQueryWrapper(itemInfo, req.getParameterMap());
        Page<ItemInfo> page = new Page<ItemInfo>(pageNo, pageSize);
        IPage<ItemInfo> pageList = itemInfoService.page(page, queryWrapper);






        return Result.OK(pageList);
    }

    @ApiOperation(value = "体检项目-根据ID获取项目", notes = "体检项目-根据ID获取项目")
    @GetMapping(value = "/getItem")
    public Result<?> getItem(String id) {
        ItemInfo itemInfo = itemInfoService.getById(id);
        if (itemInfo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(itemInfo);
    }

    @ApiOperation(value = "体检项目-获取所有项目", notes = "体检项目-获取所有项目")
    @GetMapping(value = "/listAll")
    public Result<?> listAll() {
        List<ItemInfo> list = itemInfoService.list();
        return Result.OK(list);
    }

    @ApiOperation(value = "体检项目-根据大项ID获取小项", notes = "体检项目-根据大项ID获取小项")
    @GetMapping(value = "/getItemByGroupId")
    public Result<?> getItemByGroupId(@RequestParam String groupId) {
        List<ItemInfo> list = itemInfoService.getItemByGroupId(groupId);
        return Result.OK(list);
    }

    /**
     * 添加
     *
     * @param itemInfo
     * @return
     */
    @AutoLog(value = "体检项目-添加")
    @ApiOperation(value = "体检项目-添加", notes = "体检项目-添加")
    @RequiresPermissions("basicinfo:item_info:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ItemInfo itemInfo) {
        itemInfoService.fillDepartName(itemInfo);
        itemInfoService.save(itemInfo);
        return Result.OK("添加成功！", itemInfo);
    }

    /**
     * 编辑
     *
     * @param itemInfo
     * @return
     */
    @AutoLog(value = "体检项目-编辑")
    @ApiOperation(value = "体检项目-编辑", notes = "体检项目-编辑")
    @RequiresPermissions("basicinfo:item_info:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody ItemInfo itemInfo) {
        itemInfoService.fillDepartName(itemInfo);
        itemInfoService.updateById(itemInfo);
        return Result.OK("编辑成功!", itemInfo);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检项目-通过id删除")
    @ApiOperation(value = "体检项目-通过id删除", notes = "体检项目-通过id删除")
    @RequiresPermissions("basicinfo:item_info:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        itemInfoService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检项目-批量删除")
    @ApiOperation(value = "体检项目-批量删除", notes = "体检项目-批量删除")
    @RequiresPermissions("basicinfo:item_info:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.itemInfoService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检项目-通过id查询")
    @ApiOperation(value = "体检项目-通过id查询", notes = "体检项目-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ItemInfo> queryById(@RequestParam(name = "id", required = true) String id) {
        ItemInfo itemInfo = itemInfoService.getById(id);
        if (itemInfo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(itemInfo);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "结果参考值通过主表ID查询")
    @ApiOperation(value = "结果参考值主表ID查询", notes = "结果参考值-通主表ID查询")
    @GetMapping(value = "/queryItemStandardByMainId")
    public Result<List<ItemStandard>> queryItemStandardListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<ItemStandard> itemStandardList = itemStandardService.selectByMainId(id);
        return Result.OK(itemStandardList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param itemInfo
     */
    @RequiresPermissions("basicinfo:item_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItemInfo itemInfo) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<ItemInfo> queryWrapper = QueryGenerator.initQueryWrapper(itemInfo, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //配置选中数据查询条件
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id", selectionList);
        }
        //Step.2 获取导出数据
        List<ItemInfo> itemInfoList = itemInfoService.list(queryWrapper);

        // Step.3 组装pageList
        List<ItemInfoPage> pageList = new ArrayList<ItemInfoPage>();
        for (ItemInfo main : itemInfoList) {
            ItemInfoPage vo = new ItemInfoPage();
            BeanUtils.copyProperties(main, vo);
            List<ItemStandard> itemStandardList = itemStandardService.selectByMainId(main.getId());
            vo.setItemStandardList(itemStandardList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "体检项目列表");
        mv.addObject(NormalExcelConstants.CLASS, ItemInfoPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("体检项目数据", "导出人:" + sysUser.getRealname(), "体检项目"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:item_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ItemInfoPage> list = ExcelImportUtil.importExcel(file.getInputStream(), ItemInfoPage.class, params);
                for (ItemInfoPage page : list) {
                    ItemInfo po = new ItemInfo();
                    BeanUtils.copyProperties(page, po);
                    itemInfoService.saveMain(po, page.getItemStandardList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }



    /*--------------------------------子表处理-结果参考值-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "结果参考值-通过主表ID查询")
    @ApiOperation(value = "结果参考值-通过主表ID查询", notes = "结果参考值-通过主表ID查询")
    @GetMapping(value = "/listItemStandardByMainId")
    public Result<IPage<ItemStandard>> listItemStandardByMainId(ItemStandard itemStandard, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ItemStandard> queryWrapper = QueryGenerator.initQueryWrapper(itemStandard, req.getParameterMap());
        Page<ItemStandard> page = new Page<ItemStandard>(pageNo, pageSize);
        IPage<ItemStandard> pageList = itemStandardService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param itemStandard
     * @return
     */
    @AutoLog(value = "结果参考值-添加")
    @ApiOperation(value = "结果参考值-添加", notes = "结果参考值-添加")
    @PostMapping(value = "/addItemStandard")
    public Result<String> addItemStandard(@RequestBody ItemStandard itemStandard) {
        itemStandardService.save(itemStandard);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param itemStandard
     * @return
     */
    @AutoLog(value = "结果参考值-编辑")
    @ApiOperation(value = "结果参考值-编辑", notes = "结果参考值-编辑")
    @RequestMapping(value = "/editItemStandard", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> editItemStandard(@RequestBody ItemStandard itemStandard) {
        itemStandardService.updateById(itemStandard);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "结果参考值-通过id删除")
    @ApiOperation(value = "结果参考值-通过id删除", notes = "结果参考值-通过id删除")
    @DeleteMapping(value = "/deleteItemStandard")
    public Result<String> deleteItemStandard(@RequestParam(name = "id", required = true) String id) {
        itemStandardService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "结果参考值-批量删除")
    @ApiOperation(value = "结果参考值-批量删除", notes = "结果参考值-批量删除")
    @DeleteMapping(value = "/deleteBatchItemStandard")
    public Result<String> deleteBatchItemStandard(@RequestParam(name = "ids", required = true) String ids) {
        this.itemStandardService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportItemStandard")
    public ModelAndView exportItemStandard(HttpServletRequest request, ItemStandard itemStandard) {
        // Step.1 组装查询条件
        QueryWrapper<ItemStandard> queryWrapper = QueryGenerator.initQueryWrapper(itemStandard, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<ItemStandard> pageList = itemStandardService.list(queryWrapper);
        List<ItemStandard> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "结果参考值");
        mv.addObject(NormalExcelConstants.CLASS, ItemStandard.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("结果参考值报表", "导出人:" + sysUser.getRealname(), "结果参考值"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importItemStandard/{mainId}")
    public Result<?> importItemStandard(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ItemStandard> list = ExcelImportUtil.importExcel(file.getInputStream(), ItemStandard.class, params);
                for (ItemStandard temp : list) {
                    temp.setItemId(mainId);
                }
                long start = System.currentTimeMillis();
                itemStandardService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-结果参考值-end----------------------------------------------*/


    //testGroovyExpression
    @PostMapping(value = "/testGroovyExpression")
    public Result<?> testGroovyExpression(@RequestBody JSONObject info) {
        String expression = info.getString("expression");
        // 检查expression是否为空
        if (expression == null || expression.trim().isEmpty()) {
            return Result.error("Expression cannot be null or empty");
        }

        // 检查expression是否包含可能导致安全问题的字符或字符串
        // 这只是一个基本的示例，你可能需要根据你的具体需求来扩展这个检查
        if (expression.contains("System.exit") || expression.contains("Runtime.getRuntime")) {
            return Result.error("Expression contains potentially unsafe code");
        }
        GroovyShell shell = new GroovyShell();
        try {
            //检查expression是否合法及安全性
            shell.setVariable("itemResult", 1);

            Object result = shell.evaluate(expression);
            log.info("测试Groovy表达式成功！结果为：" + result);
            if (result == null) {
                return Result.error("表达式求值结果为空，请检查表达式是否正确！");
            }
            if (result instanceof Boolean) {

                return Result.OK("测试Groovy表达式成功！");

            } else {
                return Result.error("表达式求值结果不是bool值,请检查表达式是否正确！");
            }
        } catch (Exception e) {
            log.error("测试Groovy表达式失败！", e);
            return Result.error("测试Groovy表达式失败！");
        }
    }


}
