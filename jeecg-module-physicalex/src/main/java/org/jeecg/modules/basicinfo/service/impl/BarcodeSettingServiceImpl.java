package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.basicinfo.entity.BarcodeSetting;
import org.jeecg.modules.basicinfo.entity.BarcodeSettingGroup;
import org.jeecg.modules.basicinfo.mapper.BarcodeSettingGroupMapper;
import org.jeecg.modules.basicinfo.mapper.BarcodeSettingMapper;
import org.jeecg.modules.basicinfo.service.IBarcodeSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description: 条码配置表
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Service
public class BarcodeSettingServiceImpl extends ServiceImpl<BarcodeSettingMapper, BarcodeSetting> implements IBarcodeSettingService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private BarcodeSettingMapper barcodeSettingMapper;
    @Autowired
    private BarcodeSettingGroupMapper barcodeSettingGroupMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeBatch(List<String> ids) {
        for (String id : ids) {
            removeById(id);
            jdbcTemplate.update("delete from barcode_setting_group where setting_id=?", id);
        }
    }

    @Override
    public BarcodeSetting getExamNoSetting() {

        return barcodeSettingMapper.getExamNoSetting();
    }

    @Override
    public Page<BarcodeSetting> pageBarcodeSetting(Page<BarcodeSetting> page, String name, String groupName, String source) {
        barcodeSettingMapper.pageList(page, name, groupName, source);
        page.getRecords().forEach(barcodeSetting -> {
            List<BarcodeSettingGroup> barcodeSettingGroups = barcodeSettingGroupMapper.listByBarcodeSetting(barcodeSetting.getId());
            // barcodeSetting.setBarcodeSettingGroups(barcodeSettingGroups);
            if (!barcodeSettingGroups.isEmpty()) {
                String groupNames = "";
                for (BarcodeSettingGroup barcodeSettingGroup : barcodeSettingGroups) {
                    groupNames += StringUtils.isNotBlank(barcodeSettingGroup.getGroupName()) ? barcodeSettingGroup.getGroupName() + "," : "";
                }
                barcodeSetting.setGroupNames(StringUtils.substringBeforeLast(groupNames, ","));
            }
        });

        return page;
    }
}
