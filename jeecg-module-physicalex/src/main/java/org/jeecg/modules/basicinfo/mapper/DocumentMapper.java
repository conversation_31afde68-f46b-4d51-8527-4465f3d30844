package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.Document;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 文档表
 * @Author: jeecg-boot
 * @Date:   2025-03-05
 * @Version: V1.0
 */
public interface DocumentMapper extends BaseMapper<Document> {

    Page<Document> listAll(Page<Document> page);
}
