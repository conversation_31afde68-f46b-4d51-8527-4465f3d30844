package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.OrgArticle;
import org.jeecg.modules.basicinfo.service.IOrgArticleService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 机构介绍
 * @Author: jeecg-boot
 * @Date:   2024-12-24
 * @Version: V1.0
 */
@Api(tags="机构介绍")
@RestController
@RequestMapping("/basicinfo/orgArticle")
@Slf4j
public class OrgArticleController extends JeecgController<OrgArticle, IOrgArticleService> {
	@Autowired
	private IOrgArticleService orgArticleService;
	
	/**
	 * 分页列表查询
	 *
	 * @param orgArticle
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "机构介绍-分页列表查询")
	@ApiOperation(value="机构介绍-分页列表查询", notes="机构介绍-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<OrgArticle>> queryPageList(OrgArticle orgArticle,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<OrgArticle> queryWrapper = QueryGenerator.initQueryWrapper(orgArticle, req.getParameterMap());
		Page<OrgArticle> page = new Page<OrgArticle>(pageNo, pageSize);
		IPage<OrgArticle> pageList = orgArticleService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param orgArticle
	 * @return
	 */
	@AutoLog(value = "机构介绍-添加")
	@ApiOperation(value="机构介绍-添加", notes="机构介绍-添加")
	@RequiresPermissions("basicinfo:org_article:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody OrgArticle orgArticle) {
		orgArticleService.save(orgArticle);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param orgArticle
	 * @return
	 */
	@AutoLog(value = "机构介绍-编辑")
	@ApiOperation(value="机构介绍-编辑", notes="机构介绍-编辑")
	@RequiresPermissions("basicinfo:org_article:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody OrgArticle orgArticle) {
		orgArticleService.updateById(orgArticle);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "机构介绍-通过id删除")
	@ApiOperation(value="机构介绍-通过id删除", notes="机构介绍-通过id删除")
	@RequiresPermissions("basicinfo:org_article:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		orgArticleService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "机构介绍-批量删除")
	@ApiOperation(value="机构介绍-批量删除", notes="机构介绍-批量删除")
	@RequiresPermissions("basicinfo:org_article:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.orgArticleService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "机构介绍-通过id查询")
	@ApiOperation(value="机构介绍-通过id查询", notes="机构介绍-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<OrgArticle> queryById(@RequestParam(name="id",required=true) String id) {
		OrgArticle orgArticle = orgArticleService.getById(id);
		if(orgArticle==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(orgArticle);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param orgArticle
    */
    @RequiresPermissions("basicinfo:org_article:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrgArticle orgArticle) {
        return super.exportXls(request, orgArticle, OrgArticle.class, "机构介绍");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:org_article:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OrgArticle.class);
    }

}
