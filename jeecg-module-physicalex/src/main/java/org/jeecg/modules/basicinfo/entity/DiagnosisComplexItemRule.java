package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 符合判断规则
 * @Author: jeecg-boot
 * @Date:   2024-04-29
 * @Version: V1.0
 */
@Data
@TableName("diagnosis_complex_item_rule")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="diagnosis_complex_item_rule对象", description="符合判断规则")
public class DiagnosisComplexItemRule implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**标签*/
	@Excel(name = "标签", width = 15)
    @ApiModelProperty(value = "标签")
    private java.lang.String title;
	/**项目*/
	@Excel(name = "项目", width = 15)
    @ApiModelProperty(value = "项目")
    private java.lang.String itemName;
	/**项目ID*/
	@Excel(name = "项目ID", width = 15)
    @ApiModelProperty(value = "项目ID")
    private java.lang.String itemId;
	/**字段名称*/
	@Excel(name = "字段名称", width = 15)
    @ApiModelProperty(value = "字段名称")
    private java.lang.String fieldName;
	/**字段类型*/
	@Excel(name = "字段类型", width = 15)
    @ApiModelProperty(value = "字段类型")
    private java.lang.String fieldType;
	/**规则值*/
	@Excel(name = "规则值", width = 15)
    @ApiModelProperty(value = "规则值")
    private java.lang.String fieldValue;
	/**字段单位*/
	@Excel(name = "字段单位", width = 15)
    @ApiModelProperty(value = "字段单位")
    private java.lang.String fieldUnit;
	/**操作符*/
	@Excel(name = "操作符", width = 15)
    @ApiModelProperty(value = "操作符")
    private java.lang.String operator;
    /**复合诊断ID*/
    @Excel(name = "复合诊断ID", width = 15)
    @ApiModelProperty(value = "复合诊断ID")
    private String complexId;
    /**groovy表达式*/
    @Excel(name = "groovy表达式", width = 15)
    @ApiModelProperty(value = "groovy表达式")
    private String  groovyExpression;

    @TableField(exist = false)
    private boolean matched;
}
