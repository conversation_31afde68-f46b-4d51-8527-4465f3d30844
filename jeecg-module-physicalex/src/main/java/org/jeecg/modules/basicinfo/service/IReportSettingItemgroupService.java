package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.ReportSettingItemgroup;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 报告分组设置-关联大项
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
public interface IReportSettingItemgroupService extends IService<ReportSettingItemgroup> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<ReportSettingItemgroup>
	 */
	public List<ReportSettingItemgroup> selectByMainId(String mainId);
}
