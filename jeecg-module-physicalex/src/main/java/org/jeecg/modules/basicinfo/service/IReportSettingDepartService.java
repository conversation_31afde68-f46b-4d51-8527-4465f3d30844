package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.ReportSettingDepart;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 报告分组设置-关联科室
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
public interface IReportSettingDepartService extends IService<ReportSettingDepart> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<ReportSettingDepart>
	 */
	public List<ReportSettingDepart> selectByMainId(String mainId);
}
