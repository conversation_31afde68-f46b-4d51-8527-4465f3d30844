<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.SysSettingMapper">
    <select id="getByCode" resultType="org.jeecg.modules.basicinfo.entity.SysSetting">
        SELECT * FROM sys_setting WHERE code = #{code} and del_flag='0' and enable_flag='1' limit 1
    </select>
    <select id="getValueByCode" resultType="String">
        SELECT value FROM sys_setting WHERE code = #{code} limit 1
    </select>
</mapper>