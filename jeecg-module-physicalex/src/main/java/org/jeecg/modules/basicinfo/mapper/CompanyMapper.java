package org.jeecg.modules.basicinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.basicinfo.entity.Company;

import java.util.List;
import java.util.Map;

/**
 * @Description: 单位
 * @Author: jeecg-boot
 * @Date:   2024-04-09
 * @Version: V1.0
 */
public interface CompanyMapper extends BaseMapper<Company> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id,@Param("status") String status);

	/**
	 * 【vue3专用】根据父级ID查询树节点数据
	 *
	 * @param pid
	 * @param query
	 * @return
	 */
	List<SelectTreeModel> queryListByPid(@Param("pid") String pid, @Param("query") Map<String, String> query);

	Company getTeamExamCompany(@Param("companyRegId") String companyRegId);

}
