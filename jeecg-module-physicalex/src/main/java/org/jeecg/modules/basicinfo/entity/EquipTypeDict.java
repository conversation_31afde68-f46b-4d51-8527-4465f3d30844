package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: equip_type_dict
 * @Author: jeecg-boot
 * @Date:   2025-05-21
 * @Version: V1.0
 */
@Data
@TableName("equip_type_dict")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="equip_type_dict对象", description="equip_type_dict")
public class EquipTypeDict implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**equipTypeCode*/
	@Excel(name = "equipTypeCode", width = 15)
    @ApiModelProperty(value = "equipTypeCode")
    private java.lang.String equipTypeCode;
	/**equipTypeName*/
	@Excel(name = "equipTypeName", width = 15)
    @ApiModelProperty(value = "equipTypeName")
    private java.lang.String equipTypeName;
}
