package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.basicinfo.entity.ReportSettingGroup;
import org.jeecg.modules.basicinfo.entity.ReportSettingDepart;
import org.jeecg.modules.basicinfo.entity.ReportSettingItemgroup;
import org.jeecg.modules.basicinfo.mapper.ReportSettingDepartMapper;
import org.jeecg.modules.basicinfo.mapper.ReportSettingItemgroupMapper;
import org.jeecg.modules.basicinfo.mapper.ReportSettingGroupMapper;
import org.jeecg.modules.basicinfo.service.IReportSettingGroupService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 报告分组设置
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
@Service
public class ReportSettingGroupServiceImpl extends ServiceImpl<ReportSettingGroupMapper, ReportSettingGroup> implements IReportSettingGroupService {

	@Autowired
	private ReportSettingGroupMapper reportSettingGroupMapper;
	@Autowired
	private ReportSettingDepartMapper reportSettingDepartMapper;
	@Autowired
	private ReportSettingItemgroupMapper reportSettingItemgroupMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(ReportSettingGroup reportSettingGroup, List<ReportSettingDepart> reportSettingDepartList,List<ReportSettingItemgroup> reportSettingItemgroupList) {
		reportSettingGroupMapper.insert(reportSettingGroup);
		if(reportSettingDepartList!=null && reportSettingDepartList.size()>0) {
			for(ReportSettingDepart entity:reportSettingDepartList) {
				//外键设置
				entity.setSettingId(reportSettingGroup.getId());
				reportSettingDepartMapper.insert(entity);
			}
		}
		if(reportSettingItemgroupList!=null && reportSettingItemgroupList.size()>0) {
			for(ReportSettingItemgroup entity:reportSettingItemgroupList) {
				//外键设置
				entity.setSettingId(reportSettingGroup.getId());
				reportSettingItemgroupMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(ReportSettingGroup reportSettingGroup,List<ReportSettingDepart> reportSettingDepartList,List<ReportSettingItemgroup> reportSettingItemgroupList) {
		reportSettingGroupMapper.updateById(reportSettingGroup);
		
		//1.先删除子表数据
		reportSettingDepartMapper.deleteByMainId(reportSettingGroup.getId());
		reportSettingItemgroupMapper.deleteByMainId(reportSettingGroup.getId());
		
		//2.子表数据重新插入
		if(reportSettingDepartList!=null && reportSettingDepartList.size()>0) {
			for(ReportSettingDepart entity:reportSettingDepartList) {
				//外键设置
				entity.setSettingId(reportSettingGroup.getId());
				reportSettingDepartMapper.insert(entity);
			}
		}
		if(reportSettingItemgroupList!=null && reportSettingItemgroupList.size()>0) {
			for(ReportSettingItemgroup entity:reportSettingItemgroupList) {
				//外键设置
				entity.setSettingId(reportSettingGroup.getId());
				reportSettingItemgroupMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		reportSettingDepartMapper.deleteByMainId(id);
		reportSettingItemgroupMapper.deleteByMainId(id);
		reportSettingGroupMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			reportSettingDepartMapper.deleteByMainId(id.toString());
			reportSettingItemgroupMapper.deleteByMainId(id.toString());
			reportSettingGroupMapper.deleteById(id);
		}
	}

	@Override
	public ReportSettingGroup getReportSettingGroupByCode(String code) {
		QueryWrapper<ReportSettingGroup> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("code", code);
		ReportSettingGroup reportSettingGroup = reportSettingGroupMapper.selectOne(queryWrapper);
		if(reportSettingGroup != null) {
			QueryWrapper<ReportSettingItemgroup> itemgroupQueryWrapper = new QueryWrapper<>();
			itemgroupQueryWrapper.eq("setting_id", reportSettingGroup.getId());
			itemgroupQueryWrapper.orderByAsc("seq");
			List<ReportSettingItemgroup> reportSettingItemgroupList = reportSettingItemgroupMapper.selectList(itemgroupQueryWrapper);
			reportSettingGroup.setItemgroupList(reportSettingItemgroupList);

			QueryWrapper<ReportSettingDepart> departQueryWrapper = new QueryWrapper<>();
			departQueryWrapper.eq("setting_id", reportSettingGroup.getId());
			departQueryWrapper.orderByAsc("seq");
			List<ReportSettingDepart> reportSettingDepartList = reportSettingDepartMapper.selectList(departQueryWrapper);
			reportSettingGroup.setDepartList(reportSettingDepartList);
		}

		return reportSettingGroup;
	}

	@Override
	public List<ReportSettingGroup> listReportSettingGroup() {
		QueryWrapper<ReportSettingGroup> queryWrapper = new QueryWrapper<>();
		queryWrapper.orderByAsc("seq");
		List<ReportSettingGroup> reportSettingGroupList = reportSettingGroupMapper.selectList(queryWrapper);
		if(reportSettingGroupList != null && !reportSettingGroupList.isEmpty()) {
			for(ReportSettingGroup reportSettingGroup:reportSettingGroupList) {
				QueryWrapper<ReportSettingItemgroup> itemgroupQueryWrapper = new QueryWrapper<>();
				itemgroupQueryWrapper.eq("setting_id", reportSettingGroup.getId());
				itemgroupQueryWrapper.orderByAsc("seq");
				List<ReportSettingItemgroup> reportSettingItemgroupList = reportSettingItemgroupMapper.selectList(itemgroupQueryWrapper);
				reportSettingGroup.setItemgroupList(reportSettingItemgroupList);

				QueryWrapper<ReportSettingDepart> departQueryWrapper = new QueryWrapper<>();
				departQueryWrapper.eq("setting_id", reportSettingGroup.getId());
				departQueryWrapper.orderByAsc("seq");
				List<ReportSettingDepart> reportSettingDepartList = reportSettingDepartMapper.selectList(departQueryWrapper);
				reportSettingGroup.setDepartList(reportSettingDepartList);
			}
		}

		return reportSettingGroupList;
	}
	
}
