package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 条码配置表
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Data
@TableName("barcode_setting")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "barcode_setting对象", description = "条码配置表")
public class BarcodeSetting implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
    /**
     * 条码号来源
     */
    @Excel(name = "条码号来源", width = 15, dicCode = "barcode_num_source")
    @Dict(dicCode = "barcode_num_source")
    @ApiModelProperty(value = "条码号来源")
    private java.lang.String barNumSource;
    /**
     * 条码号前缀
     */
    @Excel(name = "条码号前缀", width = 15)
    @ApiModelProperty(value = "条码号前缀")
    private java.lang.String barNumPrefix;
    /**
     * 打印内容
     */
    @Excel(name = "打印内容", width = 15)
    @ApiModelProperty(value = "打印内容")
    private java.lang.String text;
    /**
     * 打印个数
     */
    @Excel(name = "打印个数", width = 15)
    @ApiModelProperty(value = "打印个数")
    private java.lang.Integer barPage;
    /**
     * 标本类别
     */
    @Excel(name = "标本类别", width = 15, dicCode = "sample_type")
    @Dict(dicCode = "sample_type")
    @ApiModelProperty(value = "标本类别")
    private java.lang.String sampleType;
    /**
     * 检验方式
     */
    @Excel(name = "检验方式", width = 15, dicCode = "test_type")
    @Dict(dicCode = "test_type")
    @ApiModelProperty(value = "检验方式")
    private java.lang.String testType;
    /**
     * 试管颜色
     */
    @Excel(name = "试管颜色", width = 15, dicCode = "tube_color")
    @Dict(dicCode = "tube_color")
    @ApiModelProperty(value = "试管颜色")
    private java.lang.String tubeColor;
    /**
     * 条码序号
     */
    @Excel(name = "条码序号", width = 15)
    @ApiModelProperty(value = "条码序号")
    private java.lang.Integer sort;
    /**
     * 模板
     */
    @Excel(name = "模板", width = 15, dictTable = "barcode_template", dicText = "name", dicCode = "id")
    @Dict(dictTable = "barcode_template", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "模板")
    private java.lang.String templateId;
    /**
     * 使用耗材
     */
    @Excel(name = "使用耗材", width = 15)
    @ApiModelProperty(value = "使用耗材")
    private java.lang.String useConsumable;
    /**
     * 所属位置
     */
    @Excel(name = "所属位置", width = 15, dicCode = "barcode_location")
    @Dict(dicCode = "barcode_location")
    @ApiModelProperty(value = "所属位置")
    private java.lang.String printLocation;
    /**
     * 启用
     */
    @Excel(name = "启用", width = 15)
    @ApiModelProperty(value = "启用")
    private java.lang.Integer enableFlag;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 检验科室
     */
    @Excel(name = "检验科室", width = 15)
    @ApiModelProperty(value = "检验科室")
    private String checkDepart;

    /**
     * 样本类别
     */
    @ApiModelProperty(value = "样本类别")
    private String specimenCategory;

    /**
     * 样本类别代码
     */
    @ApiModelProperty(value = "样本类别代码")
    private String specimenCategoryCode;

    private String source;

    /**
     * 优先级，数值越大优先级越高
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @TableField(exist = false)
    private List<BarcodeSettingGroup> barcodeSettingGroups;

    @TableField(exist = false)
    private String groupNames;
}
