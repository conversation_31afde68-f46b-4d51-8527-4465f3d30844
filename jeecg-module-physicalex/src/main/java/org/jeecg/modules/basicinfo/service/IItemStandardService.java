package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.ItemStandard;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 结果参考值
 * @Author: jeecg-boot
 * @Date:   2024-01-31
 * @Version: V1.0
 */
public interface IItemStandardService extends IService<ItemStandard> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<ItemStandard>
	 */
	public List<ItemStandard> selectByMainId(String mainId);

	ItemStandard selectNormalStandardByItemId(String itemId);
}
