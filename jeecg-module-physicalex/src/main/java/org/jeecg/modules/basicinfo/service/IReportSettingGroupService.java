package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.ReportSettingDepart;
import org.jeecg.modules.basicinfo.entity.ReportSettingItemgroup;
import org.jeecg.modules.basicinfo.entity.ReportSettingGroup;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 报告分组设置
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
public interface IReportSettingGroupService extends IService<ReportSettingGroup> {

	/**
	 * 添加一对多
	 *
	 * @param reportSettingGroup
	 * @param reportSettingDepartList
	 * @param reportSettingItemgroupList
	 */
	public void saveMain(ReportSettingGroup reportSettingGroup,List<ReportSettingDepart> reportSettingDepartList,List<ReportSettingItemgroup> reportSettingItemgroupList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param reportSettingGroup
	 * @param reportSettingDepartList
	 * @param reportSettingItemgroupList
	 */
	public void updateMain(ReportSettingGroup reportSettingGroup,List<ReportSettingDepart> reportSettingDepartList,List<ReportSettingItemgroup> reportSettingItemgroupList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);


	ReportSettingGroup getReportSettingGroupByCode(String code);

	List<ReportSettingGroup> listReportSettingGroup();
}
