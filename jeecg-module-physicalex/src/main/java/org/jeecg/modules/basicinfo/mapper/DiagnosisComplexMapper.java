package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplex;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 符合判断
 * @Author: jeecg-boot
 * @Date:   2024-04-26
 * @Version: V1.0
 */
public interface DiagnosisComplexMapper extends BaseMapper<DiagnosisComplex> {

    Page<DiagnosisComplex> pageDiagnosisComplex(Page<DiagnosisComplex> page, @Param("departmentId") String departmentId, @Param("name") String name, @Param("enableFlag") Integer enableFlag);
}
