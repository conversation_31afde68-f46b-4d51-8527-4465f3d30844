package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.Pcca;
import org.jeecg.modules.basicinfo.service.IPccaService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 省市区县
 * @Author: jeecg-boot
 * @Date:   2024-12-13
 * @Version: V1.0
 */
@Api(tags="省市区县")
@RestController
@RequestMapping("/basicinfo/pcca")
@Slf4j
public class PccaController extends JeecgController<Pcca, IPccaService> {
	@Autowired
	private IPccaService pccaService;

	 /**
	  * 根据pid进行查询
	  * @return
	  */
	 @ApiOperation(value = "省市区数据-分页列表查询", notes = "省市区数据-分页列表查询")
	 @GetMapping(value = "/queryByPid")
	 public Result<?> queryByPid(String pid) {
		 QueryWrapper<Pcca> queryWrapper = new QueryWrapper<>();
		 if (StringUtils.isNotBlank(pid)) {
			 queryWrapper.eq("pid", pid);
		 } else {
			 queryWrapper.isNull("pid").or().eq("pid", "0");
		 }
		 List<Pcca> list = pccaService.list(queryWrapper);
		 return Result.OK(list);
	 }


	 /**
	 * 分页列表查询
	 *
	 * @param pcca
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "省市区县-分页列表查询")
	@ApiOperation(value="省市区县-分页列表查询", notes="省市区县-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Pcca>> queryPageList(Pcca pcca,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Pcca> queryWrapper = QueryGenerator.initQueryWrapper(pcca, req.getParameterMap());
		Page<Pcca> page = new Page<Pcca>(pageNo, pageSize);
		IPage<Pcca> pageList = pccaService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pcca
	 * @return
	 */
	@AutoLog(value = "省市区县-添加")
	@ApiOperation(value="省市区县-添加", notes="省市区县-添加")
	@RequiresPermissions("basicinfo:pcca:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Pcca pcca) {
		pccaService.save(pcca);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pcca
	 * @return
	 */
	@AutoLog(value = "省市区县-编辑")
	@ApiOperation(value="省市区县-编辑", notes="省市区县-编辑")
	@RequiresPermissions("basicinfo:pcca:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Pcca pcca) {
		pccaService.updateById(pcca);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "省市区县-通过id删除")
	@ApiOperation(value="省市区县-通过id删除", notes="省市区县-通过id删除")
	@RequiresPermissions("basicinfo:pcca:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pccaService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "省市区县-批量删除")
	@ApiOperation(value="省市区县-批量删除", notes="省市区县-批量删除")
	@RequiresPermissions("basicinfo:pcca:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pccaService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "省市区县-通过id查询")
	@ApiOperation(value="省市区县-通过id查询", notes="省市区县-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Pcca> queryById(@RequestParam(name="id",required=true) String id) {
		Pcca pcca = pccaService.getById(id);
		if(pcca==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pcca);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pcca
    */
    @RequiresPermissions("basicinfo:pcca:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Pcca pcca) {
        return super.exportXls(request, pcca, Pcca.class, "省市区县");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:pcca:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Pcca.class);
    }

}
