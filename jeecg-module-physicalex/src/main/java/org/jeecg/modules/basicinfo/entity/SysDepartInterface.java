package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: sys_depart_interface
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
@Data
@TableName("sys_depart_interface")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_depart_interface对象", description="sys_depart_interface")
public class SysDepartInterface implements Serializable {
    private static final long serialVersionUID = 1L;

	/**dept_code科室代码*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "dept_code科室代码")
    private java.lang.String id;
	/**机构代码*/
	@Excel(name = "机构代码", width = 15)
    @ApiModelProperty(value = "机构代码")
    private java.lang.String orgCode;
	/**院区代码*/
	@Excel(name = "院区代码", width = 15)
    @ApiModelProperty(value = "院区代码")
    private java.lang.String districtCode;
	/**科室代码*/
	@Excel(name = "科室代码", width = 15)
    @ApiModelProperty(value = "科室代码")
    private java.lang.String deptCode;
	/**科室名称*/
	@Excel(name = "科室名称", width = 15)
    @ApiModelProperty(value = "科室名称")
    private java.lang.String deptName;
	/**科室级别*/
	@Excel(name = "科室级别", width = 15)
    @ApiModelProperty(value = "科室级别")
    private java.lang.String deptGrade;
	/**上级科室代码*/
	@Excel(name = "上级科室代码", width = 15)
    @ApiModelProperty(value = "上级科室代码")
    private java.lang.String upperDeptCode;
	/**上级科室名称*/
	@Excel(name = "上级科室名称", width = 15)
    @ApiModelProperty(value = "上级科室名称")
    private java.lang.String upperDeptName;
	/**所在病区代码*/
	@Excel(name = "所在病区代码", width = 15)
    @ApiModelProperty(value = "所在病区代码")
    private java.lang.String wardCode;
	/**所在病区*/
	@Excel(name = "所在病区", width = 15)
    @ApiModelProperty(value = "所在病区")
    private java.lang.String wardName;
	/**科室属性*/
	@Excel(name = "科室属性", width = 15)
    @ApiModelProperty(value = "科室属性")
    private java.lang.String deptAttr;
	/**核定床位数*/
	@Excel(name = "核定床位数", width = 15)
    @ApiModelProperty(value = "核定床位数")
    private java.lang.String checkBedCount;
	/**有效标志*/
	@Excel(name = "有效标志", width = 15)
    @ApiModelProperty(value = "有效标志")
    private java.lang.String validFlag;
	/**拼音码*/
	@Excel(name = "拼音码", width = 15)
    @ApiModelProperty(value = "拼音码")
    private java.lang.String spellCode;
	/**五笔音首码*/
	@Excel(name = "五笔音首码", width = 15)
    @ApiModelProperty(value = "五笔音首码")
    private java.lang.String wbzxCode;
	/**科室类型代码*/
	@Excel(name = "科室类型代码", width = 15)
    @ApiModelProperty(value = "科室类型代码")
    private java.lang.String deptTypeCode;
	/**科室类型名称*/
	@Excel(name = "科室类型名称", width = 15)
    @ApiModelProperty(value = "科室类型名称")
    private java.lang.String deptTypeName;
	/**科室特殊标记*/
	@Excel(name = "科室特殊标记", width = 15)
    @ApiModelProperty(value = "科室特殊标记")
    private java.lang.String specialDeptCode;
	/**联系电话*/
	@Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private java.lang.String deptPhone;
	/**科室物理地址*/
	@Excel(name = "科室物理地址", width = 15)
    @ApiModelProperty(value = "科室物理地址")
    private java.lang.String deptLocation;
	/**简称*/
	@Excel(name = "简称", width = 15)
    @ApiModelProperty(value = "简称")
    private java.lang.String deptShortName;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**科室简介*/
	@Excel(name = "科室简介", width = 15)
    @ApiModelProperty(value = "科室简介")
    private java.lang.String deptIntro;
	/**科室别名*/
	@Excel(name = "科室别名", width = 15)
    @ApiModelProperty(value = "科室别名")
    private java.lang.String deptAlias;
	/**专业科室代码*/
	@Excel(name = "专业科室代码", width = 15)
    @ApiModelProperty(value = "专业科室代码")
    private java.lang.String professionalDeptCode;
	/**专业科室名称*/
	@Excel(name = "专业科室名称", width = 15)
    @ApiModelProperty(value = "专业科室名称")
    private java.lang.String professionalDeptName;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**联系电话*/
	@Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private java.lang.String tel;
}
