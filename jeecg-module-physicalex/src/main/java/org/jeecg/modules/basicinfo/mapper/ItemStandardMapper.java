package org.jeecg.modules.basicinfo.mapper;

import java.util.List;
import org.jeecg.modules.basicinfo.entity.ItemStandard;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

/**
 * @Description: 结果参考值
 * @Author: jeecg-boot
 * @Date:   2024-01-31
 * @Version: V1.0
 */
public interface ItemStandardMapper extends BaseMapper<ItemStandard> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<ItemStandard>
   */
	 List<ItemStandard> selectByMainId(@Param("mainId") String mainId);

	ItemStandard selectStandardByResultSymbo(@Param("itemId") String itemId,@Param("symbo") String symbo);

	ItemStandard selectDefaultStandard(@Param("itemId") String itemId);

}
