package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.MsgTemplate;
import org.jeecg.modules.basicinfo.mapper.MsgTemplateMapper;
import org.jeecg.modules.basicinfo.service.IMsgTemplateService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 消息模版
 * @Author: jeecg-boot
 * @Date:   2024-11-10
 * @Version: V1.0
 */
@Service
public class MsgTemplateServiceImpl extends ServiceImpl<MsgTemplateMapper, MsgTemplate> implements IMsgTemplateService {

}
