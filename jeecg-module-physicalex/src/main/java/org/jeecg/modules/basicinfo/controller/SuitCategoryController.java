package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.SuitCategory;
import org.jeecg.modules.basicinfo.service.ISuitCategoryService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 套餐类别
 * @Author: jeecg-boot
 * @Date:   2024-10-08
 * @Version: V1.0
 */
@Api(tags="套餐类别")
@RestController
@RequestMapping("/basicinfo/suitCategory")
@Slf4j
public class SuitCategoryController extends JeecgController<SuitCategory, ISuitCategoryService> {
	@Autowired
	private ISuitCategoryService suitCategoryService;
	
	/**
	 * 分页列表查询
	 *
	 * @param suitCategory
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "套餐类别-分页列表查询")
	@ApiOperation(value="套餐类别-分页列表查询", notes="套餐类别-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SuitCategory>> queryPageList(SuitCategory suitCategory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SuitCategory> queryWrapper = QueryGenerator.initQueryWrapper(suitCategory, req.getParameterMap());
		Page<SuitCategory> page = new Page<SuitCategory>(pageNo, pageSize);
		IPage<SuitCategory> pageList = suitCategoryService.page(page, queryWrapper);
		return Result.OK(pageList);
	}


	 @ApiOperation(value="套餐类别-列表查询", notes="套餐类别-列表查询")
	 @GetMapping(value = "/queryList")
	 public Result<List<SuitCategory>> queryList() {
		 List<SuitCategory> categories = suitCategoryService.list(new LambdaQueryWrapper<SuitCategory>().orderByAsc(SuitCategory::getCategoryOrder));
		 return Result.OK(categories);
	 }
	/**
	 *   添加
	 *
	 * @param suitCategory
	 * @return
	 */
	@AutoLog(value = "套餐类别-添加")
	@ApiOperation(value="套餐类别-添加", notes="套餐类别-添加")
	@RequiresPermissions("basicinfo:suit_category:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SuitCategory suitCategory) {
		suitCategoryService.save(suitCategory);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param suitCategory
	 * @return
	 */
	@AutoLog(value = "套餐类别-编辑")
	@ApiOperation(value="套餐类别-编辑", notes="套餐类别-编辑")
	@RequiresPermissions("basicinfo:suit_category:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SuitCategory suitCategory) {
		suitCategoryService.updateById(suitCategory);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "套餐类别-通过id删除")
	@ApiOperation(value="套餐类别-通过id删除", notes="套餐类别-通过id删除")
	@RequiresPermissions("basicinfo:suit_category:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		suitCategoryService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "套餐类别-批量删除")
	@ApiOperation(value="套餐类别-批量删除", notes="套餐类别-批量删除")
	@RequiresPermissions("basicinfo:suit_category:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.suitCategoryService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "套餐类别-通过id查询")
	@ApiOperation(value="套餐类别-通过id查询", notes="套餐类别-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SuitCategory> queryById(@RequestParam(name="id",required=true) String id) {
		SuitCategory suitCategory = suitCategoryService.getById(id);
		if(suitCategory==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(suitCategory);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param suitCategory
    */
    @RequiresPermissions("basicinfo:suit_category:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SuitCategory suitCategory) {
        return super.exportXls(request, suitCategory, SuitCategory.class, "套餐类别");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:suit_category:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SuitCategory.class);
    }

}
