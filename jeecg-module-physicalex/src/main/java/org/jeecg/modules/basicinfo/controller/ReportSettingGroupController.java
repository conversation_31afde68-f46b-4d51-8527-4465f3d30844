package org.jeecg.modules.basicinfo.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.ReportSettingDepart;
import org.jeecg.modules.basicinfo.entity.ReportSettingItemgroup;
import org.jeecg.modules.basicinfo.entity.ReportSettingGroup;
import org.jeecg.modules.basicinfo.vo.ReportSettingGroupPage;
import org.jeecg.modules.basicinfo.service.IReportSettingGroupService;
import org.jeecg.modules.basicinfo.service.IReportSettingDepartService;
import org.jeecg.modules.basicinfo.service.IReportSettingItemgroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 报告分组设置
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
@Api(tags="报告分组设置")
@RestController
@RequestMapping("/basicinfo/reportSettingGroup")
@Slf4j
public class ReportSettingGroupController {
	@Autowired
	private IReportSettingGroupService reportSettingGroupService;
	@Autowired
	private IReportSettingDepartService reportSettingDepartService;
	@Autowired
	private IReportSettingItemgroupService reportSettingItemgroupService;
	
	/**
	 * 分页列表查询
	 *
	 * @param reportSettingGroup
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "报告分组设置-分页列表查询")
	@ApiOperation(value="报告分组设置-分页列表查询", notes="报告分组设置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ReportSettingGroup>> queryPageList(ReportSettingGroup reportSettingGroup,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ReportSettingGroup> queryWrapper = QueryGenerator.initQueryWrapper(reportSettingGroup, req.getParameterMap());
		Page<ReportSettingGroup> page = new Page<ReportSettingGroup>(pageNo, pageSize);
		IPage<ReportSettingGroup> pageList = reportSettingGroupService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param reportSettingGroupPage
	 * @return
	 */
	@AutoLog(value = "报告分组设置-添加")
	@ApiOperation(value="报告分组设置-添加", notes="报告分组设置-添加")
    @RequiresPermissions("basicinfo:report_setting_group:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ReportSettingGroupPage reportSettingGroupPage) {
		ReportSettingGroup reportSettingGroup = new ReportSettingGroup();
		BeanUtils.copyProperties(reportSettingGroupPage, reportSettingGroup);
		reportSettingGroupService.saveMain(reportSettingGroup, reportSettingGroupPage.getReportSettingDepartList(),reportSettingGroupPage.getReportSettingItemgroupList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param reportSettingGroupPage
	 * @return
	 */
	@AutoLog(value = "报告分组设置-编辑")
	@ApiOperation(value="报告分组设置-编辑", notes="报告分组设置-编辑")
    @RequiresPermissions("basicinfo:report_setting_group:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ReportSettingGroupPage reportSettingGroupPage) {
		ReportSettingGroup reportSettingGroup = new ReportSettingGroup();
		BeanUtils.copyProperties(reportSettingGroupPage, reportSettingGroup);
		ReportSettingGroup reportSettingGroupEntity = reportSettingGroupService.getById(reportSettingGroup.getId());
		if(reportSettingGroupEntity==null) {
			return Result.error("未找到对应数据");
		}
		reportSettingGroupService.updateMain(reportSettingGroup, reportSettingGroupPage.getReportSettingDepartList(),reportSettingGroupPage.getReportSettingItemgroupList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "报告分组设置-通过id删除")
	@ApiOperation(value="报告分组设置-通过id删除", notes="报告分组设置-通过id删除")
    @RequiresPermissions("basicinfo:report_setting_group:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		reportSettingGroupService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "报告分组设置-批量删除")
	@ApiOperation(value="报告分组设置-批量删除", notes="报告分组设置-批量删除")
    @RequiresPermissions("basicinfo:report_setting_group:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.reportSettingGroupService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "报告分组设置-通过id查询")
	@ApiOperation(value="报告分组设置-通过id查询", notes="报告分组设置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ReportSettingGroup> queryById(@RequestParam(name="id",required=true) String id) {
		ReportSettingGroup reportSettingGroup = reportSettingGroupService.getById(id);
		if(reportSettingGroup==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(reportSettingGroup);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "报告分组设置-关联科室通过主表ID查询")
	@ApiOperation(value="报告分组设置-关联科室主表ID查询", notes="报告分组设置-关联科室-通主表ID查询")
	@GetMapping(value = "/queryReportSettingDepartByMainId")
	public Result<List<ReportSettingDepart>> queryReportSettingDepartListByMainId(@RequestParam(name="id",required=true) String id) {
		List<ReportSettingDepart> reportSettingDepartList = reportSettingDepartService.selectByMainId(id);
		return Result.OK(reportSettingDepartList);
	}
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "报告分组设置-关联大项通过主表ID查询")
	@ApiOperation(value="报告分组设置-关联大项主表ID查询", notes="报告分组设置-关联大项-通主表ID查询")
	@GetMapping(value = "/queryReportSettingItemgroupByMainId")
	public Result<List<ReportSettingItemgroup>> queryReportSettingItemgroupListByMainId(@RequestParam(name="id",required=true) String id) {
		List<ReportSettingItemgroup> reportSettingItemgroupList = reportSettingItemgroupService.selectByMainId(id);
		return Result.OK(reportSettingItemgroupList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param reportSettingGroup
    */
    @RequiresPermissions("basicinfo:report_setting_group:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ReportSettingGroup reportSettingGroup) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<ReportSettingGroup> queryWrapper = QueryGenerator.initQueryWrapper(reportSettingGroup, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //配置选中数据查询条件
       String selections = request.getParameter("selections");
       if(oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id",selectionList);
       }
       //Step.2 获取导出数据
       List<ReportSettingGroup> reportSettingGroupList = reportSettingGroupService.list(queryWrapper);

      // Step.3 组装pageList
      List<ReportSettingGroupPage> pageList = new ArrayList<ReportSettingGroupPage>();
      for (ReportSettingGroup main : reportSettingGroupList) {
          ReportSettingGroupPage vo = new ReportSettingGroupPage();
          BeanUtils.copyProperties(main, vo);
          List<ReportSettingDepart> reportSettingDepartList = reportSettingDepartService.selectByMainId(main.getId());
          vo.setReportSettingDepartList(reportSettingDepartList);
          List<ReportSettingItemgroup> reportSettingItemgroupList = reportSettingItemgroupService.selectByMainId(main.getId());
          vo.setReportSettingItemgroupList(reportSettingItemgroupList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "报告分组设置列表");
      mv.addObject(NormalExcelConstants.CLASS, ReportSettingGroupPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("报告分组设置数据", "导出人:"+sysUser.getRealname(), "报告分组设置"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:report_setting_group:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<ReportSettingGroupPage> list = ExcelImportUtil.importExcel(file.getInputStream(), ReportSettingGroupPage.class, params);
              for (ReportSettingGroupPage page : list) {
                  ReportSettingGroup po = new ReportSettingGroup();
                  BeanUtils.copyProperties(page, po);
                  reportSettingGroupService.saveMain(po, page.getReportSettingDepartList(),page.getReportSettingItemgroupList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
