package org.jeecg.modules.basicinfo.entity;

import lombok.Data;

import java.util.List;

@Data
public class GroupRelationVO {

    private String groupId;
    private String groupName;
    private List<ItemGroupRelation> attachGroups;
    private List<String> exclusiveGroups;
    // 新增：依赖项目配置
    private List<ItemGroupRelation> dependentGroups;
    // 新增：赠送项目配置
    private List<ItemGroupRelation> giftGroups;

   class  AttachGroup {
       private List<String> groupIds;
       private Integer quantity;
   }
}
