package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.basicinfo.entity.ItemDict;
import org.jeecg.modules.basicinfo.mapper.ItemDictMapper;
import org.jeecg.modules.basicinfo.service.IItemDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 项目结果字典
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 * @Version: V1.0
 */
@Service
@CacheConfig(cacheNames = "itemDictCache", cacheManager = "caffeineCacheManager")
public class ItemDictServiceImpl extends ServiceImpl<ItemDictMapper, ItemDict> implements IItemDictService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ItemDictMapper itemDictMapper;

    @Override
    public ItemDict getDefaultDict(String itemId) {

        return itemDictMapper.getDefaultDict(itemId);
    }

    @Override
    public Long getNextSeqNo(String itemId) {

        Long maxCount = 0L;
        try {
            maxCount = jdbcTemplate.queryForObject("select max(seq) from item_dict where item_id=?", Long.class, itemId);
        } catch (Exception e) {
        }

        return maxCount == null ? 1 : maxCount + 1;
    }

    @Override
    public boolean duplicateCheck(String itemId, String dictText, String id) {
        if (StringUtils.isBlank(dictText)) {
            return false;
        }
        dictText = StringUtils.trim(dictText);
        List<String> queryParam = new ArrayList<>();
        String sql = "select count(1) from item_dict where item_id=? and dict_text=?";
        queryParam.add(itemId);
        queryParam.add(dictText);

        if (StringUtils.isNotBlank(id)) {
            sql += " and id!=?";
            queryParam.add(id);
        }
        Long existCount = jdbcTemplate.queryForObject(sql, Long.class, queryParam.toArray());
        existCount = existCount == null ? 0 : existCount;
        return existCount > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void resetDefault(ItemDict itemDict, String dictId) {
        if (StringUtils.equals(itemDict.getDefaultFlag(), "1")) {
            jdbcTemplate.update("update item_dict set default_flag='0' where item_id=?", itemDict);
            jdbcTemplate.update("update item_dict set default_flag='1' where item_id=? and id=?", itemDict, dictId);
        }
    }

    @Override
    @Cacheable(key = "#itemId", unless = "#result == null")
    public List<ItemDict> listByItemId(String itemId) {
        return itemDictMapper.listByItemId(itemId);
    }

    @Override
    public void increaseUseCount(String dictId, Integer count) {
        jdbcTemplate.update("update item_dict set use_count=use_count+? where id=?", count, dictId);
    }
}
