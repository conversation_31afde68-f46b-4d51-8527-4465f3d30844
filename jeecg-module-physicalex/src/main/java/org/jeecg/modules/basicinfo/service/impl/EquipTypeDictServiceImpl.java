package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.EquipTypeDict;
import org.jeecg.modules.basicinfo.mapper.EquipTypeDictMapper;
import org.jeecg.modules.basicinfo.service.IEquipTypeDictService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: equip_type_dict
 * @Author: jeecg-boot
 * @Date:   2025-05-21
 * @Version: V1.0
 */
@Service
public class EquipTypeDictServiceImpl extends ServiceImpl<EquipTypeDictMapper, EquipTypeDict> implements IEquipTypeDictService {

}
