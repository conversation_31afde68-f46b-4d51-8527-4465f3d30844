package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: item_group_relation
 * @Author: jeecg-boot
 * @Date:   2024-12-02
 * @Version: V1.0
 */
@Data
@TableName("item_group_relation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="item_group_relation对象", description="item_group_relation")
public class ItemGroupRelation implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**关联主项*/
	@Excel(name = "关联主项", width = 15)
    @ApiModelProperty(value = "关联主项")
    private java.lang.String groupId;
    @Excel(name = "关联主项", width = 15)
    @ApiModelProperty(value = "关联主项")
    private java.lang.String groupName;
	/**关联关系*/
	@Excel(name = "关联关系", width = 15)
    @ApiModelProperty(value = "关联关系")
    private java.lang.String relation;
    @ApiModelProperty(value = "关联项id")
    private java.lang.String relationGroupId;
    @ApiModelProperty(value = "关联项")
    private java.lang.String relationGroupName;
    @ApiModelProperty(value = "数量")
    private Integer quantity;

    // 新增字段：支持大小项依赖
    @ApiModelProperty(value = "关联项类型：GROUP-大项，ITEM-小项")
    private java.lang.String relationItemType;
    @ApiModelProperty(value = "关联小项ID")
    private java.lang.String relationItemId;
    @ApiModelProperty(value = "关联小项名称")
    private java.lang.String relationItemName;

    // 新增字段：支持部位逻辑
    @ApiModelProperty(value = "主项目部位ID")
    private java.lang.String mainCheckPartId;
    @ApiModelProperty(value = "主项目部位名称")
    private java.lang.String mainCheckPartName;
    @ApiModelProperty(value = "主项目部位编码")
    private java.lang.String mainCheckPartCode;
    @ApiModelProperty(value = "关联项目部位ID")
    private java.lang.String relationCheckPartId;
    @ApiModelProperty(value = "关联项目部位名称")
    private java.lang.String relationCheckPartName;
    @ApiModelProperty(value = "关联项目部位编码")
    private java.lang.String relationCheckPartCode;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**关联项（多个逗号分割）*/
	@Excel(name = "关联项（多个逗号分割）", width = 15)
    @ApiModelProperty(value = "关联项（多个逗号分割）")
    @TableField(exist = false)
    private java.lang.String relateGroupIds;

}
