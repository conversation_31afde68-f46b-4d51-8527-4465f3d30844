package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.SmsCounter;
import org.jeecg.modules.basicinfo.mapper.SmsCounterMapper;
import org.jeecg.modules.basicinfo.service.ISmsCounterService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 短信计数
 * @Author: jeecg-boot
 * @Date:   2024-11-10
 * @Version: V1.0
 */
@Service
public class SmsCounterServiceImpl extends ServiceImpl<SmsCounterMapper, SmsCounter> implements ISmsCounterService {

}
