package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.basicinfo.entity.Icd;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: icd
 * @Author: jeecg-boot
 * @Date:   2025-05-08
 * @Version: V1.0
 */
public interface IIcdService extends IService<Icd> {

    Page<Icd> listByKeyword(Page<Icd> page, String keyword);
}
