package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.AbnormalSummarySetting;
import org.jeecg.modules.basicinfo.service.IAbnormalSummarySettingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 异常汇总设置
 * @Author: jeecg-boot
 * @Date:   2024-11-26
 * @Version: V1.0
 */
@Api(tags="异常汇总设置")
@RestController
@RequestMapping("/basicinfo/abnormalSummarySetting")
@Slf4j
public class AbnormalSummarySettingController extends JeecgController<AbnormalSummarySetting, IAbnormalSummarySettingService> {
	@Autowired
	private IAbnormalSummarySettingService abnormalSummarySettingService;
	
	/**
	 * 分页列表查询
	 *
	 * @param abnormalSummarySetting
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "异常汇总设置-分页列表查询")
	@ApiOperation(value="异常汇总设置-分页列表查询", notes="异常汇总设置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AbnormalSummarySetting>> queryPageList(AbnormalSummarySetting abnormalSummarySetting,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<AbnormalSummarySetting> queryWrapper = QueryGenerator.initQueryWrapper(abnormalSummarySetting, req.getParameterMap());
		Page<AbnormalSummarySetting> page = new Page<AbnormalSummarySetting>(pageNo, pageSize);
		IPage<AbnormalSummarySetting> pageList = abnormalSummarySettingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param abnormalSummarySetting
	 * @return
	 */
	@AutoLog(value = "异常汇总设置-添加")
	@ApiOperation(value="异常汇总设置-添加", notes="异常汇总设置-添加")
	@RequiresPermissions("basicinfo:abnormal_summary_setting:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AbnormalSummarySetting abnormalSummarySetting) {
		abnormalSummarySettingService.save(abnormalSummarySetting);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param abnormalSummarySetting
	 * @return
	 */
	@AutoLog(value = "异常汇总设置-编辑")
	@ApiOperation(value="异常汇总设置-编辑", notes="异常汇总设置-编辑")
	@RequiresPermissions("basicinfo:abnormal_summary_setting:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AbnormalSummarySetting abnormalSummarySetting) {
		abnormalSummarySettingService.updateById(abnormalSummarySetting);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "异常汇总设置-通过id删除")
	@ApiOperation(value="异常汇总设置-通过id删除", notes="异常汇总设置-通过id删除")
	@RequiresPermissions("basicinfo:abnormal_summary_setting:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		abnormalSummarySettingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "异常汇总设置-批量删除")
	@ApiOperation(value="异常汇总设置-批量删除", notes="异常汇总设置-批量删除")
	@RequiresPermissions("basicinfo:abnormal_summary_setting:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.abnormalSummarySettingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "异常汇总设置-通过id查询")
	@ApiOperation(value="异常汇总设置-通过id查询", notes="异常汇总设置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AbnormalSummarySetting> queryById(@RequestParam(name="id",required=true) String id) {
		AbnormalSummarySetting abnormalSummarySetting = abnormalSummarySettingService.getById(id);
		if(abnormalSummarySetting==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(abnormalSummarySetting);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param abnormalSummarySetting
    */
    @RequiresPermissions("basicinfo:abnormal_summary_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AbnormalSummarySetting abnormalSummarySetting) {
        return super.exportXls(request, abnormalSummarySetting, AbnormalSummarySetting.class, "异常汇总设置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:abnormal_summary_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AbnormalSummarySetting.class);
    }

}
