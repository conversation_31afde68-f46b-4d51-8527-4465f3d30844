package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.SmsCounter;
import org.jeecg.modules.basicinfo.service.ISmsCounterService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 短信计数
 * @Author: jeecg-boot
 * @Date:   2024-11-10
 * @Version: V1.0
 */
@Api(tags="短信计数")
@RestController
@RequestMapping("/basicinfo/smsCounter")
@Slf4j
public class SmsCounterController extends JeecgController<SmsCounter, ISmsCounterService> {
	@Autowired
	private ISmsCounterService smsCounterService;
	
	/**
	 * 分页列表查询
	 *
	 * @param smsCounter
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "短信计数-分页列表查询")
	@ApiOperation(value="短信计数-分页列表查询", notes="短信计数-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SmsCounter>> queryPageList(SmsCounter smsCounter,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SmsCounter> queryWrapper = QueryGenerator.initQueryWrapper(smsCounter, req.getParameterMap());
		Page<SmsCounter> page = new Page<SmsCounter>(pageNo, pageSize);
		IPage<SmsCounter> pageList = smsCounterService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param smsCounter
	 * @return
	 */
	@AutoLog(value = "短信计数-添加")
	@ApiOperation(value="短信计数-添加", notes="短信计数-添加")
	@RequiresPermissions("basicinfo:sms_counter:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SmsCounter smsCounter) {
		smsCounterService.save(smsCounter);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param smsCounter
	 * @return
	 */
	@AutoLog(value = "短信计数-编辑")
	@ApiOperation(value="短信计数-编辑", notes="短信计数-编辑")
	@RequiresPermissions("basicinfo:sms_counter:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SmsCounter smsCounter) {
		smsCounterService.updateById(smsCounter);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "短信计数-通过id删除")
	@ApiOperation(value="短信计数-通过id删除", notes="短信计数-通过id删除")
	@RequiresPermissions("basicinfo:sms_counter:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		smsCounterService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "短信计数-批量删除")
	@ApiOperation(value="短信计数-批量删除", notes="短信计数-批量删除")
	@RequiresPermissions("basicinfo:sms_counter:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.smsCounterService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "短信计数-通过id查询")
	@ApiOperation(value="短信计数-通过id查询", notes="短信计数-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SmsCounter> queryById(@RequestParam(name="id",required=true) String id) {
		SmsCounter smsCounter = smsCounterService.getById(id);
		if(smsCounter==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(smsCounter);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param smsCounter
    */
    @RequiresPermissions("basicinfo:sms_counter:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SmsCounter smsCounter) {
        return super.exportXls(request, smsCounter, SmsCounter.class, "短信计数");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:sms_counter:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SmsCounter.class);
    }

}
