package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.basicinfo.entity.Document;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 文档表
 * @Author: jeecg-boot
 * @Date:   2025-03-05
 * @Version: V1.0
 */
public interface IDocumentService extends IService<Document> {

    void listAll(Page<Document> page);

    void saveDocument(Document document);
}
