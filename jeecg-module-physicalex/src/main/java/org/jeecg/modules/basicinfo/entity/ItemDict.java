package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 项目结果字典
 * @Author: jeecg-boot
 * @Date:   2024-05-16
 * @Version: V1.0
 */
@Data
@TableName("item_dict")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="item_dict对象", description="项目结果字典")
public class ItemDict implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**序号*/
	@Excel(name = "序号", width = 15)
    @ApiModelProperty(value = "序号")
    private java.lang.Integer seq;
	/**字典值*/
	@Excel(name = "字典值", width = 15)
    @ApiModelProperty(value = "字典值")
    private java.lang.String dictText;
	/**助记码*/
	@Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private java.lang.String helpChar;
	/**诊断*/
	@Excel(name = "诊断", width = 15)
    @ApiModelProperty(value = "诊断")
    private java.lang.String diagnose;
	/**疾病类型*/
	@Excel(name = "疾病类型", width = 15, dicCode = "disease_type")
	@Dict(dicCode = "disease_type")
    @ApiModelProperty(value = "疾病类型")
    private java.lang.String diagnosisClass;
	/**程度*/
	@Excel(name = "程度", width = 15, dicCode = "disease_grade")
	@Dict(dicCode = "disease_grade")
    @ApiModelProperty(value = "程度")
    private java.lang.String degree;
	/**是否默认*/
	@Excel(name = "是否默认", width = 15)
    @ApiModelProperty(value = "是否默认")
    private java.lang.String defaultFlag;
	/**项目ID*/
	@Excel(name = "项目ID", width = 15)
    @ApiModelProperty(value = "项目ID")
    private java.lang.String itemId;
	/**科室ID*/
	@Excel(name = "科室ID", width = 15)
    @ApiModelProperty(value = "科室ID")
    private java.lang.String departmentId;
	/**使用频率*/
	@Excel(name = "使用频率", width = 15)
    @ApiModelProperty(value = "使用频率")
    private java.lang.Integer useCount;
	/**创建人账号*/
    @ApiModelProperty(value = "创建人账号")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
}
