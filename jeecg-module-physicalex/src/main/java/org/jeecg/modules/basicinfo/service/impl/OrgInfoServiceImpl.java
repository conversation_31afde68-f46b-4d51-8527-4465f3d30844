package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.jeecg.modules.basicinfo.entity.OrgInfo;
import org.jeecg.modules.basicinfo.entity.OrgArticle;
import org.jeecg.modules.basicinfo.mapper.OrgArticleMapper;
import org.jeecg.modules.basicinfo.mapper.OrgInfoMapper;
import org.jeecg.modules.basicinfo.service.IOrgInfoService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 机构信息表
 * @Author: jeecg-boot
 * @Date:   2024-10-08
 * @Version: V1.0
 */
@Service
public class OrgInfoServiceImpl extends ServiceImpl<OrgInfoMapper, OrgInfo> implements IOrgInfoService {

	@Autowired
	private OrgInfoMapper orgInfoMapper;
	@Autowired
	private OrgArticleMapper orgArticleMapper;

	@Override
	public OrgInfo getDefultOrgInfo() {
		OrgInfo orgInfo = orgInfoMapper.selectOne(new LambdaQueryWrapper<OrgInfo>()
				.eq(OrgInfo::getState,"1").last("limit 1"));
		Validate.notNull(orgInfo,"未查询到机构相关信息");
	/*	List<OrgArticle> orgArticles = orgArticleMapper.selectByMainId(orgInfo.getId());
		if (CollectionUtils.isNotEmpty(orgArticles)){
			OrgArticle orgArticle = orgArticles.get(0);
			orgInfo.setIntroduce(orgArticle.getIntroduce());
			orgInfo.setIntroduceVideo(orgArticle.getIntroduceVideo());
			orgInfo.setRequiremnetVideo(orgArticle.getRequiremnetVideo());
			orgInfo.setLogoPicture(orgArticle.getLogoPicture());
			orgInfo.setCoverPicture(orgArticle.getCoverPicture());
		}*/
		return orgInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(OrgInfo orgInfo, List<OrgArticle> orgArticleList) {
		orgInfoMapper.insert(orgInfo);
		if(orgArticleList!=null && orgArticleList.size()>0) {
			for(OrgArticle entity:orgArticleList) {
				//外键设置
				entity.setOrgId(orgInfo.getId());
				orgArticleMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(OrgInfo orgInfo,List<OrgArticle> orgArticleList) {
		orgInfoMapper.updateById(orgInfo);
		
		//1.先删除子表数据
		orgArticleMapper.deleteByMainId(orgInfo.getId());
		
		//2.子表数据重新插入
		if(orgArticleList!=null && orgArticleList.size()>0) {
			for(OrgArticle entity:orgArticleList) {
				//外键设置
				entity.setOrgId(orgInfo.getId());
				orgArticleMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		orgArticleMapper.deleteByMainId(id);
		orgInfoMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			orgArticleMapper.deleteByMainId(id.toString());
			orgInfoMapper.deleteById(id);
		}
	}

	@Override
	public OrgInfo queryByOrgCode(String orgCode) {
		OrgInfo orgInfo = orgInfoMapper.selectOne(new LambdaQueryWrapper<OrgInfo>()
				.eq(OrgInfo::getOrgCode,orgCode));
		Validate.notNull(orgInfo,"未查询到机构相关信息");
		List<OrgArticle> orgArticles = orgArticleMapper.selectByMainId(orgInfo.getId());
		if (CollectionUtils.isNotEmpty(orgArticles)){
			OrgArticle orgArticle = orgArticles.get(0);
			orgInfo.setIntroduce(orgArticle.getIntroduce());
			orgInfo.setIntroduceVideo(orgArticle.getIntroduceVideo());
			orgInfo.setRequiremnetVideo(orgArticle.getRequiremnetVideo());
			orgInfo.setLogoPicture(orgArticle.getLogoPicture());
			orgInfo.setCoverPicture(orgArticle.getCoverPicture());
		}
		return orgInfo;
	}
}
