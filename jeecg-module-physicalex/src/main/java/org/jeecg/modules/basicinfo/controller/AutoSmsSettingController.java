package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.AutoSmsSetting;
import org.jeecg.modules.basicinfo.service.IAutoSmsSettingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 自动发送短信配置
 * @Author: jeecg-boot
 * @Date:   2024-11-10
 * @Version: V1.0
 */
@Api(tags="自动发送短信配置")
@RestController
@RequestMapping("/basicinfo/autoSmsSetting")
@Slf4j
public class AutoSmsSettingController extends JeecgController<AutoSmsSetting, IAutoSmsSettingService> {
	@Autowired
	private IAutoSmsSettingService autoSmsSettingService;
	
	/**
	 * 分页列表查询
	 *
	 * @param autoSmsSetting
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "自动发送短信配置-分页列表查询")
	@ApiOperation(value="自动发送短信配置-分页列表查询", notes="自动发送短信配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AutoSmsSetting>> queryPageList(AutoSmsSetting autoSmsSetting,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("bizType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("includeExamCategory", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<AutoSmsSetting> queryWrapper = QueryGenerator.initQueryWrapper(autoSmsSetting, req.getParameterMap(),customeRuleMap);
		Page<AutoSmsSetting> page = new Page<AutoSmsSetting>(pageNo, pageSize);
		IPage<AutoSmsSetting> pageList = autoSmsSettingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param autoSmsSetting
	 * @return
	 */
	@AutoLog(value = "自动发送短信配置-添加")
	@ApiOperation(value="自动发送短信配置-添加", notes="自动发送短信配置-添加")
	@RequiresPermissions("basicinfo:auto_sms_setting:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AutoSmsSetting autoSmsSetting) {
		autoSmsSettingService.save(autoSmsSetting);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param autoSmsSetting
	 * @return
	 */
	@AutoLog(value = "自动发送短信配置-编辑")
	@ApiOperation(value="自动发送短信配置-编辑", notes="自动发送短信配置-编辑")
	@RequiresPermissions("basicinfo:auto_sms_setting:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AutoSmsSetting autoSmsSetting) {
		autoSmsSettingService.updateById(autoSmsSetting);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "自动发送短信配置-通过id删除")
	@ApiOperation(value="自动发送短信配置-通过id删除", notes="自动发送短信配置-通过id删除")
	@RequiresPermissions("basicinfo:auto_sms_setting:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		autoSmsSettingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "自动发送短信配置-批量删除")
	@ApiOperation(value="自动发送短信配置-批量删除", notes="自动发送短信配置-批量删除")
	@RequiresPermissions("basicinfo:auto_sms_setting:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.autoSmsSettingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "自动发送短信配置-通过id查询")
	@ApiOperation(value="自动发送短信配置-通过id查询", notes="自动发送短信配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AutoSmsSetting> queryById(@RequestParam(name="id",required=true) String id) {
		AutoSmsSetting autoSmsSetting = autoSmsSettingService.getById(id);
		if(autoSmsSetting==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(autoSmsSetting);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param autoSmsSetting
    */
    @RequiresPermissions("basicinfo:auto_sms_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AutoSmsSetting autoSmsSetting) {
        return super.exportXls(request, autoSmsSetting, AutoSmsSetting.class, "自动发送短信配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:auto_sms_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AutoSmsSetting.class);
    }

}
