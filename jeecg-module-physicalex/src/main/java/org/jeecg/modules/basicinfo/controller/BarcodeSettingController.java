package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.Collections;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.basicinfo.entity.BarcodeSetting;
import org.jeecg.modules.basicinfo.service.IBarcodeSettingService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 条码配置表
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Api(tags = "条码配置表")
@RestController
@RequestMapping("/basicinfo/barcodeSetting")
@Slf4j
public class BarcodeSettingController extends JeecgController<BarcodeSetting, IBarcodeSettingService> {
    @Autowired
    private IBarcodeSettingService barcodeSettingService;

    /**
     * 分页列表查询
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "条码配置表-分页列表查询")
    @ApiOperation(value = "条码配置表-分页列表查询", notes = "条码配置表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<BarcodeSetting>> queryPageList(@RequestParam(name = "pageNo", defaultValue = "10") Integer pageNo,@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String source = req.getParameter("source");
        String name = req.getParameter("name");
        String groupName = req.getParameter("groupName");
        Page<BarcodeSetting> page = new Page<BarcodeSetting>(pageNo, pageSize);
        IPage<BarcodeSetting> pageList = barcodeSettingService.pageBarcodeSetting(page, name, groupName, source);
        return Result.OK(pageList);
    }

    /**
     * 获取只生成体检号的配置
     * @return
     */
    @ApiOperation(value = "获取只生成体检号的配置")
    @GetMapping(value = "/getExamNoSetting")
    public Result<BarcodeSetting> getExamNoSetting() {
        BarcodeSetting barcodeSetting = barcodeSettingService.getExamNoSetting();
        if(barcodeSetting == null){
            return Result.error("未配置仅打印体检号的条码配置！");
        }
        return Result.OK(barcodeSetting);
    }

    /**
     * 添加
     *
     * @param barcodeSetting
     * @return
     */
    @AutoLog(value = "条码配置表-添加")
    @ApiOperation(value = "条码配置表-添加", notes = "条码配置表-添加")
    @RequiresPermissions("basicinfo:barcode_setting:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody BarcodeSetting barcodeSetting) {
        barcodeSettingService.save(barcodeSetting);
        return Result.OK("添加成功！", barcodeSetting);
    }

    /**
     * 编辑
     *
     * @param barcodeSetting
     * @return
     */
    @AutoLog(value = "条码配置表-编辑")
    @ApiOperation(value = "条码配置表-编辑", notes = "条码配置表-编辑")
    @RequiresPermissions("basicinfo:barcode_setting:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody BarcodeSetting barcodeSetting) {
        barcodeSettingService.updateById(barcodeSetting);
        return Result.OK("编辑成功!", barcodeSetting);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "条码配置表-通过id删除")
    @ApiOperation(value = "条码配置表-通过id删除", notes = "条码配置表-通过id删除")
    @RequiresPermissions("basicinfo:barcode_setting:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        barcodeSettingService.removeBatch(Collections.singletonList(id));
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "条码配置表-批量删除")
    @ApiOperation(value = "条码配置表-批量删除", notes = "条码配置表-批量删除")
    @RequiresPermissions("basicinfo:barcode_setting:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.barcodeSettingService.removeBatch(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "条码配置表-通过id查询")
    @ApiOperation(value = "条码配置表-通过id查询", notes = "条码配置表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<BarcodeSetting> queryById(@RequestParam(name = "id", required = true) String id) {
        BarcodeSetting barcodeSetting = barcodeSettingService.getById(id);
        if (barcodeSetting == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(barcodeSetting);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param barcodeSetting
     */
    @RequiresPermissions("basicinfo:barcode_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BarcodeSetting barcodeSetting) {
        return super.exportXls(request, barcodeSetting, BarcodeSetting.class, "条码配置表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:barcode_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BarcodeSetting.class);
    }

}
