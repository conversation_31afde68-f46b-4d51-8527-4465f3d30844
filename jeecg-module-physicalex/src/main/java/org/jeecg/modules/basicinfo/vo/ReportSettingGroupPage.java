package org.jeecg.modules.basicinfo.vo;

import java.util.List;
import org.jeecg.modules.basicinfo.entity.ReportSettingGroup;
import org.jeecg.modules.basicinfo.entity.ReportSettingDepart;
import org.jeecg.modules.basicinfo.entity.ReportSettingItemgroup;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 报告分组设置
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
@Data
@ApiModel(value="report_setting_groupPage对象", description="报告分组设置")
public class ReportSettingGroupPage {

	/**id*/
	@ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**分组名称*/
	@Excel(name = "分组名称", width = 15)
	@ApiModelProperty(value = "分组名称")
    private java.lang.String name;
	/**分组代码*/
	@Excel(name = "分组代码", width = 15)
	@ApiModelProperty(value = "分组代码")
    private java.lang.String code;
	/**排序号*/
	@Excel(name = "排序号", width = 15)
	@ApiModelProperty(value = "排序号")
    private java.lang.Integer seq;

	@ExcelCollection(name="报告分组设置-关联科室")
	@ApiModelProperty(value = "报告分组设置-关联科室")
	private List<ReportSettingDepart> reportSettingDepartList;
	@ExcelCollection(name="报告分组设置-关联大项")
	@ApiModelProperty(value = "报告分组设置-关联大项")
	private List<ReportSettingItemgroup> reportSettingItemgroupList;

}
