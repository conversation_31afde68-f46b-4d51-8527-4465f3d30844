package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.SysSetting;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 参数配置
 * @Author: jeecg-boot
 * @Date:   2024-05-25
 * @Version: V1.0
 */
public interface SysSettingMapper extends BaseMapper<SysSetting> {

    SysSetting getByCode(String code);

    String getValueByCode(String code);
}
