package org.jeecg.modules.basicinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.Template;
import org.jeecg.modules.basicinfo.service.IItemGroupService;
import org.jeecg.modules.basicinfo.service.ITemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: 模版管理
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Api(tags = "模版管理")
@RestController
@RequestMapping("/basicinfo/template")
@Slf4j
public class TemplateController extends JeecgController<Template, ITemplateService> {
    @Autowired
    private ITemplateService templateService;
    @Autowired
    private JdbcTemplate jdbcTemplate;


    //getApplyTemplateIdsByReg
    @ApiOperation(value = "通过登记id获取模板id", notes = "通过登记id获取模板id")
    @GetMapping(value = "/getApplyTemplateIdsByReg")
    public Result<?> getApplyTemplateIdsByReg(String customerRegId,Boolean reprintFlag) {
        List<String> list = templateService.getApplyTemplateIdsByReg(customerRegId,reprintFlag);
        return Result.OK("获取成功", list);
    }

    @ApiOperation(value = "获取更新时间", notes = "获取更新时间")
    @GetMapping(value = "/getUpdateTime")
    public Result<String> getUpdateTime(String id) {
        return Result.OK("获取成功", templateService.getUpdateTime(id));
    }

    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping(value = "/getById")
    public Result<Template> getById(String id) {
        Template template = templateService.getById(id);
        if (template == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(template);
    }

    @ApiOperation(value = "获取类别默认值", notes = "获取类别默认值")
    @GetMapping(value = "/getDefaultOfType")
    public Result<?> getDefaultOfType(String type) {
        Template template = templateService.getDefaultOfTypes(type);
        return Result.OK("获取成功", template);
    }

    @ApiOperation(value = "获取类别默认值", notes = "获取类别默认值")
    @GetMapping(value = "/getDefaultIdOfType")
    public Result<?> getDefaultIdOfType(String type) {
        String id = templateService.getDefaultIdOfType(type);
        return Result.OK("获取成功", id);
    }

    /**
     * 列表查询
     * @return
     */
    @ApiOperation(value = "模版管理-查询列表", notes = "模版管理-分页列表查询")
    @GetMapping(value = "/listAll")
    public Result<?> list() {
        return Result.OK(templateService.listByRegType(null,"报告"));
    }

    /**
     * 根据类型查询
     * @param regType
     * @return
     */
    @ApiOperation(value = "模版管理-根据类型查询", notes = "模版管理-根据类型查询")
    @GetMapping(value = "/listByType")
    public Result<?> list(String regType,String type) {
        return Result.OK(templateService.listByRegType(regType,type));
    }


    /**
     * 分页列表查询
     *
     * @param template
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "模版管理-分页列表查询", notes = "模版管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Template>> queryPageList(Template template, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<Template> queryWrapper = QueryGenerator.initQueryWrapper(template, req.getParameterMap());
        Page<Template> page = new Page<Template>(pageNo, pageSize);
        IPage<Template> pageList = templateService.page(page, queryWrapper);
        pageList.getRecords().forEach(item->{
            if(StringUtils.equals(item.getType(),"申请单")){
                List<Map> maps = Lists.newArrayList();
                try {
                    maps = jdbcTemplate.queryForList("select * from template_group where template_id = ?", Map.class, item.getId());
                } catch (DataAccessException e) {

                }
                if (CollectionUtils.isNotEmpty(maps)) {
                    List<String> groupIdList=Lists.newArrayList();
                    maps.forEach(templateGroup->{
                        String groupId = String.valueOf(templateGroup.get("group_id"));
                        String checkPartCode = String.valueOf(templateGroup.get("check_part_code"));
                        if(StringUtils.isNotBlank(checkPartCode)){
                            groupId=groupId+"_"+checkPartCode;
                        }
                        groupIdList.add(groupId);
                    });
                    item.setGroupId(StringUtils.join(groupIdList, ","));
                }

            }
        });
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param template
     * @return
     */
    @AutoLog(value = "模版管理-添加")
    @ApiOperation(value = "模版管理-添加", notes = "模版管理-添加")
    @RequiresPermissions("basicinfo:template:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody Template template) {
        templateService.preDealDefaultFlag(template);
        templateService.save(template);
        templateService.setTemplateGroup(template);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param template
     * @return
     */
    @AutoLog(value = "模版管理-编辑")
    @ApiOperation(value = "模版管理-编辑", notes = "模版管理-编辑")
    @RequiresPermissions("basicinfo:template:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody Template template) {
        templateService.preDealDefaultFlag(template);
        templateService.updateById(template);
        templateService.setTemplateGroup(template);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "模版管理-通过id删除")
    @ApiOperation(value = "模版管理-通过id删除", notes = "模版管理-通过id删除")
    @RequiresPermissions("basicinfo:template:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        templateService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "模版管理-批量删除")
    @ApiOperation(value = "模版管理-批量删除", notes = "模版管理-批量删除")
    @RequiresPermissions("basicinfo:template:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.templateService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "模版管理-通过id查询")
    @ApiOperation(value = "模版管理-通过id查询", notes = "模版管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Template> queryById(@RequestParam(name = "id", required = true) String id) {
        Template template = templateService.getById(id);
        if (template == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(template);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param template
     */
    @RequiresPermissions("basicinfo:template:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Template template) {
        return super.exportXls(request, template, Template.class, "模版管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:template:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Template.class);
    }

}
