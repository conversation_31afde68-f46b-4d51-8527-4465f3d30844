package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.MedKnowledge;
import org.jeecg.modules.basicinfo.service.IMedKnowledgeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 知识库
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Api(tags="知识库")
@RestController
@RequestMapping("/basicinfo/medKnowledge")
@Slf4j
public class MedKnowledgeController extends JeecgController<MedKnowledge, IMedKnowledgeService> {
	@Autowired
	private IMedKnowledgeService medKnowledgeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param medKnowledge
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "知识库-分页列表查询")
	@ApiOperation(value="知识库-分页列表查询", notes="知识库-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<MedKnowledge>> queryPageList(MedKnowledge medKnowledge,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<MedKnowledge> queryWrapper = QueryGenerator.initQueryWrapper(medKnowledge, req.getParameterMap());
		Page<MedKnowledge> page = new Page<MedKnowledge>(pageNo, pageSize);
		IPage<MedKnowledge> pageList = medKnowledgeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param medKnowledge
	 * @return
	 */
	@AutoLog(value = "知识库-添加")
	@ApiOperation(value="知识库-添加", notes="知识库-添加")
	@RequiresPermissions("basicinfo:med_knowledge:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody MedKnowledge medKnowledge) {
		medKnowledgeService.save(medKnowledge);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param medKnowledge
	 * @return
	 */
	@AutoLog(value = "知识库-编辑")
	@ApiOperation(value="知识库-编辑", notes="知识库-编辑")
	@RequiresPermissions("basicinfo:med_knowledge:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody MedKnowledge medKnowledge) {
		medKnowledgeService.updateById(medKnowledge);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "知识库-通过id删除")
	@ApiOperation(value="知识库-通过id删除", notes="知识库-通过id删除")
	@RequiresPermissions("basicinfo:med_knowledge:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		medKnowledgeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "知识库-批量删除")
	@ApiOperation(value="知识库-批量删除", notes="知识库-批量删除")
	@RequiresPermissions("basicinfo:med_knowledge:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.medKnowledgeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "知识库-通过id查询")
	@ApiOperation(value="知识库-通过id查询", notes="知识库-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<MedKnowledge> queryById(@RequestParam(name="id",required=true) String id) {
		MedKnowledge medKnowledge = medKnowledgeService.getById(id);
		if(medKnowledge==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(medKnowledge);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param medKnowledge
    */
    @RequiresPermissions("basicinfo:med_knowledge:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MedKnowledge medKnowledge) {
        return super.exportXls(request, medKnowledge, MedKnowledge.class, "知识库");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:med_knowledge:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MedKnowledge.class);
    }

}
