package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.jeecgframework.poi.excel.annotation.Excel;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 符合判断
 * @Author: jeecg-boot
 * @Date:   2024-04-26
 * @Version: V1.0
 */
@Data
@TableName("diagnosis_complex")
@ApiModel(value="diagnosis_complex对象", description="符合判断")
public class DiagnosisComplex implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**科室ID*/
    @Excel(name = "科室ID", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @ApiModelProperty(value = "科室ID")
    private java.lang.String departmentId;
	/**科室*/
    @Excel(name = "科室", width = 15)
    @ApiModelProperty(value = "科室")
    private java.lang.String departmentName;
	/**诊断名称*/
    @Excel(name = "诊断名称", width = 15)
    @ApiModelProperty(value = "诊断名称")
    private java.lang.String name;
	/**性别限制*/
    @Excel(name = "性别限制", width = 15, dicCode = "sexLimit")
    @Dict(dicCode = "sexLimit")
    @ApiModelProperty(value = "性别限制")
    private java.lang.String genderLimit;
	/**最小年龄*/
    @Excel(name = "最小年龄", width = 15)
    @ApiModelProperty(value = "最小年龄")
    private java.lang.Integer minAge;
	/**最大年龄*/
    @Excel(name = "最大年龄", width = 15)
    @ApiModelProperty(value = "最大年龄")
    private java.lang.Integer maxAge;
	/**适用婚别*/
    @Excel(name = "适用婚别", width = 15, dicCode = "material_type")
    @Dict(dicCode = "material_type")
    @ApiModelProperty(value = "适用婚别")
    private java.lang.String marriageLimit;
	/**启用*/
    @Excel(name = "启用", width = 15)
    @ApiModelProperty(value = "启用")
    private java.lang.Integer enableFlag;
	/**计算公式*/
    @Excel(name = "计算公式", width = 15)
    @ApiModelProperty(value = "计算公式")
    private java.lang.String formula;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
	/**creatTime*/
    @Excel(name = "createTime", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;

    private Integer rulesCount;
    /**危急值程度*/
    @ApiModelProperty(value = "危急值程度")
    private String severityDegree;

    /**参考文献*/
    @ApiModelProperty(value = "参考文献")
    private String baseOn;

    @ApiModelProperty(value = "诊断项规则")
    @TableField(exist = false)
    private List<DiagnosisComplexItemRule> itemRules;
}
