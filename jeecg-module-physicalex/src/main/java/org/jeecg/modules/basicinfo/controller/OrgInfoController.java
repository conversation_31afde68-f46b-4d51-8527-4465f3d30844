package org.jeecg.modules.basicinfo.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.basicinfo.entity.OrgInfo;
import org.jeecg.modules.basicinfo.service.IOrgInfoService;
import org.jeecg.modules.mobile.utils.FileUrlUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.OrgArticle;
import org.jeecg.modules.basicinfo.vo.OrgInfoPage;
import org.jeecg.modules.basicinfo.service.IOrgArticleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;


 /**
 * @Description: 机构信息表
 * @Author: jeecg-boot
 * @Date:   2024-10-08
 * @Version: V1.0
 */
@Api(tags="机构信息表")
@RestController
@RequestMapping("/orginfo/orgInfo")
@Slf4j
public class OrgInfoController {
	@Autowired
	private IOrgInfoService orgInfoService;
	@Autowired
	private IOrgArticleService orgArticleService;
	 @Autowired
	 private ISysSettingService sysSettingService;
	
	/**
	 * 分页列表查询
	 *
	 * @param orgInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "机构信息表-分页列表查询")
	@ApiOperation(value="机构信息表-分页列表查询", notes="机构信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<OrgInfo>> queryPageList(OrgInfo orgInfo,
                                                @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                HttpServletRequest req) {
		QueryWrapper<OrgInfo> queryWrapper = QueryGenerator.initQueryWrapper(orgInfo, req.getParameterMap());
		Page<OrgInfo> page = new Page<OrgInfo>(pageNo, pageSize);
		IPage<OrgInfo> pageList = orgInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param orgInfo
	 * @return
	 */
	@AutoLog(value = "机构信息表-添加")
	@ApiOperation(value="机构信息表-添加", notes="机构信息表-添加")
    @RequiresPermissions("orginfo:org_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody OrgInfo orgInfo) {
		orgInfoService.save(orgInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param orgInfo
	 * @return
	 */
	@AutoLog(value = "机构信息表-编辑")
	@ApiOperation(value="机构信息表-编辑", notes="机构信息表-编辑")
    @RequiresPermissions("orginfo:org_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody OrgInfo orgInfo) {
		OrgInfo orgInfoEntity = orgInfoService.getById(orgInfo.getId());
		if(orgInfoEntity==null) {
			return Result.error("未找到对应数据");
		}
		orgInfoService.updateById(orgInfo);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "机构信息表-通过id删除")
	@ApiOperation(value="机构信息表-通过id删除", notes="机构信息表-通过id删除")
    @RequiresPermissions("orginfo:org_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		orgInfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "机构信息表-批量删除")
	@ApiOperation(value="机构信息表-批量删除", notes="机构信息表-批量删除")
    @RequiresPermissions("orginfo:org_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.orgInfoService.removeBatchByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "机构信息表-通过id查询")
	@ApiOperation(value="机构信息表-通过id查询", notes="机构信息表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<OrgInfo> queryById(@RequestParam(name="id",required=true) String id) {
		OrgInfo orgInfo = orgInfoService.getById(id);
		if(orgInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(orgInfo);

	}

	 /**
	  * 通过id查询
	  *
	  * @param orgCode
	  * @return
	  */
	 //@AutoLog(value = "机构信息表-通过id查询")
	 @ApiOperation(value="机构信息表-通过id查询", notes="机构信息表-通过id查询")
	 @GetMapping(value = "/queryByOrgCode")
	 public Result<OrgInfo> queryByOrgCode(@RequestParam(name="orgCode") String orgCode) {
		 OrgInfo orgInfo = orgInfoService.getDefultOrgInfo();

		 String openFileUrl = sysSettingService.getValueByCode("open_file_url");
//		 if(StringUtils.isBlank(openFileUrl)) {
//			 return Result.error("未找到公网文件服务url配置");
//		 }
         orgInfo.setCoverPicture(FileUrlUtils.replaceUrl(orgInfo.getCoverPicture(),openFileUrl));
         orgInfo.setLogoPicture(FileUrlUtils.replaceUrl(orgInfo.getLogoPicture(),openFileUrl));
         orgInfo.setIntroduceVideo(FileUrlUtils.replaceUrl(orgInfo.getIntroduceVideo(),openFileUrl));
         orgInfo.setRequiremnetVideo(FileUrlUtils.replaceUrl(orgInfo.getRequiremnetVideo(),openFileUrl));
		 if(orgInfo==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(orgInfo);

	 }
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "机构其他信息通过主表ID查询")
	@ApiOperation(value="机构其他信息主表ID查询", notes="机构其他信息-通主表ID查询")
	@GetMapping(value = "/queryOrgArticleByMainId")
	public Result<List<OrgArticle>> queryOrgArticleListByMainId(@RequestParam(name="id",required=true) String id) {
		List<OrgArticle> orgArticleList = orgArticleService.selectByMainId(id);
		return Result.OK(orgArticleList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param orgInfo
    */
    @RequiresPermissions("orginfo:org_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrgInfo orgInfo) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<OrgInfo> queryWrapper = QueryGenerator.initQueryWrapper(orgInfo, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<OrgInfo> orgInfoList = orgInfoService.list(queryWrapper);

      // Step.3 组装pageList
      List<OrgInfoPage> pageList = new ArrayList<OrgInfoPage>();
      for (OrgInfo main : orgInfoList) {
          OrgInfoPage vo = new OrgInfoPage();
          BeanUtils.copyProperties(main, vo);
          List<OrgArticle> orgArticleList = orgArticleService.selectByMainId(main.getId());
          vo.setOrgArticleList(orgArticleList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "机构信息表列表");
      mv.addObject(NormalExcelConstants.CLASS, OrgInfoPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("机构信息表数据", "导出人:"+sysUser.getRealname(), "机构信息表"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("orginfo:org_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<OrgInfoPage> list = ExcelImportUtil.importExcel(file.getInputStream(), OrgInfoPage.class, params);
              for (OrgInfoPage page : list) {
                  OrgInfo po = new OrgInfo();
                  BeanUtils.copyProperties(page, po);
                  orgInfoService.saveMain(po, page.getOrgArticleList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
