package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.DiagnosisComplexItemRule;
import org.jeecg.modules.basicinfo.mapper.DiagnosisComplexItemRuleMapper;
import org.jeecg.modules.basicinfo.service.IDiagnosisComplexItemRuleService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 符合判断规则
 * @Author: jeecg-boot
 * @Date:   2024-04-29
 * @Version: V1.0
 */
@Service
public class DiagnosisComplexItemRuleServiceImpl extends ServiceImpl<DiagnosisComplexItemRuleMapper, DiagnosisComplexItemRule> implements IDiagnosisComplexItemRuleService {

}
