package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.modules.basicinfo.entity.AutoSmsSetting;
import org.jeecg.modules.basicinfo.entity.MsgTemplate;
import org.jeecg.modules.basicinfo.mapper.AutoSmsSettingMapper;
import org.jeecg.modules.basicinfo.mapper.MsgTemplateMapper;
import org.jeecg.modules.basicinfo.service.IAutoSmsSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 自动发送短信配置
 * @Author: jeecg-boot
 * @Date: 2024-11-10
 * @Version: V1.0
 */
@Service
public class AutoSmsSettingServiceImpl extends ServiceImpl<AutoSmsSettingMapper, AutoSmsSetting> implements IAutoSmsSettingService {

    @Autowired
    private AutoSmsSettingMapper autoSmsSettingMapper;
    @Autowired
    private MsgTemplateMapper msgTemplateMapper;

    @Override
    public List<AutoSmsSetting> listAutoSmsSetting(String bizType) {
        LambdaQueryWrapper<AutoSmsSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AutoSmsSetting::getBizType, bizType);
        queryWrapper.eq(AutoSmsSetting::getEnableFlag, 1);

        return list(queryWrapper);
    }
}
