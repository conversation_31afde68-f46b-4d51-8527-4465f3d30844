package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: item_group_interface
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
@Data
@TableName("item_group_interface")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="item_group_interface对象", description="item_group_interface")
public class ItemGroupInterface implements Serializable {
    private static final long serialVersionUID = 1L;

	/**item_code  项目代码*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "item_code  项目代码")
    private java.lang.String id;
	/**项目代码*/
	@Excel(name = "项目代码", width = 15)
    @ApiModelProperty(value = "项目代码")
    private java.lang.String itemCode;
	/**类别*/
	@Excel(name = "类别", width = 15)
    @ApiModelProperty(value = "类别")
    private java.lang.String classCode;
	/**项目名称*/
	@Excel(name = "项目名称", width = 15)
    @ApiModelProperty(value = "项目名称")
    private java.lang.String itemName;
	/**药品规格*/
	@Excel(name = "药品规格", width = 15)
    @ApiModelProperty(value = "药品规格")
    private java.lang.String itemSpec;
	/**药品规格单位*/
	@Excel(name = "药品规格单位", width = 15)
    @ApiModelProperty(value = "药品规格单位")
    private java.lang.String itemSpecUnit;
	/**单价*/
	@Excel(name = "单价", width = 15)
    @ApiModelProperty(value = "单价")
    private java.math.BigDecimal itemPrice;
	/**单位名称*/
	@Excel(name = "单位名称", width = 15)
    @ApiModelProperty(value = "单位名称")
    private java.lang.String unitName;
	/**拼音码*/
	@Excel(name = "拼音码", width = 15)
    @ApiModelProperty(value = "拼音码")
    private java.lang.String spellCode;
	/**五笔音首码*/
	@Excel(name = "五笔音首码", width = 15)
    @ApiModelProperty(value = "五笔音首码")
    private java.lang.String wbzxCode;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;

    @TableField(exist = false)
    private String departmentId;
}
