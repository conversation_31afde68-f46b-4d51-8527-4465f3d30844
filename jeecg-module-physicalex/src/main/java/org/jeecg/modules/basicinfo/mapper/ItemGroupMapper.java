package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.basicinfo.entity.ItemInfo;

/**
 * @Description: 项目组合
 * @Author: jeecg-boot
 * @Date:   2024-01-31
 * @Version: V1.0
 */
public interface ItemGroupMapper extends BaseMapper<ItemGroup> {

    List<ItemInfo> listItemByGroupId(@Param("groupId") String groupId);

    Page<ItemGroup> pageItemGroup(Page<ItemGroup> page, @Param("name")String name,@Param("helpChar")String helpChar,@Param("feeType")String feeType,@Param("keyword")String keyword,@Param("departmentId")String departmentId,String delFlag,@Param("enableFlag")String enableFlag,@Param("chargeItemOnlyFlag")String chargeItemOnlyFlag);

    List<ItemGroup> listByKeyword(@Param("keyword")String keyword);

    List<ItemGroup> listByRiskFactor(@Param("riskFactorIds")List<String> riskFactorIds,@Param("post")String post);


    List<ItemGroup> listItemGroupByNames(@Param("names")List<String> names);

    ItemGroup getItemGroupById(@Param("groupId")String groupId);

    List<ItemGroup> getItemGroupByHisCode(@Param("hisCode")String hisCode);
}
