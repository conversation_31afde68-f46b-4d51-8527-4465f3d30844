package org.jeecg.modules.basicinfo.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.GroupChangeRecord;
import org.jeecg.modules.basicinfo.service.IGroupChangeRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 项目价格变动操作记录表
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Api(tags="项目价格变动操作记录表")
@RestController
@RequestMapping("/basicinfo/groupChangeRecord")
@Slf4j
public class GroupChangeRecordController extends JeecgController<GroupChangeRecord, IGroupChangeRecordService> {
	@Autowired
	private IGroupChangeRecordService groupChangeRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param groupChangeRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "项目价格变动操作记录表-分页列表查询")
	@ApiOperation(value="项目价格变动操作记录表-分页列表查询", notes="项目价格变动操作记录表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<GroupChangeRecord>> queryPageList(GroupChangeRecord groupChangeRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		LambdaQueryWrapper<GroupChangeRecord> queryWrapper=new LambdaQueryWrapper<>();
		if (Objects.nonNull(groupChangeRecord.getGroupId())) {
			queryWrapper.eq(GroupChangeRecord::getGroupId,groupChangeRecord.getGroupId());
		}
		if (StringUtils.isNotBlank(groupChangeRecord.getGroupName())){
			queryWrapper.like(GroupChangeRecord::getGroupName,groupChangeRecord.getGroupName());
		}
		if (StringUtils.isNotBlank(groupChangeRecord.getOperatorName())){
			queryWrapper.like(GroupChangeRecord::getOperatorName,groupChangeRecord.getOperatorName());
		}
		if (Objects.nonNull(groupChangeRecord.getOperateTimeStart())){
			queryWrapper.between(GroupChangeRecord::getOperateTime,groupChangeRecord.getOperateTimeStart(),groupChangeRecord.getOperateTimeEnd());
		}

		Page<GroupChangeRecord> page = new Page<GroupChangeRecord>(pageNo, pageSize);
		IPage<GroupChangeRecord> pageList = groupChangeRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param groupChangeRecord
	 * @return
	 */
	@AutoLog(value = "项目价格变动操作记录表-添加")
	@ApiOperation(value="项目价格变动操作记录表-添加", notes="项目价格变动操作记录表-添加")
	@RequiresPermissions("basicinfo:group_change_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody GroupChangeRecord groupChangeRecord) {
		groupChangeRecordService.save(groupChangeRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param groupChangeRecord
	 * @return
	 */
	@AutoLog(value = "项目价格变动操作记录表-编辑")
	@ApiOperation(value="项目价格变动操作记录表-编辑", notes="项目价格变动操作记录表-编辑")
	@RequiresPermissions("basicinfo:group_change_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody GroupChangeRecord groupChangeRecord) {
		groupChangeRecordService.updateById(groupChangeRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "项目价格变动操作记录表-通过id删除")
	@ApiOperation(value="项目价格变动操作记录表-通过id删除", notes="项目价格变动操作记录表-通过id删除")
	@RequiresPermissions("basicinfo:group_change_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		groupChangeRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "项目价格变动操作记录表-批量删除")
	@ApiOperation(value="项目价格变动操作记录表-批量删除", notes="项目价格变动操作记录表-批量删除")
	@RequiresPermissions("basicinfo:group_change_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.groupChangeRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "项目价格变动操作记录表-通过id查询")
	@ApiOperation(value="项目价格变动操作记录表-通过id查询", notes="项目价格变动操作记录表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<GroupChangeRecord> queryById(@RequestParam(name="id",required=true) String id) {
		GroupChangeRecord groupChangeRecord = groupChangeRecordService.getById(id);
		if(groupChangeRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(groupChangeRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param groupChangeRecord
    */
    @RequiresPermissions("basicinfo:group_change_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, GroupChangeRecord groupChangeRecord) {
        return super.exportXls(request, groupChangeRecord, GroupChangeRecord.class, "项目价格变动操作记录表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:group_change_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, GroupChangeRecord.class);
    }

}
