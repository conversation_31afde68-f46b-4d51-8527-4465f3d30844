package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.BarcodeSetting;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 条码配置表
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
public interface BarcodeSettingMapper extends BaseMapper<BarcodeSetting> {
    BarcodeSetting getExamNoSetting();

    Page<BarcodeSetting> pageList(Page<BarcodeSetting> page, @Param("name") String name, @Param("groupName") String groupName, @Param("source") String source);
}
