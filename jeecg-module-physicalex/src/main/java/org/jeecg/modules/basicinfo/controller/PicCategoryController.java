package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.PicCategory;
import org.jeecg.modules.basicinfo.service.IPicCategoryService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 图片分类
 * @Author: jeecg-boot
 * @Date:   2024-10-11
 * @Version: V1.0
 */
@Api(tags="图片分类")
@RestController
@RequestMapping("/basicinfo/picCategory")
@Slf4j
public class PicCategoryController extends JeecgController<PicCategory, IPicCategoryService> {
	@Autowired
	private IPicCategoryService picCategoryService;
	
	/**
	 * 分页列表查询
	 *
	 * @param picCategory
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "图片分类-分页列表查询")
	@ApiOperation(value="图片分类-分页列表查询", notes="图片分类-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PicCategory>> queryPageList(PicCategory picCategory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<PicCategory> queryWrapper = QueryGenerator.initQueryWrapper(picCategory, req.getParameterMap());
		Page<PicCategory> page = new Page<PicCategory>(pageNo, pageSize);
		IPage<PicCategory> pageList = picCategoryService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param picCategory
	 * @return
	 */
	@AutoLog(value = "图片分类-添加")
	@ApiOperation(value="图片分类-添加", notes="图片分类-添加")
	@RequiresPermissions("basicinfo:pic_category:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PicCategory picCategory) {
		picCategoryService.save(picCategory);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param picCategory
	 * @return
	 */
	@AutoLog(value = "图片分类-编辑")
	@ApiOperation(value="图片分类-编辑", notes="图片分类-编辑")
	@RequiresPermissions("basicinfo:pic_category:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PicCategory picCategory) {
		picCategoryService.updateById(picCategory);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "图片分类-通过id删除")
	@ApiOperation(value="图片分类-通过id删除", notes="图片分类-通过id删除")
	@RequiresPermissions("basicinfo:pic_category:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		picCategoryService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "图片分类-批量删除")
	@ApiOperation(value="图片分类-批量删除", notes="图片分类-批量删除")
	@RequiresPermissions("basicinfo:pic_category:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.picCategoryService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "图片分类-通过id查询")
	@ApiOperation(value="图片分类-通过id查询", notes="图片分类-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PicCategory> queryById(@RequestParam(name="id",required=true) String id) {
		PicCategory picCategory = picCategoryService.getById(id);
		if(picCategory==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(picCategory);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param picCategory
    */
    @RequiresPermissions("basicinfo:pic_category:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PicCategory picCategory) {
        return super.exportXls(request, picCategory, PicCategory.class, "图片分类");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:pic_category:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PicCategory.class);
    }

}
