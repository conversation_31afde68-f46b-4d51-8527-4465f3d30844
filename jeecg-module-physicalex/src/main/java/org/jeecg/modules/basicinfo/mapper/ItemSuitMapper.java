package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemSuit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.basicinfo.entity.SuitGroup;

/**
 * @Description: 体检套餐
 * @Author: jeecg-boot
 * @Date: 2024-02-03
 * @Version: V1.0
 */
public interface ItemSuitMapper extends BaseMapper<ItemSuit> {

    List<ItemGroup> getItemGroupOfSuit(@Param("suitId") String suitId);

    List<SuitGroup> getSuitGroup(@Param("suitId") String suitId);

    List<ItemGroup> getGroupOfSuit(@Param("suitId") String suitId);

    List<String> getSysSuitGroupIds(@Param("suitType") String suitType);

    List<ItemSuit> getSuitByKeyword(@Param("keyword") String keyword);

    Page<ItemSuit> getDeletedSuitByKeyword(Page<ItemSuit> page, @Param("name") String name, @Param("helpChar") String helpChar, @Param("keyword") String keyword, String delFlag, @Param("enableFlag") String enableFlag);

    Page<ItemSuit> pageSuit4H5(Page<ItemSuit> page, @Param("categoryId") String categoryId, @Param("priceStart") String priceStart, @Param("priceEnd") String priceEnd, @Param("gender") String gender);
}
