package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.ReportSettingItemgroup;
import org.jeecg.modules.basicinfo.mapper.ReportSettingItemgroupMapper;
import org.jeecg.modules.basicinfo.service.IReportSettingItemgroupService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 报告分组设置-关联大项
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
@Service
public class ReportSettingItemgroupServiceImpl extends ServiceImpl<ReportSettingItemgroupMapper, ReportSettingItemgroup> implements IReportSettingItemgroupService {
	
	@Autowired
	private ReportSettingItemgroupMapper reportSettingItemgroupMapper;
	
	@Override
	public List<ReportSettingItemgroup> selectByMainId(String mainId) {
		return reportSettingItemgroupMapper.selectByMainId(mainId);
	}
}
