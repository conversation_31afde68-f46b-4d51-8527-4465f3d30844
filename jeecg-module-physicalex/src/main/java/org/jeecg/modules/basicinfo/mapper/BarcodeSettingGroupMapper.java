package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.BarcodeSettingGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 条码设置与项目组合关联表
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
public interface BarcodeSettingGroupMapper extends BaseMapper<BarcodeSettingGroup> {

    List<BarcodeSettingGroup> listByBarcodeSetting(@Param("settingId") String settingId);

    List<BarcodeSettingGroup> listItemBySettingIdAndRegId(@Param("settingId") String settingId, @Param("customerRegId") String customerRegId,@Param("autoCharge") String autoCharge);
}
