package org.jeecg.modules.basicinfo.vo;

import java.util.List;

import org.jeecg.modules.basicinfo.entity.OrgArticle;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 机构信息表
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Data
@ApiModel(value="org_infoPage对象", description="机构信息表")
public class OrgInfoPage {

	/**id*/
	@ApiModelProperty(value = "id")
    private java.lang.String id;
	/**机构编码*/
	@Excel(name = "机构编码", width = 15)
	@ApiModelProperty(value = "机构编码")
    private java.lang.String orgCode;
	/**机构名称*/
	@Excel(name = "机构名称", width = 15)
	@ApiModelProperty(value = "机构名称")
    private java.lang.String orgName;
	/**营业时间*/
	@Excel(name = "营业时间", width = 15)
	@ApiModelProperty(value = "营业时间")
    private java.lang.String businessTime;
	/**体检时间*/
	@Excel(name = "体检时间", width = 15)
	@ApiModelProperty(value = "体检时间")
    private java.lang.String examTime;
	/**联系电话*/
	@Excel(name = "联系电话", width = 15)
	@ApiModelProperty(value = "联系电话")
    private java.lang.String phone;
	/**位置*/
	@Excel(name = "位置", width = 15)
	@ApiModelProperty(value = "位置")
    private java.lang.String location;
	/**详细地址*/
	@Excel(name = "详细地址", width = 15)
	@ApiModelProperty(value = "详细地址")
    private java.lang.String detailAddress;
	/**经度*/
	@Excel(name = "经度", width = 15)
	@ApiModelProperty(value = "经度")
    private java.lang.String longitude;
	/**纬度*/
	@Excel(name = "纬度", width = 15)
	@ApiModelProperty(value = "纬度")
    private java.lang.String latitude;
	/**状态*/
	@Excel(name = "状态", width = 15, dictTable = "sys_dict_item where dict_id='4c753b5293304e7a445fd2741b46529d'", dicText = "item_text", dicCode = "item_value")
    @Dict(dictTable = "sys_dict_item where dict_id='4c753b5293304e7a445fd2741b46529d'", dicText = "item_text", dicCode = "item_value")
	@ApiModelProperty(value = "状态")
    private java.lang.String state;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
	/**createBy*/
	@ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
	/**updateTiem*/
	@Excel(name = "updateTiem", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "updateTiem")
    private java.util.Date updateTiem;
	/**updateBy*/
	@ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;

	@ExcelCollection(name="机构其他信息")
	@ApiModelProperty(value = "机构其他信息")
	private List<OrgArticle> orgArticleList;

}
