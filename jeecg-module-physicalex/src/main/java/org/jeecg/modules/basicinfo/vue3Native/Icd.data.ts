import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '编码',
    align: "center",
    dataIndex: 'code'
  },
  {
    title: '副编码',
    align: "center",
    dataIndex: 'subCode'
  },
  {
    title: '拼音助记码',
    align: "center",
    dataIndex: 'assistCode'
  },
  {
    title: '版本',
    align: "center",
    dataIndex: 'version'
  },
  {
    title: '名称',
    align: "center",
    dataIndex: 'name'
  },
];

// 高级查询数据
export const superQuerySchema = {
  code: {title: '编码',order: 0,view: 'text', type: 'string',},
  subCode: {title: '副编码',order: 1,view: 'text', type: 'string',},
  assistCode: {title: '拼音助记码',order: 2,view: 'text', type: 'string',},
  version: {title: '版本',order: 3,view: 'text', type: 'string',},
  name: {title: '名称',order: 4,view: 'text', type: 'string',},
};
