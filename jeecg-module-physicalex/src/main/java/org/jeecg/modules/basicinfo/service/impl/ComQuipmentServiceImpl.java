package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.ComQuipment;
import org.jeecg.modules.basicinfo.mapper.ComQuipmentMapper;
import org.jeecg.modules.basicinfo.service.IComQuipmentService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 串口设备管理
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
@Service
public class ComQuipmentServiceImpl extends ServiceImpl<ComQuipmentMapper, ComQuipment> implements IComQuipmentService {

}
