package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.CheckPartUsageStat;

import java.util.List;

/**
 * @Description: 检查部位使用统计
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
public interface ICheckPartUsageStatService extends IService<CheckPartUsageStat> {

    /**
     * 增加使用次数
     *
     * @param itemGroupId 项目组合ID
     * @param checkPartId 部位ID
     */
    void incrementUsage(String itemGroupId, String checkPartId);

    /**
     * 根据项目组合ID获取统计列表
     *
     * @param itemGroupId 项目组合ID
     * @return 统计列表
     */
    List<CheckPartUsageStat> listByItemGroupId(String itemGroupId);
}
