package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.BarcodeSettingGroup;
import org.jeecg.modules.basicinfo.service.IBarcodeSettingGroupService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 条码设置与项目组合关联表
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Api(tags="条码设置与项目组合关联表")
@RestController
@RequestMapping("/basicinfo/barcodeSettingGroup")
@Slf4j
public class BarcodeSettingGroupController extends JeecgController<BarcodeSettingGroup, IBarcodeSettingGroupService> {
	@Autowired
	private IBarcodeSettingGroupService barcodeSettingGroupService;

	@ApiOperation(value = "保存组合")
	@PostMapping(value = "/saveGroup")
	public Result<?> saveGroup(@RequestBody JSONArray groupListJSON) {
		List<BarcodeSettingGroup> groupList = groupListJSON.toJavaList(BarcodeSettingGroup.class);

		barcodeSettingGroupService.saveOrUpdateBatch(groupList);
		return Result.OK("操作成功!");
	}

	//setShortName
	@ApiOperation(value = "设置组合简称")
	@GetMapping(value = "/setShortName")
	public Result<?> setShortName(@RequestParam(name="id",required=true) String id, @RequestParam(name="shortName",required=true) String shortName) {
		barcodeSettingGroupService.setShortName(id, shortName);
		return Result.OK("操作成功!");
	}
	
	/**
	 * 分页列表查询
	 *
	 * @param barcodeSettingGroup
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "条码设置与项目组合关联表-分页列表查询")
	@ApiOperation(value="条码设置与项目组合关联表-分页列表查询", notes="条码设置与项目组合关联表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BarcodeSettingGroup>> queryPageList(BarcodeSettingGroup barcodeSettingGroup,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<BarcodeSettingGroup> queryWrapper = QueryGenerator.initQueryWrapper(barcodeSettingGroup, req.getParameterMap());
		Page<BarcodeSettingGroup> page = new Page<BarcodeSettingGroup>(pageNo, pageSize);
		IPage<BarcodeSettingGroup> pageList = barcodeSettingGroupService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param barcodeSettingGroup
	 * @return
	 */
	@AutoLog(value = "条码设置与项目组合关联表-添加")
	@ApiOperation(value="条码设置与项目组合关联表-添加", notes="条码设置与项目组合关联表-添加")
	@RequiresPermissions("basicinfo:barcode_setting_group:add")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody BarcodeSettingGroup barcodeSettingGroup) {
		barcodeSettingGroupService.save(barcodeSettingGroup);
		return Result.OK("添加成功！",barcodeSettingGroup);
	}
	
	/**
	 *  编辑
	 *
	 * @param barcodeSettingGroup
	 * @return
	 */
	@AutoLog(value = "条码设置与项目组合关联表-编辑")
	@ApiOperation(value="条码设置与项目组合关联表-编辑", notes="条码设置与项目组合关联表-编辑")
	@RequiresPermissions("basicinfo:barcode_setting_group:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<?> edit(@RequestBody BarcodeSettingGroup barcodeSettingGroup) {
		barcodeSettingGroupService.updateById(barcodeSettingGroup);
		return Result.OK("编辑成功!",barcodeSettingGroup);
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "条码设置与项目组合关联表-通过id删除")
	@ApiOperation(value="条码设置与项目组合关联表-通过id删除", notes="条码设置与项目组合关联表-通过id删除")
	//@RequiresPermissions("basicinfo:barcode_setting_group:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		barcodeSettingGroupService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "条码设置与项目组合关联表-批量删除")
	@ApiOperation(value="条码设置与项目组合关联表-批量删除", notes="条码设置与项目组合关联表-批量删除")
	//@RequiresPermissions("basicinfo:barcode_setting_group:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.barcodeSettingGroupService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "条码设置与项目组合关联表-通过id查询")
	@ApiOperation(value="条码设置与项目组合关联表-通过id查询", notes="条码设置与项目组合关联表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BarcodeSettingGroup> queryById(@RequestParam(name="id",required=true) String id) {
		BarcodeSettingGroup barcodeSettingGroup = barcodeSettingGroupService.getById(id);
		if(barcodeSettingGroup==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(barcodeSettingGroup);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param barcodeSettingGroup
    */
    @RequiresPermissions("basicinfo:barcode_setting_group:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BarcodeSettingGroup barcodeSettingGroup) {
        return super.exportXls(request, barcodeSettingGroup, BarcodeSettingGroup.class, "条码设置与项目组合关联表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:barcode_setting_group:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BarcodeSettingGroup.class);
    }

}
