package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.PicCategory;
import org.jeecg.modules.basicinfo.mapper.PicCategoryMapper;
import org.jeecg.modules.basicinfo.service.IPicCategoryService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 图片分类
 * @Author: jeecg-boot
 * @Date:   2024-10-11
 * @Version: V1.0
 */
@Service
public class PicCategoryServiceImpl extends ServiceImpl<PicCategoryMapper, PicCategory> implements IPicCategoryService {

}
