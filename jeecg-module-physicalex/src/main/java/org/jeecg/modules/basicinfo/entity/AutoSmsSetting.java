package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 自动发送短信配置
 * @Author: jeecg-boot
 * @Date: 2024-11-10
 * @Version: V1.0
 */
@Data
@TableName("auto_sms_setting")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "auto_sms_setting对象", description = "自动发送短信配置")
public class AutoSmsSetting implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 标题
     */
    @Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private java.lang.String title;
    /**
     * 业务类别
     */
    @Excel(name = "业务类别", width = 15, dicCode = "sms_biz_type")
    @Dict(dicCode = "sms_biz_type")
    @ApiModelProperty(value = "业务类别")
    private java.lang.String bizType;
    /**
     * 体检类别
     */
    @Excel(name = "体检类别", width = 15, dicCode = "examination_type")
    @Dict(dicCode = "examination_type")
    @ApiModelProperty(value = "体检类别")
    private java.lang.String includeExamCategory;
    /**
     * 消息模版
     */
    @Excel(name = "消息模版", width = 15, dictTable = "msg_template where enable_flag=1", dicText = "title", dicCode = "id")
    @Dict(dictTable = "msg_template where enable_flag=1", dicText = "title", dicCode = "id")
    @ApiModelProperty(value = "消息模版")
    private java.lang.String msgTemplateId;
    /**
     * 启用
     */
    @Excel(name = "启用", width = 15, replace = {"是_1", "否_0"})
    @ApiModelProperty(value = "启用")
    private java.lang.String enableFlag;
    /**
     * 适用的危急值分类
     */
    @Excel(name = "适用的危急值分类", width = 15)
    @ApiModelProperty(value = "适用的危急值分类")
    private String severityDegree;
    /**
     * 消息内容
     */
    @Excel(name = "消息内容", width = 15)
    @ApiModelProperty(value = "消息内容")
    private String templateContent;
    /**
     * 发送的时间节点
     */
    @Excel(name = "发送的时间节点", width = 15)
    @ApiModelProperty(value = "发送的时间节点")
    private String sendNode;
    /**
     * 发送时间推移量，单位天
     */
    @Excel(name = "发送时间推移量，单位天", width = 15)
    @ApiModelProperty(value = "发送时间推移量，单位天")
    private Integer triggerTimeShift;

    public boolean canSend(java.util.Date createTime) {
        if (this.triggerTimeShift == null || this.triggerTimeShift <= 0) {
            return true;
        }

        // 将 createTime 转换为 LocalDate（仅日期部分）
        LocalDate createDate = createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 获取当前日期的 LocalDate（仅日期部分）
        LocalDate nowDate = LocalDate.now();

        // 计算日期差
        long daysDiff = nowDate.toEpochDay() - createDate.toEpochDay();

        return daysDiff >= this.triggerTimeShift;
    }

    public static void main(String[] args) {
        AutoSmsSetting autoSmsSetting = new AutoSmsSetting();
        autoSmsSetting.setTriggerTimeShift(1);

        // 模拟创建时间为昨天 23:59:59
        LocalDateTime localDateTime = LocalDateTime.of(2025, 2, 16, 23, 59, 59);
        Date createTime = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

        System.out.println(autoSmsSetting.canSend(createTime)); // 输出 true
    }

}
