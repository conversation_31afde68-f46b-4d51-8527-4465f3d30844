package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.ItemDict;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 项目结果字典
 * @Author: jeecg-boot
 * @Date:   2024-05-16
 * @Version: V1.0
 */
public interface IItemDictService extends IService<ItemDict> {

    ItemDict getDefaultDict(String itemId);

    Long getNextSeqNo(String itemId);

    boolean duplicateCheck(String itemId, String dictText,String id);

    void resetDefault(ItemDict item, String dictId);

    List<ItemDict>listByItemId(String itemId);

    void increaseUseCount(String dictId,Integer count);
}
