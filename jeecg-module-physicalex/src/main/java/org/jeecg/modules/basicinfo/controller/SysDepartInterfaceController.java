package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.SysDepartInterface;
import org.jeecg.modules.basicinfo.service.ISysDepartInterfaceService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: sys_depart_interface
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
@Api(tags="sys_depart_interface")
@RestController
@RequestMapping("/basicinfo/sysDepartInterface")
@Slf4j
public class SysDepartInterfaceController extends JeecgController<SysDepartInterface, ISysDepartInterfaceService> {
	@Autowired
	private ISysDepartInterfaceService sysDepartInterfaceService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysDepartInterface
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "sys_depart_interface-分页列表查询")
	@ApiOperation(value="sys_depart_interface-分页列表查询", notes="sys_depart_interface-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysDepartInterface>> queryPageList(SysDepartInterface sysDepartInterface,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SysDepartInterface> queryWrapper = QueryGenerator.initQueryWrapper(sysDepartInterface, req.getParameterMap());
		Page<SysDepartInterface> page = new Page<SysDepartInterface>(pageNo, pageSize);
		IPage<SysDepartInterface> pageList = sysDepartInterfaceService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysDepartInterface
	 * @return
	 */
	@AutoLog(value = "sys_depart_interface-添加")
	@ApiOperation(value="sys_depart_interface-添加", notes="sys_depart_interface-添加")
	@RequiresPermissions("basicinfo:sys_depart_interface:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysDepartInterface sysDepartInterface) {
		sysDepartInterfaceService.save(sysDepartInterface);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysDepartInterface
	 * @return
	 */
	@AutoLog(value = "sys_depart_interface-编辑")
	@ApiOperation(value="sys_depart_interface-编辑", notes="sys_depart_interface-编辑")
	@RequiresPermissions("basicinfo:sys_depart_interface:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysDepartInterface sysDepartInterface) {
		sysDepartInterfaceService.updateById(sysDepartInterface);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "sys_depart_interface-通过id删除")
	@ApiOperation(value="sys_depart_interface-通过id删除", notes="sys_depart_interface-通过id删除")
	@RequiresPermissions("basicinfo:sys_depart_interface:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysDepartInterfaceService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "sys_depart_interface-批量删除")
	@ApiOperation(value="sys_depart_interface-批量删除", notes="sys_depart_interface-批量删除")
	@RequiresPermissions("basicinfo:sys_depart_interface:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysDepartInterfaceService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "sys_depart_interface-通过id查询")
	@ApiOperation(value="sys_depart_interface-通过id查询", notes="sys_depart_interface-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysDepartInterface> queryById(@RequestParam(name="id",required=true) String id) {
		SysDepartInterface sysDepartInterface = sysDepartInterfaceService.getById(id);
		if(sysDepartInterface==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysDepartInterface);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysDepartInterface
    */
    @RequiresPermissions("basicinfo:sys_depart_interface:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysDepartInterface sysDepartInterface) {
        return super.exportXls(request, sysDepartInterface, SysDepartInterface.class, "sys_depart_interface");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:sys_depart_interface:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysDepartInterface.class);
    }

}
