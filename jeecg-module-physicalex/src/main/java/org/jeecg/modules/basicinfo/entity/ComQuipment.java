package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 串口设备管理
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
@Data
@TableName("com_quipment")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="com_quipment对象", description="串口设备管理")
public class ComQuipment implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**设备名称*/
	@Excel(name = "设备名称", width = 15)
    @ApiModelProperty(value = "设备名称")
    private java.lang.String name;
	/**设备代码*/
	@Excel(name = "设备代码", width = 15)
    @ApiModelProperty(value = "设备代码")
    private java.lang.String code;
	/**串口描述符*/
	@Excel(name = "串口描述符", width = 15)
    @ApiModelProperty(value = "串口描述符")
    private java.lang.String portDescriptor;
	/**波特率*/
	@Excel(name = "波特率", width = 15)
    @ApiModelProperty(value = "波特率")
    private java.lang.Integer baudRate;
	/**数据位*/
	@Excel(name = "数据位", width = 15)
    @ApiModelProperty(value = "数据位")
    private java.lang.Integer dataBits;
	/**停止位*/
	@Excel(name = "停止位", width = 15)
    @ApiModelProperty(value = "停止位")
    private java.lang.Integer stopBits;
	/**校验位*/
	@Excel(name = "校验位", width = 15)
    @ApiModelProperty(value = "校验位")
    private java.lang.Integer verifyBits;
	/**睡眠毫秒数*/
	@Excel(name = "睡眠毫秒数", width = 15)
    @ApiModelProperty(value = "睡眠毫秒数")
    private java.lang.Integer safetySleepTime;
	/**指令发送函数*/
	@Excel(name = "指令发送函数", width = 15)
    @ApiModelProperty(value = "指令发送函数")
    private java.lang.String cmdFunction;
	/**数据处理函数*/
	@Excel(name = "数据处理函数", width = 15)
    @ApiModelProperty(value = "数据处理函数")
    private java.lang.String dataFunction;
	/**websocket地址*/
	@Excel(name = "websocket地址", width = 15)
    @ApiModelProperty(value = "websocket地址")
    private java.lang.String websocketUrl;
    /**关联项目*/
    @Excel(name = "关联项目", width = 15)
    @ApiModelProperty(value = "关联项目")
    private java.lang.String groupId;
    /**启用标志*/
    @Excel(name = "启用标志", width = 15)
    @ApiModelProperty(value = "启用标志")
    private java.lang.String enableFlag;
    /**租户ID*/
    @Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
    private java.lang.String tenantId;

    private String deviceType;

    private String deviceModel;

    private Integer priority;

    private String autoStartMeasurement;

    private String extraConfig;

}
