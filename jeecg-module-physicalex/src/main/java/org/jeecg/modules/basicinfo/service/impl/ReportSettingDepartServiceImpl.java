package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.ReportSettingDepart;
import org.jeecg.modules.basicinfo.mapper.ReportSettingDepartMapper;
import org.jeecg.modules.basicinfo.service.IReportSettingDepartService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 报告分组设置-关联科室
 * @Author: jeecg-boot
 * @Date:   2024-11-07
 * @Version: V1.0
 */
@Service
public class ReportSettingDepartServiceImpl extends ServiceImpl<ReportSettingDepartMapper, ReportSettingDepart> implements IReportSettingDepartService {
	
	@Autowired
	private ReportSettingDepartMapper reportSettingDepartMapper;
	
	@Override
	public List<ReportSettingDepart> selectByMainId(String mainId) {
		return reportSettingDepartMapper.selectByMainId(mainId);
	}
}
