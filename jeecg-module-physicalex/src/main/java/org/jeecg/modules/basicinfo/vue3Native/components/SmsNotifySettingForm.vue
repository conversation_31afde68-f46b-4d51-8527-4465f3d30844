<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="SmsNotifySettingForm">
          <a-row>
						<a-col :span="24">
							<a-form-item label="模块" v-bind="validateInfos.module" id="SmsNotifySettingForm-module" name="module">
								<j-dict-select-tag v-model:value="formData.module" dictCode="sms_notify_module" placeholder="请选择模块"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="被通知手机号" v-bind="validateInfos.targetPhone" id="SmsNotifySettingForm-targetPhone" name="targetPhone">
								<a-textarea v-model:value="formData.targetPhone" :rows="4" placeholder="请输入被通知手机号" />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="内容模版" v-bind="validateInfos.content" id="SmsNotifySettingForm-content" name="content">
								<a-textarea v-model:value="formData.content" :rows="4" placeholder="请输入内容模版" />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="条件" v-bind="validateInfos.condiiton" id="SmsNotifySettingForm-condiiton" name="condiiton">
								<a-textarea v-model:value="formData.condiiton" :rows="4" placeholder="请输入条件" />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="启用" v-bind="validateInfos.enableFlag" id="SmsNotifySettingForm-enableFlag" name="enableFlag">
								<j-switch v-model:value="formData.enableFlag" :options="[1,0]" ></j-switch>
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../SmsNotifySetting.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    module: '',   
    targetPhone: '',   
    content: '',   
    condiiton: '',   
    enableFlag: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    module: [{ required: true, message: '请输入模块!'},],
    targetPhone: [{ required: true, message: '请输入被通知手机号!'},],
    content: [{ required: true, message: '请输入内容模版!'},],
    enableFlag: [{ required: true, message: '请输入启用!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
