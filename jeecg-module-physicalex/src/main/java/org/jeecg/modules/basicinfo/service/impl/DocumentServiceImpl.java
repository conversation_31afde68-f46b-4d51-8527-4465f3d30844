package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.netty.util.internal.UnstableApi;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.utils.JsoupRichTextUtils;
import org.jeecg.modules.basicinfo.entity.Document;
import org.jeecg.modules.basicinfo.mapper.DocumentMapper;
import org.jeecg.modules.basicinfo.service.IDocumentService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description: 文档表
 * @Author: jeecg-boot
 * @Date: 2025-03-05
 * @Version: V1.0
 */
@Service
public class DocumentServiceImpl extends ServiceImpl<DocumentMapper, Document> implements IDocumentService {

    @Autowired
    private DocumentMapper documentMapper;
    @Autowired
    private ISysSettingService sysSettingService;

    @Override
    public void listAll(Page<Document> page) {
        documentMapper.listAll(page);
        for(Document document : page.getRecords()) {
            String localFsBaseUrl = sysSettingService.getValueByCode("local_file_url");
            String wanFsBaseUrl = sysSettingService.getValueByCode("open_file_url");
            //如果wamFsBaseUrl末尾没有/，则加上/
            if (StringUtils.isNotBlank(wanFsBaseUrl) && !wanFsBaseUrl.endsWith("/")) {
                wanFsBaseUrl += "/";
            }
            String content = document.getContent();
            if (StringUtils.isNotBlank(content)) {
                String contentWan = JsoupRichTextUtils.replaceCommonLinks(content, localFsBaseUrl, wanFsBaseUrl);
                document.setContent(contentWan);
            }
        }
    }

    @Transactional
    @Override
    public void saveDocument(Document document) {

        String author = sysSettingService.getValueByCode("article_author");
        author = StringUtils.isBlank(author) ? "体检中心" : author;
        document.setAuthor(author);

        saveOrUpdate(document);
    }
}
