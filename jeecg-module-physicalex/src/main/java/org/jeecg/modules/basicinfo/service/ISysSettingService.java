package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.SysSetting;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 参数配置
 * @Author: jeecg-boot
 * @Date:   2024-05-25
 * @Version: V1.0
 */
public interface ISysSettingService extends IService<SysSetting> {

    SysSetting getByCode(String code);

    String getValueByCode(String code);

    void evictCache();
}
