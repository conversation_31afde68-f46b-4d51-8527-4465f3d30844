package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: sys_user_interface
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
@Data
@TableName("sys_user_interface")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_user_interface对象", description="sys_user_interface")
public class SysUserInterface implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id  staff_code  员工代码*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id  staff_code  员工代码")
    private java.lang.String id;
	/**机构代码*/
	@Excel(name = "机构代码", width = 15)
    @ApiModelProperty(value = "机构代码")
    private java.lang.String orgCode;
	/**院区代码*/
	@Excel(name = "院区代码", width = 15)
    @ApiModelProperty(value = "院区代码")
    private java.lang.String districtCode;
	/**员工代码*/
	@Excel(name = "员工代码", width = 15)
    @ApiModelProperty(value = "员工代码")
    private java.lang.String staffCode;
	/**员工工作牌号*/
	@Excel(name = "员工工作牌号", width = 15)
    @ApiModelProperty(value = "员工工作牌号")
    private java.lang.String employeNo;
	/**员工姓名*/
	@Excel(name = "员工姓名", width = 15)
    @ApiModelProperty(value = "员工姓名")
    private java.lang.String staffName;
	/**员工性别*/
	@Excel(name = "员工性别", width = 15)
    @ApiModelProperty(value = "员工性别")
    private java.lang.String sexCode;
	/**员工出生日期*/
	@Excel(name = "员工出生日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "员工出生日期")
    private java.util.Date birthDate;
	/**员工身份证号*/
	@Excel(name = "员工身份证号", width = 15)
    @ApiModelProperty(value = "员工身份证号")
    private java.lang.String identityNo;
	/**任职时间*/
	@Excel(name = "任职时间", width = 15)
    @ApiModelProperty(value = "任职时间")
    private java.lang.String entryDate;
	/**行政科室代码*/
	@Excel(name = "行政科室代码", width = 15)
    @ApiModelProperty(value = "行政科室代码")
    private java.lang.String adminDeptCode;
	/**行政科室名称*/
	@Excel(name = "行政科室名称", width = 15)
    @ApiModelProperty(value = "行政科室名称")
    private java.lang.String adminDeptName;
	/**行政级别*/
	@Excel(name = "行政级别", width = 15)
    @ApiModelProperty(value = "行政级别")
    private java.lang.String adminClassCode;
	/**工作类别编码*/
	@Excel(name = "工作类别编码", width = 15)
    @ApiModelProperty(value = "工作类别编码")
    private java.lang.String workClassCode;
	/**工作类别名称*/
	@Excel(name = "工作类别名称", width = 15)
    @ApiModelProperty(value = "工作类别名称")
    private java.lang.String workClassName;
	/**手机号码*/
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    private java.lang.String mobilePhone;
	/**在职状态*/
	@Excel(name = "在职状态", width = 15)
    @ApiModelProperty(value = "在职状态")
    private java.lang.String workStatus;
	/**职称代码*/
	@Excel(name = "职称代码", width = 15)
    @ApiModelProperty(value = "职称代码")
    private java.lang.String techTitlesCode;
	/**职称名称：主任，副主任*/
	@Excel(name = "职称名称：主任，副主任", width = 15)
    @ApiModelProperty(value = "职称名称：主任，副主任")
    private java.lang.String techTitlesName;
	/**员工简介*/
	@Excel(name = "员工简介", width = 15)
    @ApiModelProperty(value = "员工简介")
    private java.lang.String staffIntroduce;
	/**员工特长*/
	@Excel(name = "员工特长", width = 15)
    @ApiModelProperty(value = "员工特长")
    private java.lang.String staffSpeciality;
	/**行数*/
	@Excel(name = "行数", width = 15)
    @ApiModelProperty(value = "行数")
    private java.lang.String rowNumber;
	/**总数*/
	@Excel(name = "总数", width = 15)
    @ApiModelProperty(value = "总数")
    private java.lang.String totalNumber;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
}
