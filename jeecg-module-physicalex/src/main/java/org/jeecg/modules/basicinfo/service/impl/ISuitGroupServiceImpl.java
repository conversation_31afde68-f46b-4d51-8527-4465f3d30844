package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.SuitGroup;
import org.jeecg.modules.basicinfo.mapper.SuitGroupMapper;
import org.jeecg.modules.basicinfo.service.ISuitGroupService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: itemsuit_item
 * @Author: jeecg-boot
 * @Date:   2024-02-03
 * @Version: V1.0
 */
@Service
public class ISuitGroupServiceImpl extends ServiceImpl<SuitGroupMapper, SuitGroup> implements ISuitGroupService {

}
