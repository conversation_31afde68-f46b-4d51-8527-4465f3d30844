package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.GroupChangeRecord;
import org.jeecg.modules.basicinfo.mapper.GroupChangeRecordMapper;
import org.jeecg.modules.basicinfo.service.IGroupChangeRecordService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 项目价格变动操作记录表
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Service
public class GroupChangeRecordServiceImpl extends ServiceImpl<GroupChangeRecordMapper, GroupChangeRecord> implements IGroupChangeRecordService {

}
