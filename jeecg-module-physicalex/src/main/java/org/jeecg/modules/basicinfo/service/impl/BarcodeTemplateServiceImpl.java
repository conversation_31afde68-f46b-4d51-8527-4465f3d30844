package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.basicinfo.entity.BarcodeTemplate;
import org.jeecg.modules.basicinfo.mapper.BarcodeTemplateMapper;
import org.jeecg.modules.basicinfo.service.IBarcodeTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 条码模板
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Service
public class BarcodeTemplateServiceImpl extends ServiceImpl<BarcodeTemplateMapper, BarcodeTemplate> implements IBarcodeTemplateService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getUpdateTime(String id) {
        String updateTime = null;
        try {
            updateTime = jdbcTemplate.queryForObject("select update_time from barcode_template where id = ?", String.class, id);
        } catch (Exception ignored) {
        }

        return updateTime;
    }

    @Override
    public String getIdByCategory(String category) {
        String id = null;
        try {
            id = jdbcTemplate.queryForObject("select id from barcode_template where category = ? limit 1", String.class, category);
        } catch (Exception ignored) {
        }

        return id;
    }
}
