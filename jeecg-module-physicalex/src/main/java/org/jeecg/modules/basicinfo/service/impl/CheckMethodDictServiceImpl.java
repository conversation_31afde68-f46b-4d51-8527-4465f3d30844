package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.CheckMethodDict;
import org.jeecg.modules.basicinfo.mapper.CheckMethodDictMapper;
import org.jeecg.modules.basicinfo.service.ICheckMethodDictService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: check_method_dict
 * @Author: jeecg-boot
 * @Date:   2025-06-19
 * @Version: V1.0
 */
@Service
public class CheckMethodDictServiceImpl extends ServiceImpl<CheckMethodDictMapper, CheckMethodDict> implements ICheckMethodDictService {

}
