package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.SysSetting;
import org.jeecg.modules.basicinfo.service.ISysSettingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 参数配置
 * @Author: jeecg-boot
 * @Date:   2024-05-25
 * @Version: V1.0
 */
@Api(tags="参数配置")
@RestController
@RequestMapping("/basicinfo/sysSetting")
@Slf4j
public class SysSettingController extends JeecgController<SysSetting, ISysSettingService> {
	@Autowired
	private ISysSettingService sysSettingService;

	/**
	 * 根据代码获取参数值
	 * @param code
	 * @param req
	 * @return
	 */
	@ApiOperation(value="根据代码获取参数值", notes="根据代码获取参数值")
	@GetMapping(value = "/getValueByCode")
	public Result<String> getValueByCode(@RequestParam(name="code",required=true) String code, HttpServletRequest req) {
		String value = sysSettingService.getValueByCode(code);
		return Result.OK("获取成功！",value);
	}

	/**
	 * 分页列表查询
	 *
	 * @param sysSetting
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "参数配置-分页列表查询")
	@ApiOperation(value="参数配置-分页列表查询", notes="参数配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysSetting>> queryPageList(SysSetting sysSetting,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SysSetting> queryWrapper = QueryGenerator.initQueryWrapper(sysSetting, req.getParameterMap());
		Page<SysSetting> page = new Page<SysSetting>(pageNo, pageSize);
		IPage<SysSetting> pageList = sysSettingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   清除缓存
	 *
	 * @param code
	 * @return
	 */
	@AutoLog(value = "参数配置-清除缓存")
	@ApiOperation(value="参数配置-清除缓存", notes="参数配置-清除缓存")
	@RequiresPermissions("basicinfo:sys_setting:edit")
	@GetMapping(value = "/evictCache")
	public Result<String> evictCache(@RequestParam(name="code",required=true) String code) {
		sysSettingService.evictCache();
		return Result.OK("清除缓存成功！");
	}
	
	/**
	 *   添加
	 *
	 * @param sysSetting
	 * @return
	 */
	@AutoLog(value = "参数配置-添加")
	@ApiOperation(value="参数配置-添加", notes="参数配置-添加")
	@RequiresPermissions("basicinfo:sys_setting:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysSetting sysSetting) {
		sysSettingService.save(sysSetting);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysSetting
	 * @return
	 */
	@AutoLog(value = "参数配置-编辑")
	@ApiOperation(value="参数配置-编辑", notes="参数配置-编辑")
	@RequiresPermissions("basicinfo:sys_setting:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysSetting sysSetting) {
		sysSettingService.updateById(sysSetting);
		sysSettingService.evictCache();
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "参数配置-通过id删除")
	@ApiOperation(value="参数配置-通过id删除", notes="参数配置-通过id删除")
	@RequiresPermissions("basicinfo:sys_setting:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysSettingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "参数配置-批量删除")
	@ApiOperation(value="参数配置-批量删除", notes="参数配置-批量删除")
	@RequiresPermissions("basicinfo:sys_setting:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysSettingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "参数配置-通过id查询")
	@ApiOperation(value="参数配置-通过id查询", notes="参数配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysSetting> queryById(@RequestParam(name="id",required=true) String id) {
		SysSetting sysSetting = sysSettingService.getById(id);
		if(sysSetting==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysSetting);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysSetting
    */
    @RequiresPermissions("basicinfo:sys_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysSetting sysSetting) {
        return super.exportXls(request, sysSetting, SysSetting.class, "参数配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:sys_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysSetting.class);
    }

}
