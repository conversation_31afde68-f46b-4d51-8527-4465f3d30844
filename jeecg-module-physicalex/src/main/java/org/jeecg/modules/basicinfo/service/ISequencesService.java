package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.Sequences;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: sequences
 * @Author: jeecg-boot
 * @Date:   2024-12-18
 * @Version: V1.0
 */
public interface ISequencesService extends IService<Sequences> {
    Long getNextSequence(String seqName);
    String getNextSequenceWithPrefix(String seqName);

}
