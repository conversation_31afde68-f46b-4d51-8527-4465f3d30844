package org.jeecg.modules.basicinfo.mapper;

import java.util.List;
import org.jeecg.modules.basicinfo.entity.OrgArticle;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 机构其他信息
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
public interface OrgArticleMapper extends BaseMapper<OrgArticle> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<OrgArticle>
   */
	public List<OrgArticle> selectByMainId(@Param("mainId") String mainId);
}
