<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.DocumentMapper">

    <select id="listAll" resultType="org.jeecg.modules.basicinfo.entity.Document">
        select id, title, type, sub_title, create_by,create_time,creator, update_time, update_by from document where enable_flag = 1
    </select>
</mapper>