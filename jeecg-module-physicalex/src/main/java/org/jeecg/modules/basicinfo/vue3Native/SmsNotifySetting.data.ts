import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '模块',
    align: "center",
    dataIndex: 'module_dictText'
  },
  {
    title: '被通知手机号',
    align: "center",
    dataIndex: 'targetPhone'
  },
  {
    title: '内容模版',
    align: "center",
    dataIndex: 'content'
  },
  {
    title: '条件',
    align: "center",
    dataIndex: 'condiiton'
  },
  {
    title: '启用',
    align: "center",
    dataIndex: 'enableFlag',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}]);
     },
  },
];

// 高级查询数据
export const superQuerySchema = {
  module: {title: '模块',order: 0,view: 'list', type: 'string',dictCode: 'sms_notify_module',},
  targetPhone: {title: '被通知手机号',order: 1,view: 'textarea', type: 'string',},
  content: {title: '内容模版',order: 2,view: 'textarea', type: 'string',},
  condiiton: {title: '条件',order: 3,view: 'textarea', type: 'string',},
  enableFlag: {title: '启用',order: 4,view: 'switch', type: 'string',},
};
