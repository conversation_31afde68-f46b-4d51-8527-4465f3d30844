package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.basicinfo.entity.AutoPreSummarySetting;
import org.jeecg.modules.basicinfo.mapper.AutoPreSummarySettingMapper;
import org.jeecg.modules.basicinfo.service.IAutoPreSummarySettingService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 自动预总检设置
 * @Author: jeecg-boot
 * @Date: 2024-11-08
 * @Version: V1.0
 */
@Service
public class AutoPreSummarySettingServiceImpl extends ServiceImpl<AutoPreSummarySettingMapper, AutoPreSummarySetting> implements IAutoPreSummarySettingService {

    @Override
    public AutoPreSummarySetting getSetting() {
        LambdaQueryWrapper<AutoPreSummarySetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.last("limit 1");
        AutoPreSummarySetting setting = getOne(queryWrapper);
        if (setting == null) {
            setting = new AutoPreSummarySetting();
            save(setting);
        }

        return setting;
    }
}
