package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 单位
 * @Author: jeecg-boot
 * @Date:   2024-04-09
 * @Version: V1.0
 */
@Data
@TableName("company")
@ApiModel(value="company对象", description="单位")
public class Company implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**编码*/
	@Excel(name = "编码", width = 15)
    @ApiModelProperty(value = "编码")
    private java.lang.String orgCode;
	/**类型*/
	@Excel(name = "类型", width = 15, dicCode = "org_type")
	@Dict(dicCode = "org_type")
    @ApiModelProperty(value = "类型")
    private java.lang.String orgType;
	/**上级单位*/
	@Excel(name = "上级单位", width = 15)
    @ApiModelProperty(value = "上级单位")
    private java.lang.String pid;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
	/**简称*/
	@Excel(name = "简称", width = 15)
    @ApiModelProperty(value = "简称")
    private java.lang.String shortName;
	/**助记码*/
	@Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private java.lang.String helpChar;
	/**来源*/
	@Excel(name = "来源", width = 15, dicCode = "customer_source")
	@Dict(dicCode = "customer_source")
    @ApiModelProperty(value = "来源")
    private java.lang.String source;
	/**联系电话*/
	@Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private java.lang.String telephone;
	/**合同性质*/
	@Excel(name = "合同性质", width = 15, dicCode = "contract_type")
	@Dict(dicCode = "contract_type")
    @ApiModelProperty(value = "合同性质")
    private java.lang.String contractType;
	/**所在省市区*/
	@Excel(name = "所在省市区", width = 15)
    @ApiModelProperty(value = "所在省市区")
    private java.lang.String pcca;
	/**详细地址*/
	@Excel(name = "详细地址", width = 15)
    @ApiModelProperty(value = "详细地址")
    private java.lang.String address;
	/**所在省*/
	@Excel(name = "所在省", width = 15)
    @ApiModelProperty(value = "所在省")
    private java.lang.String addressProvince;
	/**所在市*/
	@Excel(name = "所在市", width = 15)
    @ApiModelProperty(value = "所在市")
    private java.lang.String addressCity;
	/**所在区*/
	@Excel(name = "所在区", width = 15)
    @ApiModelProperty(value = "所在区")
    private java.lang.String addressArea;
	/**单位分类*/
	@Excel(name = "单位分类", width = 15, dictTable = "company_category", dicText = "name", dicCode = "id")
	@Dict(dictTable = "company_category", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "单位分类")
    private java.lang.String categoryId;
	/**行业*/
	@Excel(name = "行业", width = 15, dictTable = "zy_industry", dicText = "name", dicCode = "id")
	@Dict(dictTable = "zy_industry", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "行业")
    private java.lang.String indutry;
	/**经济类型*/
	@Excel(name = "经济类型", width = 15, dicCode = "economic_type")
	@Dict(dicCode = "economic_type")
    @ApiModelProperty(value = "经济类型")
    private java.lang.String economicType;
	/**包含职业体检*/
	@Excel(name = "包含职业体检", width = 15)
    @ApiModelProperty(value = "包含职业体检")
    private java.lang.Integer occupationalExFlag;
	/**统一社会信用代码*/
	@Excel(name = "统一社会信用代码", width = 15)
    @ApiModelProperty(value = "统一社会信用代码")
    private java.lang.String creditCode;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
	/**是否有子节点*/
	@Excel(name = "是否有子节点", width = 15, dicCode = "yn")
	@Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否有子节点")
    private java.lang.String hasChild;
	/**企业规模*/
	@Excel(name = "企业规模", width = 15)
    @ApiModelProperty(value = "企业规模")
    private java.lang.String enSize;
	/**填表单位名称*/
	@Excel(name = "填表单位名称", width = 15)
    @ApiModelProperty(value = "填表单位名称")
    private java.lang.String formCom;
	/**填表人*/
	@Excel(name = "填表人", width = 15)
    @ApiModelProperty(value = "填表人")
    private java.lang.String formUser;
	/**报告时间*/
	@Excel(name = "报告时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报告时间")
    private java.util.Date reportTime;
	/**填表人电话*/
	@Excel(name = "填表人电话", width = 15)
    @ApiModelProperty(value = "填表人电话")
    private java.lang.String formPhone;
	/**报告单位名称*/
	@Excel(name = "报告单位名称", width = 15)
    @ApiModelProperty(value = "报告单位名称")
    private java.lang.String reportCom;
	/**报告人姓名*/
	@Excel(name = "报告人姓名", width = 15)
    @ApiModelProperty(value = "报告人姓名")
    private java.lang.String reporter;
	/**填表日期*/
	@Excel(name = "填表日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "填表日期")
    private java.util.Date formTime;
	/**报告人电话*/
	@Excel(name = "报告人电话", width = 15)
    @ApiModelProperty(value = "报告人电话")
    private java.lang.String reporterPhone;
	/**所在区全名*/
	@Excel(name = "所在区全名", width = 15)
    @ApiModelProperty(value = "所在区全名")
    private java.lang.String areaName;
	/**用工单位名称*/
	@Excel(name = "用工单位名称", width = 15)
    @ApiModelProperty(value = "用工单位名称")
    private java.lang.String workCom;
	/**用工经济类型*/
	@Excel(name = "用工经济类型", width = 15, dicCode = "economic_type")
	@Dict(dicCode = "economic_type")
    @ApiModelProperty(value = "用工经济类型")
    private java.lang.String workEcoType;
	/**用工信用代码*/
	@Excel(name = "用工信用代码", width = 15)
    @ApiModelProperty(value = "用工信用代码")
    private java.lang.String workCreditCard;
	/**用工企业规模*/
	@Excel(name = "用工企业规模", width = 15)
    @ApiModelProperty(value = "用工企业规模")
    private java.lang.String workEnSize;
	/**用工地区编码*/
	@Excel(name = "用工地区编码", width = 15)
    @ApiModelProperty(value = "用工地区编码")
    private java.lang.String workAreaCode;
	/**删除状态*/
	@Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    @TableLogic
    private java.lang.Integer delFlag;
	/**用工行业*/
	@Excel(name = "用工行业", width = 15, dictTable = "zy_industry", dicText = "name", dicCode = "id")
	@Dict(dictTable = "zy_industry", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "用工行业")
    private java.lang.String workIndustry;
	/**添加时间*/
	@Excel(name = "添加时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "添加时间")
    private java.util.Date createTime;

	/**省*/
	@Excel(name = "省", width = 15)
	@ApiModelProperty(value = "省")
	private String province;

	/**城市*/
	@Excel(name = "城市", width = 15)
	@ApiModelProperty(value = "城市")
	private String city;

	/**区域*/
	@Excel(name = "区域", width = 15)
	@ApiModelProperty(value = "区域")
	private String area;

	/**街道*/
	@Excel(name = "街道", width = 15)
	@ApiModelProperty(value = "街道")
	private String street;

	/**省代码*/
	@Excel(name = "省代码", width = 15)
	@ApiModelProperty(value = "省代码")
	private String provinceCode;

	/**城市代码*/
	@Excel(name = "城市代码", width = 15)
	@ApiModelProperty(value = "城市代码")
	private String cityCode;

	/**区域代码*/
	@Excel(name = "区域代码", width = 15)
	@ApiModelProperty(value = "区域代码")
	private String areaCode;

	/**街道、旗县代码*/
	// @Excel(name = "区域代码", width = 15)
	@ApiModelProperty(value = "街道、旗县代码")
	private String streetCode;

	/**详细地址*/
	@Excel(name = "详细地址", width = 15)
	@ApiModelProperty(value = "详细地址")
	private String addressDetail;
	/**员工人数*/
	@Excel(name = "员工人数", width = 15)
	@ApiModelProperty(value = "员工人数")
	private Integer employeeCount;
	/**接触危害因素员工人数*/
	@Excel(name = "接触危害因素员工人数", width = 15)
	@ApiModelProperty(value = "接触危害因素员工人数")
	private Integer riskEmployeeCount;
}
