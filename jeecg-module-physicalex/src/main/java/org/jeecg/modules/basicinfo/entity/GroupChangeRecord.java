package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 项目价格变动操作记录表
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Data
@TableName("group_change_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="group_change_record对象", description="项目价格变动操作记录表")
public class GroupChangeRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**大项id*/
	@Excel(name = "大项id", width = 15)
    @ApiModelProperty(value = "大项id")
    private String groupId;
	/**大项名称*/
	@Excel(name = "大项名称", width = 15)
    @ApiModelProperty(value = "大项名称")
    private String groupName;
	/**旧价格*/
	@Excel(name = "旧价格", width = 15)
    @ApiModelProperty(value = "旧价格")
    private BigDecimal oldPrice;
	/**新价格*/
	@Excel(name = "新价格", width = 15)
    @ApiModelProperty(value = "新价格")
    private BigDecimal newPrice;
	/**变动原因*/
	@Excel(name = "变动原因", width = 15)
    @ApiModelProperty(value = "变动原因")
    private String changeReason;
	/**操作人*/
	@Excel(name = "操作人", width = 15)
    @ApiModelProperty(value = "操作人工号")
    private String operator;
    /**创建人*/
    @ApiModelProperty(value = "操作人名字")
    private String operatorName;
	/**操作时间*/
	@Excel(name = "操作时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作时间")
    private Date operateTime;
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date operateTimeStart;
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date operateTimeEnd;
}
