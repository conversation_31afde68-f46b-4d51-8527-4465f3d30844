package org.jeecg.modules.basicinfo.mapper;

import java.util.List;
import org.jeecg.modules.basicinfo.entity.ReportSettingItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 报告设置条目
 * @Author: jeecg-boot
 * @Date:   2024-07-25
 * @Version: V1.0
 */
public interface ReportSettingItemMapper extends BaseMapper<ReportSettingItem> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<ReportSettingItem>
   */
	public List<ReportSettingItem> selectByMainId(@Param("mainId") String mainId);
}
