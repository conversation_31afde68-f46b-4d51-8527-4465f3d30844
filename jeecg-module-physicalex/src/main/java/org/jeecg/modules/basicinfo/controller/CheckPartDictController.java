package org.jeecg.modules.basicinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.CheckPartDict;
import org.jeecg.modules.basicinfo.service.ICheckPartDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 检查部位字典
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Api(tags = "检查部位字典")
@RestController
@RequestMapping("/basicinfo/checkPartDict")
@Slf4j
public class CheckPartDictController extends JeecgController<CheckPartDict, ICheckPartDictService> {

    @Autowired
    private ICheckPartDictService checkPartDictService;

    /**
     * 分页列表查询
     *
     * @param checkPartDict
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "检查部位字典-分页列表查询")
    @ApiOperation(value = "检查部位字典-分页列表查询", notes = "检查部位字典-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CheckPartDict>> queryPageList(CheckPartDict checkPartDict,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        QueryWrapper<CheckPartDict> queryWrapper = QueryGenerator.initQueryWrapper(checkPartDict, req.getParameterMap());
        Page<CheckPartDict> page = new Page<CheckPartDict>(pageNo, pageSize);
        IPage<CheckPartDict> pageList = checkPartDictService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param checkPartDict
     * @return
     */
    @AutoLog(value = "检查部位字典-添加")
    @ApiOperation(value = "检查部位字典-添加", notes = "检查部位字典-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CheckPartDict checkPartDict) {
        checkPartDictService.save(checkPartDict);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param checkPartDict
     * @return
     */
    @AutoLog(value = "检查部位字典-编辑")
    @ApiOperation(value = "检查部位字典-编辑", notes = "检查部位字典-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CheckPartDict checkPartDict) {
        checkPartDictService.updateById(checkPartDict);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "检查部位字典-通过id删除")
    @ApiOperation(value = "检查部位字典-通过id删除", notes = "检查部位字典-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        checkPartDictService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "检查部位字典-批量删除")
    @ApiOperation(value = "检查部位字典-批量删除", notes = "检查部位字典-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.checkPartDictService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "检查部位字典-通过id查询")
    @ApiOperation(value = "检查部位字典-通过id查询", notes = "检查部位字典-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CheckPartDict> queryById(@RequestParam(name = "id", required = true) String id) {
        CheckPartDict checkPartDict = checkPartDictService.getById(id);
        if (checkPartDict == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(checkPartDict);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param checkPartDict
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CheckPartDict checkPartDict) {
        return super.exportXls(request, checkPartDict, CheckPartDict.class, "检查部位字典");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CheckPartDict.class);
    }

    /**
     * 根据项目ID获取部位选项（带使用频次排序）
     *
     * @param itemGroupId 项目组合ID
     * @param keyword     搜索关键字
     * @return
     */
    @AutoLog(value = "检查部位字典-根据项目获取部位选项")
    @ApiOperation(value = "根据项目获取部位选项", notes = "根据项目获取部位选项，按使用频次排序")
    @GetMapping(value = "/listByItemGroup")
    public Result<List<CheckPartDict>> listByItemGroup(@RequestParam(name = "itemGroupId", required = true) String itemGroupId,
                                                       @RequestParam(name = "keyword", required = false) String keyword) {
        List<CheckPartDict> list = checkPartDictService.listByItemGroupWithFrequency(itemGroupId, keyword);
        return Result.OK(list);
    }

    /**
     * 通用部位搜索接口
     *
     * @param keyword 搜索关键字
     * @return
     */
    @AutoLog(value = "检查部位字典-通用搜索")
    @ApiOperation(value = "通用部位搜索", notes = "支持部位名称、拼音缩写、编码搜索")
    @GetMapping(value = "/searchByKeyword")
    public Result<List<CheckPartDict>> searchByKeyword(@RequestParam(name = "keyword", required = false) String keyword) {
        List<CheckPartDict> list = checkPartDictService.searchByKeyword(keyword);
        return Result.OK(list);
    }
}
