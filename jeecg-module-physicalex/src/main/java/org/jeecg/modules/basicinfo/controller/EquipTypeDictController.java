package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.EquipTypeDict;
import org.jeecg.modules.basicinfo.service.IEquipTypeDictService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: equip_type_dict
 * @Author: jeecg-boot
 * @Date:   2025-05-21
 * @Version: V1.0
 */
@Api(tags="equip_type_dict")
@RestController
@RequestMapping("/basicinfo/equipTypeDict")
@Slf4j
public class EquipTypeDictController extends JeecgController<EquipTypeDict, IEquipTypeDictService> {
	@Autowired
	private IEquipTypeDictService equipTypeDictService;
	
	/**
	 * 分页列表查询
	 *
	 * @param equipTypeDict
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "equip_type_dict-分页列表查询")
	@ApiOperation(value="equip_type_dict-分页列表查询", notes="equip_type_dict-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<EquipTypeDict>> queryPageList(EquipTypeDict equipTypeDict,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<EquipTypeDict> queryWrapper = QueryGenerator.initQueryWrapper(equipTypeDict, req.getParameterMap());
		Page<EquipTypeDict> page = new Page<EquipTypeDict>(pageNo, pageSize);
		IPage<EquipTypeDict> pageList = equipTypeDictService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param equipTypeDict
	 * @return
	 */
	@AutoLog(value = "equip_type_dict-添加")
	@ApiOperation(value="equip_type_dict-添加", notes="equip_type_dict-添加")
	@RequiresPermissions("basicinfo:equip_type_dict:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EquipTypeDict equipTypeDict) {
		equipTypeDictService.save(equipTypeDict);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param equipTypeDict
	 * @return
	 */
	@AutoLog(value = "equip_type_dict-编辑")
	@ApiOperation(value="equip_type_dict-编辑", notes="equip_type_dict-编辑")
	@RequiresPermissions("basicinfo:equip_type_dict:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody EquipTypeDict equipTypeDict) {
		equipTypeDictService.updateById(equipTypeDict);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "equip_type_dict-通过id删除")
	@ApiOperation(value="equip_type_dict-通过id删除", notes="equip_type_dict-通过id删除")
	@RequiresPermissions("basicinfo:equip_type_dict:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		equipTypeDictService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "equip_type_dict-批量删除")
	@ApiOperation(value="equip_type_dict-批量删除", notes="equip_type_dict-批量删除")
	@RequiresPermissions("basicinfo:equip_type_dict:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.equipTypeDictService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "equip_type_dict-通过id查询")
	@ApiOperation(value="equip_type_dict-通过id查询", notes="equip_type_dict-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<EquipTypeDict> queryById(@RequestParam(name="id",required=true) String id) {
		EquipTypeDict equipTypeDict = equipTypeDictService.getById(id);
		if(equipTypeDict==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(equipTypeDict);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param equipTypeDict
    */
    @RequiresPermissions("basicinfo:equip_type_dict:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EquipTypeDict equipTypeDict) {
        return super.exportXls(request, equipTypeDict, EquipTypeDict.class, "equip_type_dict");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:equip_type_dict:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EquipTypeDict.class);
    }

}
