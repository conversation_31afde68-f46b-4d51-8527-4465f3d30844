package org.jeecg.modules.basicinfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 体检项目
 * @Author: jeecg-boot
 * @Date: 2024-01-31
 * @Version: V1.0
 */
@ApiModel(value = "item_info对象", description = "体检项目")
@Data
@TableName("item_info")
public class ItemInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 科室
     */
    @Excel(name = "科室", width = 15)
    @ApiModelProperty(value = "科室")
    private java.lang.String departmentId;

    @Excel(name = "科室名称", width = 15)
    @ApiModelProperty(value = "科室名称")
    private String departmentName;

    @Excel(name = "科室代码", width = 15)
    @ApiModelProperty(value = "科室代码")
    private String departmentCode;
    /**
     *
     *
     * 排序号
     */
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.Integer sort;
    /**
     * 项目名称
     */
    @Excel(name = "项目名称", width = 15)
    @ApiModelProperty(value = "项目名称")
    private java.lang.String name;
    /**
     * 项目助记码
     */
    @Excel(name = "项目助记码", width = 15)
    @ApiModelProperty(value = "项目助记码")
    private java.lang.String helpChar;
    /**
     * HIS代码
     */
    @Excel(name = "HIS代码", width = 15)
    @ApiModelProperty(value = "HIS代码")
    private java.lang.String hisCode;
    /**
     * HIS名称
     */
    @Excel(name = "HIS名称", width = 15)
    @ApiModelProperty(value = "HIS名称")
    private java.lang.String hisName;
    /**公卫名称*/
    @Excel(name = "公卫名称", width = 15)
    @ApiModelProperty(value = "公卫名称")
    private java.lang.String platName;
    /**公卫代码*/
    @Excel(name = "公卫代码", width = 15)
    @ApiModelProperty(value = "公卫代码")
    private java.lang.String platCode;
    /**
     * 五笔码
     */
    @Excel(name = "五笔码", width = 15)
    @ApiModelProperty(value = "五笔码")
    private java.lang.String wubiChar;
    /**
     * 性别
     */
    @Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private java.lang.String sex;
    /**
     * 备孕适用
     */
    @Excel(name = "备孕适用", width = 15)
    @ApiModelProperty(value = "备孕适用")
    private java.lang.String pregnancyFlag;
    /**
     * 最小年龄
     */
    @Excel(name = "最小年龄", width = 15)
    @ApiModelProperty(value = "最小年龄")
    private java.lang.Integer minAge;
    /**
     * 最大年龄
     */
    @Excel(name = "最大年龄", width = 15)
    @ApiModelProperty(value = "最大年龄")
    private java.lang.Integer maxAge;
    /**
     * 项目类型
     */
    @Excel(name = "项目类型", width = 15, dicCode = "item_type")
    @Dict(dicCode = "item_type")
    @ApiModelProperty(value = "项目类型")
    private java.lang.String itemType;
    /**
     * 临床类别
     */
    @Excel(name = "临床类别", width = 15, dicCode = "clinical_type")
    @Dict(dicCode = "clinical_type")
    @ApiModelProperty(value = "临床类别")
    private java.lang.String clinicalType;
    /**
     * 单位
     */
    @Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String unit;
    /**
     * 小结格式
     */
    @Excel(name = "小结格式", width = 15)
    @ApiModelProperty(value = "小结格式")
    private java.lang.String summaryFormat;
    /**
     * 小数点位数
     */
    @Excel(name = "小数点位数", width = 15)
    @ApiModelProperty(value = "小数点位数")
    private java.lang.Integer decimalPlaces;
    /**
     * 互斥项目
     */
    @Excel(name = "互斥项目", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @ApiModelProperty(value = "互斥项目")
    private java.lang.String mutuallyExclusiveItem;
    /**
     * 注意事项
     */
    @Excel(name = "注意事项", width = 15)
    @ApiModelProperty(value = "注意事项")
    private java.lang.String notice;
    /**
     * 项目说明
     */
    @Excel(name = "项目说明", width = 15)
    @ApiModelProperty(value = "项目说明")
    private java.lang.String remark;
    /**
     * 计算公式
     */
    @Excel(name = "计算公式", width = 15)
    @ApiModelProperty(value = "计算公式")
    private java.lang.String formula;
    /**
     * 限制词条
     */
    @Excel(name = "限制词条", width = 15)
    @ApiModelProperty(value = "限制词条")
    private java.lang.String limitWord;
    /**
     * 启用
     */
    @Excel(name = "启用", width = 15)
    @ApiModelProperty(value = "启用")
    private java.lang.Integer enableFlag;
    /**
     * 仅收费项目
     */
    @Excel(name = "仅收费项目", width = 15)
    @ApiModelProperty(value = "仅收费项目")
    private java.lang.Integer chargeOnlyFlag;
    /**
     * 报告打印
     */
    @Excel(name = "报告打印", width = 15)
    @ApiModelProperty(value = "报告打印")
    private java.lang.Integer printReportFlag;
    /**
     * 复合诊断
     */
    @Excel(name = "复合诊断", width = 15)
    @ApiModelProperty(value = "复合诊断")
    private java.lang.Integer complexDiagFlag;
    /**
     * 图片项目
     */
    @Excel(name = "图片项目", width = 15)
    @ApiModelProperty(value = "图片项目")
    private java.lang.Integer picItemFlag;
    /**
     * 是否必填
     */
    @Excel(name = "是否必填", width = 15)
    @ApiModelProperty(value = "是否必填")
    private java.lang.Integer requiredFlag;
    /**
     * 是否进入小结
     */
    @Excel(name = "是否进入小结", width = 15)
    @ApiModelProperty(value = "是否进入小结")
    private java.lang.Integer sumableFlag;
    /**
     * 正常值进入小结
     */
    @Excel(name = "正常值进入小结", width = 15)
    @ApiModelProperty(value = "正常值进入小结")
    private java.lang.Integer sumableNormalvalFlag;
    /**
     * 判断小结
     */
    @Excel(name = "判断小结", width = 15)
    @ApiModelProperty(value = "判断小结")
    private java.lang.Integer sumableBoolFlag;

    /**使用体检参考范围判断结果*/
    @Excel(name = "使用体检参考范围判断结果", width = 15)
    @ApiModelProperty(value = "使用体检参考范围判断结果")
    private Integer judgeFlag;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private java.lang.String createTy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;

    /**
     * 参考值
     */
    @Excel(name = "参考值", width = 15)
    @ApiModelProperty(value = "参考值")
    private String normalRef;

    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private CustomerRegItemResult itemResult;

    @TableField(exist = false)
    private List<ItemStandard> itemStandardList;

    @TableField(exist = false)
    private String normalStandard;

    @TableField(exist = false)
    private ItemStandard matchedStandard;

    @TableField(exist = false)
    private String checkStatus;

    @TableField(exist = false)
    private  String normalDefaultValue;

    @TableField(exist = false)
    private  String groupId;

    @TableField(exist = false)
    private Integer seq;

}
