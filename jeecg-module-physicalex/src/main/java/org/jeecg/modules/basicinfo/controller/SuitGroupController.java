package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.SuitGroup;
import org.jeecg.modules.basicinfo.service.ISuitGroupService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: itemsuit_item
 * @Author: jeecg-boot
 * @Date:   2024-02-03
 * @Version: V1.0
 */
@Api(tags="itemsuit_item")
@RestController
@RequestMapping("/basicinfo/suitGroup")
@Slf4j
public class SuitGroupController extends JeecgController<SuitGroup, ISuitGroupService> {
	@Autowired
	private ISuitGroupService suitGroupService;

	/**
	 * 分页列表查询
	 *
	 * @param itemsuitItem
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "itemsuit_item-分页列表查询")
	@ApiOperation(value="itemsuit_item-分页列表查询", notes="itemsuit_item-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SuitGroup>> queryPageList(SuitGroup itemsuitItem,
												  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												  HttpServletRequest req) {
		QueryWrapper<SuitGroup> queryWrapper = QueryGenerator.initQueryWrapper(itemsuitItem, req.getParameterMap());
		Page<SuitGroup> page = new Page<SuitGroup>(pageNo, pageSize);
		IPage<SuitGroup> pageList = suitGroupService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param itemsuitItem
	 * @return
	 */
	@AutoLog(value = "itemsuit_item-添加")
	@ApiOperation(value="itemsuit_item-添加", notes="itemsuit_item-添加")
	@RequiresPermissions("basicinfo:itemsuit_item:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SuitGroup itemsuitItem) {
		suitGroupService.save(itemsuitItem);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param itemsuitItem
	 * @return
	 */
	@AutoLog(value = "itemsuit_item-编辑")
	@ApiOperation(value="itemsuit_item-编辑", notes="itemsuit_item-编辑")
	@RequiresPermissions("basicinfo:itemsuit_item:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SuitGroup itemsuitItem) {
		suitGroupService.updateById(itemsuitItem);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "itemsuit_item-通过id删除")
	@ApiOperation(value="itemsuit_item-通过id删除", notes="itemsuit_item-通过id删除")
	@RequiresPermissions("basicinfo:itemsuit_item:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		suitGroupService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "itemsuit_item-批量删除")
	@ApiOperation(value="itemsuit_item-批量删除", notes="itemsuit_item-批量删除")
	@RequiresPermissions("basicinfo:itemsuit_item:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.suitGroupService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "itemsuit_item-通过id查询")
	@ApiOperation(value="itemsuit_item-通过id查询", notes="itemsuit_item-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SuitGroup> queryById(@RequestParam(name="id",required=true) String id) {
		SuitGroup itemsuitItem = suitGroupService.getById(id);
		if(itemsuitItem==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(itemsuitItem);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param itemsuitItem
    */
    @RequiresPermissions("basicinfo:itemsuit_item:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SuitGroup itemsuitItem) {
        return super.exportXls(request, itemsuitItem, SuitGroup.class, "itemsuit_item");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:itemsuit_item:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SuitGroup.class);
    }

}
