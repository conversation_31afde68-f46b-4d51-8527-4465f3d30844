package org.jeecg.modules.basicinfo.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.Document;
import org.jeecg.modules.basicinfo.service.IDocumentService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 文档表
 * @Author: jeecg-boot
 * @Date: 2025-03-05
 * @Version: V1.0
 */
@Api(tags = "文档表")
@RestController
@RequestMapping("/basicinfo/document")
@Slf4j
public class DocumentController extends JeecgController<Document, IDocumentService> {
    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ISysSettingService sysSettingService;

    //listAll
    @ApiOperation(value = "文档表-查询所有", notes = "文档表-查询所有")
    @GetMapping(value = "/listAll")
    public Result<?> listAll(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<Document> page = new Page<>(pageNo, pageSize);
        documentService.listAll(page);
        return Result.OK(page);
    }

    /**
     * 分页列表查询
     *
     * @param document
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "文档表-分页列表查询")
    @ApiOperation(value = "文档表-分页列表查询", notes = "文档表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Document>> queryPageList(Document document, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<Document> queryWrapper = new QueryWrapper<>();
        String title = req.getParameter("title");
        String subTitle = req.getParameter("subTitle");
        String type = req.getParameter("type");
        String enableFlag = req.getParameter("enableFlag");
        if (StringUtils.isNotBlank(title)) {
            queryWrapper.like("title", title);
        }
        if (StringUtils.isNotBlank(subTitle)) {
            queryWrapper.like("sub_title", subTitle);
        }
        if (StringUtils.isNotBlank(type)) {
            queryWrapper.like("type", type);
        }
        if (StringUtils.isNotBlank(enableFlag)) {
            queryWrapper.like("enable_flag", enableFlag);
        }
        String uploadTimeStart = req.getParameter("uploadTimeStart");
        String uploadTimeEnd = req.getParameter("uploadTimeEnd");
        if (StringUtils.isNotBlank(uploadTimeStart)) {
            queryWrapper.between("upload_time", uploadTimeStart, uploadTimeEnd);
        }
        Page<Document> page = new Page<Document>(pageNo, pageSize);
        IPage<Document> pageList = documentService.page(page, queryWrapper);

        return Result.OK(pageList);
    }

    @ApiOperation(value = "微信端-文档查询", notes = "微信端-文档查询")
    @GetMapping(value = "/list4Introduce")
    public Result<List<Document>> list4Introduce(Document document, HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        QueryWrapper<Document> queryWrapper = QueryGenerator.initQueryWrapper(document, req.getParameterMap(), customeRuleMap);
        queryWrapper.eq("enable_flag", 1);
        queryWrapper.orderByAsc("sort");
        List<Document> documents = documentService.list(queryWrapper);
        String openFileUrl = sysSettingService.getValueByCode("open_file_url");
        if (CollectionUtils.isNotEmpty(documents)) {
            documents.forEach(record -> {
                if (StringUtils.containsIgnoreCase(record.getContent(), "<img src=\"http://")) {
                    String newContent = StringUtils.replaceAll(record.getContent(), "http://[^/]+(?=/images/)", openFileUrl);
                    record.setContent(newContent);
                }
            });
        }
        return Result.OK(documents);
    }

    /**
     * 添加
     *
     * @param document
     * @return
     */
    @AutoLog(value = "文档表-添加")
    @ApiOperation(value = "文档表-添加", notes = "文档表-添加")
    @RequiresPermissions("basicinfo:document:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody Document document) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (Objects.nonNull(sysUser)) {
            document.setCreateBy(sysUser.getUsername());
        }
        document.setUploadTime(new Date());
        documentService.saveDocument(document);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param document
     * @return
     */
    @AutoLog(value = "文档表-编辑")
    @ApiOperation(value = "文档表-编辑", notes = "文档表-编辑")
    @RequiresPermissions("basicinfo:document:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody Document document) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (Objects.nonNull(sysUser)) {
            document.setUpdateBy(sysUser.getUsername());
        }
        document.setUpdateTime(new Date());
        documentService.saveDocument(document);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "文档表-通过id删除")
    @ApiOperation(value = "文档表-通过id删除", notes = "文档表-通过id删除")
    @RequiresPermissions("basicinfo:document:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        documentService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "文档表-批量删除")
    @ApiOperation(value = "文档表-批量删除", notes = "文档表-批量删除")
    @RequiresPermissions("basicinfo:document:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.documentService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "文档表-通过id查询")
    @ApiOperation(value = "文档表-通过id查询", notes = "文档表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Document> queryById(@RequestParam(name = "id", required = true) String id) {
        Document document = documentService.getById(id);
        if (document == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(document);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param document
     */
    @RequiresPermissions("basicinfo:document:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Document document) {
        return super.exportXls(request, document, Document.class, "文档表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:document:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Document.class);
    }

}
