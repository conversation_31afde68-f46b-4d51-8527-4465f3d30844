package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 项目组合
 * @Author: jeecg-boot
 * @Date:   2024-01-31
 * @Version: V1.0
 */
@Data
@TableName("item_group")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="item_group对象", description="项目组合")
public class ItemGroup implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**排序号*/
	@Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.Integer sort;
	/**组合名称*/
	@Excel(name = "组合名称", width = 15)
    @ApiModelProperty(value = "组合名称")
    private java.lang.String name;
	/**助记码*/
	@Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private java.lang.String helpChar;
	/**HIS名称*/
	@Excel(name = "HIS名称", width = 15)
    @ApiModelProperty(value = "HIS名称")
    private java.lang.String hisName;
	/**HIS代码*/
	@Excel(name = "HIS代码", width = 15)
    @ApiModelProperty(value = "HIS代码")
    private java.lang.String hisCode;

    /**公卫名称*/
    @Excel(name = "公卫名称", width = 15)
    @ApiModelProperty(value = "公卫名称")
    private java.lang.String platName;
    /**公卫代码*/
    @Excel(name = "公卫代码", width = 15)
    @ApiModelProperty(value = "公卫代码")
    private java.lang.String platCode;

	/**性别限制*/
	@Excel(name = "性别限制", width = 15, dicCode = "sexLimit")
	@Dict(dicCode = "sexLimit")
    @ApiModelProperty(value = "性别限制")
    private java.lang.String sexLimit;
	/**最小年龄*/
	@Excel(name = "最小年龄", width = 15)
    @ApiModelProperty(value = "最小年龄")
    private java.lang.Integer minAge;
	/**最大年龄*/
	@Excel(name = "最大年龄", width = 15)
    @ApiModelProperty(value = "最大年龄")
    private java.lang.Integer maxAge;
    /**最大折扣*/
    @Excel(name = "最大折扣", width = 15)
    @ApiModelProperty(value = "最大折扣")
    private BigDecimal minDiscountRate;
	/**单价*/
	@Excel(name = "单价", width = 15)
    @ApiModelProperty(value = "单价")
    private java.math.BigDecimal price;
	/**成本价*/
	@Excel(name = "成本价", width = 15)
    @ApiModelProperty(value = "成本价")
    private java.math.BigDecimal costPrice;
	/**科室ID*/
    @ApiModelProperty(value = "科室")
    private java.lang.String departmentId;

    /**科室ID*/
    @Excel(name = "科室", width = 15)
    @ApiModelProperty(value = "科室")
    private java.lang.String departmentName;

    /**科室ID*/
    @Excel(name = "科室代码", width = 15)
    @ApiModelProperty(value = "科室代码")
    private java.lang.String departmentCode;

    /**科室*/
    @TableField(exist = false)
    @ApiModelProperty(value = "科室")
    private java.lang.String department;
	/**收费类别*/
	@Excel(name = "收费类别", width = 15, dicCode = "fee_type")
	@Dict(dicCode = "fee_type")
    @ApiModelProperty(value = "收费类别")
    private java.lang.String feeType;
	/**最大体检量*/
	@Excel(name = "最大体检量", width = 15)
    @ApiModelProperty(value = "最大体检量")
    private java.lang.Integer capacityPerDay;
	/**组合小结格式*/
	@Excel(name = "组合小结格式", width = 15, dicCode = "sumFormat")
	@Dict(dicCode = "sumFormat")
    @ApiModelProperty(value = "组合小结格式")
    private java.lang.String combineSummaryFormat;
	/**异常汇总格式*/
	@Excel(name = "异常汇总格式", width = 15)
    @ApiModelProperty(value = "异常汇总格式")
    private java.lang.String normalSummaryFormat;

	/**是否申请单组合*/
	@Excel(name = "是否申请单组合", width = 15)
    @ApiModelProperty(value = "是否申请单组合")
    private java.lang.Integer groupApplyFlag;
	/**仅收费项目*/
	@Excel(name = "仅收费项目", width = 15)
    @ApiModelProperty(value = "仅收费项目")
    private java.lang.Integer chargeItemOnlyFlag;
	/**是否系统项目组合*/
	@Excel(name = "是否系统项目组合", width = 15)
    @ApiModelProperty(value = "是否系统项目组合")
    private java.lang.Integer sysItemFlag;
    /**异常小项值是否显示在小结内*/
    @ApiModelProperty(value = "异常小项值是否显示在小结内")
    private Integer itemSumableFlag;
    /**更新人*/
    @ApiModelProperty(value = "正常小项值是否显示在小结内")
    private Integer itemNormalvalSumableFlag;
    /**隐私项目*/
	@Excel(name = "隐私项目", width = 15)
    @ApiModelProperty(value = "隐私项目")
    private java.lang.Integer privacyFlag;
	/**抽血*/
	@Excel(name = "抽血", width = 15)
    @ApiModelProperty(value = "抽血")
    private java.lang.Integer drawBloodFlag;
	/**空腹*/
	@Excel(name = "空腹", width = 15)
    @ApiModelProperty(value = "空腹")
    private java.lang.Integer fastsFlag;
	/**妇检*/
	@Excel(name = "妇检", width = 15)
    @ApiModelProperty(value = "妇检")
    private java.lang.Integer gynecologicalFlag;
	/**备孕项目*/
	@Excel(name = "备孕项目", width = 15)
    @ApiModelProperty(value = "备孕项目")
    private java.lang.Integer pregnancyFlag;
	/**早餐*/
	@Excel(name = "早餐", width = 15)
    @ApiModelProperty(value = "早餐")
    private java.lang.Integer breakfastFlag;
	/**VIP项目*/
	@Excel(name = "VIP项目", width = 15)
    @ApiModelProperty(value = "VIP项目")
    private java.lang.Integer vipFlag;
	/**外送*/
	@Excel(name = "外送", width = 15)
    @ApiModelProperty(value = "外送")
    private java.lang.Integer outwardDeliveryFlag;
	/**启用标志*/
	@Excel(name = "启用标志", width = 15)
    @ApiModelProperty(value = "启用标志")
    private java.lang.Integer enableFlag;
	/**询问自愿*/
	@Excel(name = "询问自愿", width = 15)
    @ApiModelProperty(value = "询问自愿")
    private java.lang.Integer inquireVoluntarilyFlag;
	/**统计数量*/
	@Excel(name = "统计数量", width = 15)
    @ApiModelProperty(value = "统计数量")
    private java.lang.Integer statCount;
	/**注意事项*/
	@Excel(name = "注意事项", width = 15)
    @ApiModelProperty(value = "注意事项")
    private java.lang.String notice;
	/**组合介绍*/
	@Excel(name = "组合介绍", width = 15)
    @ApiModelProperty(value = "组合介绍")
    private java.lang.String intro;
	/**备注说明*/
	@Excel(name = "备注说明", width = 15)
    @ApiModelProperty(value = "备注说明")
    private java.lang.String remark;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    @TableLogic
    private Integer delFlag;
    /**检查或检验*/
    @ApiModelProperty(value = "检查或检验")
    private String classCode;

    /**是否有检查部位*/
    @Excel(name = "是否有检查部位", width = 15)
    @ApiModelProperty(value = "是否有检查部位(1-是,0-否)")
    private String hasCheckPart;

    /**使用体检参考范围判断结果*/
    @ApiModelProperty(value = "使用体检参考范围判断结果")
    private Integer judgeFlag;
    /**使用体检关联关系调整小项归属*/
    @ApiModelProperty(value = "使用体检关联关系调整小项归属")
    private Integer relationFlag;
    @ApiModelProperty(value = "是否同步his信息")
    private Integer syncFlag;
    @TableField(exist = false)
    @ApiModelProperty(value = "检查部位ID")
    private String checkPartId;
    @ApiModelProperty(value = "检查部位编码")
    @TableField(exist = false)
    private String checkPartCode;
    @ApiModelProperty(value = "检查部位名称")
    @TableField(exist = false)
    private String checkPartName;
    @ApiModelProperty(value = "设备类型编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String equipTypeCode;
    @ApiModelProperty(value = "设备类型名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String equipTypeName;
    @ApiModelProperty(value = "设备类型编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String checkMethodCode;
    @ApiModelProperty(value = "设备类型名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String checkMethodName;



    @TableField(exist = false)
    @ApiModelProperty(value = "折后价格")
    private java.math.BigDecimal priceAfterDisOfSuit;
    /**因折扣产生的差额*/
    @TableField(exist = false)
    @ApiModelProperty(value = "因折扣产生的差额")
    private BigDecimal priceDisDiffAmountOfSuit;
    @TableField(exist = false)
    @ApiModelProperty(value = "组合的最小折扣率")
    private BigDecimal minDiscountRateOfSuit;
    @TableField(exist = false)
    @ApiModelProperty(value = "组合的最小折扣率")
    private BigDecimal disRateOfSuit;

    /**样本类别*/
    @Excel(name = "样本类别", width = 15)
    @ApiModelProperty(value = "样本类别")
    private java.lang.String clinicalType;
    @ApiModelProperty(value = "样本代码")
    private String clinicalTypeCode;

    private String unit;

    private String reportPicAttachable;

    private Integer summaryFlag;

    private Integer pushCheckFlag;
    @ApiModelProperty(value = "检查部位名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String checkSiteCode;
    @ApiModelProperty(value = "检查部位名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String checkSiteName;

    @TableField(exist = false)
    private List<ItemInfo> itemList;
    @TableField(exist = false)
    private Boolean isRecommend;
    @TableField(exist = false)
    private String itemNames;
    @TableField(exist = false)
    private Map<String,Object> isRecommendObj;
    @TableField(exist = false)
    private String fastsFlagStr;
}
