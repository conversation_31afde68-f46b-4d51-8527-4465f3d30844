package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.OrgArticle;
import org.jeecg.modules.basicinfo.entity.OrgInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 机构信息表
 * @Author: jeecg-boot
 * @Date:   2024-10-08
 * @Version: V1.0
 */
public interface IOrgInfoService extends IService<OrgInfo> {

	OrgInfo getDefultOrgInfo();

	/**
	 * 添加一对多
	 *
	 * @param orgInfo
	 * @param orgArticleList
	 */
	public void saveMain(OrgInfo orgInfo,List<OrgArticle> orgArticleList) ;
	
	/**
	 * 修改一对多
	 *
   * @param orgInfo
   * @param orgArticleList
	 */
	public void updateMain(OrgInfo orgInfo,List<OrgArticle> orgArticleList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	OrgInfo queryByOrgCode(String orgCode);

}
