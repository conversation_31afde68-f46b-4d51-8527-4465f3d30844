package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 机构其他信息
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@ApiModel(value="org_article对象", description="机构其他信息")
@Data
@TableName("org_article")
public class OrgArticle implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**机构信息表id*/
    @ApiModelProperty(value = "机构信息表id")
    private java.lang.String orgId;
	/**机构logo地址*/
	@Excel(name = "机构logo地址", width = 15)
    @ApiModelProperty(value = "机构logo地址")
    private java.lang.String logoPicture;
	/**机构封面地址*/
	@Excel(name = "机构封面地址", width = 15)
    @ApiModelProperty(value = "机构封面地址")
    private java.lang.String coverPicture;
	/**机构介绍视频*/
	@Excel(name = "机构介绍视频", width = 15)
    @ApiModelProperty(value = "机构介绍视频")
    private java.lang.String introduceVideo;
	/**体检说明视频*/
	@Excel(name = "体检说明视频", width = 15)
    @ApiModelProperty(value = "体检说明视频")
    private java.lang.String requiremnetVideo;
	/**机构相关介绍*/
	@Excel(name = "机构相关介绍", width = 15)
    @ApiModelProperty(value = "机构相关介绍")
    private java.lang.String introduce;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;
}
