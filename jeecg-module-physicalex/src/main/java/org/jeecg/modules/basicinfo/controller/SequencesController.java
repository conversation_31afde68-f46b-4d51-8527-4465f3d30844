package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.Sequences;
import org.jeecg.modules.basicinfo.service.ISequencesService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: sequences
 * @Author: jeecg-boot
 * @Date:   2024-12-18
 * @Version: V1.0
 */
@Api(tags="sequences")
@RestController
@RequestMapping("/basicinfo/sequences")
@Slf4j
public class SequencesController extends JeecgController<Sequences, ISequencesService> {
	@Autowired
	private ISequencesService sequencesService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sequences
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "sequences-分页列表查询")
	@ApiOperation(value="sequences-分页列表查询", notes="sequences-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Sequences>> queryPageList(Sequences sequences,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Sequences> queryWrapper = QueryGenerator.initQueryWrapper(sequences, req.getParameterMap());
		Page<Sequences> page = new Page<Sequences>(pageNo, pageSize);
		IPage<Sequences> pageList = sequencesService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sequences
	 * @return
	 */
	@AutoLog(value = "sequences-添加")
	@ApiOperation(value="sequences-添加", notes="sequences-添加")
	@RequiresPermissions("basicinfo:sequences:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Sequences sequences) {
		sequencesService.save(sequences);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sequences
	 * @return
	 */
	@AutoLog(value = "sequences-编辑")
	@ApiOperation(value="sequences-编辑", notes="sequences-编辑")
	@RequiresPermissions("basicinfo:sequences:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Sequences sequences) {
		sequencesService.updateById(sequences);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "sequences-通过id删除")
	@ApiOperation(value="sequences-通过id删除", notes="sequences-通过id删除")
	@RequiresPermissions("basicinfo:sequences:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sequencesService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "sequences-批量删除")
	@ApiOperation(value="sequences-批量删除", notes="sequences-批量删除")
	@RequiresPermissions("basicinfo:sequences:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sequencesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "sequences-通过id查询")
	@ApiOperation(value="sequences-通过id查询", notes="sequences-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Sequences> queryById(@RequestParam(name="id",required=true) String id) {
		Sequences sequences = sequencesService.getById(id);
		if(sequences==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sequences);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sequences
    */
    @RequiresPermissions("basicinfo:sequences:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Sequences sequences) {
        return super.exportXls(request, sequences, Sequences.class, "sequences");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:sequences:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Sequences.class);
    }

}
