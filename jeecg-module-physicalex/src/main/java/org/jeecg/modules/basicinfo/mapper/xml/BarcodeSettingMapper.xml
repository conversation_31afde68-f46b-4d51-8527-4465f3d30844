<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.BarcodeSettingMapper">

    <select id="getExamNoSetting" resultType="org.jeecg.modules.basicinfo.entity.BarcodeSetting">
        select bs.*
        from barcode_setting bs
        where enable_flag = '1'
          and bar_num_source = '体检号'
          and not exists (select 1 from barcode_setting_group where setting_id = bs.id)
        limit 1
    </select>
    <select id="pageList" resultType="org.jeecg.modules.basicinfo.entity.BarcodeSetting">
        select s.* from barcode_setting s where 1=1
        <if test="name!=null">and name like concat('%',${name},'%')</if>
        <if test="source!=null">and source =#{source}</if>
        <if test="groupName!=null">and exists(select id from barcode_setting_group g where g.setting_id = s.id
            and g.group_name like concat('%',#{groupName},'%'))
        </if>
    </select>

    <resultMap id="barcodeSettingResult" type="org.jeecg.modules.basicinfo.entity.BarcodeSetting">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="barNumSource" column="bar_num_source"/>
        <result property="barNumPrefix" column="bar_num_prefix"/>
        <result property="text" column="text"/>
        <result property="barPage" column="bar_page"/>
        <result property="sampleType" column="sample_type"/>
        <result property="testType" column="test_type"/>
        <result property="tubeColor" column="tube_color"/>
        <result property="sort" column="sort"/>
        <result property="templateId" column="template_id"/>
        <result property="useConsumable" column="use_consumable"/>
        <result property="printLocation" column="print_location"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="checkDepart" column="check_depart"/>
        <result property="specimenCategory" column="specimen_category"/>
        <result property="specimenCategoryCode" column="specimen_category_code"/>
        <result property="source" column="source"/>
        <result property="priority" column="priority"/>
    </resultMap>
</mapper>