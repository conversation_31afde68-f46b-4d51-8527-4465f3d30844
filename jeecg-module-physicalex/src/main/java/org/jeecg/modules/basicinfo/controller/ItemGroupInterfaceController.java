package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.ItemGroupInterface;
import org.jeecg.modules.basicinfo.service.IItemGroupInterfaceService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: item_group_interface
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
@Api(tags="item_group_interface")
@RestController
@RequestMapping("/basicinfo/itemGroupInterface")
@Slf4j
public class ItemGroupInterfaceController extends JeecgController<ItemGroupInterface, IItemGroupInterfaceService> {
	@Autowired
	private IItemGroupInterfaceService itemGroupInterfaceService;
	
	/**
	 * 分页列表查询
	 *
	 * @param itemGroupInterface
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "item_group_interface-分页列表查询")
	@ApiOperation(value="item_group_interface-分页列表查询", notes="item_group_interface-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ItemGroupInterface>> queryPageList(ItemGroupInterface itemGroupInterface,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ItemGroupInterface> queryWrapper = QueryGenerator.initQueryWrapper(itemGroupInterface, req.getParameterMap());
		Page<ItemGroupInterface> page = new Page<ItemGroupInterface>(pageNo, pageSize);
		IPage<ItemGroupInterface> pageList = itemGroupInterfaceService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param itemGroupInterface
	 * @return
	 */
	@AutoLog(value = "item_group_interface-添加")
	@ApiOperation(value="item_group_interface-添加", notes="item_group_interface-添加")
	@RequiresPermissions("basicinfo:item_group_interface:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ItemGroupInterface itemGroupInterface) {
		itemGroupInterfaceService.save(itemGroupInterface);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param itemGroupInterface
	 * @return
	 */
	@AutoLog(value = "item_group_interface-编辑")
	@ApiOperation(value="item_group_interface-编辑", notes="item_group_interface-编辑")
	@RequiresPermissions("basicinfo:item_group_interface:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ItemGroupInterface itemGroupInterface) {
		itemGroupInterfaceService.updateById(itemGroupInterface);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "item_group_interface-通过id删除")
	@ApiOperation(value="item_group_interface-通过id删除", notes="item_group_interface-通过id删除")
	@RequiresPermissions("basicinfo:item_group_interface:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		itemGroupInterfaceService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "item_group_interface-批量删除")
	@ApiOperation(value="item_group_interface-批量删除", notes="item_group_interface-批量删除")
	@RequiresPermissions("basicinfo:item_group_interface:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.itemGroupInterfaceService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "item_group_interface-通过id查询")
	@ApiOperation(value="item_group_interface-通过id查询", notes="item_group_interface-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ItemGroupInterface> queryById(@RequestParam(name="id",required=true) String id) {
		ItemGroupInterface itemGroupInterface = itemGroupInterfaceService.getById(id);
		if(itemGroupInterface==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(itemGroupInterface);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param itemGroupInterface
    */
    @RequiresPermissions("basicinfo:item_group_interface:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItemGroupInterface itemGroupInterface) {
        return super.exportXls(request, itemGroupInterface, ItemGroupInterface.class, "item_group_interface");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:item_group_interface:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ItemGroupInterface.class);
    }

}
