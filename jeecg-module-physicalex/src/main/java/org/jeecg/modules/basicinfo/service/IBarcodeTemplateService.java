package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.BarcodeTemplate;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 条码模板
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
public interface IBarcodeTemplateService extends IService<BarcodeTemplate> {

    String getUpdateTime(String id);

    String getIdByCategory(String category);

}
