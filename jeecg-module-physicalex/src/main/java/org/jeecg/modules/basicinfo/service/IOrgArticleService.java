package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.OrgArticle;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 机构其他信息
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
public interface IOrgArticleService extends IService<OrgArticle> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<OrgArticle>
	 */
	public List<OrgArticle> selectByMainId(String mainId);
}
