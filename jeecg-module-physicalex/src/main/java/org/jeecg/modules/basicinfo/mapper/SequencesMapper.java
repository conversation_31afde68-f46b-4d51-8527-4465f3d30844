package org.jeecg.modules.basicinfo.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.Sequences;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: sequences
 * @Author: jeecg-boot
 * @Date:   2024-12-18
 * @Version: V1.0
 */
public interface SequencesMapper extends BaseMapper<Sequences> {

    Sequences getByName(@Param("seqName") String seqName);
}
