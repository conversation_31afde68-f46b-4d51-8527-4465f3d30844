package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.basicinfo.entity.BarcodeSetting;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 条码配置表
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
public interface IBarcodeSettingService extends IService<BarcodeSetting> {
    void removeBatch(List<String> ids);

    BarcodeSetting getExamNoSetting();

    Page<BarcodeSetting> pageBarcodeSetting(Page<BarcodeSetting> page, String name, String groupName, String source);
}
