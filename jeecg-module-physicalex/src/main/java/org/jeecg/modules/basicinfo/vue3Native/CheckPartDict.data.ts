import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '检查部位编码',
    align: "center",
    dataIndex: 'code'
  },
  {
    title: '检查部位名称',
    align: "center",
    dataIndex: 'name'
  },
];

// 高级查询数据
export const superQuerySchema = {
  code: {title: '检查部位编码',order: 0,view: 'text', type: 'string',},
  name: {title: '检查部位名称',order: 1,view: 'text', type: 'string',},
};
