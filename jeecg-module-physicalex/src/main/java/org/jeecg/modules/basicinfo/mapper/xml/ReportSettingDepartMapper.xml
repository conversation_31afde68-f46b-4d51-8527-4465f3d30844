<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.ReportSettingDepartMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  report_setting_depart 
		WHERE
			 setting_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.basicinfo.entity.ReportSettingDepart">
		SELECT * 
		FROM  report_setting_depart
		WHERE
			 setting_id = #{mainId} 	</select>
</mapper>
