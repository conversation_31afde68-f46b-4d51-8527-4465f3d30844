<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.ItemDictMapper">

    <select id="listByItemId" resultType="org.jeecg.modules.basicinfo.entity.ItemDict">
        select * from item_dict where item_id=#{itemId} order by use_count desc,seq asc
    </select>
    <select id="getDefaultDict" resultType="org.jeecg.modules.basicinfo.entity.ItemDict">
        select * from item_dict where item_id=#{itemId} and default_flag='1' limit 1
    </select>
</mapper>