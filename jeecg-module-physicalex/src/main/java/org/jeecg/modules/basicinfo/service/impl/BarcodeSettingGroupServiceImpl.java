package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.basicinfo.entity.BarcodeSettingGroup;
import org.jeecg.modules.basicinfo.mapper.BarcodeSettingGroupMapper;
import org.jeecg.modules.basicinfo.service.IBarcodeSettingGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 条码设置与项目组合关联表
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Service
public class BarcodeSettingGroupServiceImpl extends ServiceImpl<BarcodeSettingGroupMapper, BarcodeSettingGroup> implements IBarcodeSettingGroupService {
   @Autowired
   private JdbcTemplate jdbcTemplate;
   @Autowired
   private BarcodeSettingGroupMapper barcodeSettingGroupMapper;

    @Override
    public void setShortName(String id, String shortName) {
        jdbcTemplate.update("update barcode_setting_group set short_name=? where id=?", shortName, id);
    }

    @Override
    public List<BarcodeSettingGroup> list4BarcodeSetting(String barcodeSettingId) {

        return barcodeSettingGroupMapper.listByBarcodeSetting(barcodeSettingId);
    }

    @Override
    public List<BarcodeSettingGroup> listItemBySettingIdAndRegId(String settingId, String customerRegId,String autoCharge) {

        return barcodeSettingGroupMapper.listItemBySettingIdAndRegId(settingId,customerRegId,autoCharge);
    }
}
