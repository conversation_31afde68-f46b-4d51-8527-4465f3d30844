package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.OrgArticle;
import org.jeecg.modules.basicinfo.mapper.OrgArticleMapper;
import org.jeecg.modules.basicinfo.service.IOrgArticleService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 机构其他信息
 * @Author: jeecg-boot
 * @Date:   2024-10-09
 * @Version: V1.0
 */
@Service
public class OrgArticleServiceImpl extends ServiceImpl<OrgArticleMapper, OrgArticle> implements IOrgArticleService {
	
	@Autowired
	private OrgArticleMapper orgArticleMapper;
	
	@Override
	public List<OrgArticle> selectByMainId(String mainId) {
		return orgArticleMapper.selectByMainId(mainId);
	}
}
