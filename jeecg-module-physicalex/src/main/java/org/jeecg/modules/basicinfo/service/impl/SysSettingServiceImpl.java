package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.SysSetting;
import org.jeecg.modules.basicinfo.mapper.SysSettingMapper;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 参数配置
 * @Author: jeecg-boot
 * @Date:   2024-05-25
 * @Version: V1.0
 */
@CacheConfig(cacheNames = "sysSetting")
@Service
public class SysSettingServiceImpl extends ServiceImpl<SysSettingMapper, SysSetting> implements ISysSettingService {

    @Autowired
    private SysSettingMapper sysSettingMapper;


    @Cacheable(cacheNames = "SysSetting", key = "'SysSetting:' + #code", unless = "#result == null")
    @Override
    public SysSetting getByCode(String code) {
        return sysSettingMapper.getByCode(code);
    }

    @Cacheable(cacheNames = "SysSettingValue", key = "'SysSettingValue:' + #code", unless = "#result == null")
    @Override
    public String getValueByCode(String code) {
        SysSetting sysSetting = getByCode(code);
        if (sysSetting != null) {
            return sysSetting.getValue();
        }
        return null;
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = "SysSetting", allEntries = true),
            @CacheEvict(cacheNames = "SysSettingValue", allEntries = true)
    })
    public void evictCache() {
        //通过代码删除缓存
    }
}
