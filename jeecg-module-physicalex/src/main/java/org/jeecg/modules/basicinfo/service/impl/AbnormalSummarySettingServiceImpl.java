package org.jeecg.modules.basicinfo.service.impl;

import org.jeecg.modules.basicinfo.entity.AbnormalSummarySetting;
import org.jeecg.modules.basicinfo.mapper.AbnormalSummarySettingMapper;
import org.jeecg.modules.basicinfo.service.IAbnormalSummarySettingService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 异常汇总设置
 * @Author: jeecg-boot
 * @Date:   2024-11-26
 * @Version: V1.0
 */
@Service
public class AbnormalSummarySettingServiceImpl extends ServiceImpl<AbnormalSummarySettingMapper, AbnormalSummarySetting> implements IAbnormalSummarySettingService {

}
