package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 异常汇总设置
 * @Author: jeecg-boot
 * @Date:   2024-11-26
 * @Version: V1.0
 */
@Data
@TableName("abnormal_summary_setting")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="abnormal_summary_setting对象", description="异常汇总设置")
public class AbnormalSummarySetting implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private java.lang.String title;
	/**排序号*/
	@Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.Integer seq;
	/**适配科室*/
	@Excel(name = "适配科室", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @ApiModelProperty(value = "适配科室")
    private java.lang.String departmentId;
	/**大项排序号范围*/
	@Excel(name = "大项排序号范围", width = 15)
    @ApiModelProperty(value = "大项排序号范围")
    private java.lang.String itemgroupSortRang;
	/**适配大项*/
	@Excel(name = "适配大项", width = 15, dictTable = "item_group where del_flag=0 and enable_flag=1", dicText = "name", dicCode = "id")
	@Dict(dictTable = "item_group where del_flag=0 and enable_flag=1", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "适配大项")
    private java.lang.String itemgroupIds;
	/**汇总内容类型*/
	@Excel(name = "汇总内容类型", width = 15, dicCode = "abnormal_summary_content_type")
	@Dict(dicCode = "abnormal_summary_content_type")
    @ApiModelProperty(value = "汇总内容类型")
    private java.lang.String summaryTextType;
	/**启用标志*/
    @Excel(name = "启用标志", width = 15,replace = {"是_1","否_0"} )
    @ApiModelProperty(value = "启用标志")
    private java.lang.String enableFlag;
    /**组内大项格式*/
    @Excel(name = "组内大项格式", width = 15)
    @ApiModelProperty(value = "组内大项格式")
    private String itemgroupFormate;
    /**组内汇总粒度*/
    @Excel(name = "组内汇总粒度", width = 15)
    @ApiModelProperty(value = "组内汇总粒度")
    private String itemOrGroup;
    /**异常值判断公式*/
    @Excel(name = "异常值判断公式", width = 15)
    @ApiModelProperty(value = "异常值判断公式")
    private String judgeExpression;
    /**格式*/
    @Excel(name = "格式", width = 15)
    @ApiModelProperty(value = "格式")
    private String format;
    /**诊断分隔符*/
    @Excel(name = "诊断分隔符", width = 15)
    @ApiModelProperty(value = "诊断分隔符")
    private String disgnoseSeparator;
}
