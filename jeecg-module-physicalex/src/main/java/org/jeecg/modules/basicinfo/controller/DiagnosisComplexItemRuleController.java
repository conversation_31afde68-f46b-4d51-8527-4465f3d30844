package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplexItemRule;
import org.jeecg.modules.basicinfo.service.IDiagnosisComplexItemRuleService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 符合判断规则
 * @Author: jeecg-boot
 * @Date:   2024-04-29
 * @Version: V1.0
 */
@Api(tags="符合判断规则")
@RestController
@RequestMapping("/basicinfo/diagnosisComplexItemRule")
@Slf4j
public class DiagnosisComplexItemRuleController extends JeecgController<DiagnosisComplexItemRule, IDiagnosisComplexItemRuleService> {
	@Autowired
	private IDiagnosisComplexItemRuleService diagnosisComplexItemRuleService;
	
	/**
	 * 分页列表查询
	 *
	 * @param diagnosisComplexItemRule
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "符合判断规则-分页列表查询")
	@ApiOperation(value="符合判断规则-分页列表查询", notes="符合判断规则-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<DiagnosisComplexItemRule>> queryPageList(DiagnosisComplexItemRule diagnosisComplexItemRule,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<DiagnosisComplexItemRule> queryWrapper = QueryGenerator.initQueryWrapper(diagnosisComplexItemRule, req.getParameterMap());
		Page<DiagnosisComplexItemRule> page = new Page<DiagnosisComplexItemRule>(pageNo, pageSize);
		IPage<DiagnosisComplexItemRule> pageList = diagnosisComplexItemRuleService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param diagnosisComplexItemRule
	 * @return
	 */
	@AutoLog(value = "符合判断规则-添加")
	@ApiOperation(value="符合判断规则-添加", notes="符合判断规则-添加")
	@RequiresPermissions("basicinfo:diagnosis_complex_item_rule:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody DiagnosisComplexItemRule diagnosisComplexItemRule) {
		diagnosisComplexItemRuleService.save(diagnosisComplexItemRule);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param diagnosisComplexItemRule
	 * @return
	 */
	@AutoLog(value = "符合判断规则-编辑")
	@ApiOperation(value="符合判断规则-编辑", notes="符合判断规则-编辑")
	@RequiresPermissions("basicinfo:diagnosis_complex_item_rule:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody DiagnosisComplexItemRule diagnosisComplexItemRule) {
		diagnosisComplexItemRuleService.updateById(diagnosisComplexItemRule);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "符合判断规则-通过id删除")
	@ApiOperation(value="符合判断规则-通过id删除", notes="符合判断规则-通过id删除")
	@RequiresPermissions("basicinfo:diagnosis_complex_item_rule:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		diagnosisComplexItemRuleService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "符合判断规则-批量删除")
	@ApiOperation(value="符合判断规则-批量删除", notes="符合判断规则-批量删除")
	@RequiresPermissions("basicinfo:diagnosis_complex_item_rule:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.diagnosisComplexItemRuleService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "符合判断规则-通过id查询")
	@ApiOperation(value="符合判断规则-通过id查询", notes="符合判断规则-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<DiagnosisComplexItemRule> queryById(@RequestParam(name="id",required=true) String id) {
		DiagnosisComplexItemRule diagnosisComplexItemRule = diagnosisComplexItemRuleService.getById(id);
		if(diagnosisComplexItemRule==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(diagnosisComplexItemRule);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param diagnosisComplexItemRule
    */
    @RequiresPermissions("basicinfo:diagnosis_complex_item_rule:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DiagnosisComplexItemRule diagnosisComplexItemRule) {
        return super.exportXls(request, diagnosisComplexItemRule, DiagnosisComplexItemRule.class, "符合判断规则");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("basicinfo:diagnosis_complex_item_rule:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DiagnosisComplexItemRule.class);
    }

}
