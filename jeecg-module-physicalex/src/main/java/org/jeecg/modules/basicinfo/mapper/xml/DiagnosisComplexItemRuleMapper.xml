<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.DiagnosisComplexItemRuleMapper">
    <delete id="deleteByMainId">
        DELETE FROM diagnosis_complex_item_rule WHERE complex_id = #{mainId}
    </delete>

    <select id="selectByMainId" resultType="org.jeecg.modules.basicinfo.entity.DiagnosisComplexItemRule">
        SELECT * FROM diagnosis_complex_item_rule WHERE complex_id = #{mainId} order by title asc
    </select>
    <select id="selectByDepartmentId" resultType="org.jeecg.modules.basicinfo.entity.DiagnosisComplexItemRule">
        SELECT * FROM diagnosis_complex_item_rule r join diagnosis_complex d on r.complex_id=d.id WHERE d.department_id = #{departmentId} and d.enable_flag = 1
        <if test="severityDegree!=null and severityDegree!=''"> and d.severity_degree=#{severityDegree} </if>
        <if test="age!=null"> and d.min_age &lt; #{age} and d.max_age &gt; #{age} </if>
        <if test="marriage!=null and marriage!=''"> and (d.marriage_limit='不限' or d.marriage_limit=#{marriage})</if>
        <if test="gender!=null and gender!=''"> and (d.gender_limit='不限' or d.gender_limit=#{gender})</if>
    </select>
</mapper>