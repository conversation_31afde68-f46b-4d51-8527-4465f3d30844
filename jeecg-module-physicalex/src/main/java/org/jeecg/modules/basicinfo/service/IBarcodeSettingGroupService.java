package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.BarcodeSettingGroup;

import java.util.List;

/**
 * @Description: 条码设置与项目组合关联表
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
public interface IBarcodeSettingGroupService extends IService<BarcodeSettingGroup> {

    void setShortName(String id, String shortName);

    List<BarcodeSettingGroup> list4BarcodeSetting(String barcodeSettingId);

    List<BarcodeSettingGroup> listItemBySettingIdAndRegId(String settingId, String customerRegId, String autoCharge);
}
