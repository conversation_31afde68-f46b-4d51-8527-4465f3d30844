package org.jeecg.modules.basicinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.ItemDict;
import org.jeecg.modules.basicinfo.service.IItemDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 项目结果字典
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 * @Version: V1.0
 */
@Api(tags = "项目结果字典")
@RestController
@RequestMapping("/basicinfo/itemDict")
@Slf4j
public class ItemDictController extends JeecgController<ItemDict, IItemDictService> {
    @Autowired
    private IItemDictService itemDictService;

    //getDefaultDict
    @ApiOperation(value = "项目结果字典-获取默认值", notes = "项目结果字典-获取默认值")
    @GetMapping(value = "/getDefaultDict")
    public Result<?> getDefaultDict(String itemId) {
        ItemDict defaultDict = itemDictService.getDefaultDict(itemId);
        return Result.OK(defaultDict);
    }


    @ApiOperation(value = "项目结果字典-重复检查", notes = "项目结果字典-重复检查")
    @GetMapping(value = "/increaseUseCount")
    public Result<?> increaseUseCount(String dictId, Integer count) {
        itemDictService.increaseUseCount(dictId, count);
        return Result.OK();
    }


    @ApiOperation(value = "项目结果字典-重复检查", notes = "项目结果字典-重复检查")
    @GetMapping(value = "/listByItemId")
    public Result<?> listByItemId(String itemId) {
        List<ItemDict> dictList = itemDictService.listByItemId(itemId);
        return Result.OK(dictList);
    }

    //duplicateCheck
    @ApiOperation(value = "项目结果字典-重复检查", notes = "项目结果字典-重复检查")
    @GetMapping(value = "/duplicateCheck")
    public Result<?> duplicateCheck(String itemId, String dictText, String id) {
        boolean isDuplicate = itemDictService.duplicateCheck(itemId, dictText, id);
        return Result.OK(isDuplicate);
    }

    //getNextSeqNo
    @ApiOperation(value = "体检项目-获取下一个排序号", notes = "体检项目-获取下一个排序号")
    @GetMapping(value = "/getNextSeqNo")
    public Result<?> getNextSeqNo(String itemId) {
        Long seqNo = itemDictService.getNextSeqNo(itemId);
        return Result.OK(seqNo);
    }

    /**
     * 分页列表查询
     *
     * @param itemDict
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "项目结果字典-分页列表查询")
    @ApiOperation(value = "项目结果字典-分页列表查询", notes = "项目结果字典-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ItemDict>> queryPageList(ItemDict itemDict, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ItemDict> queryWrapper = QueryGenerator.initQueryWrapper(itemDict, req.getParameterMap());
        Page<ItemDict> page = new Page<ItemDict>(pageNo, pageSize);
        IPage<ItemDict> pageList = itemDictService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param itemDict
     * @return
     */
    @AutoLog(value = "项目结果字典-添加")
    @ApiOperation(value = "项目结果字典-添加", notes = "项目结果字典-添加")
    @RequiresPermissions("basicinfo:item_dict:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ItemDict itemDict) {
        itemDictService.save(itemDict);
        itemDictService.resetDefault(itemDict, itemDict.getId());
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param itemDict
     * @return
     */
    @AutoLog(value = "项目结果字典-编辑")
    @ApiOperation(value = "项目结果字典-编辑", notes = "项目结果字典-编辑")
    @RequiresPermissions("basicinfo:item_dict:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ItemDict itemDict) {
        itemDictService.updateById(itemDict);
        itemDictService.resetDefault(itemDict, itemDict.getId());
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "项目结果字典-通过id删除")
    @ApiOperation(value = "项目结果字典-通过id删除", notes = "项目结果字典-通过id删除")
    @RequiresPermissions("basicinfo:item_dict:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        itemDictService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "项目结果字典-批量删除")
    @ApiOperation(value = "项目结果字典-批量删除", notes = "项目结果字典-批量删除")
    @RequiresPermissions("basicinfo:item_dict:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.itemDictService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "项目结果字典-通过id查询")
    @ApiOperation(value = "项目结果字典-通过id查询", notes = "项目结果字典-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ItemDict> queryById(@RequestParam(name = "id", required = true) String id) {
        ItemDict itemDict = itemDictService.getById(id);
        if (itemDict == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(itemDict);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param itemDict
     */
    @RequiresPermissions("basicinfo:item_dict:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItemDict itemDict) {
        return super.exportXls(request, itemDict, ItemDict.class, "项目结果字典");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:item_dict:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ItemDict.class);
    }

}
