package org.jeecg.modules.basicinfo.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 体检套餐
 * @Author: jeecg-boot
 * @Date: 2024-02-03
 * @Version: V1.0
 */
@Data
@TableName("item_suit")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "item_suit对象", description = "体检套餐")
public class ItemSuit implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.Integer sort;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
    /**
     * 助记码
     */
    @Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private java.lang.String helpChar;
    /**
     * 五笔码
     */
    @Excel(name = "五笔码", width = 15)
    @ApiModelProperty(value = "五笔码")
    private java.lang.String wubiChar;
    /**
     * 性别限制
     */
    @Excel(name = "性别限制", width = 15, dicCode = "sexLimit")
    @Dict(dicCode = "sexLimit")
    @ApiModelProperty(value = "性别限制")
    private java.lang.String sexLimit;
    /**
     * 最小年龄
     */
    @Excel(name = "最小年龄", width = 15)
    @ApiModelProperty(value = "最小年龄")
    private java.lang.Integer minAge;
    /**
     * 最大年龄
     */
    @Excel(name = "最大年龄", width = 15)
    @ApiModelProperty(value = "最大年龄")
    private java.lang.Integer maxAge;
    /**
     * 折后价
     */
    @Excel(name = "折后价", width = 15)
    @ApiModelProperty(value = "折后价")
    private java.math.BigDecimal price;
    /**
     * 原价
     */
    @Excel(name = "原价", width = 15)
    @ApiModelProperty(value = "原价")
    private java.math.BigDecimal costPrice;
    /**
     * 差价
     */
    @Excel(name = "差价", width = 15)
    @ApiModelProperty(value = "差价")
    private java.math.BigDecimal diffPrice;
    /**
     * 体检类别
     */
    @Excel(name = "体检类别", width = 15, dicCode = "examination_type")
    @Dict(dicCode = "examination_type")
    @ApiModelProperty(value = "体检类别")
    private java.lang.String examinationType;
    /**
     * 套餐类型
     */
    @Excel(name = "套餐类型", width = 15, dicCode = "suit_type")
    @Dict(dicCode = "suit_type")
    @ApiModelProperty(value = "套餐类型")
    private java.lang.String suitType;
    /**
     * 健康证行业
     */
    @Excel(name = "健康证行业", width = 15, dictTable = "zy_industry where del_flag=0", dicText = "name", dicCode = "id")
    @Dict(dictTable = "zy_industry where del_flag=0", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "健康证行业")
    private java.lang.String healthCertIndustry;
    /**
     * 加减项
     */
    @Excel(name = "加减项", width = 15)
    @ApiModelProperty(value = "加减项")
    private java.lang.String adjustType;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 危害因素
     */
    @Excel(name = "危害因素", width = 15, dictTable = "zy_risk_factor", dicText = "name", dicCode = "id")
    @Dict(dictTable = "zy_risk_factor", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "危害因素")
    private java.lang.String riskFactor;
    /**
     * 问卷
     */
    @Excel(name = "问卷", width = 15)
    @ApiModelProperty(value = "问卷")
    private java.lang.String questId;
    /**
     * 岗位
     */
    @Excel(name = "岗位", width = 15, dicCode = "job_status")
    @Dict(dicCode = "job_status")
    @ApiModelProperty(value = "岗位")
    private java.lang.String job;
    /**
     * 备孕项目
     */
    @Excel(name = "备孕项目", width = 15)
    @ApiModelProperty(value = "备孕项目")
    private java.lang.Integer pregnancyFlag;
    /**
     * 注意事项
     */
    @Excel(name = "注意事项", width = 15)
    @ApiModelProperty(value = "注意事项")
    private java.lang.String notice;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**
     * 启用
     */
    @Excel(name = "启用", width = 15)
    @ApiModelProperty(value = "启用")
    private java.lang.Integer enableFlag;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    @TableLogic(value = "0")
    @ApiModelProperty(value = "删除标识")
    private String delFlag;
    @ApiModelProperty(value = "套餐类别")
    private java.lang.String categoryId;
    @ApiModelProperty(value = "套餐图片")
    private java.lang.String suitPicture;
    @ApiModelProperty(value = "对外开放")
    private String pubAvailable;
    @TableField(exist = false)
    List<ItemGroup> itemGroupList;
    @TableField(exist = false)
    private Integer saledCount;
    @TableField(exist = false)
    List<SuitGroup> suitGroupList;
}
