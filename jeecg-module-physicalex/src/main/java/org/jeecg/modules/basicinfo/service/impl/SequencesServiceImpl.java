package org.jeecg.modules.basicinfo.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.Sequences;
import org.jeecg.modules.basicinfo.mapper.SequencesMapper;
import org.jeecg.modules.basicinfo.service.ISequencesService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: sequences
 * @Author: jeecg-boot
 * @Date:   2024-12-18
 * @Version: V1.0
 */
@Service
public class SequencesServiceImpl extends ServiceImpl<SequencesMapper, Sequences> implements ISequencesService {

    @Autowired
    private SequencesMapper sequenceMapper;
    @Autowired
    private ISysSettingService sysSettingService;

    @Transactional
    public synchronized Long getNextSequence(String seqName) {
        // 查询序列当前的值
        Sequences sequence = sequenceMapper.getByName(seqName);

        if (sequence == null) {
            // 如果没有该序列，则创建一个新的
            sequence = new Sequences();
            sequence.setSeqName(seqName);
            sequence.setSeqValue(1L);
            sequenceMapper.insert(sequence);
            return 1L;
        } else {
            // 如果序列已存在，更新并返回下一个值
            Long nextValue = sequence.getSeqValue() + 1;
            sequence.setSeqValue(nextValue);
            sequenceMapper.updateById(sequence);
            return nextValue;
        }
    }

    @Override
    @Transactional
    public  synchronized String getNextSequenceWithPrefix(String seqName) {
        // 查询序列当前的值
        Sequences sequence = sequenceMapper.getByName(seqName);
        //查询有无定义业务前缀
        String prefix = sysSettingService.getValueByCode(seqName);
        prefix=StringUtils.isNotBlank(prefix) ? prefix : "" ;

        if (sequence == null) {
            // 如果没有该序列，则创建一个新的
            sequence = new Sequences();
            sequence.setSeqName(seqName);
            sequence.setSeqValue(1L);
            sequenceMapper.insert(sequence);
            return prefix + 1L;
        } else {
            // 如果序列已存在，更新并返回下一个值
            Long nextValue = sequence.getSeqValue() + 1;
            sequence.setSeqValue(nextValue);
            sequenceMapper.updateById(sequence);
            return prefix + nextValue;
        }
    }


}
