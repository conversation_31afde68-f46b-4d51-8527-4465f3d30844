package org.jeecg.modules.basicinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.Template;

import java.util.List;

/**
 * @Description: 模版管理
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
public interface TemplateMapper extends BaseMapper<Template> {

    List<Template> listTemplate(@Param("type") String type, @Param("examCategory") String examCategory, @Param("regType") String regType, @Param("defaultFlag") String defaultFlag,@Param("groupId") String groupId);

    List<String> getApplyTemplateIdsByReg(@Param("customerRegId") String customerRegId, @Param("templateType") String templateType,@Param("reprintFlag")Boolean reprintFlag);

    List<Template> listByRegType(@Param("regType") String regType, @Param("type") String type);
}
