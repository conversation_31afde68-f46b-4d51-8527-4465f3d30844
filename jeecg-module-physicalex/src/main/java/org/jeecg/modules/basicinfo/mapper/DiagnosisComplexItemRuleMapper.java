package org.jeecg.modules.basicinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplexItemRule;

import java.util.List;

/**
 * @Description: 符合判断规则
 * @Author: jeecg-boot
 * @Date: 2024-04-29
 * @Version: V1.0
 */
public interface DiagnosisComplexItemRuleMapper extends BaseMapper<DiagnosisComplexItemRule> {

    List<DiagnosisComplexItemRule> selectByMainId(@Param("mainId") String mainId);

    List<DiagnosisComplexItemRule> selectByDepartmentId(@Param("departmentId") String departmentId, @Param("severityDegree") String severityDegree, @Param("age") Integer age, @Param("marriage") String marriage, @Param("gender") String gender);

    void deleteByMainId(@Param("mainId") String mainId);
}
