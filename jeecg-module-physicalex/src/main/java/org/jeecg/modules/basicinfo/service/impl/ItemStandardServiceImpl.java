package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.ItemStandard;
import org.jeecg.modules.basicinfo.mapper.ItemStandardMapper;
import org.jeecg.modules.basicinfo.service.IItemStandardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 结果参考值
 * @Author: jeecg-boot
 * @Date: 2024-01-31
 * @Version: V1.0
 */
@Service
public class ItemStandardServiceImpl extends ServiceImpl<ItemStandardMapper, ItemStandard> implements IItemStandardService {

    @Autowired
    private ItemStandardMapper itemStandardMapper;

    @Override
    public List<ItemStandard> selectByMainId(String mainId) {
        return itemStandardMapper.selectByMainId(mainId);
    }

    @Override
    public ItemStandard selectNormalStandardByItemId(String itemId) {

        return itemStandardMapper.selectStandardByResultSymbo(itemId, ExConstants.ITEM_SYMBO_正常);
    }
}
