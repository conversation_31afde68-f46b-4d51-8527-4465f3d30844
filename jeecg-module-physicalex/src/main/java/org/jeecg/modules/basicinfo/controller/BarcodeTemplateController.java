package org.jeecg.modules.basicinfo.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.BarcodeTemplate;
import org.jeecg.modules.basicinfo.service.IBarcodeTemplateService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 条码模板
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Api(tags = "条码模板")
@RestController
@RequestMapping("/basicinfo/barcodeTemplate")
@Slf4j
public class BarcodeTemplateController extends JeecgController<BarcodeTemplate, IBarcodeTemplateService> {
    @Autowired
    private IBarcodeTemplateService barcodeTemplateService;


    //getUpdateTime
    @ApiOperation(value = "获取更新时间")
    @GetMapping(value = "/getUpdateTime")
    public Result<?> getUpdateTime(@RequestParam(name = "id", required = true) String id) {
        return Result.OK(barcodeTemplateService.getUpdateTime(id));
    }

    //获取体检号模板
    @ApiOperation(value = "获取体检号模板ID")
    @GetMapping(value = "/getBarcodeTemplateIdByCategory")
    public Result<?> getByCategory(@RequestParam(name = "category", required = true) String category) {
        String id = barcodeTemplateService.getIdByCategory(category);
        if (id == null) {
            return Result.error("未找到对应列别的条码模版！");
        }
        return Result.OK(id);
    }


    /**
     * getById
     */
    @ApiOperation(value = "根据ID获取条码模板", notes = "根据ID获取条码模板")
    @GetMapping(value = "/getById")
    public Result<?> getById(@RequestParam(name = "id", required = true) String id) {
        BarcodeTemplate barcodeTemplate = barcodeTemplateService.getById(id);
        if (barcodeTemplate == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(barcodeTemplate);
    }

    /**
     * 分页列表查询
     *
     * @param barcodeTemplate
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "条码模板-分页列表查询")
    @ApiOperation(value = "条码模板-分页列表查询", notes = "条码模板-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<BarcodeTemplate>> queryPageList(BarcodeTemplate barcodeTemplate, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<BarcodeTemplate> queryWrapper = QueryGenerator.initQueryWrapper(barcodeTemplate, req.getParameterMap());
        Page<BarcodeTemplate> page = new Page<BarcodeTemplate>(pageNo, pageSize);
        IPage<BarcodeTemplate> pageList = barcodeTemplateService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param barcodeTemplate
     * @return
     */
    @AutoLog(value = "条码模板-添加")
    @ApiOperation(value = "条码模板-添加", notes = "条码模板-添加")
    @RequiresPermissions("basicinfo:barcode_template:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody BarcodeTemplate barcodeTemplate) {
        barcodeTemplateService.save(barcodeTemplate);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param barcodeTemplate
     * @return
     */
    @AutoLog(value = "条码模板-编辑")
    @ApiOperation(value = "条码模板-编辑", notes = "条码模板-编辑")
    @RequiresPermissions("basicinfo:barcode_template:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody BarcodeTemplate barcodeTemplate) {
        barcodeTemplateService.updateById(barcodeTemplate);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "条码模板-通过id删除")
    @ApiOperation(value = "条码模板-通过id删除", notes = "条码模板-通过id删除")
    @RequiresPermissions("basicinfo:barcode_template:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        barcodeTemplateService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "条码模板-批量删除")
    @ApiOperation(value = "条码模板-批量删除", notes = "条码模板-批量删除")
    @RequiresPermissions("basicinfo:barcode_template:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.barcodeTemplateService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "条码模板-通过id查询")
    @ApiOperation(value = "条码模板-通过id查询", notes = "条码模板-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<BarcodeTemplate> queryById(@RequestParam(name = "id", required = true) String id) {
        BarcodeTemplate barcodeTemplate = barcodeTemplateService.getById(id);
        if (barcodeTemplate == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(barcodeTemplate);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param barcodeTemplate
     */
    @RequiresPermissions("basicinfo:barcode_template:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BarcodeTemplate barcodeTemplate) {
        return super.exportXls(request, barcodeTemplate, BarcodeTemplate.class, "条码模板");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:barcode_template:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BarcodeTemplate.class);
    }

}
