package org.jeecg.modules.fee.controller;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.LimitOperationRecord;
import org.jeecg.modules.fee.service.ILimitOperationRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 体检限额操作记录
 * @Author: jeecg-boot
 * @Date: 2025-01-14
 * @Version: V1.0
 */
@Api(tags = "体检限额操作记录")
@RestController
@RequestMapping("/fee/limitOperationRecord")
@Slf4j
public class LimitOperationRecordController extends JeecgController<LimitOperationRecord, ILimitOperationRecordService> {
    @Autowired
    private ILimitOperationRecordService limitOperationRecordService;

    /**
     * 转赠额度
     *
     * @return
     */
    @AutoLog(value = "体检限额操作记录-转赠额度")
    @ApiOperation(value = "体检限额操作记录-转赠额度", notes = "体检限额操作记录-转赠额度")
    @GetMapping(value = "/transfer2Another")
    @RequiresPermissions("fee:limit_operation_record:transfer2Another")
    public Result<?> transfer2Another(@RequestParam(name = "regId", required = true) String regId,
                                      @RequestParam(name = "targetLimitId", required = true) String targetLimitId,
                                      @RequestParam(name = "amount", required = true) BigDecimal amount) {
        try {
            limitOperationRecordService.transfer2Another(regId, targetLimitId, amount);
            return Result.OK("转赠成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 转赠额度到卡
     *
     * @return
     */
    @AutoLog(value = "体检限额操作记录-转赠额度到卡")
    @ApiOperation(value = "体检限额操作记录-转赠额度到卡", notes = "体检限额操作记录-转赠额度到卡")
    @GetMapping(value = "/transfer2Card")
    @RequiresPermissions("fee:limit_operation_record:transfer2Card")
    public Result<?> transfer2Card(@RequestParam(name = "regId", required = true) String regId,
                                   @RequestParam(name = "cardNo", required = true) String cardNo,
                                   @RequestParam(name = "amount", required = true) BigDecimal amount,
                                   @RequestParam(name = "limitPic", required = true) String limitPic) {
        try {
            limitOperationRecordService.transfer2Card(regId, cardNo, amount,limitPic);
            return Result.OK("转赠成功!");
        } catch (Exception e) {

            return Result.error(e.getMessage());
        }
    }

    /**
     * 扣减额度
     *
     * @return
     */
    @AutoLog(value = "体检限额操作记录-扣减额度")
    @ApiOperation(value = "体检限额操作记录-扣减额度", notes = "体检限额操作记录-扣减额度")
    @GetMapping(value = "/subtractLimitAmount")
    @RequiresPermissions("fee:limit_operation_record:subtractLimitAmount")
    public Result<?> subtractLimitAmount(@RequestParam(name = "regId", required = true) String regId,
                                         @RequestParam(name = "amount", required = true) BigDecimal amount,
                                         @RequestParam(name = "limitPic", required = true) String limitPic) {
        try {
            limitOperationRecordService.subtractLimitAmount(regId, amount,limitPic);
            return Result.OK("扣减成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分页列表查询
     *
     * @param limitOperationRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检限额操作记录-分页列表查询")
    @ApiOperation(value = "体检限额操作记录-分页列表查询", notes = "体检限额操作记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<LimitOperationRecord>> queryPageList(LimitOperationRecord limitOperationRecord,
                                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                             HttpServletRequest req) {
        String customerRegId = req.getParameter("customerRegId");
        if(StringUtils.isBlank(customerRegId)){
            return Result.error("登记记录ID不能为空");
        }
        QueryWrapper<LimitOperationRecord> queryWrapper = QueryGenerator.initQueryWrapper(limitOperationRecord, req.getParameterMap());
        Page<LimitOperationRecord> page = new Page<LimitOperationRecord>(pageNo, pageSize);
        queryWrapper.orderByDesc("create_time");
        IPage<LimitOperationRecord> pageList = limitOperationRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param limitOperationRecord
     * @return
     */
    @AutoLog(value = "体检限额操作记录-添加")
    @ApiOperation(value = "体检限额操作记录-添加", notes = "体检限额操作记录-添加")
//    @RequiresPermissions("fee:limit_operation_record:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody LimitOperationRecord limitOperationRecord) {
        limitOperationRecordService.save(limitOperationRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param limitOperationRecord
     * @return
     */
    @AutoLog(value = "体检限额操作记录-编辑")
    @ApiOperation(value = "体检限额操作记录-编辑", notes = "体检限额操作记录-编辑")
//    @RequiresPermissions("fee:limit_operation_record:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody LimitOperationRecord limitOperationRecord) {
        limitOperationRecordService.updateById(limitOperationRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检限额操作记录-通过id删除")
    @ApiOperation(value = "体检限额操作记录-通过id删除", notes = "体检限额操作记录-通过id删除")
    @RequiresPermissions("fee:limit_operation_record:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        limitOperationRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检限额操作记录-批量删除")
    @ApiOperation(value = "体检限额操作记录-批量删除", notes = "体检限额操作记录-批量删除")
    @RequiresPermissions("fee:limit_operation_record:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.limitOperationRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检限额操作记录-通过id查询")
    @ApiOperation(value = "体检限额操作记录-通过id查询", notes = "体检限额操作记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<LimitOperationRecord> queryById(@RequestParam(name = "id", required = true) String id) {
        LimitOperationRecord limitOperationRecord = limitOperationRecordService.getById(id);
        if (limitOperationRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(limitOperationRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param limitOperationRecord
     */
    @RequiresPermissions("fee:limit_operation_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LimitOperationRecord limitOperationRecord) {
        return super.exportXls(request, limitOperationRecord, LimitOperationRecord.class, "体检限额操作记录");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("fee:limit_operation_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LimitOperationRecord.class);
    }

}
