package org.jeecg.modules.fee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.fee.bo.CardNo;
import org.jeecg.modules.fee.entity.Card;
import org.jeecg.modules.fee.entity.CardTrade;
import org.jeecg.modules.fee.mapper.CardMapper;
import org.jeecg.modules.fee.mapper.CardTradeMapper;
import org.jeecg.modules.fee.service.ICardHandoverRecordService;
import org.jeecg.modules.fee.service.ICardService;
import org.jeecg.modules.fee.service.ICardTradeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: card
 * @Author: jeecg-boot
 * @Date: 2024-07-14
 * @Version: V1.0
 */
@Service
public class CardServiceImpl extends ServiceImpl<CardMapper, Card> implements ICardService {

    @Autowired
    private CardTradeMapper cardTradeMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private ICardTradeService cardTradeService;
    @Autowired
    private ICardHandoverRecordService cardHandoverRecordService;

    @Override
    public synchronized List<CardNo> getCardNo(Integer count, String prefix) throws Exception {

        Integer cardLengthVal = null;

        try {
            String cardLength = sysSettingService.getValueByCode("card_length");
            cardLengthVal = Integer.parseInt(cardLength);
        } catch (NumberFormatException ignore) {
        }
        cardLengthVal = cardLengthVal == null ? 6 : cardLengthVal;

        String cardPrefix = prefix;
        if (StringUtils.isBlank(cardPrefix)) {
            cardPrefix = sysSettingService.getValueByCode("card_prefix");
        }

        Integer maxCardNumber = jdbcTemplate.queryForObject("select max(card_number) from card", Integer.class);
        if (maxCardNumber == null) {
            maxCardNumber = 0;
        }
        maxCardNumber++;

        List<CardNo> cardNos = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            CardNo cardNo = new CardNo();
            cardNo.setCardNo(formatCardNo(maxCardNumber + i, cardPrefix, cardLengthVal));
            cardNo.setCardNumber(maxCardNumber + i);
            cardNos.add(cardNo);
        }
        return cardNos;
    }

    @Override
    public synchronized CardNo getMaxCardNo() {

        Integer cardLengthVal = null;
        String cardPrefix = null;
        try {
            cardPrefix = sysSettingService.getValueByCode("card_prefix");
            String cardLength = sysSettingService.getValueByCode("card_length");
            cardLengthVal = Integer.parseInt(cardLength);
        } catch (NumberFormatException ignore) {
        }
        cardLengthVal = cardLengthVal == null ? 6 : cardLengthVal;
        cardPrefix = cardPrefix == null ? "" : cardPrefix;

        Integer maxCardNumber = jdbcTemplate.queryForObject("select max(card_number) from card", Integer.class);
        if (maxCardNumber == null) {
            maxCardNumber = 0;
        }
        maxCardNumber++;

        String cardNo = formatCardNo(maxCardNumber, cardPrefix, cardLengthVal);

        CardNo cardNoObj = new CardNo();
        cardNoObj.setCardNo(cardNo);
        cardNoObj.setCardNumber(maxCardNumber);
        return cardNoObj;
    }

    @Override
    public String formatCardNo(Integer cardNumber, String prefix, Integer cardLength) {
        // 将卡号转换为字符串
        String cardStr = cardNumber.toString();

        // 如果卡号长度小于要求的卡长度，用0在前面补齐
        if (cardStr.length() < cardLength) {
            cardStr = String.format("%0" + cardLength + "d", cardNumber);
        }

        // 在卡号前添加前缀
        return StringUtils.stripToEmpty(prefix) + cardStr;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddCard(Card card) throws Exception {
        Integer count = card.getTotalCount();
        if (count == null || count <= 0) {
            throw new Exception("卡片数量必须大于0！");
        }
        if (card.getDenomination() != null && card.getDenomination().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("卡片面额不正确！");
        }
        if (card.getAmount() != null && card.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("卡片实收金额不正确！");
        }

        String status = ExConstants.CRAD_STATUS_待写入;
        if (card.getDenomination() != null && card.getDenomination().compareTo(BigDecimal.ZERO) > 0) {
            status = ExConstants.CRAD_STATUS_待发行;
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        String prefix = card.getPrefix();
        List<CardNo> cardNos = getCardNo(count, prefix);
        List<Card> cards = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            Card cardCopied = new Card();
            BeanUtils.copyProperties(card, cardCopied);

            CardNo cardNo = cardNos.get(i);
            cardCopied.setCardNumber(cardNo.getCardNumber());
            cardCopied.setCardNo(cardNo.getCardNo());
            cardCopied.setStatus(ExConstants.CRAD_STATUS_待写入);
            String secret = generateRadomSecret();
            String qrCode = cardCopied.getCardNo() + ":" + secret;
            cardCopied.setPwd(secret);
            cardCopied.setQrContent(qrCode);
            cardCopied.setStatus(status);
            if (StringUtils.equals(status, ExConstants.CRAD_STATUS_待发行)) {
                cardCopied.setProduceTime(new Date());
                cardCopied.setProduceBy(loginUser.getUsername());
            }
            cardCopied.setVersion(0);
            cards.add(cardCopied);
        }
        saveBatch(cards);
    }

    @Override
    public void addCard(Card card) throws Exception {
        save(card);
    }

    @Override
    public String generateRadomSecret() {
        // 生成随机的6位密码，包含数字和字母
        int length = 6;
        String characters = "abcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder secret = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            secret.append(characters.charAt(random.nextInt(characters.length())));
        }
        return secret.toString();
    }

    @Override
    public void markCardAsProducedByCardNo(List<String> cardNo) throws Exception {
        UpdateWrapper<Card> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("card_no", cardNo);
        updateWrapper.set("status", ExConstants.CRAD_STATUS_待发行);
        updateWrapper.set("produce_time", new Date());
        update(updateWrapper);
    }

    @Override
    public void markCardAsUnProducedByCardNo(List<String> cardNo) throws Exception {

        UpdateWrapper<Card> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("card_no", cardNo);
        updateWrapper.set("status", ExConstants.CRAD_STATUS_待写入);
        updateWrapper.set("produce_time", null);
        update(updateWrapper);
    }

    @Override
    public void markCardAsReleasedByCardNo(List<String> cardNo) throws Exception {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        UpdateWrapper<Card> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("card_no", cardNo);
        updateWrapper.set("status", ExConstants.CRAD_STATUS_已激活);
        updateWrapper.set("release_time", new Date());
        updateWrapper.set("release_by", loginUser.getUsername());
        update(updateWrapper);
    }

    @Override
    public void markCardAsInvalidByCardNo(List<String> cardNo) throws Exception {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        UpdateWrapper<Card> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("card_no", cardNo);
        updateWrapper.set("status", ExConstants.CRAD_STATUS_已作废);
        updateWrapper.set("nullify_time", new Date());
        updateWrapper.set("nullify_by", loginUser.getUsername());
        update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> setDenominationBatch(Card card, QueryWrapper<Card> queryWrapper) throws Exception {
        if (card.getDenomination() == null || card.getDenomination().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("卡片面额不正确！");
        }
        if (card.getAmount() == null || card.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("卡片实收金额不正确！");
        }
        long count = count(queryWrapper);
        if (count == 0) {
            throw new Exception("没有符合条件的卡片！");
        }
        if (count > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        List<Card> batchUpdateList = new ArrayList<>();
        List<Card> records = list(queryWrapper);
        for (Card item : records) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_待写入) || StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_待发行)) {
                item.setDenomination(card.getDenomination());
                item.setAmount(card.getAmount());
                item.setDiscountRate(card.getDiscountRate());
                item.setStatus(ExConstants.CRAD_STATUS_待发行);
                item.setCompanyId(card.getCompanyId());
                item.setCompanyName(card.getCompanyName());
                item.setRemark(card.getRemark());
                item.setProduceTime(new Date());
                item.setProduceBy(loginUser.getUsername());
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> setDenominationBatchByIds(Card card, List<String> idList) throws Exception {
        if (card.getDenomination() == null || card.getDenomination().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("卡片面额不正确！");
        }
        if (card.getAmount() == null || card.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("卡片实收金额不正确！");
        }
        if (idList.isEmpty()) {
            throw new Exception("没有选择卡片！");
        }
        if (idList.size() > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Card> cards = listByIds(idList);
        List<Card> batchUpdateList = new ArrayList<>();
        for (Card item : cards) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_待写入) || StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_待发行)) {
                item.setDenomination(card.getDenomination());
                item.setAmount(card.getAmount());
                item.setDiscountRate(card.getDiscountRate());
                item.setStatus(ExConstants.CRAD_STATUS_待发行);
                item.setCompanyId(card.getCompanyId());
                item.setCompanyName(card.getCompanyName());
                item.setRemark(card.getRemark());
                item.setProduceTime(new Date());
                item.setProduceBy(loginUser.getUsername());
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> releaseBatch(QueryWrapper<Card> queryWrapper) throws Exception {
        long count = count(queryWrapper);
        if (count == 0) {
            throw new Exception("没有符合条件的卡片！");
        }
        if (count > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        List<Card> batchUpdateList = new ArrayList<>();
        List<Card> records = list(queryWrapper);
        for (Card item : records) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_待发行)) {
                item.setStatus(ExConstants.CRAD_STATUS_已发行);
                item.setReleaseTime(new Date());
                item.setReleaseBy(loginUser.getUsername());
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> releaseBatchByIds(List<String> idList) throws Exception {
        if (idList.isEmpty()) {
            throw new Exception("没有选择卡片！");
        }
        if (idList.size() > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Card> cards = listByIds(idList);
        List<Card> batchUpdateList = new ArrayList<>();
        for (Card item : cards) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_待发行)) {
                item.setStatus(ExConstants.CRAD_STATUS_已发行);
                item.setReleaseTime(new Date());
                item.setReleaseBy(loginUser.getUsername());
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> lockBatchByIds(List<String> idList, String reason) throws Exception {
        if (idList.isEmpty()) {
            throw new Exception("没有选择卡片！");
        }
        if (idList.size() > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Card> cards = listByIds(idList);
        List<Card> batchUpdateList = new ArrayList<>();
        for (Card item : cards) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_已激活)) {
                item.setStatus(ExConstants.CRAD_STATUS_已锁定);
                item.setLockReason(reason);
                item.setLockTime(new Date());
                item.setLockBy(loginUser.getUsername());
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> lockBatch(QueryWrapper<Card> queryWrapper, String reason) throws Exception {
        long count = count(queryWrapper);
        if (count == 0) {
            throw new Exception("没有符合条件的卡片！");
        }
        if (count > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        List<Card> batchUpdateList = new ArrayList<>();
        List<Card> records = list(queryWrapper);
        for (Card item : records) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_已激活)) {
                item.setStatus(ExConstants.CRAD_STATUS_已锁定);
                item.setLockReason(reason);
                item.setLockTime(new Date());
                item.setLockBy(loginUser.getUsername());
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> unlockBatchByIds(List<String> idList) throws Exception {
        if (idList.isEmpty()) {
            throw new Exception("没有选择卡片！");
        }
        if (idList.size() > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Card> cards = listByIds(idList);
        List<Card> batchUpdateList = new ArrayList<>();
        for (Card item : cards) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_已锁定)) {
                item.setStatus(ExConstants.CRAD_STATUS_已激活);
                item.setLockReason(null);
                item.setLockTime(new Date());
                item.setLockBy(loginUser.getUsername());
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> unlockBatch(QueryWrapper<Card> queryWrapper) throws Exception {
        long count = count(queryWrapper);
        if (count == 0) {
            throw new Exception("没有符合条件的卡片！");
        }
        if (count > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        List<Card> batchUpdateList = new ArrayList<>();
        List<Card> records = list(queryWrapper);
        for (Card item : records) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_已锁定)) {
                item.setStatus(ExConstants.CRAD_STATUS_已激活);
                item.setLockReason(null);
                item.setLockTime(null);
                item.setLockBy(null);
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> invalidBatchByIds(List<String> idList) throws Exception {
        if (idList.isEmpty()) {
            throw new Exception("没有选择卡片！");
        }
        if (idList.size() > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Card> cards = listByIds(idList);
        List<Card> batchUpdateList = new ArrayList<>();
        for (Card item : cards) {

            item.setStatus(ExConstants.CRAD_STATUS_已作废);
            item.setInvalidTime(new Date());
            item.setInvalidBy(loginUser.getUsername());
            batchUpdateList.add(item);
            successCount++;
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> invalidBatch(QueryWrapper<Card> queryWrapper) throws Exception {
        long count = count(queryWrapper);
        if (count == 0) {
            throw new Exception("没有符合条件的卡片！");
        }
        if (count > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Card> batchUpdateList = new ArrayList<>();
        List<Card> records = list(queryWrapper);
        for (Card item : records) {

            item.setStatus(ExConstants.CRAD_STATUS_已作废);
            item.setInvalidTime(new Date());
            item.setInvalidBy(loginUser.getUsername());
            batchUpdateList.add(item);
            successCount++;
            batchUpdateList.add(item);
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<Card> deleteBatchByIds(List<String> idList) throws Exception {
        if (idList.isEmpty()) {
            throw new Exception("没有选择卡片！");
        }
        if (idList.size() > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        List<Card> cards = listByIds(idList);
        List<Card> batchUpdateList = new ArrayList<>();
        for (Card item : cards) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_待写入) || StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_待发行)) {
                batchUpdateList.add(item);
                successCount++;
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            removeByIds(batchUpdateList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

    @Override
    public void deleteCardById(String id) throws Exception {
        Card card = getById(id);
        if (card == null) {
            throw new Exception("卡片不存在！");
        }
        if (!StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_待写入) && !StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_待发行)) {
            throw new Exception("卡片状态不正确！");
        }
        removeById(id);
    }

    @Override
    public List<Card> getCardByRange(Integer startNum, Integer endNum) throws Exception {

        QueryWrapper<Card> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("card_number", startNum, endNum);
        queryWrapper.eq("status", ExConstants.CRAD_STATUS_待写入);
        //queryWrapper.eq("del_flag", "0");
        queryWrapper.isNull("order_id");
        queryWrapper.eq("category", "代金卡");
        return list(queryWrapper);
    }

    @Override
    public Card getCardByCardNo(String cardNo) throws Exception {
        QueryWrapper<Card> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("card_no", cardNo);
        return getOne(queryWrapper);
    }

    @Override
    public void replaceCard(String reason, String originCradId, String cardNo) throws Exception {
        Card originCard = getById(originCradId);
        if (originCard == null) {
            throw new Exception("原卡片不存在！");
        }
        if (!(StringUtils.equals(originCard.getStatus(), ExConstants.CRAD_STATUS_已激活) || StringUtils.equals(originCard.getStatus(), ExConstants.CRAD_STATUS_已锁定))) {
            throw new Exception("原卡片状态不正确！");
        }
        if (originCard.getBalance().compareTo(BigDecimal.ZERO) <= 0) {
            throw new Exception("原卡片余额为0！");
        }

        Card newCard = getCardByCardNo(cardNo);
        if (newCard == null) {
            throw new Exception("新卡片不存在！");
        }
        if (!StringUtils.equals(newCard.getStatus(), ExConstants.CRAD_STATUS_待写入)) {
            throw new Exception("新卡片状态不正确！");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        originCard.setStatus(ExConstants.CRAD_STATUS_已作废);
        originCard.setInvalidTime(new Date());
        originCard.setInvalidBy(loginUser.getUsername());
        originCard.setRemark("已补发新卡片，原卡片作废。新卡号：" + newCard.getCardNo() + "。 原因：" + reason);
        updateById(originCard);

        newCard.setStatus(ExConstants.CRAD_STATUS_已激活);
        newCard.setDenomination(originCard.getDenomination());
        newCard.setAmount(originCard.getAmount());
        newCard.setDiscountRate(originCard.getDiscountRate());
        newCard.setBalance(originCard.getBalance());
        newCard.setSource(originCard.getSource());
        newCard.setSaleTime(new Date());
        updateById(newCard);

        //为新卡增加交易记录
        CardTrade cardTrade = new CardTrade();
        cardTrade.setCardId(newCard.getId());
        cardTrade.setCardNo(newCard.getCardNo());
        cardTrade.setTradeType(ExConstants.CARD_TRADE_TYPE_入);
        cardTrade.setAmount(originCard.getBalance());
        cardTrade.setAmountActual(originCard.getAmount());
        cardTrade.setBlance(originCard.getBalance());
        cardTrade.setRemark("补卡充值");
        cardTrade.setBizId(originCard.getId());
        cardTradeMapper.insert(cardTrade);

       /* CardTrade cardTrade2 = new CardTrade();
        cardTrade2.setCardId(newCard.getId());
        cardTrade2.setCardNo(newCard.getCardNo());
        cardTrade2.setTradeType(ExConstants.CARD_TRADE_TYPE_出);
        BigDecimal consumeAmount = originCard.getDenomination().subtract(originCard.getBalance());
        cardTrade2.setAmount(consumeAmount.negate());
        cardTrade2.setBlance(originCard.getBalance());
        cardTrade2.setRemark("补卡抵扣");
        cardTrade2.setBizId(originCard.getId());
        cardTradeMapper.insert(cardTrade2);*/
    }

    @Override
    public boolean checkCardPwd(String cardNo, String pwd) throws Exception {
        Integer exsitCount = jdbcTemplate.queryForObject("select count(1) from card where card_no = ? and pwd = ?", Integer.class, cardNo, pwd);

        exsitCount = exsitCount == null ? 0 : exsitCount;
        return exsitCount > 0;
    }

    @Override
    public BatchResult<Card> resetBatchByIds(List<String> idList, String reason) throws Exception {
        if (idList.isEmpty()) {
            throw new Exception("没有选择卡片！");
        }
        if (idList.size() > 20000) {
            throw new Exception("批量操作数量不能超过20000！");
        }
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failedList = new ArrayList<>();
        int successCount = 0;

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Card> batchUpdateList = new ArrayList<>();
        List<String> batchRemoveHandoverIdList = new ArrayList<>();
        List<CardTrade> batchAddTradeList = new ArrayList<>();
        QueryWrapper<Card> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", idList);
        List<Card> records = list(queryWrapper);
        for (Card item : records) {
            if (StringUtils.equals(item.getStatus(), ExConstants.CRAD_STATUS_已激活)) {
                BigDecimal originBalance = item.getBalance();
                if (StringUtils.isNotBlank(item.getHandoverId())) {
                    String[] split = StringUtils.split(item.getHandoverId(), ",");
                    batchRemoveHandoverIdList.addAll(Arrays.asList(split));
                    item.setHandoverId(null);
                }
                item.setOrderId(null);
                item.setGoodsId(null);
                item.setBalance(null);
                item.setAmount(null);
                item.setDenomination(null);
                item.setSaleTime(null);
                item.setSaleBy(null);
                item.setStatus(ExConstants.CRAD_STATUS_待写入);
                item.setResetReason(reason);
                item.setResetTime(new Date());
                item.setResetBy(loginUser.getUsername());
                batchUpdateList.add(item);
                successCount++;
                //新增一条交易记录
                //1、支付记录
                if (Objects.nonNull(originBalance)) {
                    CardTrade cardTrade = new CardTrade();
                    cardTrade.setCardId(item.getId());
                    cardTrade.setCardNo(item.getCardNo());
                    cardTrade.setAmount(originBalance.negate());
                    cardTrade.setTradeType(ExConstants.CARD_TRADE_TYPE_出);
                    cardTrade.setCategory(ExConstants.PAY_BIZ_TYPE_体检卡重置);
                    cardTrade.setCreateTime(new Date());
                    cardTrade.setStatus(ExConstants.CARD_TRADE_STATUS_成功);
                    cardTrade.setCreateBy(loginUser.getUsername());
                    cardTrade.setBlance(BigDecimal.ZERO);
                    cardTrade.setRemark("体检卡重置");
                    batchAddTradeList.add(cardTrade);
                }
            } else {
                failedList.add(new BatchResult.FailureResult<>(item, "卡片状态不正确！"));
            }
        }
        if (!batchUpdateList.isEmpty()) {
            updateBatchById(batchUpdateList);
        }
        if (!batchAddTradeList.isEmpty()) {
            cardTradeService.saveBatch(batchAddTradeList);
        }
        if (!batchRemoveHandoverIdList.isEmpty()) {
            cardHandoverRecordService.removeBatchByIds(batchRemoveHandoverIdList);
        }

        result.setSuccessCount(successCount);
        result.setFailureResults(failedList);

        return result;
    }

}