package org.jeecg.modules.fee.controller;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.CardTrade;
import org.jeecg.modules.fee.service.ICardTradeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: card_trade
 * @Author: jeecg-boot
 * @Date: 2024-07-14
 * @Version: V1.0
 */
@Api(tags = "card_trade")
@RestController
@RequestMapping("/fee/cardTrade")
@Slf4j
public class CardTradeController extends JeecgController<CardTrade, ICardTradeService> {
    @Autowired
    private ICardTradeService cardTradeService;


    /**
     * 分页列表查询
     *
     * @param cardTrade
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "card_trade-分页列表查询")
    @ApiOperation(value = "card_trade-分页列表查询", notes = "card_trade-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CardTrade>> queryPageList(CardTrade cardTrade, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
  /*      QueryWrapper<CardTrade> queryWrapper = QueryGenerator.initQueryWrapper(cardTrade, req.getParameterMap());
        Page<CardTrade> page = new Page<CardTrade>(pageNo, pageSize);
        IPage<CardTrade> pageList = cardTradeService.page(page, queryWrapper);
        return Result.OK(pageList);*/

        Page<CardTrade> page = new Page<CardTrade>(pageNo, pageSize);
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String cardNo = StringUtils.trimToNull(req.getParameter("cardNo"));
        String tradeType = StringUtils.trimToNull(req.getParameter("tradeType"));
        String createTimeStart = StringUtils.trimToNull(req.getParameter("createTime_begin"));
        String createTimeEnd = StringUtils.trimToNull(req.getParameter("createTime_end"));


        cardTradeService.pageCardTrade(page, cardNo, tradeType, createTimeStart, createTimeEnd, name, examNo);
        return Result.OK(page);
    }

    /**
     * 根据卡号获取余额
     *
     * @param cardNo
     * @return
     */
    //@AutoLog(value = "card_trade-根据卡号获取余额")
    @ApiOperation(value = "card_trade-根据卡号获取余额", notes = "card_trade-根据卡号获取余额")
    @GetMapping(value = "/getBalance")
    public Result<?> getBalance(@RequestParam(name = "cardNo", required = true) String cardNo) {
        BigDecimal balance = cardTradeService.getBalanceByCardNo(cardNo);
        if (balance == null) {
            return Result.error("未找到对应卡号的信息！");
        }
        return Result.OK(balance);
    }
    @ApiOperation(value = "card_trade-根据卡号获取余额和公司名称", notes = "card_trade-根据卡号获取余额和公司名称")
    @GetMapping(value = "/getBalanceAndCompany")
    public Result<?> getBalanceAndCompany(@RequestParam(name = "cardNo", required = true) String cardNo) {
        Map<String, Object> balanceAndCompany = cardTradeService.getBalanceAndCompany(cardNo);
        if (MapUtils.isEmpty(balanceAndCompany)) {
            return Result.error("未找到对应卡号的信息！");
        }
        return Result.OK(balanceAndCompany);
    }

    /**
     * 添加
     *
     * @param cardTrade
     * @return
     */
    @AutoLog(value = "card_trade-添加")
    @ApiOperation(value = "card_trade-添加", notes = "card_trade-添加")
    @RequiresPermissions("fee:card_trade:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CardTrade cardTrade) {
        cardTradeService.save(cardTrade);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param cardTrade
     * @return
     */
    @AutoLog(value = "card_trade-编辑")
    @ApiOperation(value = "card_trade-编辑", notes = "card_trade-编辑")
    @RequiresPermissions("fee:card_trade:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CardTrade cardTrade) {
        cardTradeService.updateById(cardTrade);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "card_trade-通过id删除")
    @ApiOperation(value = "card_trade-通过id删除", notes = "card_trade-通过id删除")
    @RequiresPermissions("fee:card_trade:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        cardTradeService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "card_trade-批量删除")
    @ApiOperation(value = "card_trade-批量删除", notes = "card_trade-批量删除")
    @RequiresPermissions("fee:card_trade:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.cardTradeService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "card_trade-通过id查询")
    @ApiOperation(value = "card_trade-通过id查询", notes = "card_trade-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CardTrade> queryById(@RequestParam(name = "id", required = true) String id) {
        CardTrade cardTrade = cardTradeService.getById(id);
        if (cardTrade == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(cardTrade);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param cardTrade
     */
    @RequiresPermissions("fee:card_trade:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CardTrade cardTrade) {
        return super.exportXls(request, cardTrade, CardTrade.class, "card_trade");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("fee:card_trade:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CardTrade.class);
    }

}
