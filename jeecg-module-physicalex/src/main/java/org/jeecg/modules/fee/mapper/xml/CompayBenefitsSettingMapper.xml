<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.fee.mapper.CompayBenefitsSettingMapper">

    <select id="selectByTeamId" resultType="org.jeecg.modules.fee.entity.CompayBenefitsSetting">
        select * from compay_benefits_setting where team_id = #{teamId} <if test="startTime!=null"> and start_time &gt;= #{startTime}</if> <if test="endTime!=null"> and end_time &lt;= #{endTime}</if>
    </select>
</mapper>