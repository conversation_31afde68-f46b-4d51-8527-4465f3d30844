package org.jeecg.modules.fee.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 支付网关-支付订单
 * @Author: jeecg-boot
 * @Date:   2024-07-02
 * @Version: V1.0
 */
@Data
@TableName("t_pay_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_pay_order对象", description="支付网关-支付订单")
public class TPayOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "pay_order_id", type = IdType.ASSIGN_ID)
	/**支付订单号*/
	@Excel(name = "支付订单号", width = 15)
    @ApiModelProperty(value = "支付订单号")
    private java.lang.String payOrderId;
	/**商户号*/
	@Excel(name = "商户号", width = 15)
    @ApiModelProperty(value = "商户号")
    private java.lang.String mchNo;
	/**服务商号*/
	@Excel(name = "服务商号", width = 15)
    @ApiModelProperty(value = "服务商号")
    private java.lang.String isvNo;
	/**应用ID*/
	@Excel(name = "应用ID", width = 15)
    @ApiModelProperty(value = "应用ID")
    private java.lang.String appId;
	/**商户名称*/
	@Excel(name = "商户名称", width = 15)
    @ApiModelProperty(value = "商户名称")
    private java.lang.String mchName;
	/**类型: 1-普通商户, 2-特约商户(服务商模式)*/
	@Excel(name = "类型: 1-普通商户, 2-特约商户(服务商模式)", width = 15)
    @ApiModelProperty(value = "类型: 1-普通商户, 2-特约商户(服务商模式)")
    private java.lang.Integer mchType;
	/**商户订单号*/
	@Excel(name = "商户订单号", width = 15)
    @ApiModelProperty(value = "商户订单号")
    private java.lang.String mchOrderNo;
	/**支付接口代码*/
	@Excel(name = "支付接口代码", width = 15)
    @ApiModelProperty(value = "支付接口代码")
    private java.lang.String ifCode;
	/**支付方式代码*/
	@Excel(name = "支付方式代码", width = 15)
    @ApiModelProperty(value = "支付方式代码")
    private java.lang.String wayCode;
	/**支付金额,单位分*/
	@Excel(name = "支付金额,单位分", width = 15)
    @ApiModelProperty(value = "支付金额,单位分")
    private java.lang.Integer amount;
	/**商户手续费费率快照*/
	@Excel(name = "商户手续费费率快照", width = 15)
    @ApiModelProperty(value = "商户手续费费率快照")
    private java.math.BigDecimal mchFeeRate;
	/**商户手续费,单位分*/
	@Excel(name = "商户手续费,单位分", width = 15)
    @ApiModelProperty(value = "商户手续费,单位分")
    private java.lang.Integer mchFeeAmount;
	/**三位货币代码,人民币:cny*/
	@Excel(name = "三位货币代码,人民币:cny", width = 15)
    @ApiModelProperty(value = "三位货币代码,人民币:cny")
    private java.lang.String currency;
	/**支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭*/
	@Excel(name = "支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭", width = 15)
    @ApiModelProperty(value = "支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭")
    private java.lang.Integer state;
	/**向下游回调状态, 0-未发送,  1-已发送*/
	@Excel(name = "向下游回调状态, 0-未发送,  1-已发送", width = 15)
    @ApiModelProperty(value = "向下游回调状态, 0-未发送,  1-已发送")
    private java.lang.Integer notifyState;
	/**客户端IP*/
	@Excel(name = "客户端IP", width = 15)
    @ApiModelProperty(value = "客户端IP")
    private java.lang.String clientIp;
	/**商品标题*/
	@Excel(name = "商品标题", width = 15)
    @ApiModelProperty(value = "商品标题")
    private java.lang.String subject;
	/**商品描述信息*/
	@Excel(name = "商品描述信息", width = 15)
    @ApiModelProperty(value = "商品描述信息")
    private java.lang.String body;
	/**特定渠道发起额外参数*/
	@Excel(name = "特定渠道发起额外参数", width = 15)
    @ApiModelProperty(value = "特定渠道发起额外参数")
    private java.lang.String channelExtra;
	/**渠道用户标识,如微信openId,支付宝账号*/
	@Excel(name = "渠道用户标识,如微信openId,支付宝账号", width = 15)
    @ApiModelProperty(value = "渠道用户标识,如微信openId,支付宝账号")
    private java.lang.String channelUser;
	/**渠道订单号*/
	@Excel(name = "渠道订单号", width = 15)
    @ApiModelProperty(value = "渠道订单号")
    private java.lang.String channelOrderNo;
	/**退款状态: 0-未发生实际退款, 1-部分退款, 2-全额退款*/
	@Excel(name = "退款状态: 0-未发生实际退款, 1-部分退款, 2-全额退款", width = 15)
    @ApiModelProperty(value = "退款状态: 0-未发生实际退款, 1-部分退款, 2-全额退款")
    private java.lang.Integer refundState;
	/**退款次数*/
	@Excel(name = "退款次数", width = 15)
    @ApiModelProperty(value = "退款次数")
    private java.lang.Integer refundTimes;
	/**退款总金额,单位分*/
	@Excel(name = "退款总金额,单位分", width = 15)
    @ApiModelProperty(value = "退款总金额,单位分")
    private java.lang.Integer refundAmount;
	/**订单分账模式：0-该笔订单不允许分账, 1-支付成功按配置自动完成分账, 2-商户手动分账(解冻商户金额)*/
	@Excel(name = "订单分账模式：0-该笔订单不允许分账, 1-支付成功按配置自动完成分账, 2-商户手动分账(解冻商户金额)", width = 15)
    @ApiModelProperty(value = "订单分账模式：0-该笔订单不允许分账, 1-支付成功按配置自动完成分账, 2-商户手动分账(解冻商户金额)")
    private java.lang.Integer divisionMode;
	/**订单分账状态：0-未发生分账, 1-等待分账任务处理, 2-分账处理中, 3-分账任务已结束(不体现状态)*/
	@Excel(name = "订单分账状态：0-未发生分账, 1-等待分账任务处理, 2-分账处理中, 3-分账任务已结束(不体现状态)", width = 15)
    @ApiModelProperty(value = "订单分账状态：0-未发生分账, 1-等待分账任务处理, 2-分账处理中, 3-分账任务已结束(不体现状态)")
    private java.lang.Integer divisionState;
	/**最新分账时间*/
	@Excel(name = "最新分账时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最新分账时间")
    private java.util.Date divisionLastTime;
	/**渠道支付错误码*/
	@Excel(name = "渠道支付错误码", width = 15)
    @ApiModelProperty(value = "渠道支付错误码")
    private java.lang.String errCode;
	/**渠道支付错误描述*/
	@Excel(name = "渠道支付错误描述", width = 15)
    @ApiModelProperty(value = "渠道支付错误描述")
    private java.lang.String errMsg;
	/**商户扩展参数*/
	@Excel(name = "商户扩展参数", width = 15)
    @ApiModelProperty(value = "商户扩展参数")
    private java.lang.String extParam;
	/**异步通知地址*/
	@Excel(name = "异步通知地址", width = 15)
    @ApiModelProperty(value = "异步通知地址")
    private java.lang.String notifyUrl;
	/**页面跳转地址*/
	@Excel(name = "页面跳转地址", width = 15)
    @ApiModelProperty(value = "页面跳转地址")
    private java.lang.String returnUrl;
	/**订单失效时间*/
	@Excel(name = "订单失效时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单失效时间")
    private java.util.Date expiredTime;
	/**订单支付成功时间*/
	@Excel(name = "订单支付成功时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单支付成功时间")
    private java.util.Date successTime;
	/**创建时间*/
	@Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createdAt;
	/**更新时间*/
	@Excel(name = "更新时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updatedAt;
}
