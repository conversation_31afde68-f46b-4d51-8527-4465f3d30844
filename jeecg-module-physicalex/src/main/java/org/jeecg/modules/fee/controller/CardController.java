package org.jeecg.modules.fee.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.fee.bo.CardNo;
import org.jeecg.modules.fee.entity.Card;
import org.jeecg.modules.fee.service.ICardService;
import org.jeecg.modules.fee.service.ICardTradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: card
 * @Author: jeecg-boot
 * @Date: 2024-07-14
 * @Version: V1.0
 */
@Api(tags = "card")
@RestController
@RequestMapping("/fee/card")
@Slf4j
public class CardController extends JeecgController<Card, ICardService> {
    @Autowired
    private ICardService cardService;
    @Autowired
    private ICardTradeService cardTradeService;

    /**
     * 分页列表查询
     *
     * @param card
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "card-分页列表查询")
    @ApiOperation(value = "card-分页列表查询", notes = "card-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Card>> queryPageList(Card card, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<Card> queryWrapper = QueryGenerator.initQueryWrapper(card, req.getParameterMap());
        Page<Card> page = new Page<Card>(pageNo, pageSize);
        IPage<Card> pageList = cardService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询已售卡
     *
     * @param card
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "card-分页列表查询")
    @ApiOperation(value = "card-分页列表查询", notes = "card-分页列表查询")
    @GetMapping(value = "/listSold")
    public Result<IPage<Card>> queryPageListSold(Card card, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<Card> queryWrapper = QueryGenerator.initQueryWrapper(card, req.getParameterMap());
        //queryWrapper.and(i -> i.isNotNull("order_id").or().isNotNull("handover_id"));
        Page<Card> page = new Page<Card>(pageNo, pageSize);
        IPage<Card> pageList = cardService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "card-写卡", notes = "card-写卡")
    @GetMapping(value = "/setDenomination")
    @RequiresPermissions("fee:card:setDenomination")
    public Result<?> setDenomination(Card card, HttpServletRequest req) {
        QueryWrapper<Card> queryWrapper = QueryGenerator.initQueryWrapper(card, req.getParameterMap());
        try {
            Card cardData = new Card();
            BigDecimal denomination = new BigDecimal(req.getParameter("denomination_data"));
            cardData.setDenomination(denomination);
            BigDecimal amount = new BigDecimal(req.getParameter("amount_data"));
            cardData.setAmount(amount);
            BigDecimal discountRate = new BigDecimal(req.getParameter("discountRate_data"));
            cardData.setDiscountRate(discountRate);
            String companyId = req.getParameter("companyId_data");
            cardData.setCompanyId(companyId);
            String companyName = req.getParameter("companyName_data");
            cardData.setCompanyName(companyName);
            String remark = req.getParameter("remark_data");
            cardData.setRemark(remark);

            BatchResult<Card> batchResult = cardService.setDenominationBatch(cardData, queryWrapper);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量写卡异常", e);
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "card-按照id批量写卡", notes = "card-按照id批量写卡")
    @PostMapping(value = "/setDenominationByIds")
    @RequiresPermissions("fee:card:setDenomination")
    public Result<?> setDenominationByIds(@RequestBody JSONObject info) {
        try {
            Card cardData = info.getObject("cardData", Card.class);
            List<String> idList = info.getJSONArray("idList").toJavaList(String.class);
            BatchResult<Card> batchResult = cardService.setDenominationBatchByIds(cardData, idList);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量写卡异常", e);
            return Result.error(e.getMessage());
        }
    }

    //批量发行
    @ApiOperation(value = "card-批量发行", notes = "card-批量发行")
    @PostMapping(value = "/releaseBatch")
    @RequiresPermissions("fee:card:release")
    public Result<?> releaseBatch(Card card, HttpServletRequest req) {
        QueryWrapper<Card> queryWrapper = QueryGenerator.initQueryWrapper(card, req.getParameterMap());
        try {
            BatchResult<Card> batchResult = cardService.releaseBatch(queryWrapper);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量发行异常", e);
            return Result.error(e.getMessage());
        }
    }

    //按照id批量发行
    @ApiOperation(value = "card-按照id批量发行", notes = "card-按照id批量发行")
    @PostMapping(value = "/releaseBatchByIds")
    @RequiresPermissions("fee:card:release")
    public Result<?> releaseBatchByIds(@RequestBody JSONObject info) {
        try {
            List<String> idList = info.getJSONArray("idList").toJavaList(String.class);
            BatchResult<Card> batchResult = cardService.releaseBatchByIds(idList);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量发行异常", e);
            return Result.error(e.getMessage());
        }
    }

    //批量锁定
    @ApiOperation(value = "card-批量锁定", notes = "card-批量锁定")
    @PostMapping(value = "/lockBatchByIds")
    @RequiresPermissions("fee:card:lock")
    public Result<?> lockBatchByIds(@RequestBody JSONObject info) {
        try {
            List<String> idList = info.getJSONArray("idList").toJavaList(String.class);
            String reason = info.getString("reason");
            BatchResult<Card> batchResult = cardService.lockBatchByIds(idList, reason);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量锁定异常", e);
            return Result.error(e.getMessage());
        }
    }

    //按照条件批量锁定
    @ApiOperation(value = "card-按照条件批量锁定", notes = "card-按照条件批量锁定")
    @PostMapping(value = "/lockBatch")
    @RequiresPermissions("fee:card:lock")
    public Result<?> lockBatch(Card card, HttpServletRequest req) {
        QueryWrapper<Card> queryWrapper = QueryGenerator.initQueryWrapper(card, req.getParameterMap());
        try {
            String reason = req.getParameter("reason");
            BatchResult<Card> batchResult = cardService.lockBatch(queryWrapper, reason);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量锁定异常", e);
            return Result.error(e.getMessage());
        }
    }

    //批量解锁
    @ApiOperation(value = "card-批量解锁", notes = "card-批量解锁")
    @PostMapping(value = "/unlockBatchByIds")
    @RequiresPermissions("fee:card:unlock")
    public Result<?> unlockBatchByIds(@RequestBody JSONObject info) {
        try {
            List<String> idList = info.getJSONArray("idList").toJavaList(String.class);
            BatchResult<Card> batchResult = cardService.unlockBatchByIds(idList);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量解锁异常", e);
            return Result.error(e.getMessage());
        }
    }

    //验证卡密
    @ApiOperation(value = "card-验证卡密", notes = "card-验证卡密")
    @PostMapping(value = "/checkCardPwd")
    public Result<?> checkCardPwd(@RequestBody JSONObject info) {
        try {
            String cardNo = info.getString("cardNo");
            String cardPwd = info.getString("cardPwd");
            boolean matched = cardService.checkCardPwd(cardNo, cardPwd);
            if (!matched) {
                return Result.error("卡密不正确！");
            }
            BigDecimal blance = cardTradeService.getBalanceByCardNo(cardNo);
            return Result.OK(blance);
        } catch (Exception e) {
            log.error("验证卡密异常", e);
            return Result.error(e.getMessage());
        }
    }

    //按照条件批量解锁
    @ApiOperation(value = "card-按照条件批量解锁", notes = "card-按照条件批量解锁")
    @PostMapping(value = "/unlockBatch")
    @RequiresPermissions("fee:card:unlock")
    public Result<?> unlockBatch(Card card, HttpServletRequest req) {
        QueryWrapper<Card> queryWrapper = QueryGenerator.initQueryWrapper(card, req.getParameterMap());
        try {
            BatchResult<Card> batchResult = cardService.unlockBatch(queryWrapper);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量解锁异常", e);
            return Result.error(e.getMessage());
        }
    }

    //批量作废
    @ApiOperation(value = "card-批量作废", notes = "card-批量作废")
    @PostMapping(value = "/invalidBatchByIds")
    @RequiresPermissions("fee:card:invalid")
    public Result<?> invalidBatchByIds(@RequestBody JSONObject info) {
        try {
            List<String> idList = info.getJSONArray("idList").toJavaList(String.class);
            BatchResult<Card> batchResult = cardService.invalidBatchByIds(idList);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量作废异常", e);
            return Result.error(e.getMessage());
        }
    }

    //按照条件批量作废
    @ApiOperation(value = "card-按照条件批量作废", notes = "card-按照条件批量作废")
    @PostMapping(value = "/invalidBatch")
    @RequiresPermissions("fee:card:invalid")
    public Result<?> invalidBatch(Card card, HttpServletRequest req) {
        QueryWrapper<Card> queryWrapper = QueryGenerator.initQueryWrapper(card, req.getParameterMap());
        try {
            BatchResult<Card> batchResult = cardService.invalidBatch(queryWrapper);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量作废异常", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 补卡
     */
    @ApiOperation(value = "card-补卡", notes = "card-补卡")
    @PostMapping(value = "/replaceCard")
    @RequiresPermissions("fee:card:replaceCard")
    public Result<?> replaceCard(@RequestBody JSONObject info) {
        try {
            String reason = info.getString("reason");
            String cardNo = info.getString("cardNo");
            String originCardId = info.getString("originCardId");
            cardService.replaceCard(reason, originCardId, cardNo);
            return Result.OK("操作成功！");
        } catch (Exception e) {
            log.error("补卡异常", e);
            return Result.error(e.getMessage());
        }
    }

    //批量重置卡
    @ApiOperation(value = "card-批量重置卡", notes = "card-批量重置卡")
    @PostMapping(value = "/resetBatchByIds")
    @RequiresPermissions("fee:card:reset")
    public Result<?> resetBatchByIds(@RequestBody JSONObject info) {
        try {
            List<String> idList = info.getJSONArray("idList").toJavaList(String.class);
            String reason = info.getString("reason");
            BatchResult<Card> batchResult = cardService.resetBatchByIds(idList, reason);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("批量重置异常", e);
            return Result.error(e.getMessage());
        }
    }
    /**
     * 获取最大卡号
     *
     * @return
     */
    @ApiOperation(value = "card-获取最大卡号", notes = "card-获取最大卡号")
    @GetMapping(value = "/getMaxCardNo")
    public Result<?> getMaxCardNo() {
        CardNo maxCardNo = cardService.getMaxCardNo();
        return Result.OK(maxCardNo);
    }

    /**
     * 根据卡序号获取卡列表
     *
     * @return
     */
    @ApiOperation(value = "card-根据卡序号获取卡列表", notes = "card-根据卡序号获取卡列表")
    @GetMapping(value = "/getCardByRange")
    public Result<?> getCardByRange(@RequestParam(name = "startNum", required = true) Integer startNum, @RequestParam(name = "endNum", required = true) Integer endNum) {
        List<Card> cardList = new ArrayList<>();
        try {
            cardList = cardService.getCardByRange(startNum, endNum);
            return Result.OK(cardList);
        } catch (Exception e) {
            log.error("card-根据卡序号获取卡列表异常", e);
            return Result.error("根据卡序号获取卡列表异常!");
        }
    }

    /**
     * 根据卡号获取卡
     *
     * @return
     */
    @ApiOperation(value = "card-根据卡号获取卡", notes = "card-根据卡号获取卡")
    @GetMapping(value = "/getCardByCardNo")
    public Result<?> getCardByCardNo(@RequestParam(name = "cardNo", required = true) String cardNo) {
        try {
            Card card = cardService.getCardByCardNo(cardNo);
            BigDecimal balanceByCardNo = cardTradeService.getBalanceByCardNo(cardNo);
            if (Objects.nonNull(balanceByCardNo)) {
                card.setCurrentBalance(balanceByCardNo);
            }
            //判断卡片状态
            if (card != null) {
                if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已锁定)) {
                    return Result.error("卡片已锁定！");
                } /*else if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已激活)) {
                    return Result.error("卡片已激活！");
                }*/ else if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已作废)) {
                    return Result.error("卡片已作废！");
                }
            }
            return Result.OK(card);
        } catch (Exception e) {
            log.error("card-根据卡号获取卡异常", e);
            return Result.error("根据卡号获取卡异常!");
        }
    }


    /**
     * 添加
     *
     * @param card
     * @return
     */
    @AutoLog(value = "card-添加")
    @ApiOperation(value = "card-添加", notes = "card-添加")
    @RequiresPermissions("fee:card:add")
    @PostMapping(value = "/addBatch")
    public Result<String> addBatch(@RequestBody Card card) {
        try {
            cardService.batchAddCard(card);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("添加失败！");
        }
        return Result.OK("添加成功！");
    }


    /**
     * 添加
     *
     * @param card
     * @return
     */
    @AutoLog(value = "card-添加")
    @ApiOperation(value = "card-添加", notes = "card-添加")
    @RequiresPermissions("fee:card:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody Card card) {
        cardService.save(card);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param card
     * @return
     */
    @AutoLog(value = "card-编辑")
    @ApiOperation(value = "card-编辑", notes = "card-编辑")
    @RequiresPermissions("fee:card:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody Card card) {
        cardService.updateById(card);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "card-通过id删除")
    @ApiOperation(value = "card-通过id删除", notes = "card-通过id删除")
    @RequiresPermissions("fee:card:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            cardService.deleteCardById(id);
            return Result.OK("删除成功!");
        } catch (Exception e) {
            log.error("card-通过id删除异常", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "card-批量删除")
    @ApiOperation(value = "card-批量删除", notes = "card-批量删除")
    @RequiresPermissions("fee:card:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        try {
            BatchResult<Card> result = cardService.deleteBatchByIds(Arrays.asList(ids.split(",")));
            return Result.OK("批量删除成功!", result);
        } catch (Exception e) {
            log.error("card-批量删除异常", e);
            return Result.error("删除失败!");
        }
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "card-通过id查询")
    @ApiOperation(value = "card-通过id查询", notes = "card-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Card> queryById(@RequestParam(name = "id", required = true) String id) {
        Card card = cardService.getById(id);
        if (card == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(card);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param card
     */
    @RequiresPermissions("fee:card:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Card card) {
        return super.exportXls(request, card, Card.class, "card");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("fee:card:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Card.class);
    }

}
