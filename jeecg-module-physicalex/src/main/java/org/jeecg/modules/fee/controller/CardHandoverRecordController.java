package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.PreventDuplicateSubmission;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.CardHandoverRecord;
import org.jeecg.modules.fee.service.ICardHandoverRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 体检卡换卡记录
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
@Api(tags = "体检卡换卡记录")
@RestController
@RequestMapping("/fee/cardHandoverRecord")
@Slf4j
public class CardHandoverRecordController extends JeecgController<CardHandoverRecord, ICardHandoverRecordService> {
    @Autowired
    private ICardHandoverRecordService cardHandoverRecordService;

    /**
     * 分页列表查询
     *
     * @param cardHandoverRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检卡换卡记录-分页列表查询")
    @ApiOperation(value = "体检卡换卡记录-分页列表查询", notes = "体检卡换卡记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CardHandoverRecord>> queryPageList(CardHandoverRecord cardHandoverRecord, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CardHandoverRecord> queryWrapper = QueryGenerator.initQueryWrapper(cardHandoverRecord, req.getParameterMap());
        Page<CardHandoverRecord> page = new Page<CardHandoverRecord>(pageNo, pageSize);
        IPage<CardHandoverRecord> pageList = cardHandoverRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param cardHandoverRecord
     * @return
     */
    @PreventDuplicateSubmission(timeout = 500)
    @AutoLog(value = "体检卡换卡记录-添加")
    @ApiOperation(value = "体检卡换卡记录-添加", notes = "体检卡换卡记录-添加")
    @RequiresPermissions("fee:card_handover_record:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CardHandoverRecord cardHandoverRecord) {
        try {
            cardHandoverRecordService.addCardHandoverRecord(cardHandoverRecord);
            return Result.OK("换卡成功！");
        } catch (Exception e) {
            log.error("换卡失败", e);
            return Result.error("换卡失败！详细原因：" + e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param cardHandoverRecord
     * @return
     */
    @PreventDuplicateSubmission(timeout = 500)
    @AutoLog(value = "体检卡换卡记录-编辑")
    @ApiOperation(value = "体检卡换卡记录-编辑", notes = "体检卡换卡记录-编辑")
    @RequiresPermissions("fee:card_handover_record:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CardHandoverRecord cardHandoverRecord) {
        try {
        cardHandoverRecordService.updateCardHandoverRecord(cardHandoverRecord);
        return Result.OK("编辑成功!");
        } catch (Exception e) {
            log.error("换卡失败", e);
            return Result.error("换卡失败！详细原因：" + e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检卡换卡记录-通过id删除")
    @ApiOperation(value = "体检卡换卡记录-通过id删除", notes = "体检卡换卡记录-通过id删除")
    @RequiresPermissions("fee:card_handover_record:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        cardHandoverRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检卡换卡记录-批量删除")
    @ApiOperation(value = "体检卡换卡记录-批量删除", notes = "体检卡换卡记录-批量删除")
    @RequiresPermissions("fee:card_handover_record:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.cardHandoverRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检卡换卡记录-通过id查询")
    @ApiOperation(value = "体检卡换卡记录-通过id查询", notes = "体检卡换卡记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CardHandoverRecord> queryById(@RequestParam(name = "id", required = true) String id) {
        CardHandoverRecord cardHandoverRecord = cardHandoverRecordService.getById(id);
        if (cardHandoverRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(cardHandoverRecord);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param cardHandoverRecord
     */
    @RequiresPermissions("fee:card_handover_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CardHandoverRecord cardHandoverRecord) {
        return super.exportXls(request, cardHandoverRecord, CardHandoverRecord.class, "体检卡换卡记录");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("fee:card_handover_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CardHandoverRecord.class);
    }

}
