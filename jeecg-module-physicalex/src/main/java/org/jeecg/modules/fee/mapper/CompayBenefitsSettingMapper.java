package org.jeecg.modules.fee.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.fee.entity.CompayBenefitsSetting;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 单位福利限额设置
 * @Author: jeecg-boot
 * @Date:   2025-04-01
 * @Version: V1.0
 */
public interface CompayBenefitsSettingMapper extends BaseMapper<CompayBenefitsSetting> {

    List<CompayBenefitsSetting> selectByTeamId(@Param("teamId") String teamId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
