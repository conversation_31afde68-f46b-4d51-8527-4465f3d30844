package org.jeecg.modules.fee.service;

import org.jeecg.excommons.BatchResult;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.fee.bo.PayResult;
import org.jeecg.modules.fee.bo.PaymentAnalysis;
import org.jeecg.modules.fee.entity.*;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: 结账单
 * @Author: jeecg-boot
 * @Date: 2024-12-20
 * @Version: V1.0
 */
public interface ICustomerRegBillService extends IService<CustomerRegBill> {

    List<CustomerRegBill> listByRegId(String regId);

    List<PayResult> payBill(CustomerRegBill regRecipe) throws Exception;

    void validateRecipe(CustomerRegBill regRecipe) throws Exception;

    List<CustomerRegBill> list4Refund(List<String> idList);

    void refundBatch(List<CustomerRegBill> recordList, String clientIp, String refundReason) throws Exception;

    void updatePayStatus4Bill(CustomerRegBill bill);

    PaymentAnalysis analysisPayment(List<CustomerRegItemGroup> customerRegItemGroupList, String regId) throws Exception;

    PaymentAnalysis analysisPayment(List<CustomerRegItemGroup> customerRegItemGroupList, CustomerReg reg) throws Exception;

    PaymentAnalysis analysisPayment(String regId) throws Exception;

    PaymentAnalysis analysisPayment(CustomerReg reg) throws Exception;

    PaymentAnalysis analysisPayment4ItemGroupIds(List<String> itemGroupIds, String regId) throws Exception;

    PaymentAnalysis generateBillAndPayCompanyPart(List<CustomerRegItemGroup> itemGroups, CustomerReg customerReg, String clientIp) throws Exception;

    void generateBillAndPayByCash(List<CustomerRegItemGroup> itemGroups, CustomerReg customerReg, String clientIp) throws Exception;

    BigDecimal getSpendAmountOfTeamByItemGroup(String idCard, String teamId) throws Exception;

    BigDecimal getSpendAmountOfTeamByFeePayRecord(String idCard, String teamId) throws Exception;

    List<String> fetchRelatedIdcards(String idCard);

    List<CustomerReg> fetchRelatedRegs(String customerId,String teamId);

    void invalidateBill(String billId, String clientIp) throws Exception;

    void invalidateRefundBill(String refundBillId, String clientIp) throws Exception;

    List<FeePayRecord> getRelatedCompanyPayRecords(String customerRegId);

    void fixOriginCompanyBill() throws Exception;

    List<FeePayRecord> getRelatedCompanyPayRecordsByIdacrd(String idCard);

    BatchResult<CustomerReg> sendFee2HisBatch(List<CustomerReg> list, String clientIp) throws Exception;

    void fixNoBillFeePayRecord() throws Exception;

    List<CustomerRegBill> listWithFeePayRecordsByRegId(String regId);

    CustomerRegBill listWithFeePayRecordsById(String id);

    CustomerRegBill generateBillAndPayByCard(CustomerOrder customerOrder, CustomerReg reg, List<CustomerRegItemGroup> itemGroups, String clientIp) throws Exception;

    List<CustomerRegBill> listWithFeePayRecords4RefundByOrderId(String orderId);

    List<FeeRecord> listFeeRecordsByBillId(String billId);

    void sendPayment2Jms(CustomerRegBill bill);

    void sendRefund2Jms(CustomerRegBill bill);

    Map<String,List<LimitOperationRecord>> getRelatedLimitAmountRecords(String customerRegId);


}
