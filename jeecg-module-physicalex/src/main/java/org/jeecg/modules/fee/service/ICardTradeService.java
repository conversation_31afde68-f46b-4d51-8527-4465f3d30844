package org.jeecg.modules.fee.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.fee.entity.CardTrade;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description: card_trade
 * @Author: jeecg-boot
 * @Date:   2024-07-14
 * @Version: V1.0
 */
public interface ICardTradeService extends IService<CardTrade> {

    BigDecimal getBalanceByCardNo(String cardNo);

    Page<CardTrade> pageCardTrade(Page<CardTrade> page, String cardNo, String tradeType, String createTimeStart, String createTimeEnd,String name, String examNo);

    Map<String,Object> getBalanceAndCompany(String cardNo);
}
