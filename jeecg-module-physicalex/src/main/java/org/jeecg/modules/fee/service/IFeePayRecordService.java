package org.jeecg.modules.fee.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.excommons.BatchResult;
import org.jeecg.modules.fee.bo.*;
import org.jeecg.modules.fee.entity.CustomerRegBill;
import org.jeecg.modules.fee.entity.FeePayRecord;
import org.jeecg.modules.fee.entity.FeeRefundRecord;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 收费记录
 * @Author: jeecg-boot
 * @Date: 2024-06-25
 * @Version: V1.0
 */
public interface IFeePayRecordService extends IService<FeePayRecord> {

    Page<CustomerReg> pageCustomer(Page<CustomerReg> page, String examNo, String idCard, String name, String regDateStart, String regDateEnd, String regStatus, String payStatus);

    BatchResult<CustomerReg> sendFee2HisBatch(List<CustomerReg> list, String clientIp) throws Exception;

    void payReceipt(FeePayRecord feePayRecord) throws Exception;

    void payOffline(FeePayRecord feePayRecord) throws Exception;

    void payByHis(FeePayRecord feeRefundRecord) throws Exception;

    void payCombined(FeePayRecord feePayRecord) throws Exception;

    BigDecimal payByCompany(FeePayRecord feePayRecord) throws Exception;

    void invalidatePayByCompany(FeePayRecord feePayRecord) throws Exception;

    void giveAway(FeePayRecord feePayRecord) throws Exception;

    void firstCheckAfterPay(FeePayRecord feePayRecord) throws Exception;

    String payOnline(FeePayRecord feeRefundRecord) throws Exception;

    BigDecimal getRemainAmount4Reg(String regId, String payerType);

    PayConfig getPayConfigFromSysSetting();

    List<TeamFeeStat> listTeamPayStat(String companyRegId, String accountCloseStatus);

    List<ItemGroupFeeStat> listGroupItemPayStat(String companyRegId);

    BigDecimal getPayedAmountOfCompanyReg(String companyRegId);

    CompanyFeeStat getCompanyFeeStat(String companyRegId);

    void refundBatch(List<FeePayRecord> recordList) throws Exception;

    void refundCombined(FeePayRecord parentRecordList) throws Exception;

    void refundHis(FeePayRecord feePayRecord) throws Exception;

    void refundHis4Combine(FeePayRecord feePayRecord) throws Exception;

    void refundOffline(FeePayRecord feePayRecord) throws Exception;

    void refundOnline(FeePayRecord feePayRecord) throws Exception;

    void refundCard(FeePayRecord feePayRecord) throws Exception;

    void refund4GiveAway(FeePayRecord feePayRecord) throws Exception;

    void refund4Company(FeePayRecord feePayRecord) throws Exception;

    void updateFeeState(ReceiptStatus receiptStatus) throws Exception;

    CustomerReg getCustomerRegWithFee(String customerRegId);

    void validatePaymentInfo(FeePayRecord feePayRecord) throws Exception;

    void payByCard(FeePayRecord feePayRecord) throws Exception;

    void invalidatePayByCard(FeePayRecord feePayRecord) throws Exception;

    List<FeePayRecord> getFeePayRecord4Refund(List<String> idList);

    void payByHis4Combine(FeePayRecord feePayRecord) throws Exception;

    BigDecimal getPaidAmountOfBill(String billId);

    boolean isAllBillPaied(CustomerRegBill bill);

    List<FeePayRecord> getByBillId(String billId);

    void invalidHisPayRecord(FeePayRecord feePayRecord) throws Exception;

    void invalidHisRefundRecord(FeeRefundRecord refundRecord) throws Exception;

    void refundHis4RelatedRecipe(FeePayRecord feePayRecord) throws Exception;
}
