package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.CardOrderState;
import org.jeecg.modules.fee.service.ICardOrderStateService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 卡订单状态记录
 * @Author: jeecg-boot
 * @Date:   2024-10-11
 * @Version: V1.0
 */
@Api(tags="卡订单状态记录")
@RestController
@RequestMapping("/fee/cardOrderState")
@Slf4j
public class CardOrderStateController extends JeecgController<CardOrderState, ICardOrderStateService> {
	@Autowired
	private ICardOrderStateService cardOrderStateService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cardOrderState
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "卡订单状态记录-分页列表查询")
	@ApiOperation(value="卡订单状态记录-分页列表查询", notes="卡订单状态记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CardOrderState>> queryPageList(CardOrderState cardOrderState,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CardOrderState> queryWrapper = QueryGenerator.initQueryWrapper(cardOrderState, req.getParameterMap());
		Page<CardOrderState> page = new Page<CardOrderState>(pageNo, pageSize);
		IPage<CardOrderState> pageList = cardOrderStateService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cardOrderState
	 * @return
	 */
	@AutoLog(value = "卡订单状态记录-添加")
	@ApiOperation(value="卡订单状态记录-添加", notes="卡订单状态记录-添加")
	@RequiresPermissions("fee:card_order_state:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CardOrderState cardOrderState) {
		cardOrderStateService.save(cardOrderState);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cardOrderState
	 * @return
	 */
	@AutoLog(value = "卡订单状态记录-编辑")
	@ApiOperation(value="卡订单状态记录-编辑", notes="卡订单状态记录-编辑")
	@RequiresPermissions("fee:card_order_state:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CardOrderState cardOrderState) {
		cardOrderStateService.updateById(cardOrderState);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "卡订单状态记录-通过id删除")
	@ApiOperation(value="卡订单状态记录-通过id删除", notes="卡订单状态记录-通过id删除")
	@RequiresPermissions("fee:card_order_state:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cardOrderStateService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "卡订单状态记录-批量删除")
	@ApiOperation(value="卡订单状态记录-批量删除", notes="卡订单状态记录-批量删除")
	@RequiresPermissions("fee:card_order_state:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cardOrderStateService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "卡订单状态记录-通过id查询")
	@ApiOperation(value="卡订单状态记录-通过id查询", notes="卡订单状态记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CardOrderState> queryById(@RequestParam(name="id",required=true) String id) {
		CardOrderState cardOrderState = cardOrderStateService.getById(id);
		if(cardOrderState==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cardOrderState);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cardOrderState
    */
    @RequiresPermissions("fee:card_order_state:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CardOrderState cardOrderState) {
        return super.exportXls(request, cardOrderState, CardOrderState.class, "卡订单状态记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:card_order_state:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CardOrderState.class);
    }

}
