package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.LimitOperationRecord;
import org.jeecg.modules.fee.entity.TeamCustomerLimitAmount;
import org.jeecg.modules.fee.service.ITeamCustomerLimitAmountService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: team_customer_limit_amount
 * @Author: jeecg-boot
 * @Date:   2025-04-12
 * @Version: V1.0
 */
@Api(tags="team_customer_limit_amount")
@RestController
@RequestMapping("/fee/teamCustomerLimitAmount")
@Slf4j
public class TeamCustomerLimitAmountController extends JeecgController<TeamCustomerLimitAmount, ITeamCustomerLimitAmountService> {
	@Autowired
	private ITeamCustomerLimitAmountService teamCustomerLimitAmountService;
	
	/**
	 * 分页列表查询
	 *
	 * @param teamCustomerLimitAmount
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "team_customer_limit_amount-分页列表查询")
	@ApiOperation(value="team_customer_limit_amount-分页列表查询", notes="team_customer_limit_amount-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TeamCustomerLimitAmount>> queryPageList(TeamCustomerLimitAmount teamCustomerLimitAmount,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<TeamCustomerLimitAmount> queryWrapper = QueryGenerator.initQueryWrapper(teamCustomerLimitAmount, req.getParameterMap());
		Page<TeamCustomerLimitAmount> page = new Page<TeamCustomerLimitAmount>(pageNo, pageSize);
		IPage<TeamCustomerLimitAmount> pageList = teamCustomerLimitAmountService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	 @AutoLog(value = "团检额度-操作记录")
	 @ApiOperation(value = "团检额度-操作记录", notes = "团检额度-操作记录")
	 @GetMapping(value = "/getLimitAmountRecordsByRegId")
	 public Result<?> getLimitAmountRecordsByRegId(String customerRegId) {
		 TeamCustomerLimitAmount records = teamCustomerLimitAmountService.getLimitAmountRecordsByRegId(customerRegId);
		 return Result.OK(records);
	 }
	/**
	 *   添加
	 *
	 * @param teamCustomerLimitAmount
	 * @return
	 */
	@AutoLog(value = "team_customer_limit_amount-添加")
	@ApiOperation(value="team_customer_limit_amount-添加", notes="team_customer_limit_amount-添加")
	@RequiresPermissions("fee:team_customer_limit_amount:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TeamCustomerLimitAmount teamCustomerLimitAmount) {
		teamCustomerLimitAmountService.save(teamCustomerLimitAmount);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param teamCustomerLimitAmount
	 * @return
	 */
	@AutoLog(value = "team_customer_limit_amount-编辑")
	@ApiOperation(value="team_customer_limit_amount-编辑", notes="team_customer_limit_amount-编辑")
	@RequiresPermissions("fee:team_customer_limit_amount:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TeamCustomerLimitAmount teamCustomerLimitAmount) {
		teamCustomerLimitAmountService.updateById(teamCustomerLimitAmount);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "team_customer_limit_amount-通过id删除")
	@ApiOperation(value="team_customer_limit_amount-通过id删除", notes="team_customer_limit_amount-通过id删除")
	@RequiresPermissions("fee:team_customer_limit_amount:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		teamCustomerLimitAmountService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "team_customer_limit_amount-批量删除")
	@ApiOperation(value="team_customer_limit_amount-批量删除", notes="team_customer_limit_amount-批量删除")
	@RequiresPermissions("fee:team_customer_limit_amount:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.teamCustomerLimitAmountService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "team_customer_limit_amount-通过id查询")
	@ApiOperation(value="team_customer_limit_amount-通过id查询", notes="team_customer_limit_amount-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TeamCustomerLimitAmount> queryById(@RequestParam(name="id",required=true) String id) {
		TeamCustomerLimitAmount teamCustomerLimitAmount = teamCustomerLimitAmountService.getById(id);
		if(teamCustomerLimitAmount==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(teamCustomerLimitAmount);
	}
	 @ApiOperation(value="team_customer_limit_amount-通过id查询", notes="team_customer_limit_amount-通过id查询")
	 @GetMapping(value = "/fixOldLimitAmount")
	 public Result<TeamCustomerLimitAmount> fixOldLimitAmount(@RequestParam(name="id",required=true) String id) {
		 try {
			 teamCustomerLimitAmountService.fixOldLimitAmount();
			 return Result.OK("处理成功");
		 } catch (Exception e) {
			 throw new RuntimeException(e);
		 }

	 }
	 @ApiOperation(value="team_customer_limit_amount-通过id查询", notes="team_customer_limit_amount-通过id查询")
	 @GetMapping(value = "/fixOldLimitAmountOperationRecord")
	 public Result<TeamCustomerLimitAmount> fixOldLimitAmountOperationRecord(@RequestParam(name="id",required=true) String id) {
		 try {
			 teamCustomerLimitAmountService.fixOldLimitAmountOperationRecord();
			 return Result.OK("处理成功");
		 } catch (Exception e) {
			 throw new RuntimeException(e);
		 }

	 }
    /**
    * 导出excel
    *
    * @param request
    * @param teamCustomerLimitAmount
    */
    @RequiresPermissions("fee:team_customer_limit_amount:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TeamCustomerLimitAmount teamCustomerLimitAmount) {
        return super.exportXls(request, teamCustomerLimitAmount, TeamCustomerLimitAmount.class, "team_customer_limit_amount");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:team_customer_limit_amount:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TeamCustomerLimitAmount.class);
    }

}
