package org.jeecg.modules.fee.controller;

import com.alibaba.fastjson.JSONObject;
import com.jeequan.jeepay.util.JeepayKit;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.JeePayUtils;
import org.jeecg.modules.fee.WsPayOrderServer;
import org.jeecg.modules.fee.entity.FeePayRecord;
import org.jeecg.modules.fee.entity.TMchApp;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.fee.service.ITMchAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Date;

@Api(tags = "收款通知地址")
@RestController
@RequestMapping("/fee/anon/paytestNotify/")
@Slf4j
public class PayNotifyController {

    @Autowired
    private ITMchAppService mchAppService;
    @Autowired
    private IFeePayRecordService feePayRecordService;

    @RequestMapping("/payOrder")
    public String payOrderNotify(HttpServletRequest request) throws IOException {
        try {
            JSONObject params = JeePayUtils.reqParam2JSON(request);
            String appId = request.getParameter("appId");
            String mchNo = request.getParameter("mchNo");
            String sign = request.getParameter("sign");
            TMchApp mchApp = mchAppService.getById(appId);
            if (mchApp == null || !mchApp.getMchNo().equals(mchNo)) {
                return "app is not exists";
            }

            params.remove("sign");
            if (!JeepayKit.getSign(params, mchApp.getAppSecret()).equalsIgnoreCase(sign)) {
                return "sign fail";
            }

            String interfaceCode = params.getString("ifCode");
            Integer amount = params.getInteger("amount");
            String payOrderNo = params.getString("payOrderId");
            String mchOrderNo = params.getString("mchOrderNo");
            String wayCode = params.getString("wayCode");
            String channelOrderNo = params.getString("channelOrderNo");
            String successTime = params.getString("successTime");
            String state = params.getString("state");

            FeePayRecord feePayRecord = feePayRecordService.getById(mchOrderNo);
            if(feePayRecord == null){
                return "mchOrderNo is not exists";
            }
            String payChannel = "";
            switch (interfaceCode)
            {
                case "wxpay":
                    payChannel = "微信支付";
                    break;
                case "alipay":
                    payChannel = "支付宝支付";
                    break;
                case "unionpay":
                    payChannel = "银联支付";
                    break;
                case "qqpay":
                    payChannel = "QQ支付";
                    break;
                case "jdpay":
                    payChannel = "京东支付";
                    break;
                case "bdpay":
                    payChannel = "百度支付";
                    break;
                default:
                    payChannel = "其他支付";
                    break;
            }

            feePayRecord.setPayChannel(payChannel);
            feePayRecord.setPayChannelWay(wayCode);
            feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
            feePayRecord.setChannelOrderNo(channelOrderNo);
            feePayRecord.setSuccessTime(new Date());
            feePayRecordService.updateById(feePayRecord);

            JSONObject msg = new JSONObject();
            msg.put("state", params.getIntValue("state"));
            msg.put("errCode", params.getString("errCode"));
            msg.put("errMsg", params.getString("errMsg"));
            //推送到前端
            WsPayOrderServer.sendMsgByOrderId(params.getString("payOrderId"), msg.toJSONString());

            return "SUCCESS";
        } catch (Exception e) {
            return "解析参数异常";
        }
    }
}
