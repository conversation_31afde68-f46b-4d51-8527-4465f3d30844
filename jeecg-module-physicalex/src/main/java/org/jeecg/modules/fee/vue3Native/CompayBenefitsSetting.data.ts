import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '单位预约',
    align: "center",
    dataIndex: 'companyRegId_dictText'
  },
  {
    title: '所属单位',
    align: "center",
    dataIndex: 'companyId_dictText'
  },
  {
    title: '有效标志',
    align: "center",
    dataIndex: 'validFlag',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}]);
     },
  },
  {
    title: '开始时间',
    align: "center",
    dataIndex: 'startTime'
  },
  {
    title: '到期时间',
    align: "center",
    dataIndex: 'endTime'
  },
  {
    title: '到期清空',
    align: "center",
    dataIndex: 'clearOnExpired',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}]);
     },
  },
];

// 高级查询数据
export const superQuerySchema = {
  companyRegId: {title: '单位预约',order: 0,view: 'list', type: 'string',dictTable: "company_reg where lock_status=0", dictCode: 'id', dictText: 'reg_name',},
  companyId: {title: '所属单位',order: 1,view: 'list', type: 'string',dictTable: "company where del_flag=0", dictCode: 'id', dictText: 'name',},
  validFlag: {title: '有效标志',order: 2,view: 'switch', type: 'string',},
  startTime: {title: '开始时间',order: 3,view: 'datetime', type: 'string',},
  endTime: {title: '到期时间',order: 4,view: 'datetime', type: 'string',},
  clearOnExpired: {title: '到期清空',order: 5,view: 'switch', type: 'string',},
};
