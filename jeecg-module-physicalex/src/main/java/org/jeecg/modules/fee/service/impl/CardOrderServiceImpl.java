package org.jeecg.modules.fee.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mzlion.easyokhttp.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExApiConstants;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.fee.entity.Card;
import org.jeecg.modules.fee.entity.CardOrder;
import org.jeecg.modules.fee.entity.CardTrade;
import org.jeecg.modules.fee.entity.FeePayRecord;
import org.jeecg.modules.fee.mapper.CardMapper;
import org.jeecg.modules.fee.mapper.CardOrderMapper;
import org.jeecg.modules.fee.mapper.CardTradeMapper;
import org.jeecg.modules.fee.mapper.FeePayRecordMapper;
import org.jeecg.modules.fee.service.ICardOrderService;
import org.jeecg.modules.fee.service.ICardOrderStateService;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerMapper;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.reg.service.ICustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 体检卡订单
 * @Author: jeecg-boot
 * @Date: 2024-10-11
 * @Version: V1.0
 */
@Service
@Slf4j
public class CardOrderServiceImpl extends ServiceImpl<CardOrderMapper, CardOrder> implements ICardOrderService {

    @Autowired
    private CardOrderMapper cardOrderMapper;
    @Autowired
    private ICardOrderStateService cardOrderStateService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private FeePayRecordMapper feePayRecordMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private CardTradeMapper cardTradeMapper;
    @Autowired
    private CardMapper cardMapper;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private SequenceGenerator sequenceGenerator;

    @Override
    public CardOrder saveCardOrder(CardOrder cardOrder) throws Exception {
        cardOrder.setStatus(ExConstants.CRAD_ORDER_STATUS_待支付);
        save(cardOrder);
        //保存订单状态
        cardOrderStateService.saveCardOrderState(cardOrder);

        String payMode = cardOrder.getPayMode();
        if (StringUtils.equals(payMode, ExConstants.PAY_CHANNEL_门诊)) {
            Customer customer = customerMapper.selectById(cardOrder.getCustomerId());
            CustomerReg customerReg = new CustomerReg();
            customerReg.setCustomerId(cardOrder.getCustomerId());
            customerReg.setExamNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_EXAMNO));
            customerReg.setName(customer.getName());
            customerReg.setRecipeTitle(cardOrder.getInvoiceTitle());
            customerReg.setCompanyName(cardOrder.getCompanyName());
            customerReg.setHisPid(customer.getHisPid());
            customerReg.setHisVisitNo(customer.getIcCardNo());
            if (StringUtils.equals(cardOrder.getBuyerType(), ExConstants.BUYER_TYPE_单位) && StringUtils.equals(cardOrder.getAllowPayLater(), "1")) {
                return cardOrder;
            }
            customerRegService.sendCustomerReg2Mq(customerReg);
            customerRegService.addCustomerReg2Interface(customerReg);
        }

        return cardOrder;
    }

    @Override
    public void payByHis(CardOrder cardOrder, String clientIp) throws Exception {
        //校验
        if (cardOrder == null) {
            throw new Exception("订单不存在");
        }
        if (StringUtils.equals(cardOrder.getStatus(), ExConstants.CRAD_ORDER_STATUS_已支付)) {
            throw new Exception("订单已支付");
        }
        Customer customer = customerMapper.selectById(cardOrder.getCustomerId());
        if (customer == null) {
            throw new Exception("联系人档案不存在，无法向HIS发起支付");
        }

        FeePayRecord feePayRecord = new FeePayRecord();
        feePayRecord.setCustomer(customer);
        feePayRecord.setState(ExConstants.PAY_STATE_订单生成);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setBizId(cardOrder.getId());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检卡费);
        feePayRecord.setAmount(cardOrder.getAmount());
        feePayRecord.setChannelOrderNo(cardOrder.getId());
        feePayRecord.setCurrency("CNY");
        feePayRecord.setClientIp(clientIp);
        feePayRecord.setName(cardOrder.getConcat());
        feePayRecord.setWayCode(ExConstants.PAY_CHANNEL_门诊);
        feePayRecord.setPayChannel(ExConstants.PAY_CHANNEL_门诊);
        feePayRecordMapper.insert(feePayRecord);


        BigDecimal totalAmount = cardOrder.getAmount();
        if (totalAmount.compareTo(BigDecimal.valueOf(0)) > 0) {
            String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
            hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
            log.info("体检卡订单收费接口发送：" + JSONObject.toJSONString(feePayRecord));
            String finalUrl = hipInterfaceUrl + ExApiConstants.CARD_FEE_PATH;
            String resultStr = HttpClient.textBody(finalUrl).json(JSONObject.toJSONString(feePayRecord)).execute().asString();
            log.info("体检卡订单收费接口返回：" + resultStr);
            JSONObject result = JSONObject.parseObject(resultStr);

            if (result != null && result.getInteger("code") == 0) {
                JSONObject data = result.getJSONObject("data");
                String applyNo = data.getString("applyNo");
                if (StringUtils.isBlank(applyNo)) {
                    throw new Exception(StringUtils.isNotBlank(result.getString("message")) ? result.getString("message") : "体检卡订单HIS接口未返回收费号！");
                }
                jdbcTemplate.update("update  fee_pay_record set his_apply_no=? where id=?", applyNo, feePayRecord.getId());

                //更新订单状态
                cardOrder.setHisApplyNo(applyNo);
                cardOrder.setStatus(ExConstants.CRAD_ORDER_STATUS_支付中);
                cardOrder.setFeeRecordId(feePayRecord.getId());
                cardOrderMapper.updateById(cardOrder);
            } else {
                log.error("体检卡订单收费接口失败，FeePayRecord：" + JSONObject.toJSONString(feePayRecord));
                throw new Exception("体检卡订单HIS收费接口失败！");
            }
        }
    }

    @Override
    public CardOrder saveCardOrder4SmallApp(CardOrder cardOrder) {
        cardOrder.setStatus(ExConstants.CRAD_ORDER_STATUS_待支付);
        String customerId = cardOrder.getCustomerId();
        Customer customer = customerService.getById(customerId);
        cardOrder.setIdCard(customer.getIdCard());
        cardOrder.setConcat(customer.getName());
        cardOrder.setPhone(customer.getPhone());
        cardOrder.setBuyerType("个人");
        cardOrder.setDeliverStatus("待交付");
        cardOrder.setOriginPrice(cardOrder.getAmount());
        cardOrder.setQuantityDelivered(0);
        cardOrder.setSaleChannel("线上");
        save(cardOrder);
        //保存订单状态
        try {
            cardOrderStateService.saveCardOrderState(cardOrder);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return cardOrder;
    }

    @Override
    public void invalidCardOrder(String id) throws Exception {
        CardOrder cardOrder = getById(id);
        if (cardOrder == null) {
            throw new Exception("订单不存在");
        }
        cardOrder.setStatus(ExConstants.CRAD_ORDER_STATUS_已作废);
        updateById(cardOrder);

        //更新订单状态
        cardOrderStateService.saveCardOrderState(cardOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchResult<Card> writeCard4Order(String orderId, List<Card> cardList) throws Exception {
        CardOrder cardOrder = getById(orderId);
        if (cardOrder == null) {
            throw new Exception("订单不存在!");
        }
        if (cardList == null || cardList.isEmpty()) {
            throw new Exception("未提供卡片！");
        }
        if (!StringUtils.equals(cardOrder.getAllowPayLater(), "1") && !StringUtils.equals(cardOrder.getStatus(), ExConstants.CRAD_ORDER_STATUS_已支付)) {
            throw new Exception("订单未支付，不能写卡！");
        }
        Integer quantityDelivered = cardOrder.getQuantityDelivered();
        quantityDelivered = quantityDelivered == null ? 0 : quantityDelivered;
        if (quantityDelivered + cardList.size() > cardOrder.getGoodsCount()) {
            throw new Exception("卡数量超出订单数量！");
        }

        //使用Jdbctemplate批量更新card表的orderId和goodsId字段
        BatchResult<Card> result = new BatchResult<>();
        List<BatchResult.FailureResult<Card>> failureResults = new ArrayList<>();
        List<Card> successList = new ArrayList<>();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        for (Card card : cardList) {
            try {

                if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已激活)) {
                    failureResults.add(new BatchResult.FailureResult<>(card, "卡片已激活，不可重复售卖！"));
                    continue;
                }
                if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已锁定)) {
                    failureResults.add(new BatchResult.FailureResult<>(card, "卡片已锁定，不可售卖！"));
                    continue;
                }
                if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已作废)) {
                    failureResults.add(new BatchResult.FailureResult<>(card, "卡片已作废，不可售卖！"));
                    continue;
                }

                card.setOrderId(orderId);
                card.setGoodsId(cardOrder.getGoodsId());
                card.setStatus(ExConstants.CRAD_STATUS_已激活);
                card.setProduceTime(new Date());
                card.setProduceBy(cardOrder.getCreateBy());
                card.setSaleTime(new Date());
                card.setSaleBy(loginUser.getUsername());
                card.setDenomination(cardOrder.getOriginPrice());
                card.setAmount(cardOrder.getPrice());
                card.setBalance(cardOrder.getPrice());
                cardMapper.updateById(card);

                CardTrade cardTrade = new CardTrade();
                cardTrade.setCardId(card.getId());
                cardTrade.setTradeType(ExConstants.CARD_TRADE_TYPE_入);
                cardTrade.setAmount(cardOrder.getOriginPrice());
                cardTrade.setBlance(cardOrder.getOriginPrice());
                cardTrade.setCardNo(card.getCardNo());
                cardTrade.setBizId(orderId);
                cardTrade.setStatus(ExConstants.CARD_TRADE_STATUS_成功);
                cardTrade.setAmountActual(cardOrder.getPrice());
                cardTradeMapper.insert(cardTrade);

                successList.add(card);
            } catch (Exception e) {
                log.error("写卡失败", e);
                failureResults.add(new BatchResult.FailureResult<>(card, e.getMessage()));
            }
        }


        //更新订单状态
        cardOrder.setQuantityDelivered(quantityDelivered + cardList.size());
        cardOrder.setStatus(ExConstants.CRAD_ORDER_STATUS_已收货);
        if (cardOrder.getQuantityDelivered().equals(cardOrder.getGoodsCount())) {
            cardOrder.setDeliverStatus("全部交付");
        } else {
            cardOrder.setDeliverStatus("部分交付");
        }
        updateById(cardOrder);
        cardOrderStateService.saveCardOrderState(cardOrder);

        //更新card_goods库存字段
        jdbcTemplate.update("update card_goods set stock=stock-? where id=?", successList.size(), cardOrder.getGoodsId());

        result.setFailureResults(failureResults);
        result.setSuccessResults(successList);
        return result;
    }

    @Override
    public CardOrder getByCardId(String cardId) {
        LambdaQueryWrapper<CardOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CardOrder::getId, cardId);
        queryWrapper.last("limit 1");
        return getOne(queryWrapper);
    }

}
