package org.jeecg.modules.fee.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.fee.entity.TeamCustomerLimitAmount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: team_customer_limit_amount
 * @Author: jeecg-boot
 * @Date:   2025-04-12
 * @Version: V1.0
 */
public interface TeamCustomerLimitAmountMapper extends BaseMapper<TeamCustomerLimitAmount> {

    TeamCustomerLimitAmount getCustomerLimitAmountByRegId(@Param("regId") String regId);
    TeamCustomerLimitAmount getCustomerLimitAmountById(@Param("id") String id);

    TeamCustomerLimitAmount getLatestLimit(@Param("teamId") String teamId,@Param("customerId") String customerId,@Param("idCard") String idCard);

    TeamCustomerLimitAmount getLatestLimitByIdCard(@Param("idCard") String idCard);

}
