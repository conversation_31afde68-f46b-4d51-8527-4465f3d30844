package org.jeecg.modules.fee.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.fee.entity.LimitOperationRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 体检限额操作记录
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
public interface LimitOperationRecordMapper extends BaseMapper<LimitOperationRecord> {

}
