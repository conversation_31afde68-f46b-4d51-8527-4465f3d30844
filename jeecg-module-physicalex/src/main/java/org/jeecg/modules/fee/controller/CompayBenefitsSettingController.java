package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.CompayBenefitsSetting;
import org.jeecg.modules.fee.service.ICompayBenefitsSettingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 单位福利限额设置
 * @Author: jeecg-boot
 * @Date:   2025-04-01
 * @Version: V1.0
 */
@Api(tags="单位福利限额设置")
@RestController
@RequestMapping("/fee/compayBenefitsSetting")
@Slf4j
public class CompayBenefitsSettingController extends JeecgController<CompayBenefitsSetting, ICompayBenefitsSettingService> {
	@Autowired
	private ICompayBenefitsSettingService compayBenefitsSettingService;
	
	/**
	 * 分页列表查询
	 *
	 * @param compayBenefitsSetting
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "单位福利限额设置-分页列表查询")
	@ApiOperation(value="单位福利限额设置-分页列表查询", notes="单位福利限额设置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CompayBenefitsSetting>> queryPageList(CompayBenefitsSetting compayBenefitsSetting,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CompayBenefitsSetting> queryWrapper = QueryGenerator.initQueryWrapper(compayBenefitsSetting, req.getParameterMap());
		Page<CompayBenefitsSetting> page = new Page<CompayBenefitsSetting>(pageNo, pageSize);
		IPage<CompayBenefitsSetting> pageList = compayBenefitsSettingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param compayBenefitsSetting
	 * @return
	 */
	@AutoLog(value = "单位福利限额设置-添加")
	@ApiOperation(value="单位福利限额设置-添加", notes="单位福利限额设置-添加")
	@RequiresPermissions("fee:compay_benefits_setting:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CompayBenefitsSetting compayBenefitsSetting) {
		compayBenefitsSettingService.save(compayBenefitsSetting);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param compayBenefitsSetting
	 * @return
	 */
	@AutoLog(value = "单位福利限额设置-编辑")
	@ApiOperation(value="单位福利限额设置-编辑", notes="单位福利限额设置-编辑")
	@RequiresPermissions("fee:compay_benefits_setting:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CompayBenefitsSetting compayBenefitsSetting) {
		compayBenefitsSettingService.updateById(compayBenefitsSetting);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "单位福利限额设置-通过id删除")
	@ApiOperation(value="单位福利限额设置-通过id删除", notes="单位福利限额设置-通过id删除")
	@RequiresPermissions("fee:compay_benefits_setting:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		compayBenefitsSettingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "单位福利限额设置-批量删除")
	@ApiOperation(value="单位福利限额设置-批量删除", notes="单位福利限额设置-批量删除")
	@RequiresPermissions("fee:compay_benefits_setting:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.compayBenefitsSettingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "单位福利限额设置-通过id查询")
	@ApiOperation(value="单位福利限额设置-通过id查询", notes="单位福利限额设置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CompayBenefitsSetting> queryById(@RequestParam(name="id",required=true) String id) {
		CompayBenefitsSetting compayBenefitsSetting = compayBenefitsSettingService.getById(id);
		if(compayBenefitsSetting==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(compayBenefitsSetting);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param compayBenefitsSetting
    */
    @RequiresPermissions("fee:compay_benefits_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CompayBenefitsSetting compayBenefitsSetting) {
        return super.exportXls(request, compayBenefitsSetting, CompayBenefitsSetting.class, "单位福利限额设置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:compay_benefits_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CompayBenefitsSetting.class);
    }

}
