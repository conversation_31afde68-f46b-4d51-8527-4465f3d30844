package org.jeecg.modules.fee.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.fee.entity.CardTrade;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.reg.entity.CustomerReg;

/**
 * @Description: card_trade
 * @Author: jeecg-boot
 * @Date:   2024-07-14
 * @Version: V1.0
 */
public interface CardTradeMapper extends BaseMapper<CardTrade> {

    BigDecimal getBalance(@Param("cardNo") String cardNo);

    Page<CardTrade> pageCardTrade(Page<CardTrade> page, @Param("cardNo") String cardNo, @Param("tradeType") String tradeType, @Param("createTimeStart") String createTimeStart, @Param("createTimeEnd") String createTimeEnd,@Param("name")String name, @Param("examNo")String examNo);


    String getCompanyName(String cardNo);
}
