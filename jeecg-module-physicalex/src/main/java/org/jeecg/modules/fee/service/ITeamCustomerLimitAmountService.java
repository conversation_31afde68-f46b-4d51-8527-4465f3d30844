package org.jeecg.modules.fee.service;

import org.jeecg.modules.fee.entity.TeamCustomerLimitAmount;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: team_customer_limit_amount
 * @Author: jeecg-boot
 * @Date:   2025-04-12
 * @Version: V1.0
 */
public interface ITeamCustomerLimitAmountService extends IService<TeamCustomerLimitAmount> {
     void fixOldLimitAmount();

     void fixOldLimitAmountOperationRecord();

    TeamCustomerLimitAmount getLimitAmountRecordsByRegId(String customerRegId);

}
