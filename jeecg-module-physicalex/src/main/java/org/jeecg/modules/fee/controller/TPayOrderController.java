package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.TPayOrder;
import org.jeecg.modules.fee.service.ITPayOrderService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 支付网关-支付订单
 * @Author: jeecg-boot
 * @Date:   2024-07-02
 * @Version: V1.0
 */
@Api(tags="支付网关-支付订单")
@RestController
@RequestMapping("/fee/tPayOrder")
@Slf4j
public class TPayOrderController extends JeecgController<TPayOrder, ITPayOrderService> {
	@Autowired
	private ITPayOrderService tPayOrderService;
	
	/**
	 * 分页列表查询
	 *
	 * @param tPayOrder
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "支付网关-支付订单-分页列表查询")
	@ApiOperation(value="支付网关-支付订单-分页列表查询", notes="支付网关-支付订单-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TPayOrder>> queryPageList(TPayOrder tPayOrder,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<TPayOrder> queryWrapper = QueryGenerator.initQueryWrapper(tPayOrder, req.getParameterMap());
		Page<TPayOrder> page = new Page<TPayOrder>(pageNo, pageSize);
		IPage<TPayOrder> pageList = tPayOrderService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param tPayOrder
	 * @return
	 */
	@AutoLog(value = "支付网关-支付订单-添加")
	@ApiOperation(value="支付网关-支付订单-添加", notes="支付网关-支付订单-添加")
	@RequiresPermissions("fee:t_pay_order:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TPayOrder tPayOrder) {
		tPayOrderService.save(tPayOrder);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param tPayOrder
	 * @return
	 */
	@AutoLog(value = "支付网关-支付订单-编辑")
	@ApiOperation(value="支付网关-支付订单-编辑", notes="支付网关-支付订单-编辑")
	@RequiresPermissions("fee:t_pay_order:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TPayOrder tPayOrder) {
		tPayOrderService.updateById(tPayOrder);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "支付网关-支付订单-通过id删除")
	@ApiOperation(value="支付网关-支付订单-通过id删除", notes="支付网关-支付订单-通过id删除")
	@RequiresPermissions("fee:t_pay_order:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		tPayOrderService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "支付网关-支付订单-批量删除")
	@ApiOperation(value="支付网关-支付订单-批量删除", notes="支付网关-支付订单-批量删除")
	@RequiresPermissions("fee:t_pay_order:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.tPayOrderService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "支付网关-支付订单-通过id查询")
	@ApiOperation(value="支付网关-支付订单-通过id查询", notes="支付网关-支付订单-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TPayOrder> queryById(@RequestParam(name="id",required=true) String id) {
		TPayOrder tPayOrder = tPayOrderService.getById(id);
		if(tPayOrder==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tPayOrder);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param tPayOrder
    */
    @RequiresPermissions("fee:t_pay_order:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TPayOrder tPayOrder) {
        return super.exportXls(request, tPayOrder, TPayOrder.class, "支付网关-支付订单");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:t_pay_order:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TPayOrder.class);
    }

}
