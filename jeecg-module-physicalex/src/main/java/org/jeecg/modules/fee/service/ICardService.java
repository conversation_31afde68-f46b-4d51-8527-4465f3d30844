package org.jeecg.modules.fee.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.excommons.BatchResult;
import org.jeecg.modules.fee.bo.CardNo;
import org.jeecg.modules.fee.entity.Card;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: card
 * @Author: jeecg-boot
 * @Date: 2024-07-14
 * @Version: V1.0
 */
public interface ICardService extends IService<Card> {


    List<CardNo> getCardNo(Integer count,String prefix) throws Exception;

    CardNo getMaxCardNo();

    String formatCardNo(Integer cardNumber, String prefix, Integer cardLength);

    void batchAddCard(Card card) throws Exception;

    void addCard(Card card) throws Exception;

    String generateRadomSecret();

    void markCardAsProducedByCardNo(List<String> cardNo) throws Exception;

    void markCardAsUnProducedByCardNo(List<String> cardNo) throws Exception;

    void markCardAsReleasedByCardNo(List<String> cardNo) throws Exception;

    void markCardAsInvalidByCardNo(List<String> cardNo) throws Exception;

    BatchResult<Card> setDenominationBatch(Card card,QueryWrapper<Card> queryWrapper) throws Exception;

    BatchResult<Card> setDenominationBatchByIds(Card card,List<String> idList) throws Exception;

    BatchResult<Card> releaseBatch(QueryWrapper<Card> queryWrapper) throws Exception;

    BatchResult<Card> releaseBatchByIds(List<String> idList) throws Exception;

    BatchResult<Card> lockBatchByIds(List<String> idList,String reason) throws Exception;

    BatchResult<Card> lockBatch(QueryWrapper<Card> queryWrapper,String reason) throws Exception;

    BatchResult<Card> unlockBatchByIds(List<String> idList) throws Exception;

    BatchResult<Card> unlockBatch(QueryWrapper<Card> queryWrapper) throws Exception;

    BatchResult<Card> invalidBatchByIds(List<String> idList) throws Exception;

    BatchResult<Card> invalidBatch(QueryWrapper<Card> queryWrapper) throws Exception;

    BatchResult<Card> deleteBatchByIds(List<String> idList) throws Exception;

    void deleteCardById(String id) throws Exception;

    List<Card> getCardByRange(Integer startNum,Integer endNum) throws Exception;

    Card getCardByCardNo(String cardNo) throws Exception;

    void replaceCard(String reason,String originCradId,String cardNo) throws Exception;

    boolean checkCardPwd(String cardNo,String pwd) throws Exception;

    BatchResult<Card> resetBatchByIds(List<String> idList, String reason) throws Exception;

}
