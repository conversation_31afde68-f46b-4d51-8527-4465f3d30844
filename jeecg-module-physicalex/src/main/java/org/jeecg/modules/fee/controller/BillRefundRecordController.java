package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.fee.entity.BillRefundRecord;
import org.jeecg.modules.fee.service.IBillRefundRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 退款记录
 * @Author: jeecg-boot
 * @Date:   2024-12-24
 * @Version: V1.0
 */
@Api(tags="退款记录")
@RestController
@RequestMapping("/fee/billRefundRecord")
@Slf4j
public class BillRefundRecordController extends JeecgController<BillRefundRecord, IBillRefundRecordService> {
	@Autowired
	private IBillRefundRecordService recipeRefundRecordService;

	 /**
	  * 根据登记记录ID查询退款记录
	  */
	 @ApiOperation(value="根据登记记录ID查询退款记录", notes="根据登记记录ID查询退款记录")
	 @GetMapping(value = "/listRefundBillByReg")
	 public Result<?> listRefundBillByReg(@RequestParam(name="regId",required=true) String regId) {
		 return Result.OK(recipeRefundRecordService.listWithFefundRecordsByRegId(regId));
	 }
	 /**
	  * 根据登记记录ID查询退款记录
	  */
	 @ApiOperation(value="根据登记记录ID查询退款记录", notes="根据登记记录ID查询退款记录")
	 @GetMapping(value = "/listRefundBillById")
	 public Result<?> listRefundBillById(@RequestParam(name="id",required=true) String id) {
		 return Result.OK(recipeRefundRecordService.listWithFefundRecordsById(id));
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param billRefundRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "退款记录-分页列表查询")
	@ApiOperation(value="退款记录-分页列表查询", notes="退款记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BillRefundRecord>> queryPageList(BillRefundRecord billRefundRecord,
														 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														 HttpServletRequest req) {
        QueryWrapper<BillRefundRecord> queryWrapper = QueryGenerator.initQueryWrapper(billRefundRecord, req.getParameterMap());
		Page<BillRefundRecord> page = new Page<BillRefundRecord>(pageNo, pageSize);
		IPage<BillRefundRecord> pageList = recipeRefundRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param billRefundRecord
	 * @return
	 */
	@AutoLog(value = "退款记录-添加")
	@ApiOperation(value="退款记录-添加", notes="退款记录-添加")
	@RequiresPermissions("fee:recipe_refund_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BillRefundRecord billRefundRecord) {
		recipeRefundRecordService.save(billRefundRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param billRefundRecord
	 * @return
	 */
	@AutoLog(value = "退款记录-编辑")
	@ApiOperation(value="退款记录-编辑", notes="退款记录-编辑")
	@RequiresPermissions("fee:recipe_refund_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BillRefundRecord billRefundRecord) {
		recipeRefundRecordService.updateById(billRefundRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "退款记录-通过id删除")
	@ApiOperation(value="退款记录-通过id删除", notes="退款记录-通过id删除")
	@RequiresPermissions("fee:recipe_refund_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		recipeRefundRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "退款记录-批量删除")
	@ApiOperation(value="退款记录-批量删除", notes="退款记录-批量删除")
	@RequiresPermissions("fee:recipe_refund_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.recipeRefundRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "退款记录-通过id查询")
	@ApiOperation(value="退款记录-通过id查询", notes="退款记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BillRefundRecord> queryById(@RequestParam(name="id",required=true) String id) {
		BillRefundRecord billRefundRecord = recipeRefundRecordService.getById(id);
		if(billRefundRecord ==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(billRefundRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param billRefundRecord
    */
    @RequiresPermissions("fee:recipe_refund_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BillRefundRecord billRefundRecord) {
        return super.exportXls(request, billRefundRecord, BillRefundRecord.class, "退款记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:recipe_refund_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BillRefundRecord.class);
    }

}
