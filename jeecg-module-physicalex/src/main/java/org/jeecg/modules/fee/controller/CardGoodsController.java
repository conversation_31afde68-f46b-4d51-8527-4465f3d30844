package org.jeecg.modules.fee.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.CardGoods;
import org.jeecg.modules.fee.entity.CardOrder;
import org.jeecg.modules.fee.service.ICardGoodsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.fee.service.ICardOrderService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 体检卡商品
 * @Author: jeecg-boot
 * @Date:   2024-10-11
 * @Version: V1.0
 */
@Api(tags="体检卡商品")
@RestController
@RequestMapping("/fee/cardGoods")
@Slf4j
public class CardGoodsController extends JeecgController<CardGoods, ICardGoodsService> {
	@Autowired
	private ICardGoodsService cardGoodsService;
	 @Autowired
	 private ICardOrderService cardOrderService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cardGoods
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "体检卡商品-分页列表查询")
	@ApiOperation(value="体检卡商品-分页列表查询", notes="体检卡商品-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CardGoods>> queryPageList(CardGoods cardGoods,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
//        QueryWrapper<CardGoods> queryWrapper = QueryGenerator.initQueryWrapper(cardGoods, req.getParameterMap());
		LambdaQueryWrapper<CardGoods> queryWrapper = Wrappers.lambdaQuery();
		if (StringUtils.isNotBlank(req.getParameter("name"))) {
			queryWrapper.like(CardGoods::getName, req.getParameter("name"));
		}
		if (StringUtils.isNotBlank(req.getParameter("saleOnlineFlag"))) {
			queryWrapper.eq(CardGoods::getSaleOnlineFlag, req.getParameter("saleOnlineFlag"));
		}
		if (StringUtils.isNotBlank(req.getParameter("enableFlag"))) {
			queryWrapper.eq(CardGoods::getEnableFlag, req.getParameter("enableFlag"));
		}
		if (StringUtils.isNotBlank(req.getParameter("originPrice"))) {
			queryWrapper.eq(CardGoods::getOriginPrice, req.getParameter("originPrice"));
		}
		queryWrapper.orderByDesc(CardGoods::getCreateTime);
		Page<CardGoods> page = new Page<CardGoods>(pageNo, pageSize);
		IPage<CardGoods> pageList = cardGoodsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	 /**
	  * 分页列表查询
	  *
	  * @param cardGoods
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "体检卡商品-分页列表查询")
	 @ApiOperation(value="体检卡商品-在售体检卡分页列表查询", notes="体检卡商品-在售体检卡分页列表查询")
	 @GetMapping(value = "/onSaleList")
	 public Result<IPage<CardGoods>> queryOnSaleList(CardGoods cardGoods,
												   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												   HttpServletRequest req) {
		 QueryWrapper<CardGoods> queryWrapper = QueryGenerator.initQueryWrapper(cardGoods, req.getParameterMap());
		 queryWrapper.eq("enable_flag","1").eq("sale_online_flag","1");
		 Page<CardGoods> page = new Page<CardGoods>(pageNo, pageSize);
		 IPage<CardGoods> pageList = cardGoodsService.page(page, queryWrapper);
		 List<CardGoods> goodsList = pageList.getRecords();
		 if (CollectionUtils.isNotEmpty(goodsList)){
			 List<String> goodIds = goodsList.stream().map(CardGoods::getId).collect(Collectors.toList());
			 List<CardOrder> orders = cardOrderService.list(new LambdaQueryWrapper<CardOrder>().in(CardOrder::getGoodsId, goodIds));
			 if (CollectionUtils.isNotEmpty(orders)){
				 Map<String, List<CardOrder>>	orderMap = orders.stream().collect(Collectors.groupingBy(CardOrder::getGoodsId));
				 pageList.getRecords().forEach(goods -> {
					 List<CardOrder> cardOrders = orderMap.get(goods.getId());
					 goods.setBuyerCount(CollectionUtils.isNotEmpty(cardOrders)?cardOrders.size():0);
				 });
			 }
		 }

		 return Result.OK(pageList);
	 }
	/**
	 *   添加
	 *
	 * @param cardGoods
	 * @return
	 */
	@AutoLog(value = "体检卡商品-添加")
	@ApiOperation(value="体检卡商品-添加", notes="体检卡商品-添加")
	@RequiresPermissions("fee:card_goods:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CardGoods cardGoods) {
		cardGoodsService.save(cardGoods);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cardGoods
	 * @return
	 */
	@AutoLog(value = "体检卡商品-编辑")
	@ApiOperation(value="体检卡商品-编辑", notes="体检卡商品-编辑")
	@RequiresPermissions("fee:card_goods:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CardGoods cardGoods) {
		cardGoodsService.updateById(cardGoods);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "体检卡商品-通过id删除")
	@ApiOperation(value="体检卡商品-通过id删除", notes="体检卡商品-通过id删除")
	@RequiresPermissions("fee:card_goods:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cardGoodsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "体检卡商品-批量删除")
	@ApiOperation(value="体检卡商品-批量删除", notes="体检卡商品-批量删除")
	@RequiresPermissions("fee:card_goods:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cardGoodsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "体检卡商品-通过id查询")
	@ApiOperation(value="体检卡商品-通过id查询", notes="体检卡商品-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CardGoods> queryById(@RequestParam(name="id",required=true) String id) {
		CardGoods cardGoods = cardGoodsService.getById(id);
		if(cardGoods==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cardGoods);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cardGoods
    */
    @RequiresPermissions("fee:card_goods:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CardGoods cardGoods) {
        return super.exportXls(request, cardGoods, CardGoods.class, "体检卡商品");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:card_goods:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CardGoods.class);
    }

}
