package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.fee.entity.Card;
import org.jeecg.modules.fee.entity.CardOrder;
import org.jeecg.modules.fee.service.ICardOrderService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 体检卡订单
 * @Author: jeecg-boot
 * @Date: 2024-10-11
 * @Version: V1.0
 */
@Api(tags = "体检卡订单")
@RestController
@RequestMapping("/fee/cardOrder")
@Slf4j
public class CardOrderController extends JeecgController<CardOrder, ICardOrderService> {
    @Autowired
    private ICardOrderService cardOrderService;

    /**
     * 根据体检卡ID查询订单
     *
     * @param cardId
     * @return
     */
    //@AutoLog(value = "体检卡订单-根据体检卡ID查询订单")
    @ApiOperation(value = "体检卡订单-根据体检卡ID查询订单", notes = "体检卡订单-根据体检卡ID查询订单")
    @GetMapping(value = "/getByCardId")
    public Result<CardOrder> getByCardId(@RequestParam(name = "cardId", required = true) String cardId) {
        CardOrder cardOrder = cardOrderService.getByCardId(cardId);
        if (cardOrder == null) {
            return Result.error("未找到对应售卡订单！");
        }
        return Result.OK(cardOrder);
    }

    /**
     * 分页列表查询
     *
     * @param cardOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检卡订单-分页列表查询")
    @ApiOperation(value = "体检卡订单-分页列表查询", notes = "体检卡订单-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CardOrder>> queryPageList(CardOrder cardOrder, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("status", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("payMode", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<CardOrder> queryWrapper = QueryGenerator.initQueryWrapper(cardOrder, req.getParameterMap(), customeRuleMap);
        Page<CardOrder> page = new Page<CardOrder>(pageNo, pageSize);
        IPage<CardOrder> pageList = cardOrderService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 写卡
     *
     * @return
     */
    @AutoLog(value = "体检卡订单-写卡")
    @ApiOperation(value = "体检卡订单-写卡", notes = "体检卡订单-写卡")
    @RequiresPermissions("fee:card_order:add")
    @PostMapping(value = "/writeCard")
    public Result<?> writeCard(@RequestBody JSONObject json) {
        try {
            String orderId = json.getString("orderId");
            List<Card> cardIdList = json.getJSONArray("cardList").toJavaList(Card.class);
            BatchResult<Card> batchResult = cardOrderService.writeCard4Order(orderId, cardIdList);
            return Result.OK("操作成功！", batchResult);
        } catch (Exception e) {
            log.error("体检卡订单-写卡异常", e);
            return Result.error("写卡失败！");
        }
    }

    /**
     * 添加
     *
     * @param cardOrder
     * @return
     */
    @AutoLog(value = "体检卡订单-添加")
    @ApiOperation(value = "体检卡订单-添加", notes = "体检卡订单-添加")
    @RequiresPermissions("fee:card_order:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody CardOrder cardOrder, HttpServletRequest request) {
        try {
            cardOrderService.saveCardOrder(cardOrder);
            String payFlag = cardOrder.getPayFlag();
            if (StringUtils.equals(payFlag, "1")) {
                cardOrderService.payByHis(cardOrder, request.getRemoteAddr());
            }
        } catch (Exception e) {
            log.error("体检卡订单-添加异常", e);
            return Result.error("下单失败！详细原因：" + e.getMessage());
        }
        return Result.OK("添加成功！", cardOrder);
    }

    /**
     * 作废
     *
     * @return
     */
    @AutoLog(value = "体检卡订单-作废")
    @ApiOperation(value = "体检卡订单-作废", notes = "体检卡订单-作废")
    @RequiresPermissions("fee:card_order:invalid")
    @GetMapping(value = "/invalid")
    public Result<?> invalid(String id) {
        try {
            cardOrderService.invalidCardOrder(id);
        } catch (Exception e) {
            log.error("体检卡订单-作废异常", e);
        }
        return Result.OK("作废成功！");
    }

    /**
     * 编辑
     *
     * @param cardOrder
     * @return
     */
    @AutoLog(value = "体检卡订单-编辑")
    @ApiOperation(value = "体检卡订单-编辑", notes = "体检卡订单-编辑")
    @RequiresPermissions("fee:card_order:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody CardOrder cardOrder) {
        cardOrderService.updateById(cardOrder);
        return Result.OK("编辑成功!", cardOrder);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检卡订单-通过id删除")
    @ApiOperation(value = "体检卡订单-通过id删除", notes = "体检卡订单-通过id删除")
    @RequiresPermissions("fee:card_order:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        cardOrderService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检卡订单-批量删除")
    @ApiOperation(value = "体检卡订单-批量删除", notes = "体检卡订单-批量删除")
    @RequiresPermissions("fee:card_order:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.cardOrderService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检卡订单-通过id查询")
    @ApiOperation(value = "体检卡订单-通过id查询", notes = "体检卡订单-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CardOrder> queryById(@RequestParam(name = "id", required = true) String id) {
        CardOrder cardOrder = cardOrderService.getById(id);
        if (cardOrder == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(cardOrder);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param cardOrder
     */
    @RequiresPermissions("fee:card_order:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CardOrder cardOrder) {
        return super.exportXls(request, cardOrder, CardOrder.class, "体检卡订单");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("fee:card_order:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CardOrder.class);
    }

}
