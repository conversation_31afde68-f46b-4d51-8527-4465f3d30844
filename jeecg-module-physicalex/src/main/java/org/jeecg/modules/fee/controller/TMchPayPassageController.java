package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.TMchPayPassage;
import org.jeecg.modules.fee.service.ITMchPayPassageService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 支付网关-商户支付配置
 * @Author: jeecg-boot
 * @Date:   2024-07-02
 * @Version: V1.0
 */
@Api(tags="支付网关-商户支付配置")
@RestController
@RequestMapping("/fee/tMchPayPassage")
@Slf4j
public class TMchPayPassageController extends JeecgController<TMchPayPassage, ITMchPayPassageService> {
	@Autowired
	private ITMchPayPassageService tMchPayPassageService;
	
	/**
	 * 分页列表查询
	 *
	 * @param tMchPayPassage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "支付网关-商户支付配置-分页列表查询")
	@ApiOperation(value="支付网关-商户支付配置-分页列表查询", notes="支付网关-商户支付配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TMchPayPassage>> queryPageList(TMchPayPassage tMchPayPassage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<TMchPayPassage> queryWrapper = QueryGenerator.initQueryWrapper(tMchPayPassage, req.getParameterMap());
		Page<TMchPayPassage> page = new Page<TMchPayPassage>(pageNo, pageSize);
		IPage<TMchPayPassage> pageList = tMchPayPassageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param tMchPayPassage
	 * @return
	 */
	@AutoLog(value = "支付网关-商户支付配置-添加")
	@ApiOperation(value="支付网关-商户支付配置-添加", notes="支付网关-商户支付配置-添加")
	@RequiresPermissions("fee:t_mch_pay_passage:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TMchPayPassage tMchPayPassage) {
		tMchPayPassageService.save(tMchPayPassage);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param tMchPayPassage
	 * @return
	 */
	@AutoLog(value = "支付网关-商户支付配置-编辑")
	@ApiOperation(value="支付网关-商户支付配置-编辑", notes="支付网关-商户支付配置-编辑")
	@RequiresPermissions("fee:t_mch_pay_passage:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMchPayPassage tMchPayPassage) {
		tMchPayPassageService.updateById(tMchPayPassage);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "支付网关-商户支付配置-通过id删除")
	@ApiOperation(value="支付网关-商户支付配置-通过id删除", notes="支付网关-商户支付配置-通过id删除")
	@RequiresPermissions("fee:t_mch_pay_passage:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		tMchPayPassageService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "支付网关-商户支付配置-批量删除")
	@ApiOperation(value="支付网关-商户支付配置-批量删除", notes="支付网关-商户支付配置-批量删除")
	@RequiresPermissions("fee:t_mch_pay_passage:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.tMchPayPassageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "支付网关-商户支付配置-通过id查询")
	@ApiOperation(value="支付网关-商户支付配置-通过id查询", notes="支付网关-商户支付配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMchPayPassage> queryById(@RequestParam(name="id",required=true) String id) {
		TMchPayPassage tMchPayPassage = tMchPayPassageService.getById(id);
		if(tMchPayPassage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tMchPayPassage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param tMchPayPassage
    */
    @RequiresPermissions("fee:t_mch_pay_passage:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TMchPayPassage tMchPayPassage) {
        return super.exportXls(request, tMchPayPassage, TMchPayPassage.class, "支付网关-商户支付配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:t_mch_pay_passage:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TMchPayPassage.class);
    }

}
