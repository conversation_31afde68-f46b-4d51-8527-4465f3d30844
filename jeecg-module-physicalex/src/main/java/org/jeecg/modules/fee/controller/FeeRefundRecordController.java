package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.FeeRefundRecord;
import org.jeecg.modules.fee.service.IFeeRefundRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 退费记录
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
@Api(tags="退费记录")
@RestController
@RequestMapping("/fee/feeRefundRecord")
@Slf4j
public class FeeRefundRecordController extends JeecgController<FeeRefundRecord, IFeeRefundRecordService> {
	@Autowired
	private IFeeRefundRecordService feeRefundRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param feeRefundRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "退费记录-分页列表查询")
	@ApiOperation(value="退费记录-分页列表查询", notes="退费记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<FeeRefundRecord>> queryPageList(FeeRefundRecord feeRefundRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<FeeRefundRecord> queryWrapper = QueryGenerator.initQueryWrapper(feeRefundRecord, req.getParameterMap());
		Page<FeeRefundRecord> page = new Page<FeeRefundRecord>(pageNo, pageSize);
		IPage<FeeRefundRecord> pageList = feeRefundRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param feeRefundRecord
	 * @return
	 */
	@AutoLog(value = "退费记录-添加")
	@ApiOperation(value="退费记录-添加", notes="退费记录-添加")
	@RequiresPermissions("fee:fee_refund_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody FeeRefundRecord feeRefundRecord) {
		feeRefundRecordService.save(feeRefundRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param feeRefundRecord
	 * @return
	 */
	@AutoLog(value = "退费记录-编辑")
	@ApiOperation(value="退费记录-编辑", notes="退费记录-编辑")
	@RequiresPermissions("fee:fee_refund_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody FeeRefundRecord feeRefundRecord) {
		feeRefundRecordService.updateById(feeRefundRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "退费记录-通过id删除")
	@ApiOperation(value="退费记录-通过id删除", notes="退费记录-通过id删除")
	@RequiresPermissions("fee:fee_refund_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		feeRefundRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "退费记录-批量删除")
	@ApiOperation(value="退费记录-批量删除", notes="退费记录-批量删除")
	@RequiresPermissions("fee:fee_refund_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.feeRefundRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "退费记录-通过id查询")
	@ApiOperation(value="退费记录-通过id查询", notes="退费记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<FeeRefundRecord> queryById(@RequestParam(name="id",required=true) String id) {
		FeeRefundRecord feeRefundRecord = feeRefundRecordService.getById(id);
		if(feeRefundRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(feeRefundRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param feeRefundRecord
    */
    @RequiresPermissions("fee:fee_refund_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, FeeRefundRecord feeRefundRecord) {
        return super.exportXls(request, feeRefundRecord, FeeRefundRecord.class, "退费记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:fee_refund_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, FeeRefundRecord.class);
    }

}
