<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.fee.mapper.FeeRefundRecordMapper">

    <select id="selectFeeRefundRecordByBillId" resultType="org.jeecg.modules.fee.entity.FeeRecord">
        select
        b.bill_id as billId,
        f.bill_no as billNo,
        b.id as billRefundId,
        b.refund_bill_no as billRefundNo,
        b.status as billStatus,
        f.exam_no as examNo,
        f.name as name,
        f.his_apply_no as hisApplyNo,
        f.way_code as payChannel,
        f.refund_amount as amount,
        f.client_ip as clientIp,
        f.created_time as createdTime,
        f.success_time as successTime,
        f.creator as creator,
        f.state as state,
        '退费' as recordType
        from fee_refund_record f join bill_refund_record b on f.bill_refund_id=b.id
        where b.bill_id=#{billId}
    </select>
</mapper>