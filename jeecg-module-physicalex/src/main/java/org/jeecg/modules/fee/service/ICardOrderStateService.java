package org.jeecg.modules.fee.service;

import org.jeecg.modules.fee.entity.CardOrder;
import org.jeecg.modules.fee.entity.CardOrderState;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 卡订单状态记录
 * @Author: jeecg-boot
 * @Date:   2024-10-11
 * @Version: V1.0
 */
public interface ICardOrderStateService extends IService<CardOrderState> {

    void saveCardOrderState(CardOrder cardOrder) throws Exception;

}
