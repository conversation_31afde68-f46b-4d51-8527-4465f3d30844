package org.jeecg.modules.fee.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.fee.entity.CardSetting;
import org.jeecg.modules.fee.service.ICardSettingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: card_setting
 * @Author: jeecg-boot
 * @Date:   2024-07-14
 * @Version: V1.0
 */
@Api(tags="card_setting")
@RestController
@RequestMapping("/fee/cardSetting")
@Slf4j
public class CardSettingController extends JeecgController<CardSetting, ICardSettingService> {
	@Autowired
	private ICardSettingService cardSettingService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cardSetting
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "card_setting-分页列表查询")
	@ApiOperation(value="card_setting-分页列表查询", notes="card_setting-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CardSetting>> queryPageList(CardSetting cardSetting,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CardSetting> queryWrapper = QueryGenerator.initQueryWrapper(cardSetting, req.getParameterMap());
		Page<CardSetting> page = new Page<CardSetting>(pageNo, pageSize);
		IPage<CardSetting> pageList = cardSettingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cardSetting
	 * @return
	 */
	@AutoLog(value = "card_setting-添加")
	@ApiOperation(value="card_setting-添加", notes="card_setting-添加")
	@RequiresPermissions("fee:card_setting:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CardSetting cardSetting) {
		cardSettingService.save(cardSetting);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cardSetting
	 * @return
	 */
	@AutoLog(value = "card_setting-编辑")
	@ApiOperation(value="card_setting-编辑", notes="card_setting-编辑")
	@RequiresPermissions("fee:card_setting:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CardSetting cardSetting) {
		cardSettingService.updateById(cardSetting);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "card_setting-通过id删除")
	@ApiOperation(value="card_setting-通过id删除", notes="card_setting-通过id删除")
	@RequiresPermissions("fee:card_setting:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cardSettingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "card_setting-批量删除")
	@ApiOperation(value="card_setting-批量删除", notes="card_setting-批量删除")
	@RequiresPermissions("fee:card_setting:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cardSettingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "card_setting-通过id查询")
	@ApiOperation(value="card_setting-通过id查询", notes="card_setting-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CardSetting> queryById(@RequestParam(name="id",required=true) String id) {
		CardSetting cardSetting = cardSettingService.getById(id);
		if(cardSetting==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cardSetting);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cardSetting
    */
    @RequiresPermissions("fee:card_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CardSetting cardSetting) {
        return super.exportXls(request, cardSetting, CardSetting.class, "card_setting");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("fee:card_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CardSetting.class);
    }

}
