package org.jeecg.modules.fee.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.fee.entity.CardTrade;
import org.jeecg.modules.fee.mapper.CardTradeMapper;
import org.jeecg.modules.fee.service.ICardTradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: card_trade
 * @Author: jeecg-boot
 * @Date: 2024-07-14
 * @Version: V1.0
 */
@Service
public class CardTradeServiceImpl extends ServiceImpl<CardTradeMapper, CardTrade> implements ICardTradeService {

    @Autowired
    private CardTradeMapper cardTradeMapper;

    @Override
    public BigDecimal getBalanceByCardNo(String cardNo) {
        return cardTradeMapper.getBalance(cardNo);
    }

    @Override
    public Page<CardTrade> pageCardTrade(Page<CardTrade> page, String cardNo, String tradeType, String createTimeStart, String createTimeEnd,String name, String examNo) {
        return cardTradeMapper.pageCardTrade(page, cardNo, tradeType, createTimeStart, createTimeEnd,name, examNo);
    }

    @Override
    public Map<String,Object> getBalanceAndCompany(String cardNo) {
        Map<String,Object> map = new HashMap<>();
        BigDecimal balance = cardTradeMapper.getBalance(cardNo);
        map.put("balance",balance);
        String companyName = cardTradeMapper.getCompanyName(cardNo);
        map.put("companyName",companyName);
        return map;
    }
}
