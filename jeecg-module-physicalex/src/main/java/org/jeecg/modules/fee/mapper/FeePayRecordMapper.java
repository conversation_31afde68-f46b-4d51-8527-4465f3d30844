package org.jeecg.modules.fee.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.fee.entity.FeePayRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.fee.entity.FeeRecord;
import org.jeecg.modules.reg.entity.CustomerReg;

/**
 * @Description: 收费记录
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
public interface FeePayRecordMapper extends BaseMapper<FeePayRecord> {

    Page<CustomerReg> pageCustomer(Page<CustomerReg> page, @Param("examNo") String examNo, @Param("idCard") String idCard, @Param("name") String name, @Param("regDateStart") String regDateStart, @Param("regDateEnd") String regDateEnd, @Param("regStatus") String regStatus, @Param("payStatus") String payStatus);

    BigDecimal getTotalPriceOfReg(@Param("regId") String regId,@Param("payerType") String payerType);

    BigDecimal getPayedAmountOfReg(@Param("regId") String regId,@Param("payerType") String payerType);

    BigDecimal getPayedAmountOfCompanyReg(@Param("companyRegId") String companyRegId);

    FeePayRecord getLatestPayRecordByReg(@Param("regId") String regId,@Param("payerType") String payerType,@Param("payChannel")String payChannel);

    List<FeePayRecord> listByBillId(@Param("billId") String billId, @Param("state") String state);

    List<FeeRecord> listPayRecordByBillId(@Param("billId") String billId);

    BigDecimal getPaidAmountOfBill(@Param("billId") String billId);

    Boolean isAllBillPaied(@Param("billId") String billId);

    List<FeePayRecord> selectByRelatedIdcards(@Param("idCards") List<String> idCards,@Param("payChannel")String payChannel,@Param("teamId")String teamId);
}
