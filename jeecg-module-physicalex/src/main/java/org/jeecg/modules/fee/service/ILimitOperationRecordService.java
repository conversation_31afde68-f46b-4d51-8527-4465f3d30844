package org.jeecg.modules.fee.service;

import org.jeecg.modules.fee.entity.LimitOperationRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.math.BigDecimal;

/**
 * @Description: 体检限额操作记录
 * @Author: jeecg-boot
 * @Date: 2025-01-14
 * @Version: V1.0
 */
public interface ILimitOperationRecordService extends IService<LimitOperationRecord> {

    void transfer2Another(String regId, String targetLimitId, BigDecimal amount) throws Exception;
    void transfer2AnotherOld(String regId, String targetRegId, BigDecimal amount) throws Exception;

    void transfer2Card(String regId, String cardNo, BigDecimal amount,String limitPic) throws Exception;

    void subtractLimitAmount(String regId, BigDecimal amount,String limitPic) throws Exception;
}
