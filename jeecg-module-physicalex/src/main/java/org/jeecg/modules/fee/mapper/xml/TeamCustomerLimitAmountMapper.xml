<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.fee.mapper.TeamCustomerLimitAmountMapper">


    <select id="getCustomerLimitAmountByRegId"
            resultType="org.jeecg.modules.fee.entity.TeamCustomerLimitAmount">
        select a.*  from team_customer_limit_amount a join customer_reg r on a.customer_id=r.customer_id and a.team_id=r.team_id where r.id=#{regId} order by a.create_time desc limit 1
    </select>
    <select id="getCustomerLimitAmountById" resultType="org.jeecg.modules.fee.entity.TeamCustomerLimitAmount">
        select a.*,t.limit_amount teamLimitAmount from team_customer_limit_amount a join company_team t on  a.team_id=t.id where a.id=#{id}
    </select>
    <select id="getLatestLimit" resultType="org.jeecg.modules.fee.entity.TeamCustomerLimitAmount">
        select * from team_customer_limit_amount
        <where>
            enable_flag= '1'
             and   team_id=#{teamId}
            <if test="customerId != null and customerId != ''">
                and customer_id=#{customerId}
            </if>
            <if test="idCard != null and idCard != ''">
            and id_card=#{idCard}
            </if>
        </where>
           order by create_time desc limit 1
    </select>
    <select id="getLatestLimitByIdCard" resultType="org.jeecg.modules.fee.entity.TeamCustomerLimitAmount">
        select * from team_customer_limit_amount
        <where>
            enable_flag= '1'
            and id_card=#{idCard}
        </where>
        order by create_time desc limit 1
    </select>
</mapper>