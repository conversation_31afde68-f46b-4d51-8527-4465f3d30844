<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.fee.mapper.CustomerRegBillMapper">

    <select id="listByRegId" resultType="org.jeecg.modules.fee.entity.CustomerRegBill">
        select * from customer_reg_bill where customer_reg_id = #{regId} and del_flag = 0 order by create_time desc
    </select>
    <select id="listByOrderId" resultType="org.jeecg.modules.fee.entity.CustomerRegBill">
        select * from customer_reg_bill where order_id = #{orderId} and del_flag = 0 order by create_time desc
    </select>
</mapper>