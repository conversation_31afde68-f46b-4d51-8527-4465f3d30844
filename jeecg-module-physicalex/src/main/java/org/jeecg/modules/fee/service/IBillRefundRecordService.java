package org.jeecg.modules.fee.service;

import org.jeecg.modules.fee.entity.BillRefundRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.fee.entity.CustomerRegBill;

import java.util.List;

/**
 * @Description: 退款记录
 * @Author: jeecg-boot
 * @Date:   2024-12-24
 * @Version: V1.0
 */
public interface IBillRefundRecordService extends IService<BillRefundRecord> {


    List<BillRefundRecord> listWithFefundRecordsByRegId(String regId);

    List<BillRefundRecord>listWithFefundRecordsById(String id);
}
