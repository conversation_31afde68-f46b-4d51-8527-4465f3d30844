package org.jeecg.modules.fee.controller;

import java.math.BigDecimal;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.fee.bo.PayResult;
import org.jeecg.modules.fee.bo.PaymentAnalysis;
import org.jeecg.modules.fee.entity.*;
import org.jeecg.modules.fee.mapper.TeamCustomerLimitAmountMapper;
import org.jeecg.modules.fee.service.ICustomerRegBillService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.fee.service.ILimitOperationRecordService;
import org.jeecg.modules.fee.service.ITeamCustomerLimitAmountService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 结账单
 * @Author: jeecg-boot
 * @Date: 2024-12-20
 * @Version: V1.0
 */
@Api(tags = "结账单")
@RestController
@RequestMapping("/fee/customerRegBill")
@Slf4j
public class CustomerRegBillController extends JeecgController<CustomerRegBill, ICustomerRegBillService> {
    @Autowired
    private ICustomerRegBillService customerRegBillService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private TeamCustomerLimitAmountMapper teamCustomerLimitAmountMapper;
    @Autowired
    private ILimitOperationRecordService limitOperationRecordService;
    /**
     * 收费记录-根据customerRegId获取支付单列表
     */
    @AutoLog(value = "收费记录-根据customerRegId获取支付单列表")
    @ApiOperation(value = "收费记录-根据customerRegId获取支付单列表", notes = "收费记录-根据customerRegId获取支付单列表")
    @GetMapping(value = "/listBillByReg")
    public Result<?> listBillByReg(String regId) {
        List<CustomerRegBill> list = customerRegBillService.listWithFeePayRecordsByRegId(regId);
        return Result.OK(list);
    }
    /**
     * 收费记录-根据billId获取支付单列表
     */
    @AutoLog(value = "收费记录-根据customerRegId获取支付单列表")
    @ApiOperation(value = "收费记录-根据customerRegId获取支付单列表", notes = "收费记录-根据customerRegId获取支付单列表")
    @GetMapping(value = "/listBillById")
    public Result<?> listBillById(String id) {
        CustomerRegBill bill = customerRegBillService.listWithFeePayRecordsById(id);
        return Result.OK(bill);
    }
    /**
     * 收费记录-根据billId获取支付单列表
     */
    @AutoLog(value = "收费记录-根据customerRegId获取支付单列表")
    @ApiOperation(value = "收费记录-根据customerRegId获取支付单列表", notes = "收费记录-根据customerRegId获取支付单列表")
    @GetMapping(value = "/listFeeRecordsByBillId")
    public Result<?> listFeeRecordsByBillId(String billId) {
        List<FeeRecord> feeRecords = customerRegBillService.listFeeRecordsByBillId(billId);
        return Result.OK(feeRecords);
    }

    /**
     * 收费记录-获取关联的单位支付记录
     */
    @AutoLog(value = "收费记录-获取关联的单位支付记录")
    @ApiOperation(value = "收费记录-获取关联的单位支付记录", notes = "收费记录-获取关联的单位支付记录")
    @GetMapping(value = "/getRelatedCompanyPayRecords")
    public Result<?> getRelatedCompanyPayRecords(String customerRegId) {
        List<FeePayRecord> list = customerRegBillService.getRelatedCompanyPayRecords(customerRegId);
        return Result.OK(list);
    }
    /**
     * 收费记录-获取额度操作记录
     */
    @AutoLog(value = "收费记录-获取关联的单位支付记录")
    @ApiOperation(value = "收费记录-获取关联的单位支付记录", notes = "收费记录-获取关联的单位支付记录")
    @GetMapping(value = "/getRelatedLimitAmountRecords")
    public Result<?> getRelatedLimitAmountRecords(String customerRegId) {
        Map<String, List<LimitOperationRecord>> records = customerRegBillService.getRelatedLimitAmountRecords(customerRegId);
        return Result.OK(records);
    }
    /**
     * 收费记录-根据身份证号获取关联的单位支付记录
     */
    @AutoLog(value = "收费记录-根据身份证号获取关联的单位支付记录")
    @ApiOperation(value = "收费记录-根据身份证号获取关联的单位支付记录", notes = "收费记录-根据身份证号获取关联的单位支付记录")
    @GetMapping(value = "/getRelatedCompanyPayRecordsByIdcard")
    public Result<?> getRelatedCompanyPayRecordsByIdcard(String idCard) {
        List<FeePayRecord> list = customerRegBillService.getRelatedCompanyPayRecordsByIdacrd(idCard);
        return Result.OK(list);
    }
    /**
     * 收费记录-根据团检额度id查询额度操作记录
     */
    @AutoLog(value = "收费记录-根据团检额度id查询额度操作记录")
    @ApiOperation(value = "收费记录-根据团检额度id查询额度操作记录", notes = "收费记录-根据团检额度id查询额度操作记录")
    @GetMapping(value = "/getLimitRecordsByLimitId")
    public Result<?> getLimitRecordsByLimitId(String limitId) {
        List<LimitOperationRecord> list = limitOperationRecordService.list(new LambdaQueryWrapper<LimitOperationRecord>().eq(LimitOperationRecord::getLimitId, limitId));
        return Result.OK(list);
    }

    /**
     * 收费记录-根据billId作废收费单
     */
    @AutoLog(value = "收费记录-根据billId作废收费单")
    @ApiOperation(value = "收费记录-根据billId作废收费单", notes = "收费记录-根据billId作废收费单")
    @GetMapping(value = "/invalidateBill")
    public Result<?> invalidateBill(String billId, HttpServletRequest request) {
        try {
            customerRegBillService.invalidateBill(billId, request.getRemoteAddr());
            return Result.OK("操作成功！");
        } catch (Exception e) {
            log.error("作废异常", e);
            return Result.error("作废失败,详细原因：" + e.getMessage());
        }
    }

    /**
     * 收费记录-根据refundBillId作废收费单
     */
    @AutoLog(value = "收费记录-根据refundBillId作废收费单")
    @ApiOperation(value = "收费记录-根据refundBillId作废收费单", notes = "收费记录-根据refundBillId作废收费单")
    @GetMapping(value = "/invalidateRefundBill")
    public Result<?> invalidateRefundBill(String refundBillId, HttpServletRequest request) {
        try {
            customerRegBillService.invalidateRefundBill(refundBillId, request.getRemoteAddr());
            return Result.OK("操作成功！");
        } catch (Exception e) {
            log.error("作废异常", e);
            return Result.error("作废失败,详细原因：" + e.getMessage());
        }
    }
    /**
     * 收费记录-根据customerRegId获取PaymentAnalysis
     */
    @AutoLog(value = "收费记录-根据customerRegId获取PaymentAnalysis")
    @ApiOperation(value = "收费记录-根据customerRegId获取PaymentAnalysis", notes = "收费记录-根据customerRegId获取PaymentAnalysis")
    @PostMapping(value = "/analysisPayment")
    public Result<?> analysisPayment(@RequestBody JSONObject data) {
        String regId = data.getString("regId");
        List<String> customerRegItemGroupList = data.getJSONArray("groupList").toJavaList(String.class);
        PaymentAnalysis paymentAnalysis = null;
        try {
            long start = System.currentTimeMillis();
            paymentAnalysis = customerRegBillService.analysisPayment4ItemGroupIds(customerRegItemGroupList, regId);
            long end = System.currentTimeMillis();
            System.out.println("analysisPayment4ItemGroupIds耗时：" + (end - start) + "ms");
        } catch (Exception e) {
            return Result.error("获取收费信息失败,详细原因：" + e.getMessage());
        }
        return Result.OK(paymentAnalysis);
    }

    @RequestMapping(value = "/listRecipeForRefund")
    public Result<?> listRecipeForRefund(String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        List<CustomerRegBill> list = customerRegBillService.list4Refund(idList);
        return Result.OK(list);
    }

    @AutoLog(value = "收费记录-收费单付款")
    @ApiOperation(value = "收费记录-收费单付款", notes = "收费记录-收费单付款")
    @PostMapping(value = "/payBill")
    public Result<?> payBill(@RequestBody CustomerRegBill regBill, HttpServletRequest request) {
        try {
            //获取客户端ip
            String clientIp = request.getRemoteAddr();
            regBill.setIp(clientIp);
            List<PayResult> payResultList = customerRegBillService.payBill(regBill);
            return Result.OK(payResultList);
        } catch (Exception e) {
            log.error("支付异常", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 收费记录-批量退款
     */
    @AutoLog(value = "收费记录-批量退款")
    @ApiOperation(value = "收费记录-批量退款", notes = "收费记录-批量退款")
    @PostMapping(value = "/refundRecipeBatch")
    @RequiresPermissions("fee:customer_reg_recipe:refund")
    public Result<?> refundBillBatch(@RequestBody List<CustomerRegBill> recordList, HttpServletRequest request) {
        try {
            customerRegBillService.refundBatch(recordList, request.getRemoteAddr(), "批量退款");
            return Result.OK("操作成功！");
        } catch (Exception e) {
            log.error("退款异常", e);
            return Result.error("退款失败,详细原因：" + e.getMessage());
        }
    }


    /**
     * 根据customerRegId获取未结算的大项，和支付单
     *
     * @return
     */
    //@AutoLog(value = "结账单-根据customerRegId获取未结算的大项，和支付单")
    @ApiOperation(value = "结账单-根据customerRegId获取未结算的大项，和支付单", notes = "结账单-根据customerRegId获取未结算的大项，和支付单")
    @GetMapping(value = "/listByRegId")
    public Result<?> listByRegId(@RequestParam(name = "regId", required = true) String regId) {
        Map<String, Object> result = new HashMap<>();
        List<CustomerRegBill> customerRegBillList = customerRegBillService.listByRegId(regId);
        customerRegBillList.forEach(item -> {
            if (!StringUtils.equals(item.getStatus(), ExConstants.PAY_STATE_支付成功)) {
                //customerRegBillService.updatePayStatus4Bill(item);
            }
        });
        result.put("recipeList", customerRegBillList);
        List<CustomerRegItemGroup> unpaidGroupList = customerRegItemGroupService.listUnpaidByReg(regId);
        result.put("unpaidGroupList", unpaidGroupList);
        return Result.OK(result);
    }

    /**
     * 分页列表查询
     *
     * @param customerRegBill
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "结账单-分页列表查询")
    @ApiOperation(value = "结账单-分页列表查询", notes = "结账单-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerRegBill>> queryPageList(CustomerRegBill customerRegBill, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CustomerRegBill> queryWrapper = QueryGenerator.initQueryWrapper(customerRegBill, req.getParameterMap());
        Page<CustomerRegBill> page = new Page<CustomerRegBill>(pageNo, pageSize);
        IPage<CustomerRegBill> pageList = customerRegBillService.page(page, queryWrapper);
        return Result.OK(pageList);
    }
    @AutoLog(value = "收费记录-根据customerRegId获取PaymentAnalysis")
    @ApiOperation(value = "收费记录-根据customerRegId获取PaymentAnalysis", notes = "收费记录-根据customerRegId获取PaymentAnalysis")
    @GetMapping(value = "/getAvailableCompanyAmount")
    public Result<?> getAvailableCompanyAmount(@RequestParam("billId") String billId, @RequestParam("unpaidAmount") BigDecimal unpaidAmount) {
        CustomerRegBill bill = customerRegBillService.getById(billId);
        if (unpaidAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return Result.error("支付金额为0，无需再次支付");
        }
        if (Objects.isNull(bill)) {
            return Result.error("未找到对应的收费单");
        }
        if (!StringUtils.equals(bill.getStatus(), ExConstants.PAY_STATE_支付中)) {
            return Result.error("该收费单已支付，无需再次支付");
        }
        CustomerReg reg = customerRegService.getById(bill.getCustomerRegId());
        if (Objects.isNull(reg)) {
            return Result.error("未找到对应的登记记录");
        }
        // 5. 检查额度类型
        boolean hasTeam = StringUtils.isNotBlank(reg.getTeamId());
        boolean hasShareLimit = StringUtils.isNotBlank(reg.getOriginCustomerLimitAmountId());

        if (!hasTeam && !hasShareLimit) {
            return Result.error("不是团检客户！也没有共享亲友额度！");
        }

        // 6. 获取额度信息
        Map<String, Object> resultMap = new HashMap<>();

        // 处理本人额度
        if (hasTeam) {
            TeamCustomerLimitAmount selfLimit = teamCustomerLimitAmountMapper.getLatestLimit(reg.getTeamId(), reg.getCustomerId(),null);
            if (selfLimit != null) {
                resultMap.put("selfLimitAmount", selfLimit.getAmount());
            }
        }

        // 处理亲友额度
        if (hasShareLimit) {
            TeamCustomerLimitAmount shareLimit = teamCustomerLimitAmountMapper.selectById(reg.getOriginCustomerLimitAmountId());
            if (shareLimit != null) {
                resultMap.put("shareLimitAmount", shareLimit.getAmount());
            }
        }

        // 7. 检查是否有可用额度
        if (resultMap.isEmpty()) {
            return Result.error("未找到可用额度信息");
        }

        return Result.OK(resultMap);
    }
    /**
     * 添加
     *
     * @param customerRegBill
     * @return
     */
    @AutoLog(value = "结账单-添加")
    @ApiOperation(value = "结账单-添加", notes = "结账单-添加")
    @RequiresPermissions("fee:customer_reg_recipe:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CustomerRegBill customerRegBill) {
        customerRegBillService.save(customerRegBill);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param customerRegBill
     * @return
     */
    @AutoLog(value = "结账单-编辑")
    @ApiOperation(value = "结账单-编辑", notes = "结账单-编辑")
    @RequiresPermissions("fee:customer_reg_recipe:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomerRegBill customerRegBill) {
        customerRegBillService.updateById(customerRegBill);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "结账单-通过id删除")
    @ApiOperation(value = "结账单-通过id删除", notes = "结账单-通过id删除")
    @RequiresPermissions("fee:customer_reg_recipe:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegBillService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "结账单-批量删除")
    @ApiOperation(value = "结账单-批量删除", notes = "结账单-批量删除")
    @RequiresPermissions("fee:customer_reg_recipe:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegBillService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "结账单-通过id查询")
    @ApiOperation(value = "结账单-通过id查询", notes = "结账单-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegBill> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegBill customerRegBill = customerRegBillService.getById(id);
        if (customerRegBill == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegBill);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegBill
     */
    @RequiresPermissions("fee:customer_reg_recipe:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegBill customerRegBill) {
        return super.exportXls(request, customerRegBill, CustomerRegBill.class, "结账单");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("fee:customer_reg_recipe:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegBill.class);
    }

}
