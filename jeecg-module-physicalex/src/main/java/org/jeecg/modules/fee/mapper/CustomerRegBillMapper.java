package org.jeecg.modules.fee.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.fee.entity.CustomerRegBill;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 结账单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
public interface CustomerRegBillMapper extends BaseMapper<CustomerRegBill> {

    List<CustomerRegBill> listByRegId(@Param("regId") String regId);

    List<CustomerRegBill> listByOrderId(@Param("orderId") String orderId);
}
