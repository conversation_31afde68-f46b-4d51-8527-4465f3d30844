package org.jeecg.modules.fee.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.fee.bo.CompanyFeeStat;
import org.jeecg.modules.fee.entity.FeePayRecord;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 收费记录
 * @Author: jeecg-boot
 * @Date: 2024-06-25
 * @Version: V1.0
 */
@Api(tags = "收费记录")
@RestController
@RequestMapping("/fee/feePayRecord")
@Slf4j
public class FeePayRecordController extends JeecgController<FeePayRecord, IFeePayRecordService> {
    @Autowired
    private IFeePayRecordService feePayRecordService;

    /**
     * 赠送
     */
    @AutoLog(value = "收费记录-赠送")
    @ApiOperation(value = "收费记录-赠送", notes = "收费记录-赠送")
    @PostMapping(value = "/giveAway")
    @RequiresPermissions("fee:fee_pay_record:giveAway")
    public Result<?> giveAway(@RequestBody FeePayRecord feePayRecord) {
        try {
            feePayRecordService.giveAway(feePayRecord);
            return Result.OK("赠送成功！");
        } catch (Exception e) {
            log.error("赠送异常", e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 收费记录-批量退款
     */
    @AutoLog(value = "收费记录-批量退款")
    @ApiOperation(value = "收费记录-批量退款", notes = "收费记录-批量退款")
    @PostMapping(value = "/refundBatch")
    public Result<?> refundBatch(@RequestBody List<FeePayRecord> recordList) {
        try {
            feePayRecordService.refundBatch(recordList);
            return Result.OK("操作成功！");
        } catch (Exception e) {
            log.error("退款异常", e);
            return Result.error("退款失败,详细原因：" + e.getMessage());
        }
    }


    @ApiOperation(value = "收费记录-获取登记信息", notes = "收费记录-获取登记信息")
    @GetMapping(value = "/getCustomerRegWithFee")
    public Result<?> getCustomerRegWithFee(String customerRegId) {
        CustomerReg customerReg = feePayRecordService.getCustomerRegWithFee(customerRegId);
        return Result.OK(customerReg);
    }


    @ApiOperation(value = "收费记录-分页列表查询", notes = "收费记录-分页列表查询")
    @GetMapping(value = "/regList")
    public Result<?> regList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Page<CustomerReg> page = new Page<>(pageNo, pageSize);
        String examNo = req.getParameter("examNo");
        String idCard = req.getParameter("idCard");
        String name = req.getParameter("name");
        String regDateStart = req.getParameter("regDateStart");
        String regDateEnd = req.getParameter("regDateEnd");
        String regStatus = req.getParameter("regStatus");
        String payStatus = req.getParameter("paymentState");
        page = feePayRecordService.pageCustomer(page, examNo, idCard, name, regDateStart, regDateEnd, regStatus, payStatus);

        return Result.OK(page);
    }

    @ApiOperation(value = "收费记录-单位收费统计", notes = "收费记录-单位收费统计")
    @GetMapping(value = "/companyFeeStat")
    public Result<?> companyFeeStat(String companyRegId, HttpServletRequest req) {
        CompanyFeeStat companyFeeStat = feePayRecordService.getCompanyFeeStat(companyRegId);
        return Result.OK(companyFeeStat);
    }

    @ApiOperation(value = "收费记录-获取待收金额", notes = "收费记录-获取待收金额")
    @GetMapping(value = "/getRemainAmount")
    public Result<?> getRemainAmount(String customerRegId, HttpServletRequest req) {
        BigDecimal remainAmount = feePayRecordService.getRemainAmount4Reg(customerRegId, ExConstants.PAYER_TYPE_个人支付);
        return Result.OK(remainAmount);
    }

    @AutoLog(value = "收费记录-体检卡收费")
    @ApiOperation(value = "收费记录-体检卡收费", notes = "收费记录-体检卡收费")
    @PostMapping(value = "/payByCard")
    public Result<?> payByCard(@RequestBody FeePayRecord feePayRecord, HttpServletRequest request) {
        try {
            //获取客户端ip
            String clientIp = request.getRemoteAddr();
            feePayRecord.setClientIp(clientIp);
            feePayRecordService.payByCard(feePayRecord);
            return Result.OK("收款成功！");
        } catch (Exception e) {
            log.error("支付异常", e);
            return Result.error(e.getMessage());
        }
    }

    @AutoLog(value = "收费记录-组合收费")
    @ApiOperation(value = "收费记录-组合收费", notes = "收费记录-组合收费")
    @PostMapping(value = "/payCombined")
    public Result<?> payCombined(@RequestBody FeePayRecord feePayRecord, HttpServletRequest request) {
        try {
            //获取客户端ip
            String clientIp = request.getRemoteAddr();
            feePayRecord.setClientIp(clientIp);
            feePayRecordService.payCombined(feePayRecord);
            return Result.OK("收款成功！");
        } catch (Exception e) {
            log.error("支付异常", e);
            return Result.error(e.getMessage());
        }
    }


    @AutoLog(value = "收费记录-直接收费")
    @ApiOperation(value = "收费记录-直接收费", notes = "收费记录-直接收费")
    @PostMapping(value = "/payOffline")
    public Result<?> payOffline(@RequestBody FeePayRecord feePayRecord, HttpServletRequest request) {
        try {
            //获取客户端ip
            String clientIp = request.getRemoteAddr();
            feePayRecord.setClientIp(clientIp);
            feePayRecordService.payOffline(feePayRecord);
            return Result.OK("收款成功！");
        } catch (Exception e) {
            log.error("支付异常", e);
            return Result.error(e.getMessage());
        }
    }


    @AutoLog(value = "收费记录-门诊收费")
    @ApiOperation(value = "收费记录-门诊收费", notes = "收费记录-门诊收费")
    @PostMapping(value = "/payByHis")
    public Result<?> payByHis(@RequestBody FeePayRecord feePayRecord, HttpServletRequest request) {
        try {
            //获取客户端ip
            String clientIp = request.getRemoteAddr();
            feePayRecord.setClientIp(clientIp);
            feePayRecordService.payByHis(feePayRecord);
            return Result.OK("收款申请发送成功！");
        } catch (Exception e) {
            log.error("支付异常", e);
            return Result.error(e.getMessage());
        }
    }


    @AutoLog(value = "收费记录-发起在线支付")
    @ApiOperation(value = "收费记录-发起在线支付", notes = "收费记录-发起在线支付")
    @PostMapping(value = "/payOnline")
    public Result<?> payOnline(@RequestBody FeePayRecord feePayRecord) {
        try {
            feePayRecordService.payOnline(feePayRecord);
            return Result.OK("收款成功！");
        } catch (Exception e) {
            log.error("支付异常", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分页列表查询
     *
     * @param feePayRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "收费记录-分页列表查询")
    @ApiOperation(value = "收费记录-分页列表查询", notes = "收费记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<FeePayRecord>> queryPageList(FeePayRecord feePayRecord, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<FeePayRecord> queryWrapper = QueryGenerator.initQueryWrapper(feePayRecord, req.getParameterMap());
        Page<FeePayRecord> page = new Page<FeePayRecord>(pageNo, pageSize);
        IPage<FeePayRecord> pageList = feePayRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param feePayRecord
     * @return
     */
    @AutoLog(value = "收费记录-添加")
    @ApiOperation(value = "收费记录-添加", notes = "收费记录-添加")
    @RequiresPermissions("fee:fee_pay_record:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody FeePayRecord feePayRecord) {
        feePayRecordService.save(feePayRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param feePayRecord
     * @return
     */
    @AutoLog(value = "收费记录-编辑")
    @ApiOperation(value = "收费记录-编辑", notes = "收费记录-编辑")
    @RequiresPermissions("fee:fee_pay_record:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody FeePayRecord feePayRecord) {
        feePayRecordService.updateById(feePayRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "收费记录-通过id删除")
    @ApiOperation(value = "收费记录-通过id删除", notes = "收费记录-通过id删除")
    @RequiresPermissions("fee:fee_pay_record:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        feePayRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "收费记录-批量删除")
    @ApiOperation(value = "收费记录-批量删除", notes = "收费记录-批量删除")
    @RequiresPermissions("fee:fee_pay_record:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.feePayRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "收费记录-通过id查询")
    @ApiOperation(value = "收费记录-通过id查询", notes = "收费记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<FeePayRecord> queryById(@RequestParam(name = "id", required = true) String id) {
        FeePayRecord feePayRecord = feePayRecordService.getById(id);
        if (feePayRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(feePayRecord);
    }

    /**
     * 获取收费记录，用于退费
     *
     * @param ids
     */
    //@RequiresPermissions("fee:fee_pay_record:list")
    @RequestMapping(value = "/listForRefund")
    public Result<?> listForRefund(String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        List<FeePayRecord> list = feePayRecordService.getFeePayRecord4Refund(idList);
        return Result.OK(list);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param feePayRecord
     */
    @RequiresPermissions("fee:fee_pay_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, FeePayRecord feePayRecord) {
        return super.exportXls(request, feePayRecord, FeePayRecord.class, "收费记录");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("fee:fee_pay_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, FeePayRecord.class);
    }

}
