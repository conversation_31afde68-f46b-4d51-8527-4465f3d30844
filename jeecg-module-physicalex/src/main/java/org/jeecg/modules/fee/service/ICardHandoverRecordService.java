package org.jeecg.modules.fee.service;

import org.jeecg.modules.fee.entity.CardHandoverRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * @Description: 体检卡换卡记录
 * @Author: jeecg-boot
 * @Date:   2024-10-08
 * @Version: V1.0
 */
public interface ICardHandoverRecordService extends IService<CardHandoverRecord> {

    void addCardHandoverRecord(CardHandoverRecord cardHandoverRecord) throws Exception;
    void updateCardHandoverRecord(CardHandoverRecord cardHandoverRecord) throws Exception;

    void writeCard4Handover(CardHandoverRecord cardHandoverRecord) throws Exception;
}
