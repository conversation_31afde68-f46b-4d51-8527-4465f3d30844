package org.jeecg.modules.fee.service;

import org.jeecg.modules.fee.entity.FeePayChannel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 支付渠道设置
 * @Author: jeecg-boot
 * @Date:   2024-07-14
 * @Version: V1.0
 */
public interface IFeePayChannelService extends IService<FeePayChannel> {

    List<FeePayChannel> listByScene(String scene);

    public void evictAllCache();

}
