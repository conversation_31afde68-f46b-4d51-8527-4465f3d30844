package org.jeecg.modules.fee.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.mzlion.easyokhttp.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExApiConstants;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.jms.JmsMessageSender;
import org.jeecg.excommons.utils.GroovyUtil;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.service.ISequencesService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.fee.bo.PayResult;
import org.jeecg.modules.fee.bo.PaymentAnalysis;
import org.jeecg.modules.fee.entity.*;
import org.jeecg.modules.fee.mapper.*;
import org.jeecg.modules.fee.service.ICustomerRegBillService;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.reg.entity.*;
import org.jeecg.modules.reg.mapper.CompanyTeamItemGroupMapper;
import org.jeecg.modules.reg.mapper.CompanyTeamMapper;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.ICustomerRegBarcodeService;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description: 结账单
 * @Author: jeecg-boot
 * @Date: 2024-12-20
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustomerRegBillServiceImpl extends ServiceImpl<CustomerRegBillMapper, CustomerRegBill> implements ICustomerRegBillService {
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private CustomerRegBillMapper customerRegBillMapper;
    @Autowired
    private FeePayRecordMapper feePayRecordMapper;
    @Autowired
    private ISequencesService sequencesService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private IFeePayRecordService feePayRecordService;
    @Autowired
    private BillRefundRecordMapper billRefundRecordMapper;
    @Autowired
    private CompanyTeamMapper companyTeamMapper;
    @Autowired
    private CompanyTeamItemGroupMapper companyTeamItemGroupMapper;
    @Autowired
    private ICustomerRegBarcodeService customerRegBarcodeService;
    @Autowired
    private TeamCustomerLimitAmountMapper teamCustomerLimitAmountMapper;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private FeeRefundRecordMapper refundRecordMapper;
    @Autowired
    private CardTradeMapper cardTradeMapper;
    @Autowired
    private CardMapper cardMapper;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private LimitOperationRecordMapper limitOperationRecordMapper;
    @Autowired
    private JmsMessageSender jmsMessageSender;
    @Autowired
    private CommonAPI commonAPI;
    @Resource
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    @Qualifier("asyncExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    // 定义允许的接口同步状态
    private static final Set<String> ALLOWED_INTERFACE_SYNC_STATUSES = Set.of(ExConstants.INTERFACE_SYNC_STATUS_WAIT, ExConstants.INTERFACE_SYNC_CANCELED);

    // 校验检查状态，如果状态是未检查且接口同步状态允许才可以退
    private void validateCheckStatus(List<CustomerRegItemGroup> itemGroupList) throws Exception {
        //系统参数的名称叫 接口-查询项目检查状态
        String beforeRefundQueryInterfaceStatus = sysSettingService.getValueByCode("beforeRefundQueryInterfaceStatus");
        if (StringUtils.equals(beforeRefundQueryInterfaceStatus, "1")) {
            boolean allUnChecked = itemGroupList.stream().allMatch(item -> StringUtils.equals(item.getCheckStatus(), ExConstants.CHECK_STATUS_未检) );
            if (!allUnChecked) {
                String refundFeeFialItems = itemGroupList.stream().filter(item -> !StringUtils.equals(item.getCheckStatus(), ExConstants.CHECK_STATUS_未检) ).map(i -> {
                    return String.format("【%s】：执行科室%s", i.getItemGroupName(), i.getCheckStatus() );
                }).collect(Collectors.joining("  "));
                throw new Exception("所选项目存在已检查的项目，无法退费！ " + refundFeeFialItems);
            }
            String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
            hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
            String finaApplyUrl = hipInterfaceUrl + ExApiConstants.QUERY_APPLY_STATUS_PATH;
            String applyResultStr = HttpClient.textBody(finaApplyUrl).json(JSONObject.toJSONString(itemGroupList)).execute().asString();
            JSONObject applyResult = JSONObject.parseObject(applyResultStr);
            if (applyResult == null || !applyResult.getBoolean("success")) {
                log.error("查询申请单状态异常:" + JSONObject.toJSONString(applyResult));
                throw new Exception("查询申请单状态异常,HIS接口调用失败！");
            }
            String applyStatusDesc= applyResult.getString("data");
            if (StringUtils.isNotBlank(applyStatusDesc)) {
                throw new Exception("所选项目存在已执行的项目，无法退费！ " + applyStatusDesc);
            }
        }else {
            List<CustomerRegItemGroup> invalidItems = itemGroupList.stream().filter(item -> !ExConstants.CHECK_STATUS_未检.equals(item.getCheckStatus()) || !ALLOWED_INTERFACE_SYNC_STATUSES.contains(StringUtils.trim(item.getInterfaceSyncStatus()))).toList();

            if (!invalidItems.isEmpty()) {
                String checkedItemNames = invalidItems.stream().map(item -> String.format("【%s】：执行科室%s", item.getItemGroupName(), ExConstants.CHECK_STATUS_已检.equals(item.getCheckStatus()) ? item.getCheckStatus() : item.getInterfaceSyncStatus())).collect(Collectors.joining("  "));
                throw new Exception("所选项目存在已检查的项目，无法退费！ " + checkedItemNames);
            }
        }
    }


    @Override
    public List<CustomerRegBill> listByRegId(String regId) {

        List<CustomerRegBill> list = customerRegBillMapper.listByRegId(regId);
        list.forEach(item -> {
            item.setCustomerRegItemGroups(customerRegItemGroupMapper.listByBillId(item.getId()));
            List<FeePayRecord> feePayRecords = feePayRecordMapper.listByBillId(item.getId(), ExConstants.PAY_STATE_支付成功);
            //计算已支付金额
            BigDecimal paidAmount = feePayRecords.stream().map(FeePayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setPaidAmount(paidAmount);
            BigDecimal unPaidAmount = item.getAmount().subtract(paidAmount);
            item.setUnpaidAmount(unPaidAmount);
            //获取退款单记录
            LambdaQueryWrapper<BillRefundRecord> refundRecordWrapper = new LambdaQueryWrapper<>();
            refundRecordWrapper.eq(BillRefundRecord::getBillId, item.getId());
            Long refundCount = billRefundRecordMapper.selectCount(refundRecordWrapper);
            item.setHaveRefundFlag(!Objects.equals(refundCount, 0L));
        });
        return list;
    }

    public List<CustomerRegBill> listByRegId2(String regId) {

        List<CustomerRegBill> list = customerRegBillMapper.listByRegId(regId);
        list.forEach(item -> {
            item.setCustomerRegItemGroups(customerRegItemGroupMapper.listByBillId(item.getId()));
            List<FeePayRecord> feePayRecords = feePayRecordMapper.listByBillId(item.getId(), null);
            item.setFeePayRecords(feePayRecords);
            //计算已支付金额
            BigDecimal paidAmount = feePayRecords.stream().filter(i -> StringUtils.equals(i.getState(), ExConstants.PAY_STATE_支付成功)).map(FeePayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setPaidAmount(paidAmount);
            BigDecimal unPaidAmount = item.getAmount().subtract(paidAmount);
            item.setUnpaidAmount(unPaidAmount);
            //获取退款单记录
            LambdaQueryWrapper<BillRefundRecord> refundRecordWrapper = new LambdaQueryWrapper<>();
            refundRecordWrapper.eq(BillRefundRecord::getBillId, item.getId());
            List<BillRefundRecord> refundBillList = billRefundRecordMapper.selectList(refundRecordWrapper);
            //遍历退款单，设置退款记录
            for (BillRefundRecord refundBill : refundBillList) {
                //获取支付单
                LambdaQueryWrapper<FeeRefundRecord> feeRefundRecordWrapper = new LambdaQueryWrapper<>();
                feeRefundRecordWrapper.eq(FeeRefundRecord::getBillRefundId, refundBill.getId());
                List<FeeRefundRecord> feeRefundRecordList = refundRecordMapper.selectList(feeRefundRecordWrapper);
                refundBill.setFeeRefundRecords(feeRefundRecordList);
                boolean allRefunded = feeRefundRecordList.stream().anyMatch(i -> StringUtils.equals(i.getState(), "退款中"));
                refundBill.setAllRefundedFlag(!allRefunded);
            }
//            item.setBillRefundRecords(refundBillList);
        });
        return list;
    }


    @Override
    public void validateRecipe(CustomerRegBill regRecipe) throws Exception {
        if (regRecipe == null) {
            throw new Exception("支付信息为空！");
        }
        if (regRecipe.getAmount() == null || regRecipe.getAmount().compareTo(BigDecimal.valueOf(0)) < 0) {
            throw new Exception("支付金额为空或小于0！");
        }
        if (regRecipe.getPayRecords() == null || regRecipe.getPayRecords().isEmpty()) {
            throw new Exception("支付方式为空！");
        }
        if (regRecipe.getCustomerRegItemGroupIds() == null || regRecipe.getCustomerRegItemGroupIds().isEmpty()) {
            throw new Exception("未选择收费项目！");
        }
        CustomerReg customerReg = customerRegMapper.selectById(regRecipe.getCustomerRegId());
        if (customerReg == null) {
            throw new Exception("体检登记信息不存在！");
        }
//        if (!StringUtils.equals(customerReg.getStatus(), ExConstants.REG_STATUS_REGED)) {
//            throw new Exception("未登记不可收费！");
//        }
    }

    @Override
    public List<CustomerRegBill> list4Refund(List<String> idList) {
        LambdaQueryWrapper<CustomerRegBill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CustomerRegBill::getId, idList);
//        queryWrapper.eq(CustomerRegBill::getStatus, ExConstants.PAY_STATE_支付成功);
        List<CustomerRegBill> list = list(queryWrapper);
        list.forEach(item -> {
            item.setCustomerRegItemGroups(customerRegItemGroupMapper.listByBillId(item.getId()));
            List<FeePayRecord> feePayRecords = feePayRecordMapper.listByBillId(item.getId(), ExConstants.PAY_STATE_支付成功);
            item.setPayRecords(feePayRecords);
            //计算已支付金额
            BigDecimal paidAmount = feePayRecords.stream().map(FeePayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setPaidAmount(paidAmount);
            BigDecimal unPaidAmount = item.getAmount().subtract(paidAmount);
            item.setUnpaidAmount(unPaidAmount);
        });
        return list;
    }

    @Override
    public void refundBatch(List<CustomerRegBill> recordList, String clientIp, String refundReason) throws Exception {
        String refundByRecipeNo = sysSettingService.getValueByCode("refundByRecipeNo");
        if (StringUtils.equals(refundByRecipeNo, "1")) {
            //按处方号退费
            refundByRecipeNo(recordList, clientIp, refundReason);
        } else {
            //按收费单退费
            refundByBill(recordList, clientIp, refundReason);
        }
    }
    public void refundByBill(List<CustomerRegBill> recordList, String clientIp, String refundReason) throws Exception {
        for (CustomerRegBill regBill : recordList) {
            List<String> groupIdList = regBill.getRefundItemGroupIds();
            if (groupIdList == null || groupIdList.isEmpty()) {
                throw new Exception("未选择要退费的项目！");
            }

            //1、获取退费项目
            List<CustomerRegItemGroup> itemGroupList = customerRegItemGroupMapper.listByIds(groupIdList);
            regBill.setCustomerRegItemGroups(itemGroupList);
            //校验检查状态，如果状态是未检查才可以退
            //2、校验检查状态
            validateCheckStatus(itemGroupList);
            //3、校验项目是否已经全部收费
            boolean allPayed = itemGroupList.stream().allMatch(item -> StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATE_支付成功) || StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATUS_PAYED));
            if (!allPayed) {
                throw new Exception("所选项目存在未收费的项目，无法退费！");
            }

            //4、生成退款单记录
            BillRefundRecord billRefundRecord = new BillRefundRecord();
            billRefundRecord.setBillId(regBill.getId());
            billRefundRecord.setCustomerRegId(regBill.getCustomerRegId());
            billRefundRecord.setExamNo(regBill.getExamNo());
            billRefundRecord.setRecipeAmount(regBill.getAmount());
            billRefundRecord.setRefundAmount(regBill.getApplyRefundAmount());
            billRefundRecord.setClientIp(clientIp);
            billRefundRecord.setRefundReason(refundReason);
            billRefundRecord.setStatus(ExConstants.REFUND_STATE_订单生成);
            billRefundRecord.setRefundBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_REFUND_BILL_NO)));
            billRefundRecordMapper.insert(billRefundRecord);


            List<FeePayRecord> payRecords = regBill.getPayRecords();
            List<FeePayRecord> validPayRecords = payRecords.stream().filter(record -> record.getApplyRefundAmount() != null).toList();
            if (validPayRecords.isEmpty()) {
                throw new Exception("未选择要退费的支付方式！");
            }

            for (FeePayRecord feePayRecord : validPayRecords) {
                feePayRecord.setBillRefundId(billRefundRecord.getId());
                feePayRecord.setBillRefundNo(billRefundRecord.getRefundBillNo());
                feePayRecord.setClientIp(clientIp);
                feePayRecord.setRefundCustomerRegItemGroupIds(groupIdList);

                if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_门诊)) {
                    feePayRecordService.refundHis4Combine(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_现金)) {
                    feePayRecordService.refundOffline(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_凭证支付)) {
                    feePayRecordService.refundOffline(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_体检卡)) {
                    feePayRecordService.refundCard(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_微信支付) || StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_支付宝支付)) {
                    feePayRecordService.refundOnline(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_赠送)) {
                    feePayRecordService.refund4GiveAway(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_单位支付)) {
                    feePayRecordService.refund4Company(feePayRecord);
                }
            }


            List<String> refundItemGroupIds = itemGroupList.stream().map(CustomerRegItemGroup::getId).toList();
            if (refundItemGroupIds.isEmpty()) {
                continue;
            }

            //计算所有退费记录已退总金额
            BigDecimal refundSuccessAmount = validPayRecords.stream().filter(FeePayRecord::isRefundDone).map(FeePayRecord::getApplyRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            //计算所有退费记录申请退总金额
            BigDecimal applyRefundAmount = BigDecimal.ZERO;
            for (FeePayRecord feePayRecord : validPayRecords) {
                BigDecimal amount = feePayRecord.getApplyRefundAmount() != null ? feePayRecord.getApplyRefundAmount() : BigDecimal.ZERO;
                applyRefundAmount = applyRefundAmount.add(amount);
            }
            if (validPayRecords.stream().allMatch(FeePayRecord::isRefundDone) && refundSuccessAmount.compareTo(applyRefundAmount) == 0) {
                LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper2 = new LambdaUpdateWrapper<>();
                itemGroupLambdaUpdateWrapper2.set(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功).set(CustomerRegItemGroup::getBillRefundId, billRefundRecord.getId()).set(CustomerRegItemGroup::getUpdateTime, new Date()).in(CustomerRegItemGroup::getId, refundItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, regBill.getCustomerRegId());
                customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper2);
                //customerRegBarcodeService.generateBarcode(regBill.getCustomerRegId());
                //查询所有已支付的项目
//                List<CustomerRegItemGroup> payedGroups = customerRegItemGroupMapper.selectList(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getBillRefundId, billRefundRecord.getId()).eq(CustomerRegItemGroup::getPayStatus, ExConstants.PAY_STATE_已支付));
                threadPoolTaskExecutor.execute(() -> {
                    try {
                        itemGroupList.forEach(i -> {
                            i.setPayStatus(ExConstants.REFUND_STATE_退款成功);
                            i.setBillRefundId(billRefundRecord.getId());
                        });
                        customerRegItemGroupService.sendItemGroup2Interface(itemGroupList);
                    } catch (Exception e) {
                        log.error("向接口程序推送项目失败", e);
                    }
                    //异步调用重新生成条码的接口
                    customerRegBarcodeService.generateBarcode(regBill.getCustomerRegId());
                });

            } else {
                //如果payRecords内有一个refundDone为false，则更新customerRegItemGroupIds关联的体检人项目收费状态
                LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper2 = new LambdaUpdateWrapper<>();
                itemGroupLambdaUpdateWrapper2.set(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款中).set(CustomerRegItemGroup::getBillRefundId, billRefundRecord.getId()).set(CustomerRegItemGroup::getLockByRefund, "1").in(CustomerRegItemGroup::getId, refundItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, regBill.getCustomerRegId());
                customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper2);
                threadPoolTaskExecutor.execute(() -> {
                    //中间状态只处理接口推送的项目，生成条码的逻辑放到退费成功后处理
                    try {
                        itemGroupList.forEach(i -> {
                            i.setPayStatus(ExConstants.REFUND_STATE_退款中);
                            i.setBillRefundId(billRefundRecord.getId());
                        });
                        customerRegItemGroupService.sendItemGroup2Interface(itemGroupList);
                    } catch (Exception e) {
                        log.error("向接口程序推送项目失败", e);
                    }

                });
            }
            regBill.setCustomerReg(customerRegMapper.selectById(regBill.getCustomerRegId()));

            sendRefund2Jms(regBill);

        }
    }

    public void refundByRecipeNo(List<CustomerRegBill> recordList, String clientIp, String refundReason) throws Exception {
        List<String> recipeNoList = recordList.stream().map(CustomerRegBill::getRecipeNo).distinct().toList();
        List<CustomerRegBill> allBillList = customerRegBillMapper.selectList(new LambdaQueryWrapper<CustomerRegBill>().in(CustomerRegBill::getRecipeNo, recipeNoList));

        // 从allBillList中筛选出不在recordList中的CustomerRegBill
        List<String> recordListIds = recordList.stream().map(CustomerRegBill::getId).collect(Collectors.toList());
        List<CustomerRegBill> otherBillList = allBillList.stream().filter(bill -> !recordListIds.contains(bill.getId())).collect(Collectors.toList());
        //处理实际有退费项目的收费单
        refundByBill(recordList,clientIp,refundReason);
        //处理没有实际退费项目，但在同一个发票上关联退费的收费单
        refundHis4RelatedRecipe(clientIp, "同一发票关联退费", otherBillList);

    }

    private void refundHis4RelatedRecipe(String clientIp, String refundReason, List<CustomerRegBill> otherBillList) throws Exception {
        for (CustomerRegBill regBill : otherBillList) {
            List<FeePayRecord> validPayRecords = feePayRecordService.getByBillId(regBill.getId());
            //过滤出门诊支付成功的，还有金额需要退的
            validPayRecords = validPayRecords.stream().filter(record -> StringUtils.equals(record.getPayChannel(), ExConstants.PAY_CHANNEL_门诊) && StringUtils.equals(record.getState(), ExConstants.PAY_STATE_支付成功) && record.getAmount().compareTo(record.getRefundAmount()) > 0).toList();
            if (CollectionUtils.isEmpty(validPayRecords)) {
                continue;
            }
            //1、生成退款单记录
            BillRefundRecord billRefundRecord = new BillRefundRecord();
            billRefundRecord.setBillId(regBill.getId());
            billRefundRecord.setCustomerRegId(regBill.getCustomerRegId());
            billRefundRecord.setExamNo(regBill.getExamNo());
            billRefundRecord.setRecipeAmount(regBill.getAmount());
            billRefundRecord.setRefundAmount(regBill.getApplyRefundAmount());
            billRefundRecord.setClientIp(clientIp);
            billRefundRecord.setRefundReason(refundReason);
            billRefundRecord.setStatus(ExConstants.REFUND_STATE_订单生成);
            billRefundRecord.setRefundBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_REFUND_BILL_NO)));
            billRefundRecordMapper.insert(billRefundRecord);

            for (FeePayRecord feePayRecord : validPayRecords) {
                feePayRecord.setApplyRefundAmount(feePayRecord.getAmount().subtract(feePayRecord.getRefundAmount()));
                feePayRecord.setBillRefundId(billRefundRecord.getId());
                feePayRecord.setBillRefundNo(billRefundRecord.getRefundBillNo());
                feePayRecord.setClientIp(clientIp);
                feePayRecordService.refundHis4RelatedRecipe(feePayRecord);
            }
        }
    }

    public void refundBatch2(List<CustomerRegBill> recordList, String clientIp, String refundReason) throws Exception {
        for (CustomerRegBill regBill : recordList) {
            List<String> groupIdList = regBill.getRefundItemGroupIds();
            if (groupIdList == null || groupIdList.isEmpty()) {
                throw new Exception("未选择要退费的项目！");
            }

            //1、获取退费项目
            List<CustomerRegItemGroup> itemGroupList = customerRegItemGroupMapper.listByIds(groupIdList);
            regBill.setCustomerRegItemGroups(itemGroupList);
            //校验检查状态，如果状态是未检查才可以退
            //2、校验检查状态
            validateCheckStatus(itemGroupList);
            //3、校验项目是否已经全部收费
            boolean allPayed = itemGroupList.stream().allMatch(item -> StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATE_支付成功) || StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATUS_PAYED));
            if (!allPayed) {
                throw new Exception("所选项目存在未收费的项目，无法退费！");
            }

            //4、生成退款单记录
            BillRefundRecord billRefundRecord = new BillRefundRecord();
            billRefundRecord.setBillId(regBill.getId());
            billRefundRecord.setCustomerRegId(regBill.getCustomerRegId());
            billRefundRecord.setExamNo(regBill.getExamNo());
            billRefundRecord.setRecipeAmount(regBill.getAmount());
            billRefundRecord.setRefundAmount(regBill.getApplyRefundAmount());
            billRefundRecord.setClientIp(clientIp);
            billRefundRecord.setRefundReason(refundReason);
            billRefundRecord.setStatus(ExConstants.REFUND_STATE_订单生成);
            billRefundRecord.setRefundBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_REFUND_BILL_NO)));
            billRefundRecordMapper.insert(billRefundRecord);


            List<FeePayRecord> payRecords = regBill.getPayRecords();
            List<FeePayRecord> validPayRecords = payRecords.stream().filter(record -> record.getApplyRefundAmount() != null).toList();
            if (validPayRecords.isEmpty()) {
                throw new Exception("未选择要退费的支付方式！");
            }

            for (FeePayRecord feePayRecord : validPayRecords) {
                feePayRecord.setBillRefundId(billRefundRecord.getId());
                feePayRecord.setBillRefundNo(billRefundRecord.getRefundBillNo());
                feePayRecord.setClientIp(clientIp);

                if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_门诊)) {
                    feePayRecordService.refundHis4Combine(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_现金)) {
                    feePayRecordService.refundOffline(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_凭证支付)) {
                    feePayRecordService.refundOffline(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_体检卡)) {
                    feePayRecordService.refundCard(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_微信支付) || StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_支付宝支付)) {
                    feePayRecordService.refundOnline(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_赠送)) {
                    feePayRecordService.refund4GiveAway(feePayRecord);
                } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_单位支付)) {
                    feePayRecordService.refund4Company(feePayRecord);
                }
            }


            List<String> refundItemGroupIds = itemGroupList.stream().map(CustomerRegItemGroup::getId).toList();
            if (refundItemGroupIds.isEmpty()) {
                continue;
            }

            //计算所有退费记录已退总金额
            BigDecimal refundSuccessAmount = validPayRecords.stream().filter(FeePayRecord::isRefundDone).map(FeePayRecord::getApplyRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            //计算所有退费记录申请退总金额
            BigDecimal applyRefundAmount = BigDecimal.ZERO;
            for (FeePayRecord feePayRecord : validPayRecords) {
                BigDecimal amount = feePayRecord.getApplyRefundAmount() != null ? feePayRecord.getApplyRefundAmount() : BigDecimal.ZERO;
                applyRefundAmount = applyRefundAmount.add(amount);
            }
            if (validPayRecords.stream().allMatch(FeePayRecord::isRefundDone) && refundSuccessAmount.compareTo(applyRefundAmount) == 0) {
                LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper2 = new LambdaUpdateWrapper<>();
                itemGroupLambdaUpdateWrapper2.set(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功).set(CustomerRegItemGroup::getBillRefundId, billRefundRecord.getId()).in(CustomerRegItemGroup::getId, refundItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, regBill.getCustomerRegId());
                customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper2);
                //customerRegBarcodeService.generateBarcode(regBill.getCustomerRegId());
                //查询所有已支付的项目
//                List<CustomerRegItemGroup> payedGroups = customerRegItemGroupMapper.selectList(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getBillRefundId, billRefundRecord.getId()).eq(CustomerRegItemGroup::getPayStatus, ExConstants.PAY_STATE_已支付));
                String sendItemGroupFlag = sysSettingService.getValueByCode("send_item_group_flag");
                if (StringUtils.equals(sendItemGroupFlag, "1")) {
                    itemGroupList.forEach(i -> {
                        i.setPayStatus(ExConstants.REFUND_STATE_退款成功);
                        i.setBillRefundId(billRefundRecord.getId());
                    });
                    customerRegItemGroupService.sendItemGroup2Interface(itemGroupList);
                }


            } else {
                //如果payRecords内有一个refundDone为false，则更新customerRegItemGroupIds关联的体检人项目收费状态
                LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper2 = new LambdaUpdateWrapper<>();
                itemGroupLambdaUpdateWrapper2.set(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款中).set(CustomerRegItemGroup::getBillRefundId, billRefundRecord.getId()).in(CustomerRegItemGroup::getId, refundItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, regBill.getCustomerRegId());
                customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper2);

                String sendItemGroupFlag = sysSettingService.getValueByCode("send_item_group_flag");
                if (StringUtils.equals(sendItemGroupFlag, "1")) {
                    itemGroupList.forEach(i -> {
                        i.setPayStatus(ExConstants.REFUND_STATE_退款中);
                        i.setBillRefundId(billRefundRecord.getId());
                    });
                    customerRegItemGroupService.sendItemGroup2Interface(itemGroupList);
                }
            }
            regBill.setCustomerReg(customerRegMapper.selectById(regBill.getCustomerRegId()));

            sendRefund2Jms(regBill);
        }
    }


    @Override
    public void updatePayStatus4Bill(CustomerRegBill bill) {
        if (feePayRecordService.isAllBillPaied(bill)) {
            bill.setStatus(ExConstants.PAY_STATE_支付成功);
            customerRegBillMapper.updateById(bill);
            LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            itemGroupLambdaUpdateWrapper.set(CustomerRegItemGroup::getPayStatus, ExConstants.PAY_STATE_已支付).eq(CustomerRegItemGroup::getBillId, bill.getId()).isNull(CustomerRegItemGroup::getBillRefundId);
            customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper);
        }
    }

    @Transactional
    @Override
    public void generateBillAndPayByCash(List<CustomerRegItemGroup> itemGroups, CustomerReg customerReg, String clientIp) throws Exception {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (itemGroups == null || itemGroups.isEmpty()) {
            return;
        }
        List<CustomerRegItemGroup> unpayItemGroupList = itemGroups.stream().filter(item -> StringUtils.isBlank(item.getBillId())).toList();

        BigDecimal totalAmount = unpayItemGroupList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
        CustomerRegBill regBill = new CustomerRegBill();
        regBill.setAmount(totalAmount);
        regBill.setCustomerRegId(customerReg.getId());
        regBill.setExamNo(customerReg.getExamNo());
        regBill.setCustomerRegItemGroupIds(unpayItemGroupList.stream().map(CustomerRegItemGroup::getId).collect(Collectors.toList()));
        regBill.setCustomerId(customerReg.getCustomerId());
        regBill.setBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_RECIPE_NO)));
        regBill.setCreator(loginUser.getRealname());
        regBill.setDelFlag("0");
        regBill.setStatus(ExConstants.PAY_STATE_支付成功);
        regBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);

        FeePayRecord feePayRecord = new FeePayRecord();
        feePayRecord.setClientIp(clientIp);
        feePayRecord.setAmount(totalAmount);
        feePayRecord.setPayChannel(ExConstants.PAY_CHANNEL_现金);
        feePayRecord.setBizId(customerReg.getId());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecord.setCustomerRegId(customerReg.getId());
        feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
        List<FeePayRecord> payRecords = new ArrayList<>();
        payRecords.add(feePayRecord);

        regBill.setPayRecords(payRecords);
        payBill(regBill);
    }


    @Transactional
    @Override
    public PaymentAnalysis generateBillAndPayCompanyPart(List<CustomerRegItemGroup> itemGroups, CustomerReg customerReg, String clientIp) throws Exception {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        List<CustomerRegItemGroup> unpayItemGroupList = itemGroups.stream().filter(item -> StringUtils.isBlank(item.getBillId())).toList();
        PaymentAnalysis paymentAnalysis = analysisPayment(unpayItemGroupList, customerReg);
        if ((paymentAnalysis.getCompanyAmount().compareTo(BigDecimal.ZERO) > 0) || StringUtils.isNotBlank(customerReg.getOriginCustomerLimitAmountId())) {
            CustomerRegBill regBill = new CustomerRegBill();
            regBill.setAmount(paymentAnalysis.getTotalAmount());
            regBill.setCustomerRegId(customerReg.getId());
            regBill.setExamNo(customerReg.getExamNo());
            regBill.setCustomerRegItemGroupIds(unpayItemGroupList.stream().map(CustomerRegItemGroup::getId).collect(Collectors.toList()));
            regBill.setCustomerId(customerReg.getCustomerId());
            regBill.setCreator(loginUser.getRealname());
            regBill.setDelFlag("0");
            regBill.setStatus(ExConstants.PAY_STATE_支付中);
            regBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);
            regBill.setIp(clientIp);

            FeePayRecord companyPayRecord = new FeePayRecord();
            companyPayRecord.setClientIp(clientIp);
            companyPayRecord.setAmount(paymentAnalysis.getTotalAmount());
            companyPayRecord.setPayChannel(ExConstants.PAY_CHANNEL_单位支付);
            companyPayRecord.setBizId(customerReg.getId());
            companyPayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
            companyPayRecord.setCustomerRegId(customerReg.getId());
            companyPayRecord.setState(ExConstants.PAY_STATE_支付中);
            companyPayRecord.setExamNo(customerReg.getExamNo());
            companyPayRecord.setArchivesNum(customerReg.getArchivesNum());
            companyPayRecord.setCurrency("CNY");
            List<FeePayRecord> payRecords = new ArrayList<>();
            payRecords.add(companyPayRecord);

            regBill.setPayRecords(payRecords);
            payBill(regBill);
        }

        return paymentAnalysis;
    }

    @Override
    public PaymentAnalysis analysisPayment(CustomerReg reg) throws Exception {
        if (reg == null) {
            throw new Exception("体检登记信息不存在！");
        }
        List<CustomerRegItemGroup> itemGroupList = customerRegItemGroupMapper.listByRegAndDepart(reg.getId(), null);
        return analysisPayment(itemGroupList, reg);
    }

    @Override
    public PaymentAnalysis analysisPayment(String regId) throws Exception {
        CustomerReg customerReg = customerRegMapper.selectById(regId);
        if (customerReg == null) {
            throw new Exception("体检登记信息不存在！");
        }
        List<CustomerRegItemGroup> itemGroupList = customerRegItemGroupMapper.listByRegAndDepart(regId, null);

        return analysisPayment(itemGroupList, customerReg);
    }

    @Override
    public PaymentAnalysis analysisPayment(List<CustomerRegItemGroup> customerRegItemGroupList, String regId) throws Exception {
        if (customerRegItemGroupList == null || customerRegItemGroupList.isEmpty()) {
            return new PaymentAnalysis(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }
        CustomerReg customerReg = customerRegMapper.selectById(regId);
        return analysisPayment(customerRegItemGroupList, customerReg);
    }

    @Override
    public PaymentAnalysis analysisPayment(List<CustomerRegItemGroup> itemGroupList, CustomerReg customerReg) throws Exception {
        //获取体检登记的单位ID
        if (customerReg == null) {
            throw new RuntimeException("体检登记信息不存在！");
        } else if (itemGroupList == null || itemGroupList.isEmpty()) {
            return new PaymentAnalysis(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }

        //过滤出待支付的项目
        List<CustomerRegItemGroup> unpayItemGroupList = itemGroupList.stream().filter(item -> StringUtils.isBlank(item.getBillId())).toList();
        if (unpayItemGroupList.isEmpty()) {
            return new PaymentAnalysis(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }

        //计算待付总金额
        BigDecimal shouldPayAmount = unpayItemGroupList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
        CompanyTeam companyTeam = null;
        String payerType = null;
        String addItemPayerType = null;
        if (StringUtils.isBlank(customerReg.getOriginCustomerIdcard())) {
            companyTeam = companyTeamMapper.selectById(customerReg.getTeamId());
            if (Objects.nonNull(companyTeam)) {
                payerType = companyTeam.getPayerType();
                addItemPayerType = companyTeam.getAddItemPayerType();
            }
        } else {
            TeamCustomerLimitAmount originCustomerLimitAmount = teamCustomerLimitAmountMapper.selectById(customerReg.getOriginCustomerLimitAmountId());
            BigDecimal availableLimitAmount = Objects.nonNull(originCustomerLimitAmount) ? originCustomerLimitAmount.getAmount() : BigDecimal.ZERO;
            if (StringUtils.isNotBlank(customerReg.getTeamId())) {
                CustomerReg copyReg = new CustomerReg();
                BeanUtils.copyProperties(customerReg, copyReg);
                copyReg.setOriginCustomerIdcard(null);
                // Recursive call to the same method with modified customerReg
                PaymentAnalysis paymentAnalysis = analysisPayment(unpayItemGroupList, copyReg);
                availableLimitAmount = availableLimitAmount.add(paymentAnalysis.getCompanyAmount());
            }
            if (availableLimitAmount.compareTo(BigDecimal.ZERO) > 0) {
                //有可用限额
                if (shouldPayAmount.compareTo(availableLimitAmount) <= 0) {
                    return new PaymentAnalysis(shouldPayAmount, BigDecimal.ZERO, shouldPayAmount);
                }
                return new PaymentAnalysis(shouldPayAmount, shouldPayAmount.subtract(availableLimitAmount), availableLimitAmount);
            } else {
                //没有可用限额
                return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
            }
        }
        //如果companyTeam为空，则说明是个人体检
        if (companyTeam == null) {
            return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
        }

        //如果支付方是个人支付，则个人支付金额为待支付金额，单位支付金额为0
        if (StringUtils.equals(payerType, ExConstants.PAYER_TYPE_个人支付)) {
            return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
        }

        BigDecimal limitAmount = companyTeam.getLimitAmount();
        //1：单位指定了限额；2：单位指定了项目列表；3：单位未指定限额和项目列表。有限额按限额计算，无限额按项目列表计算

        //情况1：单位指定了限额
        if (limitAmount != null && limitAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal availableLimitAmount = limitAmount;
//            BigDecimal regLimitAmount = customerReg.getLimitAmount();
            if (StringUtils.isBlank(customerReg.getOriginCustomerIdcard())) {
                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(customerReg.getTeamId(), customerReg.getCustomerId(), null);
//                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getCustomerId, customerReg.getCustomerId()).eq(TeamCustomerLimitAmount::getTeamId, customerReg.getTeamId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
                //                if (regLimitAmount != null && regLimitAmount.compareTo(BigDecimal.ZERO) > 0) {
                availableLimitAmount = Objects.nonNull(customerLimitAmount) ? customerLimitAmount.getAmount() : BigDecimal.ZERO;
//                }
            }
           /* if (availableLimitAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal spendAmountOfTeam = getSpendAmountOfTeamByFeePayRecord(customerReg.getIdCard(), customerReg.getTeamId());
                availableLimitAmount = availableLimitAmount.subtract(spendAmountOfTeam);
            }*/

            if (availableLimitAmount.compareTo(BigDecimal.ZERO) > 0) {
                //有可用限额
                if (shouldPayAmount.compareTo(availableLimitAmount) <= 0) {
                    return new PaymentAnalysis(shouldPayAmount, BigDecimal.ZERO, shouldPayAmount);
                }
                return new PaymentAnalysis(shouldPayAmount, shouldPayAmount.subtract(availableLimitAmount), availableLimitAmount);
            } else {
                //没有可用限额
                return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
            }
        } else {
            //情况2：单位指定了项目列表
            List<CompanyTeamItemGroup> companyTeamItemGroups = companyTeamItemGroupMapper.selectByCompanyTeamId(companyTeam.getId());
            if (companyTeamItemGroups != null && !companyTeamItemGroups.isEmpty()) {
                //获取单位指定的项目列表
                List<String> companyTeamItemGroupIds = companyTeamItemGroups.stream().map(CompanyTeamItemGroup::getItemGroupId).toList();
                List<CustomerRegItemGroup> companyTeamItemGroupList = unpayItemGroupList.stream().filter(item -> companyTeamItemGroupIds.contains(item.getItemGroupId())).toList();
                //获取非单位指定的项目列表
                List<CustomerRegItemGroup> otherItemGroupList = unpayItemGroupList.stream().filter(item -> !companyTeamItemGroupIds.contains(item.getItemGroupId())).toList();

                //计算单位指定项目的总金额
                BigDecimal companyTeamAmount = BigDecimal.ZERO;
                if (!companyTeamItemGroupList.isEmpty()) {
                    companyTeamAmount = companyTeamItemGroupList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                //计算非单位未指定项目的总金额
                BigDecimal otherAmount = BigDecimal.ZERO;
                if (!otherItemGroupList.isEmpty()) {
                    otherAmount = otherItemGroupList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                if (StringUtils.equals(addItemPayerType, ExConstants.PAYER_TYPE_单位支付)) {
                    companyTeamAmount = companyTeamAmount.add(otherAmount);
                }
                if (shouldPayAmount.compareTo(companyTeamAmount) <= 0) {
                    return new PaymentAnalysis(shouldPayAmount, BigDecimal.ZERO, shouldPayAmount);
                } else {
                    return new PaymentAnalysis(shouldPayAmount, shouldPayAmount.subtract(companyTeamAmount), companyTeamAmount);
                }
            } else {
                //情况3：单位未指定限额和项目列表
                if (StringUtils.equals(companyTeam.getPayerType(), ExConstants.PAYER_TYPE_单位支付)) {
                    return new PaymentAnalysis(shouldPayAmount, BigDecimal.ZERO, shouldPayAmount);
                } else {
                    return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
                }
            }
        }
    }

    public PaymentAnalysis analysisPayment3(List<CustomerRegItemGroup> itemGroupList, CustomerReg customerReg) throws Exception {
        //获取体检登记的单位ID
        if (customerReg == null) {
            throw new RuntimeException("体检登记信息不存在！");
        } else if (itemGroupList == null || itemGroupList.isEmpty()) {
            return new PaymentAnalysis(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }

        //过滤出待支付的项目
        List<CustomerRegItemGroup> unpayItemGroupList = itemGroupList.stream().filter(item -> StringUtils.isBlank(item.getBillId())).toList();
        if (unpayItemGroupList.isEmpty()) {
            return new PaymentAnalysis(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }

        //计算待付总金额
        BigDecimal shouldPayAmount = unpayItemGroupList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
        CompanyTeam companyTeam = null;
        String payerType = null;
        String addItemPayerType = null;
        if (StringUtils.isBlank(customerReg.getOriginCustomerIdcard())) {
            companyTeam = companyTeamMapper.selectById(customerReg.getTeamId());
            if (Objects.nonNull(companyTeam)) {
                payerType = companyTeam.getPayerType();
                addItemPayerType = companyTeam.getAddItemPayerType();
            }
        } else {
            TeamCustomerLimitAmount originCustomerLimitAmount = teamCustomerLimitAmountMapper.selectById(customerReg.getOriginCustomerLimitAmountId());
            BigDecimal availableLimitAmount = Objects.nonNull(originCustomerLimitAmount) ? originCustomerLimitAmount.getAmount() : BigDecimal.ZERO;
            if (StringUtils.isNotBlank(customerReg.getTeamId())) {
                customerReg.setOriginCustomerIdcard(null);
                // Recursive call to the same method with modified customerReg
                PaymentAnalysis paymentAnalysis = analysisPayment(unpayItemGroupList, customerReg);
                availableLimitAmount = availableLimitAmount.add(paymentAnalysis.getCompanyAmount());
            }
            if (availableLimitAmount.compareTo(BigDecimal.ZERO) > 0) {
                //有可用限额
                if (shouldPayAmount.compareTo(availableLimitAmount) <= 0) {
                    return new PaymentAnalysis(shouldPayAmount, BigDecimal.ZERO, shouldPayAmount);
                }
                return new PaymentAnalysis(shouldPayAmount, shouldPayAmount.subtract(availableLimitAmount), availableLimitAmount);
            } else {
                //没有可用限额
                return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
            }
        }
        //如果companyTeam为空，则说明是个人体检
        if (companyTeam == null) {
            return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
        }

        //如果支付方是个人支付，则个人支付金额为待支付金额，单位支付金额为0
        if (StringUtils.equals(payerType, ExConstants.PAYER_TYPE_个人支付)) {
            return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
        }

        BigDecimal limitAmount = companyTeam.getLimitAmount();
        //1：单位指定了限额；2：单位指定了项目列表；3：单位未指定限额和项目列表。有限额按限额计算，无限额按项目列表计算

        //情况1：单位指定了限额
        if (limitAmount != null && limitAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal availableLimitAmount = limitAmount;
//            BigDecimal regLimitAmount = customerReg.getLimitAmount();
            if (StringUtils.isBlank(customerReg.getOriginCustomerIdcard())) {
                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(customerReg.getTeamId(), customerReg.getCustomerId(), null);
//                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getCustomerId, customerReg.getCustomerId()).eq(TeamCustomerLimitAmount::getTeamId, customerReg.getTeamId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
                //                if (regLimitAmount != null && regLimitAmount.compareTo(BigDecimal.ZERO) > 0) {
                availableLimitAmount = Objects.nonNull(customerLimitAmount) ? customerLimitAmount.getAmount() : BigDecimal.ZERO;
//                }
            }
           /* if (availableLimitAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal spendAmountOfTeam = getSpendAmountOfTeamByFeePayRecord(customerReg.getIdCard(), customerReg.getTeamId());
                availableLimitAmount = availableLimitAmount.subtract(spendAmountOfTeam);
            }*/

            if (availableLimitAmount.compareTo(BigDecimal.ZERO) > 0) {
                //有可用限额
                if (shouldPayAmount.compareTo(availableLimitAmount) <= 0) {
                    return new PaymentAnalysis(shouldPayAmount, BigDecimal.ZERO, shouldPayAmount);
                }
                return new PaymentAnalysis(shouldPayAmount, shouldPayAmount.subtract(availableLimitAmount), availableLimitAmount);
            } else {
                //没有可用限额
                return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
            }
        } else {
            //情况2：单位指定了项目列表
            List<CompanyTeamItemGroup> companyTeamItemGroups = companyTeamItemGroupMapper.selectByCompanyTeamId(companyTeam.getId());
            if (companyTeamItemGroups != null && !companyTeamItemGroups.isEmpty()) {
                //获取单位指定的项目列表
                List<String> companyTeamItemGroupIds = companyTeamItemGroups.stream().map(CompanyTeamItemGroup::getItemGroupId).toList();
                List<CustomerRegItemGroup> companyTeamItemGroupList = unpayItemGroupList.stream().filter(item -> companyTeamItemGroupIds.contains(item.getItemGroupId())).toList();
                //获取非单位指定的项目列表
                List<CustomerRegItemGroup> otherItemGroupList = unpayItemGroupList.stream().filter(item -> !companyTeamItemGroupIds.contains(item.getItemGroupId())).toList();

                //计算单位指定项目的总金额
                BigDecimal companyTeamAmount = BigDecimal.ZERO;
                if (!companyTeamItemGroupList.isEmpty()) {
                    companyTeamAmount = companyTeamItemGroupList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                //计算非单位未指定项目的总金额
                BigDecimal otherAmount = BigDecimal.ZERO;
                if (!otherItemGroupList.isEmpty()) {
                    otherAmount = otherItemGroupList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                if (StringUtils.equals(addItemPayerType, ExConstants.PAYER_TYPE_单位支付)) {
                    companyTeamAmount = companyTeamAmount.add(otherAmount);
                }
                if (shouldPayAmount.compareTo(companyTeamAmount) <= 0) {
                    return new PaymentAnalysis(shouldPayAmount, BigDecimal.ZERO, shouldPayAmount);
                } else {
                    return new PaymentAnalysis(shouldPayAmount, shouldPayAmount.subtract(companyTeamAmount), companyTeamAmount);
                }
            } else {
                //情况3：单位未指定限额和项目列表
                if (StringUtils.equals(companyTeam.getPayerType(), ExConstants.PAYER_TYPE_单位支付)) {
                    return new PaymentAnalysis(shouldPayAmount, BigDecimal.ZERO, shouldPayAmount);
                } else {
                    return new PaymentAnalysis(shouldPayAmount, shouldPayAmount, BigDecimal.ZERO);
                }
            }
        }
    }

    @Override
    public PaymentAnalysis analysisPayment4ItemGroupIds(List<String> itemGroupIds, String regId) throws Exception {
        if (itemGroupIds == null || itemGroupIds.isEmpty()) {
            return new PaymentAnalysis(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }
        CustomerReg customerReg = customerRegMapper.getLiteById(regId);
        if (customerReg == null) {
            throw new Exception("体检登记信息不存在！");
        }
        List<CustomerRegItemGroup> itemGroupListOfReg = customerRegItemGroupMapper.listByReg(customerReg.getId(), null);
        List<CustomerRegItemGroup> matchedItemGroupList = itemGroupListOfReg.stream().filter(item -> itemGroupIds.contains(item.getId())).toList();

        return analysisPayment(matchedItemGroupList, customerReg);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    @Override
    public List<PayResult> payBill(CustomerRegBill regBill) throws Exception {
        List<PayResult> payResults = new ArrayList<>();
        try {
            validateRecipe(regBill);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }

        List<FeePayRecord> payRecords = regBill.getPayRecords();
        List<String> customerRegItemGroupIds = regBill.getCustomerRegItemGroupIds();

        CustomerReg customerReg = customerRegMapper.selectById(regBill.getCustomerRegId());
        LoginUser loginUser = null;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
        }

        regBill.setCustomerId(customerReg.getCustomerId());
        if (loginUser != null) {
            regBill.setCreator(loginUser.getRealname());
        }
        if (StringUtils.equals(regBill.getAfterPayFlag(), "1")) {
            regBill.setStatus(ExConstants.PAY_STATE_待支付);
        } else {
            regBill.setStatus(ExConstants.PAY_STATE_支付中);
        }
        regBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);
        regBill.setExamNo(customerReg.getExamNo());

        if (StringUtils.isBlank(regBill.getId())) {
            regBill.setBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_RECIPE_NO)));
            customerRegBillMapper.insert(regBill);
            jdbcTemplate.batchUpdate("insert into bill_group (bill_id,group_id) values (?,?)", customerRegItemGroupIds.stream().map(id -> new Object[]{regBill.getId(), id}).collect(Collectors.toList()));
        } else {
            customerRegBillMapper.updateById(regBill);
        }
        //保存收费记录
        for (FeePayRecord payRecord : payRecords) {
            try {
                CustomerRegBill finalRegBill = customerRegBillMapper.selectById(regBill.getId());
                payRecord.setBillId(finalRegBill.getId());
                payRecord.setBillNo(finalRegBill.getBillNo());
                payRecord.setCustomerRegId(customerReg.getId());
                payRecord.setExamNo(customerReg.getExamNo());
                payRecord.setName(customerReg.getName());
                payRecord.setArchivesNum(customerReg.getArchivesNum());
                payRecord.setTeamId(customerReg.getTeamId());
                payRecord.setCompanyRegId(customerReg.getCompanyRegId());
                payRecord.setCompanyId(customerReg.getCompanyId());
                payRecord.setCustomerId(customerReg.getCustomerId());
                payRecord.setOriginCustomerLimitAmountId(customerReg.getOriginCustomerLimitAmountId());
                payRecord.setCustomerRegItemGroupIds(customerRegItemGroupIds);

                String successMsg = "支付成功！";
                if (StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_门诊)) {
                    String hisPayCheckExpression = sysSettingService.getValueByCode("hisPayCheckExpression");
                    if (StringUtils.isNotBlank(hisPayCheckExpression)) {
                        boolean result = GroovyUtil.getInstance().executeExpression(hisPayCheckExpression, customerReg);
                        if (!result) {
                            throw new Exception("未建档或未挂号不可收费！");
                        }
                    }
                    feePayRecordService.payByHis4Combine(payRecord);
                    successMsg = "门诊支付申请成功！";
                } else if (StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_现金)) {
                    feePayRecordService.payOffline(payRecord);
                } else if (StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_凭证支付)) {
                    feePayRecordService.payReceipt(payRecord);
                } else if (StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_体检卡)) {
                    feePayRecordService.payByCard(payRecord);
                } else if (StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_微信支付) || StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_支付宝支付)) {
                    feePayRecordService.payOnline(payRecord);
                } else if (StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_赠送)) {
                    feePayRecordService.giveAway(payRecord);
                    successMsg = "赠送成功！";
                } else if (StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_先检后付)) {
                    feePayRecordService.firstCheckAfterPay(payRecord);
                } else if (StringUtils.equals(payRecord.getPayChannel(), ExConstants.PAY_CHANNEL_单位支付)) {
                    feePayRecordService.payByCompany(payRecord);
                } else {
                    throw new Exception("支付方式 " + payRecord.getPayChannel() + " 未实现");
                }
                PayResult payResult = new PayResult();
                payResult.setChannel(payRecord.getPayChannel());
                payResult.setStatus(ExConstants.PAY_STATE_支付成功);
                payResult.setMessage(successMsg);
                payResult.setAmount(payRecord.getAmount());
                payResults.add(payResult);
            } catch (Exception e) {
                PayResult payResult = new PayResult();
                payResult.setChannel(payRecord.getPayChannel());
                payResult.setMessage(e.getMessage());
                payResult.setStatus(ExConstants.PAY_STATE_支付失败);
                payResult.setAmount(payRecord.getAmount());
                payResults.add(payResult);
            }
        }

        String groupPayStatus = ExConstants.PAY_STATE_支付中;
        //计算支付成功的总金额，如果支付成功的总金额与amount一致，则groupPayStatus为支付成功

        if (feePayRecordService.isAllBillPaied(regBill) || StringUtils.equals(regBill.getGiveAwayFlag(), "1") || StringUtils.equals(regBill.getAfterPayFlag(), "1")) {
            groupPayStatus = ExConstants.PAY_STATE_已支付;
            if (feePayRecordService.isAllBillPaied(regBill) || StringUtils.equals(regBill.getGiveAwayFlag(), "1")) {
                regBill.setStatus(ExConstants.PAY_STATE_支付成功);
                customerRegBillMapper.updateById(regBill);
            }
        }
        //更新customerRegItemGroupIds关联的体检人项目收费状态
        String giveAwayFlag = StringUtils.equals(regBill.getGiveAwayFlag(), "1") ? "1" : "0";
        String afterPayFlag = StringUtils.equals(regBill.getAfterPayFlag(), "1") ? "1" : "0";
        LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        itemGroupLambdaUpdateWrapper.set(CustomerRegItemGroup::getBillId, regBill.getId()).set(CustomerRegItemGroup::getPayStatus, groupPayStatus).set(CustomerRegItemGroup::getGiveAwayFlag, giveAwayFlag).set(CustomerRegItemGroup::getFirstCheckAfterPayFlag, afterPayFlag).set(CustomerRegItemGroup::getUpdateTime, new Date()).in(CustomerRegItemGroup::getId, customerRegItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, regBill.getCustomerRegId());
        customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper);


        List<CustomerRegItemGroup> itemGroups = customerRegItemGroupMapper.listByBillId(regBill.getId());
        regBill.setCustomerRegItemGroups(itemGroups);
        regBill.setCustomerReg(customerReg);
        sendPayment2Jms(regBill);

        if (StringUtils.equals(groupPayStatus, ExConstants.PAY_STATE_已支付)) {
            threadPoolTaskExecutor.execute(() -> {
                //查询所有已支付的项目
                try {
                    List<CustomerRegItemGroup> payedGroups = customerRegItemGroupMapper.selectList(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getBillId, regBill.getId()).eq(CustomerRegItemGroup::getPayStatus, ExConstants.PAY_STATE_已支付));
                    customerRegItemGroupService.sendItemGroup2Interface(payedGroups);
                } catch (Exception e) {
                    log.error("向接口程序推送项目异常");
                }
                //异步调用生成条码的接口
                customerRegBarcodeService.generateBarcode(customerReg.getId());
            });
        }


        return payResults;
    }


    @Override
    public BigDecimal getSpendAmountOfTeamByItemGroup(String idCard, String teamId) throws Exception {
        BigDecimal amount = BigDecimal.ZERO;
        try {
            // Fetch all related ID cards
            List<String> allRelatedIdCards = fetchRelatedIdcards(idCard);
            if (CollectionUtils.isNotEmpty(allRelatedIdCards)) {
                // Construct the SQL query with the list of related ID cards
                String inClause = allRelatedIdCards.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
                String sql = "SELECT SUM(cg.price_after_dis) FROM customer_reg c " + "JOIN customer_reg_item_group cg ON c.id = cg.customer_reg_id " + "WHERE cg.give_away_flag = '0' AND cg.payer_type = '单位支付' " + "AND cg.add_minus_flag != '-1' AND c.team_id = ? " + "AND c.id_card IN (" + inClause + ")";
                // Execute the query
                amount = jdbcTemplate.queryForObject(sql, BigDecimal.class, teamId);
            }
        } catch (Exception e) {
            log.error("Error calculating spend amount for team", e);
        }
        return amount != null ? amount : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getSpendAmountOfTeamByFeePayRecord(String idCard, String teamId) throws Exception {

        BigDecimal amount = BigDecimal.ZERO;
        try {
            // Fetch all related ID cards
            List<String> allRelatedIdCards = fetchRelatedIdcards(idCard);
            if (CollectionUtils.isNotEmpty(allRelatedIdCards)) {
                // Construct the SQL query with the list of related ID cards
                String inClause = allRelatedIdCards.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
                String sql = "SELECT SUM(f.amount - f.refund_amount) FROM fee_pay_record f join customer_reg c on f.customer_reg_id = c.id WHERE f.pay_channel=? and c.team_id=? and  c.id_card IN (" + inClause + ")";
                // Execute the query
                amount = jdbcTemplate.queryForObject(sql, BigDecimal.class, ExConstants.PAY_CHANNEL_单位支付, teamId);

                /*if (amount == null) {
                    amount = getSpendAmountOfTeamByItemGroup(idCard, teamId);
                }*/
            }
        } catch (Exception e) {
            log.error("Error calculating spend amount for team", e);
        }
        return amount != null ? amount : BigDecimal.ZERO;
    }

    @Override
    public List<String> fetchRelatedIdcards(String idCard) {
        Set<String> idCardSet = new HashSet<>();
        Queue<String> queue = new LinkedList<>();
        queue.add(idCard);

        while (!queue.isEmpty()) {
            String currentIdCard = queue.poll();
            if (idCardSet.contains(currentIdCard)) {
                continue;
            }
            idCardSet.add(currentIdCard);

            // Fetch related idCards for the current idCard
            List<String> relatedIdCards = jdbcTemplate.queryForList("SELECT id_card FROM customer_reg WHERE origin_customer_idcard = ? and origin_customer_idcard !=''  UNION SELECT origin_customer_idcard FROM customer_reg WHERE id_card = ?", String.class, currentIdCard, currentIdCard);
            // Add new related idCards to the queue
            for (String relatedIdCard : relatedIdCards) {
                if (!idCardSet.contains(relatedIdCard)) {
                    queue.add(relatedIdCard);
                }
            }
        }
        //去除null元素
        idCardSet.remove(null);
        //去除空字符串元素
        idCardSet.remove("");

        return new ArrayList<>(idCardSet);
    }

    @Override
    public List<CustomerReg> fetchRelatedRegs(String customerId, String teamId) {
        //查询原检人本人
        List<CustomerReg> originRegs = customerRegMapper.selectList(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getCustomerId, customerId).eq(CustomerReg::getTeamId, teamId));
        //查询原检人额度表
        TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(teamId, customerId, null);

//        TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getCustomerId, customerId).eq(TeamCustomerLimitAmount::getTeamId, teamId).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
        if (Objects.nonNull(customerLimitAmount)) {
            //查询使用当前团检额度的亲友
            List<CustomerReg> relationRegs = customerRegMapper.selectList(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getOriginCustomerLimitAmountId, customerLimitAmount.getId()));
            originRegs.addAll(relationRegs);
        }
        return originRegs;
    }

    @Transactional
    @Override
    public void invalidateBill(String billId, String clientIp) throws Exception {
        //获取支付单记录
        CustomerRegBill regBill = customerRegBillMapper.selectById(billId);
        if (regBill == null) {
            throw new Exception("结账单不存在！");
        }
        if (StringUtils.equals(regBill.getStatus(), ExConstants.PAY_STATE_支付成功)) {
            throw new Exception("结账单已支付成功，无法作废！");
        }
        //获取结账单关联的支付记录
        List<FeePayRecord> payRecords = feePayRecordMapper.listByBillId(billId, null);
        if (payRecords == null || payRecords.isEmpty()) {
            jdbcTemplate.update("update customer_reg_item_group set pay_status=?,bill_id=null where bill_id=?", ExConstants.PAY_STATUS_WAIT, billId);

            regBill.setStatus(ExConstants.PAY_STATE_已撤销);
            customerRegBillMapper.updateById(regBill);
        } else {
            //过滤出已支付的支付记录
            List<FeePayRecord> paidPayRecords = payRecords.stream().filter(record -> StringUtils.equals(record.getState(), ExConstants.PAY_STATE_支付成功)).toList();
            //过滤出未支付的支付记录
            List<FeePayRecord> unPaidPayRecords = payRecords.stream().filter(record -> StringUtils.equals(record.getState(), ExConstants.PAY_STATE_支付中)).toList();

            if (!paidPayRecords.isEmpty()) {
                BillRefundRecord billRefundRecord = new BillRefundRecord();
                billRefundRecord.setBillId(regBill.getId());
                billRefundRecord.setCustomerRegId(regBill.getCustomerRegId());
                billRefundRecord.setRecipeAmount(regBill.getAmount());
                billRefundRecord.setRefundAmount(regBill.getAmount());
                billRefundRecord.setClientIp(clientIp);
                billRefundRecord.setRefundReason("作废结账单");
                billRefundRecord.setStatus(ExConstants.REFUND_STATE_订单生成);
                billRefundRecord.setRefundBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_REFUND_BILL_NO)));
                billRefundRecordMapper.insert(billRefundRecord);
                for (FeePayRecord feePayRecord : paidPayRecords) {
                    feePayRecord.setBillRefundId(billRefundRecord.getId());
                    feePayRecord.setApplyRefundAmount(feePayRecord.getAmount());
                    feePayRecord.setBillRefundNo(billRefundRecord.getRefundBillNo());

                    if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_门诊)) {
                        feePayRecordService.refundHis4Combine(feePayRecord);
                    } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_现金)) {
                        feePayRecordService.refundOffline(feePayRecord);
                    } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_凭证支付)) {
                        feePayRecordService.refundOffline(feePayRecord);
                    } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_体检卡)) {
                        feePayRecordService.refundCard(feePayRecord);
                    } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_微信支付) || StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_支付宝支付)) {
                        feePayRecordService.refundOnline(feePayRecord);
                    } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_赠送)) {
                        feePayRecordService.refund4GiveAway(feePayRecord);
                    } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_单位支付)) {
                        feePayRecordService.refund4Company(feePayRecord);
                    }
                }
            }

            if (!unPaidPayRecords.isEmpty()) {
                for (FeePayRecord feePayRecord : unPaidPayRecords) {
                    if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_门诊)) {
                        feePayRecordService.invalidHisPayRecord(feePayRecord);
                    }
                    feePayRecord.setState(ExConstants.PAY_STATE_订单关闭);
                    feePayRecordMapper.updateById(feePayRecord);
                }
            }

            jdbcTemplate.update("update customer_reg_item_group set pay_status=?,bill_id=null where bill_id=?", ExConstants.PAY_STATUS_WAIT, billId);

            regBill.setStatus(ExConstants.PAY_STATE_已撤销);
            customerRegBillMapper.updateById(regBill);
        }
    }

    @Transactional
    @Override
    public void invalidateRefundBill(String refundBillId, String clientIp) throws Exception {
        //获取退费单记录
        BillRefundRecord billRefundRecord = billRefundRecordMapper.selectById(refundBillId);
        if (billRefundRecord == null) {
            throw new Exception("退费单不存在！");
        }
        CustomerReg customerReg = customerRegMapper.selectById(billRefundRecord.getCustomerRegId());
        if (Objects.isNull(customerReg)) {
            throw new Exception("未查询到对应登记信息！");
        }
       /* if (StringUtils.equals(regBill.getStatus(), ExConstants.PAY_STATE_支付成功)) {
            throw new Exception("结账单已支付成功，无法作废！");
        }*/
        //获取退费单关联的退费记录
        List<FeeRefundRecord> feeRefundRecords = refundRecordMapper.selectList(new LambdaQueryWrapper<FeeRefundRecord>().eq(FeeRefundRecord::getBillRefundId, refundBillId));
//        List<FeePayRecord> payRecords = feePayRecordMapper.listByBillId(billId, null);
        boolean allRefundFlag = feeRefundRecords.stream().allMatch(record -> StringUtils.equals(record.getState(), ExConstants.REFUND_STATE_退款成功));
        if (allRefundFlag) {
            throw new Exception("退费单已退款成功，无法作废！");
        }
        if (feeRefundRecords == null || feeRefundRecords.isEmpty()) {
            jdbcTemplate.update("update customer_reg_item_group set pay_status=?,bill_refund_id=null where bill_refund_id=?", ExConstants.PAY_STATUS_PAYED, refundBillId);

            billRefundRecord.setStatus(ExConstants.REFUND_STATE_已撤销);
            billRefundRecordMapper.updateById(billRefundRecord);
        } else {
            //过滤出已支付的支付记录
            List<FeeRefundRecord> refundedPayRecords = feeRefundRecords.stream().filter(record -> StringUtils.equals(record.getState(), ExConstants.REFUND_STATE_退款成功)).toList();
            //过滤出未支付的支付记录
            List<FeeRefundRecord> unRefundedPayRecords = feeRefundRecords.stream().filter(record -> StringUtils.equals(record.getState(), ExConstants.REFUND_STATE_退款中)).toList();

            if (!refundedPayRecords.isEmpty()) {
//                CustomerRegBill customerRegBill=new CustomerRegBill();
//                customerRegBill.setCustomerRegId(billRefundRecord.getCustomerRegId());
//                customerRegBill.setCustomerId(customerReg.getCustomerId());
//                customerRegBill.setAmount(refundedPayRecords.stream().map(FeeRefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
//                customerRegBill.setStatus(ExConstants.PAY_STATE_支付成功);
//                customerRegBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);
//                customerRegBill.setExamNo(customerReg.getExamNo());
//                customerRegBill.setBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_RECIPE_NO)));
//                customerRegBillMapper.insert(customerRegBill);
                for (FeeRefundRecord feeRefundRecord : refundedPayRecords) {
                    FeePayRecord feePayRecord = new FeePayRecord();
                    feePayRecord.setAmount(feeRefundRecord.getRefundAmount());
                    feePayRecord.setCustomerId(customerReg.getCustomerId());
                    feePayRecord.setPayChannel(feeRefundRecord.getWayCode());
                    feePayRecord.setCustomerRegId(customerReg.getId());
//                    feePayRecord.setBillId(customerRegBill.getId());
                    feePayRecord.setName(customerReg.getName());
                    feePayRecord.setExamNo(customerReg.getExamNo());
                    if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_体检卡)) {
                        String payRecordId = feeRefundRecord.getPayRecordId();
                        FeePayRecord payRecord = feePayRecordMapper.selectById(payRecordId);
                        String originCardTradeId = payRecord.getChannelOrderNo();
                        CardTrade originCardTrade = cardTradeMapper.selectById(originCardTradeId);
                        Card card = cardMapper.getByCardNo(originCardTrade.getCardNo());
                        feePayRecord.setCardNo(card.getCardNo());
                        feePayRecord.setPwd(card.getPwd());
                        feePayRecordService.invalidatePayByCard(feePayRecord);
                    } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_单位支付)) {
                        feePayRecord.setTeamId(customerReg.getTeamId());
                        feePayRecord.setOriginCustomerLimitAmountId(customerReg.getOriginCustomerLimitAmountId());
                        feePayRecordService.invalidatePayByCompany(feePayRecord);
                    } else {
                        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款任务关闭);
                        refundRecordMapper.updateById(feeRefundRecord);
                    }
                }
            }

            if (!unRefundedPayRecords.isEmpty()) {
                for (FeeRefundRecord feeRefundRecord : unRefundedPayRecords) {
                    if (StringUtils.equals(feeRefundRecord.getWayCode(), ExConstants.PAY_CHANNEL_门诊)) {
                        feePayRecordService.invalidHisRefundRecord(feeRefundRecord);
                    }
                    feeRefundRecord.setState(ExConstants.REFUND_STATE_退款任务关闭);
                    refundRecordMapper.updateById(feeRefundRecord);
                }
            }
            //推送项目给接口程序
            threadPoolTaskExecutor.execute(() -> {
                try {
                    List<CustomerRegItemGroup> itemGroups = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getBillRefundId, refundBillId));
                    itemGroups.forEach(itemGroup -> {
                        //同一个退费单项目状态是一致的，不存在一个退费单下的项目不一致的情况，统一设成已支付是
                        itemGroup.setPayStatus(ExConstants.PAY_STATUS_PAYED);
                    });
                    customerRegItemGroupService.sendItemGroup2Interface(itemGroups);
                } catch (Exception e) {
                    log.error("向接口程序推送项目失败", e);
                }
            });
            jdbcTemplate.update("update customer_reg_item_group set pay_status=?,bill_refund_id=null,lock_by_refund='0' where bill_refund_id=?", ExConstants.PAY_STATUS_PAYED, refundBillId);
            billRefundRecord.setStatus(ExConstants.REFUND_STATE_已撤销);
            billRefundRecordMapper.updateById(billRefundRecord);
        }
    }

    @Override
    public List<FeePayRecord> getRelatedCompanyPayRecords(String customerRegId) {

        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            return new ArrayList<>();
        }
        List<CustomerReg> allRegs = Lists.newArrayList();
        if (StringUtils.isBlank(customerReg.getOriginCustomerLimitAmountId())) {
            List<CustomerReg> originRegs = customerRegMapper.selectList(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getCustomerId, customerReg.getCustomerId()).eq(CustomerReg::getTeamId, customerReg.getTeamId()));
            List<CustomerReg> relationRegs = customerRegMapper.selectRelationRegsByOriginCustomerIdAndTeamId(customerReg.getCustomerId(), customerReg.getTeamId());
            if (CollectionUtils.isNotEmpty(originRegs)) {
                allRegs.addAll(originRegs);
            }
            if (CollectionUtils.isNotEmpty(relationRegs)) {
                allRegs.addAll(relationRegs);
            }
        } else {
            List<CustomerReg> relationRegs = customerRegMapper.selectList(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getOriginCustomerLimitAmountId, customerReg.getOriginCustomerLimitAmountId()));
            List<CustomerReg> originRegs = customerRegMapper.selectOriginRegsByRelationCustomerAccountId(customerReg.getOriginCustomerLimitAmountId());
            if (CollectionUtils.isNotEmpty(originRegs)) {
                allRegs.addAll(originRegs);
            }
            if (CollectionUtils.isNotEmpty(relationRegs)) {
                allRegs.addAll(relationRegs);
            }
        }
//        List<String> allRelatedIdCards = fetchRelatedIdcards(customerReg.getIdCard());
        List<String> regIds = allRegs.stream().map(CustomerReg::getId).toList();
        if (CollectionUtils.isEmpty(regIds)) {
            return new ArrayList<>();
        }

        return feePayRecordMapper.selectList(new LambdaQueryWrapper<FeePayRecord>().eq(FeePayRecord::getPayChannel, ExConstants.PAY_CHANNEL_单位支付).in(FeePayRecord::getCustomerRegId, regIds));
    }

    public List<FeePayRecord> getRelatedCompanyPayRecords2(String customerRegId) {

        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            return new ArrayList<>();
        }
        List<String> allRelatedIdCards = fetchRelatedIdcards(customerReg.getIdCard());
        if (CollectionUtils.isEmpty(allRelatedIdCards)) {
            return new ArrayList<>();
        }

        return feePayRecordMapper.selectByRelatedIdcards(allRelatedIdCards, ExConstants.PAY_CHANNEL_单位支付, customerReg.getTeamId());
    }

    @Override
    public List<FeePayRecord> getRelatedCompanyPayRecordsByIdacrd(String idCard) {
        String sql = "SELECT team_id, name,customer_id FROM customer_reg WHERE id_card = ? and (team_id is not null or team_id !='') ORDER BY reg_time DESC LIMIT 1";
        Map<String, Object> result = jdbcTemplate.queryForMap(sql, idCard);

        if (result.isEmpty()) {
            return new ArrayList<>();
        }
        String teamId = (String) result.get("team_id");
        String customerId = (String) result.get("customer_id");
        //String customerName = (String) result.get("name");
        List<CustomerReg> customerRegs = fetchRelatedRegs(customerId, teamId);
        if (CollectionUtils.isEmpty(customerRegs)) {
            return new ArrayList<>();
        }
        List<String> allRelatedRegIds = customerRegs.stream().map(CustomerReg::getId).toList();
        if (CollectionUtils.isEmpty(allRelatedRegIds)) {
            return new ArrayList<>();
        }

        return feePayRecordMapper.selectList(new LambdaQueryWrapper<FeePayRecord>().in(FeePayRecord::getCustomerRegId, allRelatedRegIds));
    }

    public List<FeePayRecord> getRelatedCompanyPayRecordsByIdacrd2(String idCard) {
        String sql = "SELECT team_id, name FROM customer_reg WHERE id_card = ? ORDER BY reg_time DESC LIMIT 1";
        Map<String, Object> result = jdbcTemplate.queryForMap(sql, idCard);

        if (result.isEmpty()) {
            return new ArrayList<>();
        }
        String teamId = (String) result.get("team_id");
        //String customerName = (String) result.get("name");

        List<String> allRelatedIdCards = fetchRelatedIdcards(idCard);
        if (CollectionUtils.isEmpty(allRelatedIdCards)) {
            return new ArrayList<>();
        }

        return feePayRecordMapper.selectByRelatedIdcards(allRelatedIdCards, ExConstants.PAY_CHANNEL_单位支付, teamId);
    }

    @Transactional
    @Override
    public BatchResult<CustomerReg> sendFee2HisBatch(List<CustomerReg> list, String clientIp) throws Exception {
        BatchResult<CustomerReg> batchResult = new BatchResult<>();
        List<CustomerReg> successList = new ArrayList<>();
        List<BatchResult.FailureResult<CustomerReg>> failList = new ArrayList<>();
        for (CustomerReg customerReg : list) {
            try {
                //生成支付单
                List<CustomerRegItemGroup> itemGroups = customerRegItemGroupMapper.listByRegAndDepart(customerReg.getId(), null);
                if (itemGroups == null || itemGroups.isEmpty()) {
                    failList.add(new BatchResult.FailureResult<CustomerReg>(customerReg, "体检项目为空"));
                    continue;
                }
                BigDecimal totalAmount = itemGroups.stream().filter(item -> StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATUS_WAIT) && item.getAddMinusFlag() != -1).map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
                    failList.add(new BatchResult.FailureResult<CustomerReg>(customerReg, "未支付金额为0"));
                    continue;
                }
                CustomerRegBill regBill = new CustomerRegBill();
                regBill.setAmount(totalAmount);
                regBill.setCustomerRegId(customerReg.getId());
                regBill.setExamNo(customerReg.getExamNo());
                regBill.setCustomerRegItemGroupIds(itemGroups.stream().map(CustomerRegItemGroup::getId).collect(Collectors.toList()));
                regBill.setCustomerId(customerReg.getCustomerId());
                regBill.setCreator("system");
                regBill.setDelFlag("0");
                regBill.setStatus(ExConstants.PAY_STATE_支付中);
                regBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);
                regBill.setCreator(customerReg.getCreator());
                regBill.setCreateBy(customerReg.getCreatorBy());
                regBill.setIp(clientIp);

                FeePayRecord payRecord = new FeePayRecord();
                payRecord.setClientIp(clientIp);
                payRecord.setAmount(totalAmount);
                payRecord.setPayChannel(ExConstants.PAY_CHANNEL_门诊);
                payRecord.setBizId(customerReg.getId());
                payRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
                payRecord.setCustomerRegId(customerReg.getId());
                payRecord.setState(ExConstants.PAY_STATE_支付中);
                payRecord.setExamNo(customerReg.getExamNo());
                payRecord.setArchivesNum(customerReg.getArchivesNum());
                payRecord.setCurrency("CNY");
                List<FeePayRecord> payRecords = new ArrayList<>();
                payRecords.add(payRecord);

                regBill.setPayRecords(payRecords);
                payBill(regBill);

                successList.add(customerReg);
            } catch (Exception e) {
                log.error("自动收费异常:customerRegId: " + customerReg.getId(), e);
                customerReg.setRemark(e.getMessage());
                BatchResult.FailureResult<CustomerReg> failureResult = new BatchResult.FailureResult<CustomerReg>(customerReg, e.getMessage());
                failList.add(failureResult);
            }
        }

        batchResult.setSuccessResults(successList);
        batchResult.setFailureResults(failList);

        return batchResult;
    }

    @Override
    public void fixNoBillFeePayRecord() throws Exception {
        LambdaQueryWrapper<FeePayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FeePayRecord::getState, ExConstants.PAY_STATE_支付中);
        queryWrapper.isNull(FeePayRecord::getBillNo);
        //大于2025-01-20 00:00:00
        queryWrapper.gt(FeePayRecord::getCreatedTime, LocalDateTime.of(2020, 1, 20, 0, 0, 0));
        List<FeePayRecord> regBillList = feePayRecordMapper.selectList(queryWrapper);
        for (FeePayRecord feePayRecord : regBillList) {
            try {
                CustomerRegBill regBill = new CustomerRegBill();
                regBill.setAmount(feePayRecord.getAmount());
                regBill.setCustomerRegId(feePayRecord.getCustomerRegId());
                regBill.setExamNo(feePayRecord.getExamNo());
                regBill.setCustomerId(feePayRecord.getCustomerRegId());
                regBill.setCreator("system");
                regBill.setDelFlag("0");
                regBill.setStatus(ExConstants.PAY_STATE_支付中);
                regBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);
                regBill.setCreator(feePayRecord.getCreator());
                regBill.setCreateBy(feePayRecord.getCreateBy());
                regBill.setBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_RECIPE_NO)));
                regBill.setIp(feePayRecord.getClientIp());
                regBill.setGiveAwayFlag("0");
                save(regBill);

                feePayRecord.setBillNo(regBill.getBillNo());
                feePayRecord.setBillId(regBill.getId());
                feePayRecordMapper.updateById(feePayRecord);

                jdbcTemplate.update("update customer_reg_item_group set pay_status=?,bill_id=? where customer_reg_id=?", ExConstants.PAY_STATUS_WAIT, regBill.getId(), feePayRecord.getCustomerRegId());
            } catch (Exception e) {
                log.error("Error fixing no bill feePayRecord: " + feePayRecord.getId(), e);
            }
        }
    }

    @Override
    public List<CustomerRegBill> listWithFeePayRecordsByRegId(String regId) {
        //获取支付单记录
        List<CustomerRegBill> regBillList = customerRegBillMapper.listByRegId(regId);
        regBillList.forEach(item -> {
            item.setCustomerRegItemGroups(customerRegItemGroupMapper.listByBillId(item.getId()));
            List<FeePayRecord> feePayRecords = feePayRecordMapper.listByBillId(item.getId(), null);
            item.setPayRecords(feePayRecords);
            List<FeePayRecord> successPayRecords = feePayRecords.stream().filter(record -> StringUtils.equals(record.getState(), ExConstants.PAY_STATE_支付成功)).toList();
            //计算已支付金额
            BigDecimal paidAmount = successPayRecords.stream().map(FeePayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setPaidAmount(paidAmount);
            BigDecimal unPaidAmount = item.getAmount().subtract(paidAmount);
            item.setUnpaidAmount(unPaidAmount);
        });

        return regBillList;
    }

    @Override
    public CustomerRegBill listWithFeePayRecordsById(String id) {
        //获取支付单记录
        CustomerRegBill regBill = customerRegBillMapper.selectById(id);

        regBill.setCustomerRegItemGroups(customerRegItemGroupMapper.listByBillId(regBill.getId()));
        List<FeePayRecord> feePayRecords = feePayRecordMapper.listByBillId(regBill.getId(), null);
        regBill.setPayRecords(feePayRecords);
        List<FeePayRecord> successPayRecords = feePayRecords.stream().filter(record -> StringUtils.equals(record.getState(), ExConstants.PAY_STATE_支付成功)).toList();
        //计算已支付金额
        BigDecimal paidAmount = successPayRecords.stream().map(FeePayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        regBill.setPaidAmount(paidAmount);
        BigDecimal unPaidAmount = regBill.getAmount().subtract(paidAmount);
        regBill.setUnpaidAmount(unPaidAmount);

        return regBill;
    }

    @Override
    public List<FeeRecord> listFeeRecordsByBillId(String billId) {
        List<FeeRecord> payRecords = feePayRecordMapper.listPayRecordByBillId(billId);
        List<FeeRecord> refundRecords = refundRecordMapper.selectFeeRefundRecordByBillId(billId);
        if (CollectionUtils.isNotEmpty(payRecords)) {
            payRecords.addAll(refundRecords);
        }
        List<FeeRecord> sortedRecords = payRecords.stream().sorted(Comparator
                .comparing(FeeRecord::getPayChannel)
                .thenComparing(FeeRecord::getCreatedTime)
        ).toList();
        return sortedRecords;
    }

    @Override
    public List<CustomerRegBill> listWithFeePayRecords4RefundByOrderId(String orderId) {
        //获取支付单记录
        List<CustomerRegBill> regBillList = customerRegBillMapper.listByOrderId(orderId);
        regBillList.forEach(bill -> {

            List<String> customerRegItemGroupIds = customerRegItemGroupMapper.listIdByBillId(bill.getId());
            bill.setRefundItemGroupIds(customerRegItemGroupIds);

            List<FeePayRecord> feePayRecords = feePayRecordMapper.listByBillId(bill.getId(), null);

            for (FeePayRecord feePayRecord : feePayRecords) {
                feePayRecord.setApplyRefundAmount(feePayRecord.getAmount());
            }

            bill.setPayRecords(feePayRecords);
            List<FeePayRecord> successPayRecords = feePayRecords.stream().filter(record -> StringUtils.equals(record.getState(), ExConstants.PAY_STATE_支付成功)).toList();
            //计算已支付金额
            BigDecimal paidAmount = successPayRecords.stream().map(FeePayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            bill.setPaidAmount(paidAmount);
            BigDecimal unPaidAmount = bill.getAmount().subtract(paidAmount);
            bill.setUnpaidAmount(unPaidAmount);

            bill.setApplyRefundAmount(paidAmount);
        });

        return regBillList;
    }


    @Override
    public CustomerRegBill generateBillAndPayByCard(CustomerOrder customerOrder, CustomerReg reg, List<CustomerRegItemGroup> itemGroups, String clientIp) throws Exception {
        //进行必要的校验
        if (customerOrder == null || StringUtils.isBlank(customerOrder.getId())) {
            throw new Exception("订单信息不存在！");
        }
        if (reg == null || StringUtils.isBlank(reg.getId())) {
            throw new Exception("体检登记信息不存在！");
        }
        if (itemGroups == null || itemGroups.isEmpty()) {
            throw new Exception("体检项目为空！");
        }
        if (StringUtils.equals(customerOrder.getStatus(), ExConstants.ORDER_STATUS_待登记)) {
            throw new Exception("订单已支付，请勿重复支付！");
        }
        //生成支付单
        CustomerRegBill regBill = new CustomerRegBill();
        regBill.setAmount(customerOrder.getActualAmount());
        regBill.setCustomerRegId(reg.getId());
        regBill.setExamNo(reg.getExamNo());
        regBill.setCustomerRegItemGroupIds(itemGroups.stream().map(CustomerRegItemGroup::getId).collect(Collectors.toList()));
        regBill.setCustomerId(reg.getCustomerId());
        regBill.setCreator("system");
        regBill.setDelFlag("0");
        regBill.setStatus(ExConstants.PAY_STATE_支付中);
        regBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);
        regBill.setCreator(reg.getCreator());
        regBill.setCreateBy(reg.getCreatorBy());
        regBill.setIp(clientIp);
        regBill.setBillNo(String.valueOf(sequencesService.getNextSequence(ExConstants.SEQ_RECIPE_NO)));
        regBill.setGiveAwayFlag("0");
        regBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);
        regBill.setOrderId(customerOrder.getId());
        save(regBill);

        //生成支付记录
        FeePayRecord payRecord = new FeePayRecord();
        payRecord.setBillId(regBill.getId());
        payRecord.setBillNo(regBill.getBillNo());
        payRecord.setCustomerRegId(reg.getId());
        payRecord.setExamNo(reg.getExamNo());
        payRecord.setName(reg.getName());
        payRecord.setArchivesNum(reg.getArchivesNum());
        payRecord.setAmount(customerOrder.getActualAmount());
        payRecord.setClientIp(clientIp);
        payRecord.setCurrency("CNY");
        payRecord.setPayChannel(ExConstants.PAY_CHANNEL_体检卡);
        payRecord.setBizId(customerOrder.getId());
        payRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        payRecord.setState(ExConstants.PAY_STATE_支付中);
        payRecord.setCardNo(customerOrder.getCardNo());
        payRecord.setPwd(customerOrder.getCardPwd());
        payRecord.setWayCode(ExConstants.PAY_CHANNEL_体检卡);

        feePayRecordService.payByCard(payRecord);

        //
        List<String> customerRegItemGroupIds = itemGroups.stream().map(CustomerRegItemGroup::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        itemGroupLambdaUpdateWrapper.set(CustomerRegItemGroup::getBillId, regBill.getId()).set(CustomerRegItemGroup::getPayStatus, ExConstants.PAY_STATE_已支付).in(CustomerRegItemGroup::getId, customerRegItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, reg.getId());
        customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper);

        regBill.setCustomerRegItemGroups(itemGroups);
        regBill.setPayRecords(Collections.singletonList(payRecord));
        regBill.setCustomerReg(reg);
        sendPayment2Jms(regBill);

        return regBill;
    }


    @Transactional
    @Override
    public void fixOriginCompanyBill() throws Exception {
        //获取所有status为已登记的customerReg记录
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerReg::getStatus, ExConstants.REG_STATUS_REGED);
        List<CustomerReg> customerRegList = customerRegMapper.selectList(queryWrapper);

        for (CustomerReg customerReg : customerRegList) {
            try {
                LambdaQueryWrapper<CustomerRegItemGroup> itemGroupQueryWrapper = new LambdaQueryWrapper<>();
                itemGroupQueryWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerReg.getId());
                itemGroupQueryWrapper.isNull(CustomerRegItemGroup::getBillId);
                List<CustomerRegItemGroup> itemGroups = customerRegItemGroupMapper.selectList(itemGroupQueryWrapper);
                //为所有支付方是单位支付的生成单位支付的支付单和支付记录
                List<CustomerRegItemGroup> companyItemGroups = itemGroups.stream().filter(item -> StringUtils.equals(item.getPayerType(), ExConstants.PAYER_TYPE_单位支付)).toList();
                if (CollectionUtils.isNotEmpty(companyItemGroups)) {

                    BigDecimal totalCompanyAmount = companyItemGroups.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);

                    CustomerRegBill regBill = new CustomerRegBill();
                    regBill.setAmount(totalCompanyAmount);
                    regBill.setCustomerRegId(customerReg.getId());
                    regBill.setExamNo(customerReg.getExamNo());
                    regBill.setCustomerRegItemGroupIds(companyItemGroups.stream().map(CustomerRegItemGroup::getId).collect(Collectors.toList()));
                    regBill.setCustomerId(customerReg.getCustomerId());
                    regBill.setCreator("system");
                    regBill.setDelFlag("0");
                    regBill.setStatus(ExConstants.PAY_STATE_支付中);
                    regBill.setBizName(ExConstants.PAY_BIZ_TYPE_体检费);
                    regBill.setCreator(customerReg.getCreator());
                    regBill.setCreateBy(customerReg.getCreatorBy());

                    FeePayRecord companyPayRecord = new FeePayRecord();
                    companyPayRecord.setClientIp("");
                    companyPayRecord.setAmount(totalCompanyAmount);
                    companyPayRecord.setPayChannel(ExConstants.PAY_CHANNEL_单位支付);
                    companyPayRecord.setBizId(customerReg.getId());
                    companyPayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
                    companyPayRecord.setCustomerRegId(customerReg.getId());
                    companyPayRecord.setState(ExConstants.PAY_STATE_支付中);
                    companyPayRecord.setExamNo(customerReg.getExamNo());
                    companyPayRecord.setName(customerReg.getName());
                    companyPayRecord.setArchivesNum(customerReg.getArchivesNum());
                    companyPayRecord.setCurrency("CNY");
                    companyPayRecord.setCreateBy(customerReg.getCreatorBy());
                    companyPayRecord.setCreator(customerReg.getCreator());
                    List<FeePayRecord> payRecords = new ArrayList<>();
                    payRecords.add(companyPayRecord);

                    regBill.setPayRecords(payRecords);
                    payBill(regBill);
                }
            } catch (Exception e) {
                log.error("Error fixing origin company bill for customerReg: " + customerReg.getId(), e);
            }
        }
    }

    @Async
    @Override
    public void sendPayment2Jms(CustomerRegBill bill) {
        String enableJms = sysSettingService.getValueByCode("enable_jms_pay");
        if (StringUtils.equals(enableJms, "1")) {
            try {
                CustomerReg customerReg = customerRegMapper.selectById(bill.getCustomerRegId());
                if (customerReg == null) {
                    log.error("向消息队列发送支付信息失败，未找到对应的CustomerReg，regId: {}", bill.getCustomerRegId());
                    return;
                }
                //获取字典值
                String careerName = commonAPI.translateDict("career", customerReg.getCareer());
                customerReg.setCareerName(careerName);

                List<CustomerRegItemGroup> customerRegItemGroups = bill.getCustomerRegItemGroups();
                for (CustomerRegItemGroup itemGroup : customerRegItemGroups) {
                    ItemGroup itemGroupInfo = itemGroupMapper.selectById(itemGroup.getItemGroupId());
                    itemGroup.setItemGroup(itemGroupInfo);
                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("bizType", "pay");
                jsonObject.put("data", bill);
                jmsMessageSender.sendPaymentMessage(jsonObject.toJSONString());
            } catch (Exception e) {
                log.error("Error sending payment to JMS for customerRegBill: " + bill.getId(), e);
            }
        }
    }

    @Async
    @Override
    public void sendRefund2Jms(CustomerRegBill bill) {
        String enableJms = sysSettingService.getValueByCode("enable_jms_refund");
        if (StringUtils.equals(enableJms, "1")) {
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("bizType", "refund");
                jsonObject.put("data", bill);
                jmsMessageSender.sendRefundMessage(jsonObject.toJSONString());
            } catch (Exception e) {
                log.error("Error sending refund to JMS for customerRegBill: " + bill.getId(), e);
            }
        }
    }

    @Override
    public Map<String, List<LimitOperationRecord>> getRelatedLimitAmountRecords(String customerRegId) {
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        Validate.notNull(customerReg, "未查询到相关登记记录");
        Map<String, List<LimitOperationRecord>> result = new HashMap<>();
        TeamCustomerLimitAmount selfTeamLimit = teamCustomerLimitAmountMapper.getCustomerLimitAmountByRegId(customerRegId);
        if (Objects.nonNull(selfTeamLimit)) {
            List<LimitOperationRecord> selfLimitRecord = limitOperationRecordMapper.selectList(new LambdaQueryWrapper<LimitOperationRecord>().eq(LimitOperationRecord::getLimitId, selfTeamLimit.getId()));
            result.put("selfLimitRecord", selfLimitRecord);
        }
        if (StringUtils.isNotBlank(customerReg.getOriginCustomerLimitAmountId())) {
            TeamCustomerLimitAmount shareTeamLimit = teamCustomerLimitAmountMapper.selectById(customerReg.getOriginCustomerLimitAmountId());
            if (Objects.nonNull(shareTeamLimit)) {
                List<LimitOperationRecord> shareLimitRecord = limitOperationRecordMapper.selectList(new LambdaQueryWrapper<LimitOperationRecord>().eq(LimitOperationRecord::getLimitId, shareTeamLimit.getId()));
                result.put("shareLimitRecord",shareLimitRecord);
            }
        }
        return result;
    }
}
