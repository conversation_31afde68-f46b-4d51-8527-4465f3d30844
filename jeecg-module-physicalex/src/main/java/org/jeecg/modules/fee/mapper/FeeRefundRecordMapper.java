package org.jeecg.modules.fee.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.fee.entity.FeeRecord;
import org.jeecg.modules.fee.entity.FeeRefundRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 退费记录
 * @Author: jeecg-boot
 * @Date:   2024-06-25
 * @Version: V1.0
 */
public interface FeeRefundRecordMapper extends BaseMapper<FeeRefundRecord> {

    List<FeeRecord> selectFeeRefundRecordByBillId(@Param("billId") String billId);

}
