package org.jeecg.modules.reg.service.impl;

import org.jeecg.modules.reg.entity.CustomerRegBarcodeItem;
import org.jeecg.modules.reg.mapper.CustomerRegBarcodeItemMapper;
import org.jeecg.modules.reg.service.ICustomerRegBarcodeItemService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 体检登记条码关联项目
 * @Author: jeecg-boot
 * @Date:   2024-06-09
 * @Version: V1.0
 */
@Service
public class CustomerRegBarcodeItemServiceImpl extends ServiceImpl<CustomerRegBarcodeItemMapper, CustomerRegBarcodeItem> implements ICustomerRegBarcodeItemService {

}
