<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.reg.mapper.CustomerRegHealthMapper">

    <!-- SQL Queries -->
    <select id="selectHealth" resultMap="CustomerRegHealthResultMap">
        SELECT * FROM customer_reg_health WHERE customer_id = #{customerId} limit 1
    </select>

    <!-- Result Map -->
    <resultMap id="CustomerRegHealthResultMap" type="org.jeecg.modules.reg.entity.CustomerRegHealth">
        <id column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="sys_org_code" property="sysOrgCode" />
        <result column="symptoms" property="symptoms" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="pmh" property="pmh" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="hpi" property="hpi" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="fh" property="fh" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="job_type" property="jobType" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="other" property="other" />
        <result column="customer_id" property="customerId" />
        <result column="account_id" property="accountId" />
    </resultMap>



</mapper>