<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.reg.mapper.CompanyTeamItemGroupMapper">
    <resultMap id="companyTeamItemGroupMap" type="org.jeecg.modules.reg.entity.CompanyTeam">
        <id column="id" property="id" />
        <result column="company_reg_id" property="companyRegId" />
        <result column="team_num" property="teamNum" />
        <result column="name" property="name" />
        <result column="help_char" property="helpChar" />
        <result column="sex_limit" property="sexLimit" />
        <result column="min_age" property="minAge" />
        <result column="max_age" property="maxAge" />
        <result column="exam_place" property="examPlace" />
        <result column="payer_type" property="payerType" />
        <result column="add_item_payer_type" property="addItemPayerType" />
        <result column="team_price" property="teamPrice" />
        <result column="team_discount_price" property="teamDiscountPrice" />
        <result column="team_discount_rate" property="teamDiscountRate" />
        <result column="exam_category" property="examCategory" />
        <result column="lock_status" property="lockStatus" />
        <result column="remark" property="remark" />
        <result column="risks" property="risks" />
        <result column="post" property="post" />
        <result column="work_shop" property="workShop" />
        <result column="work_type" property="workType" />
        <result column="pregnancy_flag" property="pregnancyFlag" />
        <result column="health_manage_flag" property="healthManageFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="create_ty" property="createTy" />

        <collection property="companyTeamItemGroupList" javaType="ArrayList" ofType="org.jeecg.modules.reg.entity.CompanyTeamItemGroup" columnPrefix="ctig_">
    <id column="id" property="id" />
    <result column="company_reg_id" property="companyRegId" />
    <result column="team_id" property="teamId" />
    <result column="item_group_id" property="itemGroupId" />
    <result column="item_group_name" property="itemGroupName" />
    <result column="department_id" property="departmentId" />
    <result column="department_name" property="departmentName" />
    <result column="department_code" property="departmentCode" />
    <result column="item_group_category" property="itemGroupCategory" />
    <result column="item_suit_id" property="itemSuitId" />
    <result column="item_suit_name" property="itemSuitName" />
    <result column="add_minus_flag" property="addMinusFlag" />
    <result column="price" property="price" />
    <result column="dis_rate" property="disRate" />
    <result column="price_after_dis" property="priceAfterDis" />
    <result column="payer_type" property="payerType" />
    <result column="min_discount_rate" property="minDiscountRate" />
    <result column="price_dis_diff_amount" property="priceDisDiffAmount" />
    <result column="his_name" property="hisName" />
    <result column="his_code" property="hisCode" />
    <result column="plat_name" property="platName" />
    <result column="plat_code" property="platCode" />
    <result column="class_code" property="classCode" />
    <result column="check_part_id" property="checkPartId" />
    <result column="check_part_name" property="checkPartName" />
    <result column="check_part_code" property="checkPartCode" />
</collection>
</resultMap>

<select id="selectByCompanyRegId"
            resultMap="companyTeamItemGroupMap" >
        select ct.id as id,
               ct.company_reg_id as company_reg_id,
               ct.team_num as team_num,
               ct.name as name,
               ct.help_char as help_char,
               ct.sex_limit as sex_limit,
               ct.min_age as min_age,
               ct.max_age as max_age,
               ct.payer_type as payer_type,
               ct.add_item_payer_type as add_item_payer_type,
               ct.team_price as team_price,
               ct.team_discount_price as team_discount_price,
               ct.team_discount_rate as team_discount_rate,
               ct.exam_category as exam_category,
               ct.lock_status as lock_status,
               ct.remark as remark,
               ct.risks as risks,
               ct.post as post,
               ct.work_shop as work_shop,
               ct.work_type as work_type,
               ct.pregnancy_flag as pregnancy_flag,
               ct.health_manage_flag as health_manage_flag,
               ct.create_time as create_time,
               ct.update_time as update_time,
               ct.update_by as update_by,
               ct.create_ty as create_ty,
               ctig.id as ctig_id,
               ctig.company_reg_id as ctig_company_reg_id,
               ctig.team_id as ctig_team_id,
               ctig.item_group_id as ctig_item_group_id,
               ctig.item_group_name as ctig_item_group_name,
               ctig.department_id as ctig_department_id,
               ctig.department_name as ctig_department_name,
               ctig.department_code as ctig_department_code,
               ctig.item_group_category as ctig_item_group_category,
               ctig.price as ctig_price,
               ctig.dis_rate as ctig_dis_rate,
               ctig.add_minus_flag as ctig_add_minus_flag,
               ctig.price_after_dis as ctig_price_after_dis,
               ctig.payer_type as ctig_payer_type,
               ctig.min_discount_rate as ctig_min_discount_rate,
               ctig.price_dis_diff_amount as ctig_price_dis_diff_amount,
               ctig.his_name as ctig_his_name,
               ctig.his_code as ctig_his_code,
               ctig.plat_name as ctig_plat_name,
               ctig.plat_code as ctig_plat_code,
               ctig.class_code as ctig_class_code,
               ctig.check_part_id as ctig_check_part_id,
               ctig.check_part_name as ctig_check_part_name,
               ctig.check_part_code as ctig_check_part_code
        from company_team ct join company_team_item_group ctig
                                 on ct.id=ctig.team_id
        where ct.company_reg_id=#{companyRegId}
    </select>
    <select id="selectByCompanyTeamId" resultType="org.jeecg.modules.reg.entity.CompanyTeamItemGroup">
        select * from company_team_item_group where team_id=#{teamId}
    </select>
</mapper>