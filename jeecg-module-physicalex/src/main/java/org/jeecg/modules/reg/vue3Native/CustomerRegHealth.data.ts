import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '家族史',
    align: "center",
    dataIndex: 'fh'
  },
  {
    title: '既往病史',
    align: "center",
    dataIndex: 'pmh'
  },
  {
    title: '现病史',
    align: "center",
    dataIndex: 'hpi'
  },
  {
    title: '工种',
    align: "center",
    dataIndex: 'jobType'
  },
  {
    title: '其他',
    align: "center",
    dataIndex: 'other'
  },
];

// 高级查询数据
export const superQuerySchema = {
  fh: {title: '家族史',order: 0,view: 'text', type: 'string',},
  pmh: {title: '既往病史',order: 1,view: 'text', type: 'string',},
  hpi: {title: '现病史',order: 2,view: 'text', type: 'string',},
  jobType: {title: '工种',order: 3,view: 'text', type: 'string',},
  other: {title: '其他',order: 4,view: 'text', type: 'string',},
};
