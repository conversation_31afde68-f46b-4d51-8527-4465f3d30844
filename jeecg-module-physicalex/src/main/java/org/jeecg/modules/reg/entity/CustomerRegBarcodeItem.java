package org.jeecg.modules.reg.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 体检登记条码关联项目
 * @Author: jeecg-boot
 * @Date:   2024-06-09
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_barcode_item")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer_reg_barcode_item对象", description="体检登记条码关联项目")
public class CustomerRegBarcodeItem implements Serializable {
    private static final long serialVersionUID = 1L;

	/**ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private java.lang.String id;
	/**登记ID*/
	@Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
	/**条码ID*/
	@Excel(name = "条码ID", width = 15)
    @ApiModelProperty(value = "条码ID")
    private java.lang.String barcodeId;
    /**条码号*/
    @Excel(name = "条码号", width = 15)
    @ApiModelProperty(value = "条码号")
    private String barcodeNo;
	/**组合ID*/
	@Excel(name = "组合ID", width = 15)
    @ApiModelProperty(value = "组合ID")
    private java.lang.String groupId;
	/**组合名称*/
	@Excel(name = "组合名称", width = 15)
    @ApiModelProperty(value = "组合名称")
    private java.lang.String groupName;
    /**his代码*/
    @Excel(name = "his代码", width = 15)
    @ApiModelProperty(value = "his代码")
    private java.lang.String hisCode;
    /**his名称*/
    @Excel(name = "his名称", width = 15)
    @ApiModelProperty(value = "his名称")
    private java.lang.String hisName;
    /**组合简称*/
    @Excel(name = "组合简称", width = 15)
    @ApiModelProperty(value = "组合简称")
    private java.lang.String shortName;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private java.lang.String status;
	/**档案号*/
	@Excel(name = "档案号", width = 15)
    @ApiModelProperty(value = "档案号")
    private java.lang.String archivesNum;
	/**条码配置ID*/
	@Excel(name = "条码配置ID", width = 15)
    @ApiModelProperty(value = "条码配置ID")
    private java.lang.String barSettingId;
	/**采血状态*/
	@Excel(name = "采血状态", width = 15)
    @ApiModelProperty(value = "采血状态")
    private java.lang.Integer bloodSate;
	/**采血时间*/
	@Excel(name = "采血时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "采血时间")
    private java.util.Date bloodTime;
	/**采血员*/
	@Excel(name = "采血员", width = 15)
    @ApiModelProperty(value = "采血员")
    private java.lang.String bloodBy;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;

    @ApiModelProperty(value = "功能分类")
    private String classCode;

    /**样本类别*/
    @ApiModelProperty(value = "样本类别")
    private String specimenCategory;

    /**样本类别代码*/
    @ApiModelProperty(value = "样本类别代码")
    private String specimenCategoryCode;


}
