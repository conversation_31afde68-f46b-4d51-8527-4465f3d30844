package org.jeecg.modules.reg.bo;

import lombok.Data;
import org.jeecg.modules.basicinfo.entity.ItemGroup;

import java.util.List;

@Data
public class DepartGroupTree {
    private String key;
    private String name;
    private String id;
    private String type;
    private String checkStatus;
    private String preCheckStatus;
    private String doctorName;
    private String abandonStatus;
    private String abnormalFlag;
    private ItemGroup itemGroup;
    private List<DepartGroupTree> children;
}
