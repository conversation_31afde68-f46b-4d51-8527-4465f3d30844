package org.jeecg.modules.reg.service.impl;

import org.jeecg.modules.reg.entity.BarcodePrintItems;
import org.jeecg.modules.reg.mapper.BarcodePrintItemsMapper;
import org.jeecg.modules.reg.service.IBarcodePrintItemsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 条码关联的项目组合
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Service
public class BarcodePrintItemsServiceImpl extends ServiceImpl<BarcodePrintItemsMapper, BarcodePrintItems> implements IBarcodePrintItemsService {

}
