package org.jeecg.modules.reg.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 档案表
 * @Author: jeecg-boot
 * @Date:   2024-06-14
 * @Version: V1.0
 */
@Data
@TableName("customer")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer对象", description="档案表")
public class Customer implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**头像*/
	@Excel(name = "头像", width = 15)
    @ApiModelProperty(value = "头像")
    private String avatar;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String name;
	/**电话*/
	@Excel(name = "电话", width = 15)
    @ApiModelProperty(value = "电话")
    private String phone;
	/**档案号*/
	@Excel(name = "档案号", width = 15)
    @ApiModelProperty(value = "档案号")
    private String archivesNum;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    private String idCard;
	/**性别*/
	@Excel(name = "性别", width = 15, dicCode = "sexLimit")
	@Dict(dicCode = "sexLimit")
    @ApiModelProperty(value = "性别")
    private String gender;
    /**民族*/
    @Excel(name = "民族", width = 15)
    @ApiModelProperty(value = "民族")
    private String nation;
	/**年龄*/
	@Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private Integer age;
	/**年龄单位*/
	@Excel(name = "年龄单位", width = 15)
    @ApiModelProperty(value = "年龄单位")
    private String ageUnit;
	/**用户类型*/
	@Excel(name = "用户类型", width = 15, dicCode = "customer_type")
	@Dict(dicCode = "customer_type")
    @ApiModelProperty(value = "用户类型")
    private String customerType;
	/**职务*/
	@Excel(name = "职务", width = 15)
    @ApiModelProperty(value = "职务")
    private String duty;
	/**出生日期*/
	@Excel(name = "出生日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
    private Date birthday;
	/**婚姻状态*/
	@Excel(name = "婚姻状态", width = 15)
    @ApiModelProperty(value = "婚姻状态")
    private String marriageStatus;
	/**家庭电话*/
	@Excel(name = "家庭电话", width = 15)
    @ApiModelProperty(value = "家庭电话")
    private String telPhone;
    @Excel(name = "身高", width = 15)
    @ApiModelProperty(value = "身高")
    private String height;
    /**体重*/
    @Excel(name = "体重", width = 15)
    @ApiModelProperty(value = "体重")
    private String weight;
	/**通信地址*/
	@Excel(name = "通信地址", width = 15)
    @ApiModelProperty(value = "通信地址")
    private String address;
	/**邮编*/
	@Excel(name = "邮编", width = 15)
    @ApiModelProperty(value = "邮编")
    private String postCode;
	/**邮箱*/
	@Excel(name = "邮箱", width = 15)
    @ApiModelProperty(value = "邮箱")
    private String email;
	/**保密等级*/
	@Excel(name = "保密等级", width = 15, dicCode = "secret_level")
	@Dict(dicCode = "secret_level")
    @ApiModelProperty(value = "保密等级")
    private String secretLevel;
	/**密码*/
	@Excel(name = "密码", width = 15)
    @ApiModelProperty(value = "密码")
    private String pwd;
	/**工种*/
	@Excel(name = "工种", width = 15)
    @ApiModelProperty(value = "工种")
    private String workId;
	/**岗位*/
	@Excel(name = "岗位", width = 15)
    @ApiModelProperty(value = "岗位")
    private String postStateId;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private String createBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    /**岗位*/
    @Excel(name = "岗位", width = 15)
    @ApiModelProperty(value = "岗位")
    private String companyName;

    /**省*/
    @Excel(name = "省", width = 15)
    @ApiModelProperty(value = "省")
    private String province;

    /**城市*/
    @Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;

    /**区域*/
    @Excel(name = "区域", width = 15)
    @ApiModelProperty(value = "区域")
    private String area;

    /**街道*/
    @Excel(name = "街道", width = 15)
    @ApiModelProperty(value = "街道")
    private String street;

    /**省代码*/
    @Excel(name = "省代码", width = 15)
    @ApiModelProperty(value = "省代码")
    private String provinceCode;

    /**城市代码*/
    @Excel(name = "城市代码", width = 15)
    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    /**区域代码*/
    @Excel(name = "区域代码", width = 15)
    @ApiModelProperty(value = "区域代码")
    private String areaCode;

    /**街道、旗县代码*/
    // @Excel(name = "区域代码", width = 15)
    @ApiModelProperty(value = "街道、旗县代码")
    private String streetCode;

    /**详细地址*/
    @Excel(name = "详细地址", width = 15)
    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    /**健康卡号*/
    @Excel(name = "健康卡号", width = 15)
    @ApiModelProperty(value = "健康卡号")
    private String healthCardNo;

    /**民族代码*/
    @Excel(name = "民族代码", width = 15)
    @ApiModelProperty(value = "民族代码")
    private String nationCode;

    /**婚姻状态*/
    @Excel(name = "婚姻状态", width = 15)
    @ApiModelProperty(value = "婚姻状态")
    private String marriageStatusCode;

    /**证件类型*/
    @Excel(name = "证件类型", width = 15)
    @ApiModelProperty(value = "证件类型")
    private String idCardType;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String hisPid;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String icCardNo;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String hisHealthNo;
    /**证件类型*/
    @Excel(name = "证件类型", width = 15)
    @ApiModelProperty(value = "证件类型")
    private String cardType;

    private String interfaceStatus;

    @Dict(dictTable = "dict_country", dicText = "name", dicCode = "code")
    @ApiModelProperty(value = "国籍")
    private java.lang.String country;
    @Dict(dictTable = "dict_country", dicText = "name", dicCode = "code")
    @ApiModelProperty(value = "国籍编码")
    private java.lang.String countryCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "accountId")
    private String accountId;

    @TableField(exist = false)
    private String openId;
    @TableField(exist = false)
    private String accountCustomerId;

    @TableField(exist = false)
    private String relationType;
    @TableField(exist = false)
    private String token;
    @TableField(exist = false)
    private String userName;
    @TableField(exist = false)
    private String defaultProfile;
    //内蒙医院，建档需传就诊号
    @TableField(exist = false)
    private String examNo;



}
