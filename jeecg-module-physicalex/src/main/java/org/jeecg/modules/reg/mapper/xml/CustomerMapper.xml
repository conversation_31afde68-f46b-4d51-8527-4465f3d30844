<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.reg.mapper.CustomerMapper">

    <select id="selectByIdCard" resultType="org.jeecg.modules.reg.entity.Customer">
        select * from customer where id_card = #{idCard}
    </select>
    <select id="getCustomersByAccountId" resultType="org.jeecg.modules.reg.entity.Customer">
        select c.* ,
        cc.id accountCustomerId,
        cc.account_id accountId,
        cc.open_id openId,
        cc.relation_type relationType,
        cc.default_state as defaultProfile
        from customer c join customeraccount_customer  cc
        on c.id=cc.customer_id
        <where>
            cc.account_id=#{accountId}
        <if test="relationType!=null and relationType!=''">
            and cc.relation_type=#{relationType}
        </if>
        </where>
        order by cc.default_state desc

    </select>
    <select id="getCustomersByAccountIdAndIdCard" resultType="org.jeecg.modules.reg.entity.Customer">
        select c.* ,
        cc.account_id accountId,
        cc.open_id openId,
        cc.relation_type relationType
        from customer c join customeraccount_customer  cc
        on c.id=cc.customer_id
        <where>
            cc.account_id=#{accountId}
            <if test="relationType!=null and relationType!=''">
                and cc.relation_type=#{relationType}
            </if>
            <if test="idCard!=null and idCard!=''">
                and c.id_card=#{idCard}
            </if>
        </where>
        order by cc.default_state desc
    </select>
    <select id="getDefaultCustomerByAccountId" resultType="org.jeecg.modules.reg.entity.Customer">
        select c.* ,
        cc.id accountCustomerId,
        cc.account_id accountId,
        cc.open_id openId,
        cc.relation_type relationType,
        cc.default_state as defaultProfile
        from customer c join customeraccount_customer  cc
        on c.id=cc.customer_id
        <where>
            cc.account_id=#{accountId}
            <if test="defaultState!=null and defaultState!=''">
                and cc.default_state=#{defaultState}
            </if>
        </where>
    </select>

    <select id="getDefaultCustomerByOpenId" resultType="org.jeecg.modules.reg.entity.Customer">
        select c.* ,
        cc.id accountCustomerId,
        cc.account_id accountId,
        cc.open_id openId,
        cc.relation_type relationType,
        cc.default_state as defaultProfile
        from customer c join customeraccount_customer  cc
        on c.id=cc.customer_id
        <where>
            cc.open_id=#{openId}  and cc.default_state='1'
        </where>
    </select>

</mapper>