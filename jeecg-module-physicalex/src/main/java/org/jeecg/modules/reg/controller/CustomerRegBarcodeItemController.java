package org.jeecg.modules.reg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.reg.entity.CustomerRegBarcodeItem;
import org.jeecg.modules.reg.service.ICustomerRegBarcodeItemService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Arrays;

/**
 * @Description: 体检登记条码关联项目
 * @Author: jeecg-boot
 * @Date: 2024-06-09
 * @Version: V1.0
 */
@Api(tags = "体检登记条码关联项目")
@RestController
@RequestMapping("/reg/customerRegBarcodeItem")
@Slf4j
public class CustomerRegBarcodeItemController extends JeecgController<CustomerRegBarcodeItem, ICustomerRegBarcodeItemService> {
    @Autowired
    private ICustomerRegBarcodeItemService customerRegBarcodeItemService;

    @Autowired
    private ICustomerRegService customerRegService;

    /**
     * 分页列表查询
     *
     * @param customerRegBarcodeItem
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检登记条码关联项目-分页列表查询")
    @ApiOperation(value = "体检登记条码关联项目-分页列表查询", notes = "体检登记条码关联项目-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerRegBarcodeItem>> queryPageList(CustomerRegBarcodeItem customerRegBarcodeItem,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                               HttpServletRequest req) {
        QueryWrapper<CustomerRegBarcodeItem> queryWrapper = QueryGenerator.initQueryWrapper(customerRegBarcodeItem, req.getParameterMap());
        Page<CustomerRegBarcodeItem> page = new Page<CustomerRegBarcodeItem>(pageNo, pageSize);
        IPage<CustomerRegBarcodeItem> pageList = customerRegBarcodeItemService.page(page, queryWrapper);

        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param customerRegBarcodeItem
     * @return
     */
    @AutoLog(value = "体检登记条码关联项目-添加")
    @ApiOperation(value = "体检登记条码关联项目-添加", notes = "体检登记条码关联项目-添加")
    @RequiresPermissions("reg:customer_reg_barcode_item:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CustomerRegBarcodeItem customerRegBarcodeItem) {
        customerRegBarcodeItemService.save(customerRegBarcodeItem);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param customerRegBarcodeItem
     * @return
     */
    @AutoLog(value = "体检登记条码关联项目-编辑")
    @ApiOperation(value = "体检登记条码关联项目-编辑", notes = "体检登记条码关联项目-编辑")
    @RequiresPermissions("reg:customer_reg_barcode_item:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomerRegBarcodeItem customerRegBarcodeItem) {
        customerRegBarcodeItemService.updateById(customerRegBarcodeItem);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检登记条码关联项目-通过id删除")
    @ApiOperation(value = "体检登记条码关联项目-通过id删除", notes = "体检登记条码关联项目-通过id删除")
    @RequiresPermissions("reg:customer_reg_barcode_item:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegBarcodeItemService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检登记条码关联项目-批量删除")
    @ApiOperation(value = "体检登记条码关联项目-批量删除", notes = "体检登记条码关联项目-批量删除")
    @RequiresPermissions("reg:customer_reg_barcode_item:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegBarcodeItemService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检登记条码关联项目-通过id查询")
    @ApiOperation(value = "体检登记条码关联项目-通过id查询", notes = "体检登记条码关联项目-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegBarcodeItem> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegBarcodeItem customerRegBarcodeItem = customerRegBarcodeItemService.getById(id);
        if (customerRegBarcodeItem == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegBarcodeItem);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegBarcodeItem
     */
    @RequiresPermissions("reg:customer_reg_barcode_item:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegBarcodeItem customerRegBarcodeItem) {
        return super.exportXls(request, customerRegBarcodeItem, CustomerRegBarcodeItem.class, "体检登记条码关联项目");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("reg:customer_reg_barcode_item:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegBarcodeItem.class);
    }

}
