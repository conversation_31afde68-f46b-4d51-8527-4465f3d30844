package org.jeecg.modules.reg.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.modules.reg.bo.HealthValue;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 健康档案
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_health")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer_reg_health对象", description="健康档案")
public class CustomerRegHealth implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**症状*/
    @TableField(typeHandler = FastjsonTypeHandler.class)
    @Excel(name = "症状", width = 15)
    @ApiModelProperty(value = "症状")
    private HealthValue symptoms;
	/**家族史*/
    @TableField(typeHandler = FastjsonTypeHandler.class)
	@Excel(name = "家族史", width = 15)
    @ApiModelProperty(value = "家族史")
    private HealthValue fh;
	/**既往病史*/
    @TableField(typeHandler = FastjsonTypeHandler.class)
	@Excel(name = "既往病史", width = 15)
    @ApiModelProperty(value = "既往病史")
    private HealthValue pmh;
	/**现病史*/
    @TableField(typeHandler = FastjsonTypeHandler.class)
	@Excel(name = "现病史", width = 15)
    @ApiModelProperty(value = "现病史")
    private HealthValue hpi;
	/**工种*/
    @TableField(typeHandler = FastjsonTypeHandler.class)
	@Excel(name = "工种", width = 15)
    @ApiModelProperty(value = "工种")
    private HealthValue jobType;
	/**其他*/
	@Excel(name = "其他", width = 15)
    @ApiModelProperty(value = "其他")
    private java.lang.String other;

    private String customerId;

    private String accountId;
}
