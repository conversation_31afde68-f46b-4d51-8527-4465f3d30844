<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.reg.mapper.CustomerRegBarcodeMapper">

    <select id="getBarcodeSettingIds4Reg" resultType="java.lang.String">
        select distinct bsg.setting_id from customer_reg_item_group c join barcode_setting_group bsg on c.item_group_id=bsg.group_id where c.customer_reg_id =#{customerRegId}
    </select>
    <select id="listCustomerRegBarcode" resultType="org.jeecg.modules.reg.entity.CustomerRegBarcode">
        select c.* from customer_reg_barcode c  where c.customer_reg_id =#{customerRegId}
    </select>
    <select id="listByReg" resultType="org.jeecg.modules.reg.entity.CustomerRegBarcode">
        select * from customer_reg_barcode where customer_reg_id=#{customerRegId}
    </select>
    <select id="listBarcodeWithItemByReg" resultMap="BarcodeWithItemResultMap">
        select c.*,g.id as g_id,g.group_id as g_group_id,g.status as g_status,g.group_name as g_group_name  from customer_reg_barcode c join customer_reg_barcode_item g on c.id=g.barcode_id where c.customer_reg_id=#{customerRegId}
    </select>
    <select id="pageCustomerBarcode" resultType="org.jeecg.modules.reg.entity.CustomerRegBarcode">
        select b.* from customer_reg_barcode b join customer_reg c on b.customer_reg_id = c.id where c.del_flag!=1
            <if test="examNo!=null"> AND b.exam_no=#{examNo}</if> <if test="regTimeStart!=null"> and b.create_time &gt;=  #{regTimeStart}</if> <if test="regTimeEnd!=null"> and c.create_time &lt;= #{regTimeEnd}</if> <if test="bloodStatus!=null"> and b.blood_status=#{bloodStatus}</if> <if test="barNo!=null"> and b.bar_no=#{barNo}</if>
    </select>
    <select id="listByRegAndBarcodeSetting" resultType="org.jeecg.modules.reg.entity.CustomerRegBarcode">
        select * from customer_reg_barcode where customer_reg_id=#{customerRegId} and bar_setting_id=#{barcodeSettingId}
    </select>
    <select id="listGropIdsByBarcodeId" resultType="java.lang.String">
        select group_id from customer_reg_barcode_item where barcode_id=#{barcodeId}
    </select>

    <resultMap id="CustomerRegBarcodeResultMap" type="org.jeecg.modules.reg.entity.CustomerRegBarcode">
        <id property="id" column="id"/>
        <result property="customerRegId" column="customer_reg_id"/>
        <result property="archivesNum" column="archives_num"/>
        <result property="barSettingId" column="bar_setting_id"/>
        <result property="bloodStatus" column="blood_status"/>
        <result property="bloodTime" column="blood_time"/>
        <result property="bloodBy" column="blood_by"/>
        <result property="barText" column="bar_text"/>
        <result property="barNo" column="bar_no"/>
        <result property="printTime" column="print_time"/>
        <result property="printCount" column="print_count"/>
        <result property="checkFlag" column="check_flag"/>
        <result property="acceptTime" column="accept_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="barcodeTemplateId" column="barcode_template_id" />
    </resultMap>

    <resultMap id="BarcodeWithItemResultMap" type="org.jeecg.modules.reg.entity.CustomerRegBarcode">
        <id property="id" column="id"/>
        <result property="customerRegId" column="customer_reg_id"/>
        <result property="archivesNum" column="archives_num"/>
        <result property="barSettingId" column="bar_setting_id"/>
        <result property="bloodStatus" column="blood_status"/>
        <result property="bloodTime" column="blood_time"/>
        <result property="bloodBy" column="blood_by"/>
        <result property="barText" column="bar_text"/>
        <result property="barNo" column="bar_no"/>
        <result property="printTime" column="print_time"/>
        <result property="printCount" column="print_count"/>
        <result property="checkFlag" column="check_flag"/>
        <result property="acceptTime" column="accept_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <collection property="barcodeItemList" columnPrefix="g_" javaType="java.util.List" ofType="org.jeecg.modules.reg.entity.CustomerRegBarcodeItem">
            <id property="id" column="g_id"/>
            <result property="groupId" column="group_id"/>
            <result property="groupName" column="group_name"/>
            <result property="status" column="status"/>
        </collection>
    </resultMap>


</mapper>