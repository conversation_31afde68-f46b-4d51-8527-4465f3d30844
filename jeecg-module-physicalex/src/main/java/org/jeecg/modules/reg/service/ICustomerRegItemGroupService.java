package org.jeecg.modules.reg.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.reg.bo.DepartAndGroupBean;
import org.jeecg.modules.reg.bo.DepartGroupTree;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.interfacecheck.entity.InterfaceResult;

import java.util.List;

/**
 * @Description: customer_reg_item_group
 * @Author: jeecg-boot
 * @Date: 2024-04-03
 * @Version: V1.0
 */
public interface ICustomerRegItemGroupService extends IService<CustomerRegItemGroup> {
    JSONObject statReg();

    List<CustomerRegItemGroup> listByRegAndDepart(String customerRegId, List<String> departmentIds);

    List<CustomerRegItemGroup> listByReg(String customerRegId, String payStatus);

    //List<CustomerRegItemGroup> listByReg(String customerRegId, String payStatus);

    String getCheckConclusion(String id);

    CustomerRegItemGroup getByRegAndGroup(String regId, String groupId);

    List<DepartAndGroupBean> list4RetrieveGuideSheet(String regId);

    List<DepartGroupTree> listDepartGroupTree(String regId);

    List<CustomerRegItemGroup> listWithItemGroupByReg(String customerRegId, List<String> departmentIds, Boolean containFeeOnly);

    List<CustomerRegItemGroup> listUnpaidByReg(String customerRegId);

    void updateApplyPrintTimes(List<String> groupIds);

    InterfaceResult getInterfaceResultByRegGroupId(String regGroupId);

    void updateStatus(CustomerRegItemGroup customerRegItemGroup);

    void sendItemGroup2Interface(List<CustomerRegItemGroup> groups) throws Exception;


    Boolean checkIsSummary(String customerRegId);

    List<ItemGroupRelation> getRelationGroupByRegIdAndGroupId(String customerRegId, String groupId);

}
