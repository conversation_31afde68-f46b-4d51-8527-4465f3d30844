package org.jeecg.modules.reg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Description: company_team_item_group
 * @Author: jeecg-boot
 * @Date:   2024-07-02
 * @Version: V1.0
 */
@Data
@TableName("company_team_item_group")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="company_team_item_group对象", description="单位预约项目")
public class CompanyTeamItemGroup implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**单位预约ID*/
	@Excel(name = "单位预约ID", width = 15)
    @ApiModelProperty(value = "单位预约ID")
    private java.lang.String companyRegId;
	/**分组ID*/
	@Excel(name = "分组ID", width = 15)
    @ApiModelProperty(value = "分组ID")
    private java.lang.String teamId;
	/**组合ID*/
	@Excel(name = "组合ID", width = 15)
    @ApiModelProperty(value = "组合ID")
    private java.lang.String itemGroupId;
	/**组合名称*/
	@Excel(name = "组合名称", width = 15)
    @ApiModelProperty(value = "组合名称")
    private java.lang.String itemGroupName;
	/**科室ID*/
	@Excel(name = "科室ID", width = 15)
    @ApiModelProperty(value = "科室ID")
    private java.lang.String departmentId;
	/**科室名称*/
	@Excel(name = "科室名称", width = 15)
    @ApiModelProperty(value = "科室名称")
    private java.lang.String departmentName;
    /**科室名称*/
    @Excel(name = "科室代码", width = 15)
    @ApiModelProperty(value = "科室代码")
    private java.lang.String departmentCode;
	/**项目类别*/
	@Excel(name = "项目类别", width = 15)
    @ApiModelProperty(value = "项目类别")
    private java.lang.String itemGroupCategory;
	/**套餐ID*/
	@Excel(name = "套餐ID", width = 15)
    @ApiModelProperty(value = "套餐ID")
    private java.lang.String itemSuitId;
	/**套餐名称*/
	@Excel(name = "套餐名称", width = 15)
    @ApiModelProperty(value = "套餐名称")
    private java.lang.String itemSuitName;
	/**加减项标志*/
	@Excel(name = "加减项标志", width = 15)
    @ApiModelProperty(value = "加减项标志")
    private java.lang.Integer addMinusFlag;
	/**原价*/
	@Excel(name = "原价", width = 15)
    @ApiModelProperty(value = "原价")
    private java.math.BigDecimal price;
	/**折扣率*/
	@Excel(name = "折扣率", width = 15)
    @ApiModelProperty(value = "折扣率")
    private java.math.BigDecimal disRate;
	/**折后价*/
	@Excel(name = "折后价", width = 15)
    @ApiModelProperty(value = "折后价")
    private java.math.BigDecimal priceAfterDis;
	/**支付方*/
	@Excel(name = "支付方", width = 15)
    @ApiModelProperty(value = "支付方")
    private java.lang.String payerType;
	/**体检组合的最大折扣率*/
	@Excel(name = "体检组合的最大折扣率", width = 15)
    @ApiModelProperty(value = "体检组合的最大折扣率")
    private java.math.BigDecimal minDiscountRate;
	/**因折扣产生的差额*/
	@Excel(name = "因折扣产生的差额", width = 15)
    @ApiModelProperty(value = "因折扣产生的差额")
    private java.math.BigDecimal priceDisDiffAmount;
	/**HIS名称*/
	@Excel(name = "HIS名称", width = 15)
    @ApiModelProperty(value = "HIS名称")
    private java.lang.String hisName;
	/**HIS代码*/
	@Excel(name = "HIS代码", width = 15)
    @ApiModelProperty(value = "HIS代码")
    private java.lang.String hisCode;
	/**公卫平台名称*/
	@Excel(name = "公卫平台名称", width = 15)
    @ApiModelProperty(value = "公卫平台名称")
    private java.lang.String platName;
	/**公卫平台代码*/
	@Excel(name = "公卫平台代码", width = 15)
    @ApiModelProperty(value = "公卫平台代码")
    private java.lang.String platCode;
    /**项目类别，检查、检验*/
    @Excel(name = "项目类别", width = 15)
    @ApiModelProperty(value = "项目类别")
    private String classCode;
    @ApiModelProperty(value = "必检项目标志")
    private String mustCheckFlag;
    @ApiModelProperty(value = "关联风险因素")
    private String riskFactorId;
    /**检查部位ID*/
    @Excel(name = "检查部位ID", width = 15)
    @ApiModelProperty(value = "检查部位ID")
    private String checkPartId;
    /**检查部位名称*/
    @Excel(name = "检查部位名称", width = 15)
    @ApiModelProperty(value = "检查部位名称")
    private String checkPartName;
    /**检查部位代码*/
    @Excel(name = "检查部位代码", width = 15)
    @ApiModelProperty(value = "检查部位代码")
    private String checkPartCode;

    /**项目来源类型：main-主项目, attach-附属项目, gift-赠送项目, dependent-依赖项目*/
    @ApiModelProperty(value = "项目来源类型")
    @TableField(exist = false)
    private String sourceType;

    @TableField(exist = false)
    private String itemNames;
}
