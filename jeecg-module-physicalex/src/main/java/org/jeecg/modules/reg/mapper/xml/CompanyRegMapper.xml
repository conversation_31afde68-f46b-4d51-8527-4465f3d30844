<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.reg.mapper.CompanyRegMapper">

    <select id="getItemGroupOfTeam" resultType="org.jeecg.modules.reg.entity.CompanyTeamItemGroup"
            parameterType="java.lang.String">
        select * from company_team_item_group where team_id = #{teamId}
    </select>
    <select id="pageCompanyReg" resultType="org.jeecg.modules.reg.entity.CompanyReg">
        select * from company_reg where  lock_status=0
        <if test="id != null and id.size() > 0">
            and id in
            <foreach item="item" index="index" collection="id" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword != ''"> and (reg_name like concat('%',#{keyword},'%') or help_char like concat('%',#{keyword},'%')) </if> order by reg_name desc
    </select>

    <select id="pageCompanyByDeptId" resultType="org.jeecg.modules.basicinfo.entity.Company">
        select * from company where 1=1
        <if test="id != null and id.size() > 0">
            and id in
            <foreach item="item" index="index" collection="id" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pid != null and pid != ''">
           and  pid=#{pid}
        </if>
        <if test="keyword != null and keyword != ''"> and (reg_name like concat('%',#{keyword},'%') or help_char like concat('%',#{keyword},'%')) </if> order by create_time desc
    </select>
</mapper>