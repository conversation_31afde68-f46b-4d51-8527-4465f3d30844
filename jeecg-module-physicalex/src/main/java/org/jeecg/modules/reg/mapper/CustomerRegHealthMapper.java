package org.jeecg.modules.reg.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CustomerRegHealth;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 健康档案
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
public interface CustomerRegHealthMapper extends BaseMapper<CustomerRegHealth> {

    CustomerRegHealth selectHealth(@Param("customerId") String customerId);
}
