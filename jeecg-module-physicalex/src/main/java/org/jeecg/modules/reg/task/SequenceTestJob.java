package org.jeecg.modules.reg.task;

import com.alibaba.druid.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.modules.reg.service.ICustomerRegMockService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class SequenceTestJob implements Job {
    private String parameter;

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }


    @Autowired
    private SequenceGenerator sequenceGenerator;

    private final ExecutorService executorService = Executors.newFixedThreadPool(50); // Create a thread pool with 10 threads、

    private final Lock lock = new ReentrantLock();


    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                long start = System.currentTimeMillis();
                parameter = com.alibaba.druid.util.StringUtils.isEmpty(parameter) ? "test1" : parameter;
                for (int i = 0; i < 50; i++) {
                    executorService.submit(() -> {
                        for (int j = 0; j < 200; j++) {
                            System.out.println(sequenceGenerator.getFormatedSerialNumBaseToday(parameter));
                        }
                    });
                }
                long end = System.currentTimeMillis();
                System.out.println("Time:" + (end - start));
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("Task is already running!");
        }
    }
}