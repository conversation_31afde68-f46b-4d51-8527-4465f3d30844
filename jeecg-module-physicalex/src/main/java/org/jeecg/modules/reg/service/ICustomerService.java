package org.jeecg.modules.reg.service;

import org.jeecg.modules.reg.bo.CustomerExamTrack;
import org.jeecg.modules.reg.bo.HisPatient;
import org.jeecg.modules.reg.entity.Customer;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.util.Map;

/**
 * @Description: 档案表
 * @Author: jeecg-boot
 * @Date: 2024-06-14
 * @Version: V1.0
 */
public interface ICustomerService extends IService<Customer> {

    Customer saveCustomerByCustomerReg(CustomerReg customerReg);

    Customer saveCustomer(Customer customer) throws Exception;

    Customer updateCustomer(Customer customer) throws Exception;

    Customer getByIdCard(String idCard);

    void sendCustomer2Mq(Customer customer) throws Exception;

    HisPatient addCustomer2Interface(Customer customer) throws Exception;

    HisPatient updateCustomer2Interface(Customer customer) throws Exception;

    Map<String,Object> queryResultAndSummaryByRegId(String regId);

}
