package org.jeecg.modules.reg.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 条码关联的项目组合
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Data
@TableName("barcode_print_items")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="barcode_print_items对象", description="条码关联的项目组合")
public class BarcodePrintItems implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**ind*/
	@Excel(name = "ind", width = 15)
    @ApiModelProperty(value = "ind")
    private java.lang.Integer ind;
	/**条码ID*/
	@Excel(name = "条码ID", width = 15)
    @ApiModelProperty(value = "条码ID")
    private java.lang.String barcodeId;
	/**组合ID*/
	@Excel(name = "组合ID", width = 15)
    @ApiModelProperty(value = "组合ID")
    private java.lang.String itemGroupId;
	/**组合名称*/
	@Excel(name = "组合名称", width = 15)
    @ApiModelProperty(value = "组合名称")
    private java.lang.String itemGroupName;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private java.lang.Integer state;
	/**登记ID*/
	@Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
	/**档案号*/
	@Excel(name = "档案号", width = 15)
    @ApiModelProperty(value = "档案号")
    private java.lang.String archivesNum;
	/**条码ID*/
	@Excel(name = "条码ID", width = 15)
    @ApiModelProperty(value = "条码ID")
    private java.lang.String barId;
	/**采血状态*/
	@Excel(name = "采血状态", width = 15)
    @ApiModelProperty(value = "采血状态")
    private java.lang.Integer bloodSate;
	/**采血时间*/
	@Excel(name = "采血时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "采血时间")
    private java.util.Date bloodTime;
	/**采血员*/
	@Excel(name = "采血员", width = 15)
    @ApiModelProperty(value = "采血员")
    private java.lang.String bloodBy;
}
