package org.jeecg.modules.reg.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;
import org.jeecg.modules.reg.service.ICompanyTeamItemGroupService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: company_team_item_group
 * @Author: jeecg-boot
 * @Date:   2024-07-02
 * @Version: V1.0
 */
@Api(tags="company_team_item_group")
@RestController
@RequestMapping("/reg/companyTeamItemGroup")
@Slf4j
public class CompanyTeamItemGroupController extends JeecgController<CompanyTeamItemGroup, ICompanyTeamItemGroupService> {
	@Autowired
	private ICompanyTeamItemGroupService companyTeamItemGroupService;
	
	/**
	 * 分页列表查询
	 *
	 * @param companyTeamItemGroup
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "company_team_item_group-分页列表查询")
	@ApiOperation(value="company_team_item_group-分页列表查询", notes="company_team_item_group-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CompanyTeamItemGroup>> queryPageList(CompanyTeamItemGroup companyTeamItemGroup,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CompanyTeamItemGroup> queryWrapper = QueryGenerator.initQueryWrapper(companyTeamItemGroup, req.getParameterMap());
		Page<CompanyTeamItemGroup> page = new Page<CompanyTeamItemGroup>(pageNo, pageSize);
		IPage<CompanyTeamItemGroup> pageList = companyTeamItemGroupService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param companyTeamItemGroup
	 * @return
	 */
	@AutoLog(value = "company_team_item_group-添加")
	@ApiOperation(value="company_team_item_group-添加", notes="company_team_item_group-添加")
	@RequiresPermissions("reg:company_team_item_group:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CompanyTeamItemGroup companyTeamItemGroup) {
		companyTeamItemGroupService.save(companyTeamItemGroup);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param companyTeamItemGroup
	 * @return
	 */
	@AutoLog(value = "company_team_item_group-编辑")
	@ApiOperation(value="company_team_item_group-编辑", notes="company_team_item_group-编辑")
	@RequiresPermissions("reg:company_team_item_group:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CompanyTeamItemGroup companyTeamItemGroup) {
		companyTeamItemGroupService.updateById(companyTeamItemGroup);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "company_team_item_group-通过id删除")
	@ApiOperation(value="company_team_item_group-通过id删除", notes="company_team_item_group-通过id删除")
	@RequiresPermissions("reg:company_team_item_group:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		companyTeamItemGroupService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "company_team_item_group-批量删除")
	@ApiOperation(value="company_team_item_group-批量删除", notes="company_team_item_group-批量删除")
	@RequiresPermissions("reg:company_team_item_group:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.companyTeamItemGroupService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "company_team_item_group-通过id查询")
	@ApiOperation(value="company_team_item_group-通过id查询", notes="company_team_item_group-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CompanyTeamItemGroup> queryById(@RequestParam(name="id",required=true) String id) {
		CompanyTeamItemGroup companyTeamItemGroup = companyTeamItemGroupService.getById(id);
		if(companyTeamItemGroup==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(companyTeamItemGroup);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param companyTeamItemGroup
    */
    @RequiresPermissions("reg:company_team_item_group:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CompanyTeamItemGroup companyTeamItemGroup) {
        return super.exportXls(request, companyTeamItemGroup, CompanyTeamItemGroup.class, "company_team_item_group");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("reg:company_team_item_group:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CompanyTeamItemGroup.class);
    }

}
