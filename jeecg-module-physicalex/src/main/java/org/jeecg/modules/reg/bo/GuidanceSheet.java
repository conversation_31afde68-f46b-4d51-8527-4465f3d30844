package org.jeecg.modules.reg.bo;

import lombok.Data;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;

import java.util.List;

@Data
public class GuidanceSheet {
    private CustomerReg reg;
    private List<CustomerRegItemGroup> itemGroups;
    private List<DepartAndGroupBean> itemGroupByDepartment;
    private List<FunctionAndGroupBean>  itemGroupByFunction;
    private List<CustomerRegItemGroup> concatItemGroups;
}
