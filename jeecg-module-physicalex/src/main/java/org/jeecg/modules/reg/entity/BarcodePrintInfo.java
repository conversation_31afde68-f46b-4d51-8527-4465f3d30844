package org.jeecg.modules.reg.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 条码打印
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Data
@TableName("barcode_print_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="barcode_print_info对象", description="条码打印")
public class BarcodePrintInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**登记ID*/
	@Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
	/**档案号*/
	@Excel(name = "档案号", width = 15)
    @ApiModelProperty(value = "档案号")
    private java.lang.String archivesNum;
	/**条码ID*/
	@Excel(name = "条码ID", width = 15)
    @ApiModelProperty(value = "条码ID")
    private java.lang.String barId;
	/**条码内容*/
	@Excel(name = "条码内容", width = 15)
    @ApiModelProperty(value = "条码内容")
    private java.lang.String barText;
	/**条码号*/
	@Excel(name = "条码号", width = 15)
    @ApiModelProperty(value = "条码号")
    private java.lang.String barNumNo;
	/**打印时间*/
	@Excel(name = "打印时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "打印时间")
    private java.util.Date barPrintTime;
	/**打印次数*/
	@Excel(name = "打印次数", width = 15)
    @ApiModelProperty(value = "打印次数")
    private java.lang.Integer barPrintCount;
	/**检查标记*/
	@Excel(name = "检查标记", width = 15)
    @ApiModelProperty(value = "检查标记")
    private java.lang.String checkFlag;
	/**采血状态*/
	@Excel(name = "采血状态", width = 15)
    @ApiModelProperty(value = "采血状态")
    private java.lang.Integer bloodSate;
	/**采血时间*/
	@Excel(name = "采血时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "采血时间")
    private java.util.Date bloodTime;
	/**采血员*/
	@Excel(name = "采血员", width = 15)
    @ApiModelProperty(value = "采血员")
    private java.lang.String bloodBy;
	/**接收时间*/
	@Excel(name = "接收时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "接收时间")
    private java.util.Date acceptTime;
}
