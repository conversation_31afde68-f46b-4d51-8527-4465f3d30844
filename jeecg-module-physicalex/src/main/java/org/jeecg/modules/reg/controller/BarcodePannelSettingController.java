package org.jeecg.modules.reg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.reg.entity.BarcodePannelSetting;
import org.jeecg.modules.reg.service.IBarcodePannelSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 采血站设置
 * @Author: jeecg-boot
 * @Date: 2024-07-18
 * @Version: V1.0
 */
@Api(tags = "采血站设置")
@RestController
@RequestMapping("/reg/barcodePannelSetting")
@Slf4j
public class BarcodePannelSettingController extends JeecgController<BarcodePannelSetting, IBarcodePannelSettingService> {
    @Autowired
    private IBarcodePannelSettingService barcodePannelSettingService;

    @ApiOperation(value = "采血站设置-获取配置", notes = "采血站设置-获取配置")
    @GetMapping(value = "/getSetting")
    public Result<?> getSetting() {
        BarcodePannelSetting setting = barcodePannelSettingService.getSetting();
        return Result.OK(setting);
    }

    /**
     * 分页列表查询
     *
     * @param barcodePannelSetting
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "采血站设置-分页列表查询")
    @ApiOperation(value = "采血站设置-分页列表查询", notes = "采血站设置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<BarcodePannelSetting>> queryPageList(BarcodePannelSetting barcodePannelSetting, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<BarcodePannelSetting> queryWrapper = QueryGenerator.initQueryWrapper(barcodePannelSetting, req.getParameterMap());
        Page<BarcodePannelSetting> page = new Page<BarcodePannelSetting>(pageNo, pageSize);
        IPage<BarcodePannelSetting> pageList = barcodePannelSettingService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param barcodePannelSetting
     * @return
     */
    @AutoLog(value = "采血站设置-添加")
    @ApiOperation(value = "采血站设置-添加", notes = "采血站设置-添加")
    @RequiresPermissions("reg:barcode_pannel_setting:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody BarcodePannelSetting barcodePannelSetting) {
        barcodePannelSettingService.save(barcodePannelSetting);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param barcodePannelSetting
     * @return
     */
    @AutoLog(value = "采血站设置-编辑")
    @ApiOperation(value = "采血站设置-编辑", notes = "采血站设置-编辑")
    @RequiresPermissions("reg:barcode_pannel_setting:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody BarcodePannelSetting barcodePannelSetting) {
        barcodePannelSettingService.updateById(barcodePannelSetting);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "采血站设置-通过id删除")
    @ApiOperation(value = "采血站设置-通过id删除", notes = "采血站设置-通过id删除")
    @RequiresPermissions("reg:barcode_pannel_setting:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        barcodePannelSettingService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "采血站设置-批量删除")
    @ApiOperation(value = "采血站设置-批量删除", notes = "采血站设置-批量删除")
    @RequiresPermissions("reg:barcode_pannel_setting:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.barcodePannelSettingService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "采血站设置-通过id查询")
    @ApiOperation(value = "采血站设置-通过id查询", notes = "采血站设置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<BarcodePannelSetting> queryById(@RequestParam(name = "id", required = true) String id) {
        BarcodePannelSetting barcodePannelSetting = barcodePannelSettingService.getById(id);
        if (barcodePannelSetting == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(barcodePannelSetting);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param barcodePannelSetting
     */
    @RequiresPermissions("reg:barcode_pannel_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BarcodePannelSetting barcodePannelSetting) {
        return super.exportXls(request, barcodePannelSetting, BarcodePannelSetting.class, "采血站设置");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("reg:barcode_pannel_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BarcodePannelSetting.class);
    }

}
