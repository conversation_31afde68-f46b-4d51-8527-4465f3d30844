package org.jeecg.modules.reg.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.modules.basicinfo.entity.BarcodeSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 体检登记条码
 * @Author: jeecg-boot
 * @Date:   2024-06-09
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_barcode")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer_reg_barcode对象", description="体检登记条码")
public class CustomerRegBarcode implements Serializable {
    private static final long serialVersionUID = 1L;

	/**ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private java.lang.String id;
	/**登记ID*/
	@Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
	/**档案号*/
	@Excel(name = "档案号", width = 15)
    @ApiModelProperty(value = "档案号")
    private java.lang.String archivesNum;
	/**条码配置ID*/
	@Excel(name = "条码配置ID", width = 15)
    @ApiModelProperty(value = "条码配置ID")
    private java.lang.String barSettingId;
    /**条码模版ID*/
    @Excel(name = "条码模版ID", width = 15)
    @ApiModelProperty(value = "条码模版ID")
    private java.lang.String barTemplateId;
	/**采血状态*/
	@Excel(name = "采血状态", width = 15)
    @ApiModelProperty(value = "采血状态")
    private java.lang.Integer bloodStatus;
	/**采血时间*/
	@Excel(name = "采血时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "采血时间")
    private java.util.Date bloodTime;
	/**采血员*/
	@Excel(name = "采血员", width = 15)
    @ApiModelProperty(value = "采血员")
    private java.lang.String bloodBy;
    @ApiModelProperty("采血员")
    private String bloodByName;
	/**条码内容*/
	@Excel(name = "条码内容", width = 15)
    @ApiModelProperty(value = "条码内容")
    private java.lang.String barText;
	/**条码号*/
	@Excel(name = "条码号", width = 15)
    @ApiModelProperty(value = "条码号")
    private java.lang.String barNo;
	/**打印时间*/
	@Excel(name = "打印时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "打印时间")
    private java.util.Date printTime;
	/**打印次数*/
	@Excel(name = "打印次数", width = 15)
    @ApiModelProperty(value = "打印次数")
    private java.lang.Integer printCount;
	/**检查标记*/
	@Excel(name = "检查标记", width = 15)
    @ApiModelProperty(value = "检查标记")
    private java.lang.String checkFlag;
	/**接收时间*/
	@Excel(name = "接收时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "接收时间")
    private java.util.Date acceptTime;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;

    /**样本类别*/
    @ApiModelProperty(value = "样本类别")
    private String specimenCategory;

    /**体检号*/
    @ApiModelProperty(value = "体检号")
    private String examNo;

    /**顺序号*/
    @ApiModelProperty(value = "顺序号")
    private Integer seq;

    /**样本类别代码*/
    @ApiModelProperty(value = "样本类别代码")
    private String specimenCategoryCode;

    private String remark;

    private Integer lastStatus;

    private String interfaceSyncStatus;

    @TableField(exist = false)
    private CustomerReg customerReg;

    @TableField(exist = false)
    private String name;

    @TableField(exist = false)
    private List<CustomerRegBarcode> listByReg;

    @TableField(exist = false)
    private List<CustomerRegBarcodeItem> barcodeItemList;
    @TableField(exist = false)
    private BarcodeSetting barcodeSetting;
    @TableField(exist = false)
    private BigDecimal remainAmount4Reg;

}
