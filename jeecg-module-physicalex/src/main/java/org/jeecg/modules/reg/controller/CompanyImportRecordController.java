package org.jeecg.modules.reg.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.reg.entity.CompanyImportRecord;
import org.jeecg.modules.reg.service.ICompanyImportRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 团检名单导入记录表
 * @Author: jeecg-boot
 * @Date:   2025-03-04
 * @Version: V1.0
 */
@Api(tags="团检名单导入记录表")
@RestController
@RequestMapping("/reg/companyImportRecord")
@Slf4j
public class CompanyImportRecordController extends JeecgController<CompanyImportRecord, ICompanyImportRecordService> {
	@Autowired
	private ICompanyImportRecordService companyImportRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param companyImportRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "团检名单导入记录表-分页列表查询")
	@ApiOperation(value="团检名单导入记录表-分页列表查询", notes="团检名单导入记录表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CompanyImportRecord>> queryPageList(CompanyImportRecord companyImportRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		LambdaQueryWrapper<CompanyImportRecord> queryWrapper = new LambdaQueryWrapper<>();
		String companyRegId = req.getParameter("companyRegId");
		String name = req.getParameter("name");
		String idCard = req.getParameter("idCard");
		String createTimeStart = req.getParameter("createTimeStart");
		String createTimeEnd = req.getParameter("createTimeEnd");
		if (StringUtils.isNotBlank(companyRegId)){
			queryWrapper.like(CompanyImportRecord::getCompanyRegId,companyRegId);
		}
		if (StringUtils.isNotBlank(name)){
			queryWrapper.like(CompanyImportRecord::getName,name);
		}
		if (StringUtils.isNotBlank(idCard)){
			queryWrapper.like(CompanyImportRecord::getIdCard,idCard);
		}
		if (StringUtils.isNotBlank(createTimeStart)){
			queryWrapper.between(CompanyImportRecord::getCreateTime,createTimeStart,createTimeEnd);
		}
        queryWrapper.orderByDesc(CompanyImportRecord::getCreateTime);

		Page<CompanyImportRecord> page = new Page<CompanyImportRecord>(pageNo, pageSize);
		IPage<CompanyImportRecord> pageList = companyImportRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param companyImportRecord
	 * @return
	 */
	@AutoLog(value = "团检名单导入记录表-添加")
	@ApiOperation(value="团检名单导入记录表-添加", notes="团检名单导入记录表-添加")
	@RequiresPermissions("reg:company_import_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CompanyImportRecord companyImportRecord) {
		companyImportRecordService.save(companyImportRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param companyImportRecord
	 * @return
	 */
	@AutoLog(value = "团检名单导入记录表-编辑")
	@ApiOperation(value="团检名单导入记录表-编辑", notes="团检名单导入记录表-编辑")
	@RequiresPermissions("reg:company_import_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CompanyImportRecord companyImportRecord) {
		companyImportRecordService.updateById(companyImportRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "团检名单导入记录表-通过id删除")
	@ApiOperation(value="团检名单导入记录表-通过id删除", notes="团检名单导入记录表-通过id删除")
	@RequiresPermissions("reg:company_import_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		companyImportRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "团检名单导入记录表-批量删除")
	@ApiOperation(value="团检名单导入记录表-批量删除", notes="团检名单导入记录表-批量删除")
	@RequiresPermissions("reg:company_import_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.companyImportRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "团检名单导入记录表-通过id查询")
	@ApiOperation(value="团检名单导入记录表-通过id查询", notes="团检名单导入记录表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CompanyImportRecord> queryById(@RequestParam(name="id",required=true) String id) {
		CompanyImportRecord companyImportRecord = companyImportRecordService.getById(id);
		if(companyImportRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(companyImportRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param companyImportRecord
    */
    @RequiresPermissions("reg:company_import_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CompanyImportRecord companyImportRecord) {
        return super.exportXls(request, companyImportRecord, CompanyImportRecord.class, "团检名单导入记录表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("reg:company_import_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CompanyImportRecord.class);
    }

}
