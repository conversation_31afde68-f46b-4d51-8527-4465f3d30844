package org.jeecg.modules.reg.service.impl;

import org.jeecg.modules.reg.entity.CompanyImportRecord;
import org.jeecg.modules.reg.mapper.CompanyImportRecordMapper;
import org.jeecg.modules.reg.service.ICompanyImportRecordService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 团检名单导入记录表
 * @Author: jeecg-boot
 * @Date:   2025-03-04
 * @Version: V1.0
 */
@Service
public class CompanyImportRecordServiceImpl extends ServiceImpl<CompanyImportRecordMapper, CompanyImportRecord> implements ICompanyImportRecordService {

}
