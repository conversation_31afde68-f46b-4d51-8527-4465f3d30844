package org.jeecg.modules.reg.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CustomerRegBarcode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 体检登记条码
 * @Author: jeecg-boot
 * @Date: 2024-06-09
 * @Version: V1.0
 */
public interface CustomerRegBarcodeMapper extends BaseMapper<CustomerRegBarcode> {

    List<CustomerRegBarcode> listCustomerRegBarcode(@Param("customerRegId") String customerRegId);

    List<String> getBarcodeSettingIds4Reg(@Param("customerRegId") String customerRegId);

    List<CustomerRegBarcode> listByReg(@Param("customerRegId") String customerRegId);

    List<CustomerRegBarcode> listBarcodeWithItemByReg(@Param("customerRegId") String customerRegId);

    List<CustomerRegBarcode> listByRegAndBarcodeSetting(@Param("customerRegId") String customerRegId, @Param("barcodeSettingId") String barcodeSettingId);

    Page<CustomerRegBarcode> pageCustomerBarcode(Page<CustomerRegBarcode> page, @Param("examNo") String examNo, @Param("bloodStatus") String bloodStatus, @Param("regTimeStart") String regTimeStart, @Param("regTimeEnd") String regTimeEnd, @Param("barNo") String barNo);

    List<String> listGropIdsByBarcodeId(@Param("barcodeId") String barcodeId);
}
