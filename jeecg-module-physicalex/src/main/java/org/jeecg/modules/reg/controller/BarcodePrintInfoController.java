package org.jeecg.modules.reg.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.reg.entity.BarcodePrintInfo;
import org.jeecg.modules.reg.service.IBarcodePrintInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 条码打印
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Api(tags="条码打印")
@RestController
@RequestMapping("/reg/barcodePrintInfo")
@Slf4j
public class BarcodePrintInfoController extends JeecgController<BarcodePrintInfo, IBarcodePrintInfoService> {
	@Autowired
	private IBarcodePrintInfoService barcodePrintInfoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param barcodePrintInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "条码打印-分页列表查询")
	@ApiOperation(value="条码打印-分页列表查询", notes="条码打印-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BarcodePrintInfo>> queryPageList(BarcodePrintInfo barcodePrintInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<BarcodePrintInfo> queryWrapper = QueryGenerator.initQueryWrapper(barcodePrintInfo, req.getParameterMap());
		Page<BarcodePrintInfo> page = new Page<BarcodePrintInfo>(pageNo, pageSize);
		IPage<BarcodePrintInfo> pageList = barcodePrintInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param barcodePrintInfo
	 * @return
	 */
	@AutoLog(value = "条码打印-添加")
	@ApiOperation(value="条码打印-添加", notes="条码打印-添加")
	@RequiresPermissions("reg:barcode_print_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BarcodePrintInfo barcodePrintInfo) {
		barcodePrintInfoService.save(barcodePrintInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param barcodePrintInfo
	 * @return
	 */
	@AutoLog(value = "条码打印-编辑")
	@ApiOperation(value="条码打印-编辑", notes="条码打印-编辑")
	@RequiresPermissions("reg:barcode_print_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BarcodePrintInfo barcodePrintInfo) {
		barcodePrintInfoService.updateById(barcodePrintInfo);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "条码打印-通过id删除")
	@ApiOperation(value="条码打印-通过id删除", notes="条码打印-通过id删除")
	@RequiresPermissions("reg:barcode_print_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		barcodePrintInfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "条码打印-批量删除")
	@ApiOperation(value="条码打印-批量删除", notes="条码打印-批量删除")
	@RequiresPermissions("reg:barcode_print_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.barcodePrintInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "条码打印-通过id查询")
	@ApiOperation(value="条码打印-通过id查询", notes="条码打印-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BarcodePrintInfo> queryById(@RequestParam(name="id",required=true) String id) {
		BarcodePrintInfo barcodePrintInfo = barcodePrintInfoService.getById(id);
		if(barcodePrintInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(barcodePrintInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param barcodePrintInfo
    */
    @RequiresPermissions("reg:barcode_print_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BarcodePrintInfo barcodePrintInfo) {
        return super.exportXls(request, barcodePrintInfo, BarcodePrintInfo.class, "条码打印");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("reg:barcode_print_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BarcodePrintInfo.class);
    }

}
