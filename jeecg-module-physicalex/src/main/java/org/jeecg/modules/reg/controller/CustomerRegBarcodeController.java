package org.jeecg.modules.reg.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.BarcodeSetting;
import org.jeecg.modules.basicinfo.service.IBarcodeSettingService;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegBarcode;
import org.jeecg.modules.reg.service.ICustomerRegBarcodeService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 体检登记条码
 * @Author: jeecg-boot
 * @Date: 2024-06-09
 * @Version: V1.0
 */
@Api(tags = "体检登记条码")
@RestController
@RequestMapping("/reg/customerRegBarcode")
@Slf4j
public class CustomerRegBarcodeController extends JeecgController<CustomerRegBarcode, ICustomerRegBarcodeService> {
    @Autowired
    private ICustomerRegBarcodeService customerRegBarcodeService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private IBarcodeSettingService barcodeSettingService;

    /**
     * 批量放弃
     *
     * @return
     */
    @ApiOperation(value = "批量放弃")
    @PostMapping(value = "/abandeBarcode")
    public Result<?> abandeBarcode(@RequestBody JSONObject info) {
        try {

            List<String> barcodeIdList = info.getJSONArray("barcodeIdList").toJavaList(String.class);
            customerRegBarcodeService.abandeBarcode(barcodeIdList);
        } catch (Exception e) {
            log.error("批量放弃异常: " + e.getMessage(), e);
            return Result.error("操作失败!");
        }
        return Result.OK("操作成功!");
    }

    /**
     * 批量取消放弃
     *
     * @return
     */
    @ApiOperation(value = "批量取消放弃")
    @PostMapping(value = "/unAbandeBarcode")
    public Result<?> unAbandeBarcode(@RequestBody JSONObject info) {
        try {
            List<String> barcodeIdList = info.getJSONArray("barcodeIdList").toJavaList(String.class);
            customerRegBarcodeService.unAbandeBarcode(barcodeIdList);
        } catch (Exception e) {
            log.error("批量取消放弃异常: " + e.getMessage(), e);
            return Result.error("操作失败!");
        }
        return Result.OK("操作成功!");
    }

    /**
     * 作废
     *
     * @return
     */
    @ApiOperation(value = "作废")
    @PostMapping(value = "/invalidateBarcode")
    public Result<?> invalidateBarcode(@RequestBody JSONObject info) {
        try {
            List<String> barcodeIdList = info.getJSONArray("barcodeIdList").toJavaList(String.class);
            customerRegBarcodeService.invalidateBarcode(barcodeIdList, "作废");
        } catch (Exception e) {
            log.error("作废异常: " + e.getMessage(), e);
            return Result.error("操作失败!");
        }
        return Result.OK("操作成功!");
    }

    /**
     * 取消作废
     *
     * @return
     */
    @ApiOperation(value = "取消作废")
    @PostMapping(value = "/unInvalidateBarcode")
    public Result<?> unInvalidateBarcode(@RequestBody JSONObject info) {
        try {
            List<String> barcodeIdList = info.getJSONArray("barcodeIdList").toJavaList(String.class);
            customerRegBarcodeService.unInvalidateBarcode(barcodeIdList);
        } catch (Exception e) {
            log.error("取消作废异常: " + e.getMessage(), e);
            return Result.error("操作失败!");
        }
        return Result.OK("操作成功!");
    }


    /**
     * 为体检登记生成条码
     *
     * @return
     */
    @ApiOperation(value = "为体检登记生成条码")
    @GetMapping(value = "/generateBarcode")
    public Result<?> generateBarcode(@RequestParam(name = "customerRegId", required = true) String customerRegId) {
        List<CustomerRegBarcode> list = customerRegBarcodeService.generateBarcode(customerRegId);
        return Result.OK(list);
    }

    /**
     * 获取体检登记的条码
     *
     * @return
     */
    @ApiOperation(value = "获取体检登记的条码")
    @GetMapping(value = "/listCustomerRegBarcode")
    public Result<?> listCustomerRegBarcode(@RequestParam(name = "customerRegId", required = true) String customerRegId, String includeInvalid) {
        List<CustomerRegBarcode> list = customerRegBarcodeService.listCustomerRegBarcode(customerRegId, includeInvalid);
        return Result.OK(list);
    }

    /**
     * 获取体检登记的条码
     *
     * @return
     */
    @ApiOperation(value = "批量采血")
    @PostMapping(value = "/bloodBatch")
    public Result<?> bloodBatch(@RequestBody JSONArray info) {
        List<CustomerRegBarcode> list = info.toJavaList(CustomerRegBarcode.class);
        try {
            customerRegBarcodeService.bloodBatch(list);
        } catch (Exception e) {
            log.error("批量采血异常: " + e.getMessage(), e);
            return Result.error("操作失败!");
        }
        return Result.OK("操作成功!");
    }

    /**
     * 获取体检登记的条码
     *
     * @return
     */
    @ApiOperation(value = "采血")
    @PostMapping(value = "/blood")
    public Result<?> blood(@RequestBody CustomerRegBarcode info) {
        try {
            customerRegBarcodeService.blood(info);
        } catch (Exception e) {
            log.error("采血异常: " + e.getMessage(), e);
            return Result.error("操作失败!");
        }
        return Result.OK("操作成功！");
    }

    /**
     * 更新打印信息
     * @return
     */
    @ApiOperation(value = "更新打印信息")
    @GetMapping(value = "/updatePrintInfo")
    public Result<?> updatePrintInfo(@RequestParam(name = "id", required = true) String id) {
        customerRegBarcodeService.updatePrintInfo(id);
        return Result.OK("操作成功!");
    }

    /**
     * 分页列表查询
     *
     * @param req
     * @return
     */
    //@AutoLog(value = "体检登记条码-分页列表查询")
    @ApiOperation(value = "体检登记条码-分页列表查询", notes = "体检登记条码-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerRegBarcode>> queryPageList(HttpServletRequest req) {

        int pageNo = StringUtils.isNotBlank(req.getParameter("pageNo")) ? Integer.parseInt(req.getParameter("pageNo")) : 1;
        int pageSize = StringUtils.isNotBlank(req.getParameter("pageSize")) ? Integer.parseInt(req.getParameter("pageSize")) : 10;
        String examNo = req.getParameter("examNo");
        String bloodStatus = req.getParameter("bloodStatus");
        String barNo = req.getParameter("barNo");
        String createTime_begin = req.getParameter("createTime_begin");
        String createTime_end = req.getParameter("createTime_end");

        if(StringUtils.isNotBlank(createTime_begin)){
            createTime_begin = createTime_begin + " 00:00:00";
        }
        if(StringUtils.isNotBlank(createTime_end)){
            createTime_end = createTime_end + " 23:59:59";
        }

        //如果所有参数都为空，则不查询
        if (StringUtils.isBlank(examNo) && StringUtils.isBlank(barNo) && StringUtils.isBlank(createTime_begin) && StringUtils.isBlank(createTime_end)) {
            return Result.OK(new Page<CustomerRegBarcode>(pageNo, pageSize));
        }


        if (StringUtils.isNotBlank(examNo)) {
            CustomerReg customerReg = customerRegService.getByExamNo(examNo);
            if (customerReg != null) {
                customerRegBarcodeService.generateBarcode(customerReg.getId());
            }
        }

        Page<CustomerRegBarcode> page = new Page<>(pageNo, pageSize);
        customerRegBarcodeService.pageCustomerBarcode(page, examNo, bloodStatus, createTime_begin, createTime_end, barNo);

        page.getRecords().forEach(item -> {
            String customerRegId = item.getCustomerRegId();
            CustomerReg customerReg = customerRegService.getById(customerRegId);
            if (customerReg != null) {
                item.setCustomerReg(customerReg);
                item.setName(customerReg.getName());
            }

            BarcodeSetting barcodeSetting = barcodeSettingService.getById(item.getBarSettingId());
            item.setBarcodeSetting(barcodeSetting);
        });
        return Result.OK(page);
    }


    /**
     * 添加
     *
     * @param customerRegBarcode
     * @return
     */
    @AutoLog(value = "体检登记条码-添加")
    @ApiOperation(value = "体检登记条码-添加", notes = "体检登记条码-添加")
    @RequiresPermissions("reg:customer_reg_barcode:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CustomerRegBarcode customerRegBarcode) {
        customerRegBarcodeService.save(customerRegBarcode);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param customerRegBarcode
     * @return
     */
    @AutoLog(value = "体检登记条码-编辑")
    @ApiOperation(value = "体检登记条码-编辑", notes = "体检登记条码-编辑")
    @RequiresPermissions("reg:customer_reg_barcode:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomerRegBarcode customerRegBarcode) {
        customerRegBarcodeService.updateById(customerRegBarcode);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检登记条码-通过id删除")
    @ApiOperation(value = "体检登记条码-通过id删除", notes = "体检登记条码-通过id删除")
    @RequiresPermissions("reg:customer_reg_barcode:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegBarcodeService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检登记条码-批量删除")
    @ApiOperation(value = "体检登记条码-批量删除", notes = "体检登记条码-批量删除")
    @RequiresPermissions("reg:customer_reg_barcode:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegBarcodeService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检登记条码-通过id查询")
    @ApiOperation(value = "体检登记条码-通过id查询", notes = "体检登记条码-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegBarcode> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegBarcode customerRegBarcode = customerRegBarcodeService.getById(id);
        if (customerRegBarcode == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegBarcode);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegBarcode
     */
    @RequiresPermissions("reg:customer_reg_barcode:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegBarcode customerRegBarcode) {
        return super.exportXls(request, customerRegBarcode, CustomerRegBarcode.class, "体检登记条码");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("reg:customer_reg_barcode:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegBarcode.class);
    }

}
