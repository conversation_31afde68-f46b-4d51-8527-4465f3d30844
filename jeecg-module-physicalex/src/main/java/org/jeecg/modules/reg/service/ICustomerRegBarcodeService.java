package org.jeecg.modules.reg.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.reg.entity.CustomerRegBarcode;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 体检登记条码
 * @Author: jeecg-boot
 * @Date: 2024-06-09
 * @Version: V1.0
 */
public interface ICustomerRegBarcodeService extends IService<CustomerRegBarcode> {

    List<CustomerRegBarcode> listCustomerRegBarcode(String customerRegId, String includeInvalid);

    List<CustomerRegBarcode> listByReg(String customerRegId);

    void updatePrintInfo(String id);

    List<CustomerRegBarcode> generateBarcode(String customerRegId);

    void bloodBatch(List<CustomerRegBarcode> list) throws Exception;

    void blood(CustomerRegBarcode regBarcode) throws Exception;

    void sendBarcode2Mq(CustomerRegBarcode regBarcode) throws Exception;

    void invalidateBarcode(List<String> barcodeIdList, String reason) throws Exception;

    void abandeBarcode(List<String> barcodeIdList) throws Exception;

    void unAbandeBarcode(List<String> barcodeIdList) throws Exception;

    void unInvalidateBarcode(List<String> barcodeIdList) throws Exception;

    Page<CustomerRegBarcode> pageCustomerBarcode(Page<CustomerRegBarcode> page, String examNo, String bloodStatus, String regTimeStart, String regTimeEnd, String barcode);

    List<CustomerRegBarcode> listByRegAndBarcodeSetting(String customerRegId, String barcodeSettingId);

    List<String> listGropIdsByBarcodeId(String barcodeId);

}
