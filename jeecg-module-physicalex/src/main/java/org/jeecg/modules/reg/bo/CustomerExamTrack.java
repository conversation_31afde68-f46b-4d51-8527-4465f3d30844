package org.jeecg.modules.reg.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CustomerExamTrack {
    private String customerId;
    private String examNo;
    private String state;
    // 1待登记 2已登记 3已出报告
    private String stateDesc;
    private String name;
    private String suitId;
    private String suitName;
    private Integer checkedCount;
    private Integer uncheckedCount;
    private Integer abnormalRetCount;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date bookTime;
}
