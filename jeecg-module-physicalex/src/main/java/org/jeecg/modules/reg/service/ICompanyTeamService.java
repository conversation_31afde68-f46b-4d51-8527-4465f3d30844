package org.jeecg.modules.reg.service;

import org.jeecg.modules.reg.entity.CompanyTeam;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 单位分组
 * @Author: jeecg-boot
 * @Date: 2024-02-19
 * @Version: V1.0
 */
public interface ICompanyTeamService extends IService<CompanyTeam> {

    /**
     * 通过主表id查询子表数据
     *
     * @param mainId
     * @return List<CompanyTeam>
     */
    List<CompanyTeam> selectByMainId(String mainId);

    void deleteBatchItemGroup(String companyRegId, String teamId, List<String> itemgroupIds);

    void fillTeamNum(CompanyTeam companyTeam);

    void copyCompanyTeam(String teamId);
}
