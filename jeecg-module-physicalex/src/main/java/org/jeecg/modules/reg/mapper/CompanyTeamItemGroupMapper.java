package org.jeecg.modules.reg.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: company_team_item_group
 * @Author: jeecg-boot
 * @Date:   2024-07-02
 * @Version: V1.0
 */
public interface CompanyTeamItemGroupMapper extends BaseMapper<CompanyTeamItemGroup> {
    List<CompanyTeam> selectByCompanyRegId(@Param("companyRegId") String companyRegId);

    List<CompanyTeamItemGroup> selectByCompanyTeamId(@Param("teamId") String teamId);

}
