package org.jeecg.modules.reg.mapper;

import java.util.List;
import org.jeecg.modules.reg.entity.CompanyTeam;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 单位分组
 * @Author: jeecg-boot
 * @Date:   2024-02-19
 * @Version: V1.0
 */
public interface CompanyTeamMapper extends BaseMapper<CompanyTeam> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

   /**
    * 通过主表id查询子表数据
    *
    * @param mainId 主表id
    * @return List<CompanyTeam>
    */
	public List<CompanyTeam> selectByMainId(@Param("mainId") String mainId);

}
