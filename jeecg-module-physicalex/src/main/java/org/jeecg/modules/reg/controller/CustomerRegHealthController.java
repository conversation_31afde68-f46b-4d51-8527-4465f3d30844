package org.jeecg.modules.reg.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.reg.entity.CustomerRegHealth;
import org.jeecg.modules.reg.service.ICustomerRegHealthService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 健康档案
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
@Api(tags="健康档案")
@RestController
@RequestMapping("/reg/customerRegHealth")
@Slf4j
public class CustomerRegHealthController extends JeecgController<CustomerRegHealth, ICustomerRegHealthService> {
	@Autowired
	private ICustomerRegHealthService customerRegHealthService;

	//getHealthInfo
	@ApiOperation(value="获取健康档案信息", notes="获取健康档案信息")
	@GetMapping(value = "/getHealthInfo")
	public Result<CustomerRegHealth> getHealthInfo(@RequestParam(name="customerId",required=true) String customerId) {
		CustomerRegHealth customerRegHealth = customerRegHealthService.getHealthInfo(customerId);
		if(customerRegHealth==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(customerRegHealth);
	}

	//saveHealthInfo
	@ApiOperation(value="保存健康档案信息", notes="保存健康档案信息")
	@PostMapping(value = "/saveHealthInfo")
	public Result<String> saveHealthInfo(@RequestBody JSONObject info) {
		CustomerRegHealth customerRegHealth = info.toJavaObject(CustomerRegHealth.class);
		customerRegHealthService.saveOrUpdate(customerRegHealth);
		return Result.OK("保存成功！");
	}
	
	/**
	 * 分页列表查询
	 *
	 * @param customerRegHealth
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "健康档案-分页列表查询")
	@ApiOperation(value="健康档案-分页列表查询", notes="健康档案-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CustomerRegHealth>> queryPageList(CustomerRegHealth customerRegHealth,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CustomerRegHealth> queryWrapper = QueryGenerator.initQueryWrapper(customerRegHealth, req.getParameterMap());
		Page<CustomerRegHealth> page = new Page<CustomerRegHealth>(pageNo, pageSize);
		IPage<CustomerRegHealth> pageList = customerRegHealthService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param customerRegHealth
	 * @return
	 */
	@AutoLog(value = "健康档案-添加")
	@ApiOperation(value="健康档案-添加", notes="健康档案-添加")
	@RequiresPermissions("reg:customer_reg_health:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CustomerRegHealth customerRegHealth) {
		customerRegHealthService.save(customerRegHealth);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param customerRegHealth
	 * @return
	 */
	@AutoLog(value = "健康档案-编辑")
	@ApiOperation(value="健康档案-编辑", notes="健康档案-编辑")
	@RequiresPermissions("reg:customer_reg_health:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CustomerRegHealth customerRegHealth) {
		customerRegHealthService.updateById(customerRegHealth);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "健康档案-通过id删除")
	@ApiOperation(value="健康档案-通过id删除", notes="健康档案-通过id删除")
	@RequiresPermissions("reg:customer_reg_health:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		customerRegHealthService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "健康档案-批量删除")
	@ApiOperation(value="健康档案-批量删除", notes="健康档案-批量删除")
	@RequiresPermissions("reg:customer_reg_health:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.customerRegHealthService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "健康档案-通过id查询")
	@ApiOperation(value="健康档案-通过id查询", notes="健康档案-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CustomerRegHealth> queryById(@RequestParam(name="id",required=true) String id) {
		CustomerRegHealth customerRegHealth = customerRegHealthService.getById(id);
		if(customerRegHealth==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(customerRegHealth);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param customerRegHealth
    */
    @RequiresPermissions("reg:customer_reg_health:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegHealth customerRegHealth) {
        return super.exportXls(request, customerRegHealth, CustomerRegHealth.class, "健康档案");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("reg:customer_reg_health:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegHealth.class);
    }

}
