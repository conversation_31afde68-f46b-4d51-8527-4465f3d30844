package org.jeecg.modules.reg.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class FixLimitAmountJob implements Job {

    @Autowired
    private ICustomerRegService customerRegService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                customerRegService.fixLimitAmount();
            } catch (Exception e) {
                log.error("修复限额异常！", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("修复任务正在执行中!");
        }
    }
}