package org.jeecg.modules.reg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.glassfish.jersey.internal.guava.Sets;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.reg.entity.*;
import org.jeecg.modules.reg.mapper.CompanyRegMapper;
import org.jeecg.modules.reg.mapper.CompanyTeamMapper;
import org.jeecg.modules.reg.service.ICompanyRegService;
import org.jeecg.modules.reg.service.ICompanyTeamItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Collectors;
import java.util.ArrayList;
import java.math.BigDecimal;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 单位预约
 * @Author: jeecg-boot
 * @Date: 2024-02-19
 * @Version: V1.0
 */
@Slf4j
@Service
public class CompanyRegServiceImpl extends ServiceImpl<CompanyRegMapper, CompanyReg> implements ICompanyRegService {

    @Autowired
    private CompanyRegMapper companyRegMapper;
    @Autowired
    private CompanyTeamMapper companyTeamMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private CommonAPI commonAPI;
    @Autowired
    private ICompanyTeamItemGroupService companyTeamItemGroupService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private ICustomerRegSummaryService customerRegSummaryService;
    @Autowired
    private IItemGroupRelationService itemGroupRelationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        companyTeamMapper.deleteByMainId(id);
        companyRegMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            companyTeamMapper.deleteByMainId(id.toString());
            companyRegMapper.deleteById(id);
        }
    }

    @Override
    public boolean teamDuplicateCheck(String teamId, String companyId, String teamName) {
        Integer existCount = jdbcTemplate.queryForObject("select count(1) from company_team where id != ? and company_reg_id = ? and name = ?", Integer.class, StringUtils.stripToEmpty(teamId), companyId, teamName);
        return existCount > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveItemGroupOfTeam(String teamId, List<CompanyTeamItemGroup> itemGroupList) {
        // 调用重载方法，默认处理附属和赠送项目
        saveItemGroupOfTeam(teamId, itemGroupList, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveItemGroupOfTeam(String teamId, List<CompanyTeamItemGroup> itemGroupList, boolean skipGiftAndAttach) {
        try {
            // 验证数据完整性
            if (itemGroupList != null) {
                for (CompanyTeamItemGroup item : itemGroupList) {
                    if (item.getItemGroupId() == null || item.getItemGroupId().trim().isEmpty()) {
                        throw new RuntimeException("项目组ID不能为空");
                    }
                    if (item.getTeamId() == null || !item.getTeamId().equals(teamId)) {
                        item.setTeamId(teamId);
                    }
                }
            }

            // 在事务中先删除再插入
            jdbcTemplate.update("delete from company_team_item_group where team_id = ?", teamId);

            if (itemGroupList != null && !itemGroupList.isEmpty()) {
                companyTeamItemGroupService.saveOrUpdateBatch(itemGroupList);
            }

            // 如果不跳过附属和赠送项目处理
            if (!skipGiftAndAttach && itemGroupList != null && !itemGroupList.isEmpty()) {
                log.info("开始处理附属和赠送项目，主项目数量: {}, teamId: {}", itemGroupList.size(), teamId);

                // 转换为CustomerRegItemGroup格式以复用现有逻辑
                List<CustomerRegItemGroup> tempGroupList = convertToCustomerRegItemGroups(itemGroupList);
                log.info("转换后的主项目数量: {}", tempGroupList.size());

                // 获取附属项目
                List<CustomerRegItemGroup> attachGroups = itemGroupRelationService.getAttachGroups(tempGroupList);
                log.info("获取到附属项目数量: {}", attachGroups != null ? attachGroups.size() : 0);

                if (CollectionUtils.isNotEmpty(attachGroups)) {
                    log.info("开始处理附属项目，数量: {}", attachGroups.size());

                    // 验证附属项目的互斥关系
                    itemGroupRelationService.checkIsHaveMutexes(attachGroups);

                    // 转换为CompanyTeamItemGroup并保存（标记为附属项目）
                    List<CompanyTeamItemGroup> attachTeamGroups = convertToCompanyTeamItemGroups(attachGroups, teamId, "attach");
                    log.info("转换后的附属项目数量: {}", attachTeamGroups.size());

                    if (!attachTeamGroups.isEmpty()) {
                        companyTeamItemGroupService.saveBatch(attachTeamGroups);
                        // 将附属项目添加到主列表中，以便后续获取赠送项目
                        tempGroupList.addAll(attachGroups);
                        itemGroupList.addAll(attachTeamGroups);
                        log.info("成功添加 {} 个附属项目", attachTeamGroups.size());
                    }
                } else {
                    log.info("没有找到附属项目");
                }

                // 获取赠送项目（需要考虑主项目和附属项目的赠送项目）
                List<CustomerRegItemGroup> giftGroups = itemGroupRelationService.getGiftGroups(tempGroupList);
                log.info("获取到赠送项目数量: {}", giftGroups != null ? giftGroups.size() : 0);

                if (CollectionUtils.isNotEmpty(giftGroups)) {
                    log.info("开始处理赠送项目，数量: {}", giftGroups.size());

                    // 验证赠送项目的互斥关系
                    itemGroupRelationService.checkIsHaveMutexes(giftGroups);

                    // 转换为CompanyTeamItemGroup并保存（标记为赠送项目）
                    List<CompanyTeamItemGroup> giftTeamGroups = convertToCompanyTeamItemGroups(giftGroups, teamId, "gift");
                    log.info("转换后的赠送项目数量: {}", giftTeamGroups.size());

                    if (!giftTeamGroups.isEmpty()) {
                        companyTeamItemGroupService.saveBatch(giftTeamGroups);
                        // 将赠送项目添加到主列表中
                        itemGroupList.addAll(giftTeamGroups);
                        log.info("成功添加 {} 个赠送项目", giftTeamGroups.size());
                    }
                } else {
                    log.info("没有找到赠送项目");
                }

                log.info("附属和赠送项目处理完成，最终项目总数: {}", itemGroupList.size());
            } else {
                log.info("跳过附属和赠送项目处理，skipGiftAndAttach: {}", skipGiftAndAttach);
            }

            //根据itemGroupList计算分组价格，并更新到company_team表
            BigDecimal[] prices = (itemGroupList != null && !itemGroupList.isEmpty()) ?
                itemGroupList.stream()
                    .map(item -> new BigDecimal[]{item.getPrice() != null ? item.getPrice() : BigDecimal.ZERO,
                            item.getPriceAfterDis() != null ? item.getPriceAfterDis() : BigDecimal.ZERO})
                    .reduce(new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO},
                            (a, b) -> new BigDecimal[]{a[0].add(b[0]), a[1].add(b[1])}) :
                new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO};

            BigDecimal price = prices[0].setScale(2, RoundingMode.HALF_UP);
            BigDecimal priceAfterDis = prices[1].setScale(2, RoundingMode.HALF_UP);

            jdbcTemplate.update("update company_team set team_price = ?, team_discount_price = ? where id = ?", price, priceAfterDis, teamId);

        //同步更新未检人员项目
        CompanyTeam companyTeam = companyTeamMapper.selectById(teamId);
        if (StringUtils.equals(companyTeam.getAllowChangeExaminerGroup(),"1")){
        Map<String, List<CompanyTeamItemGroup>> teamGroupMap = itemGroupList.stream().collect(Collectors.groupingBy(CompanyTeamItemGroup::getItemGroupId));
        Set<String> teamGroupIds = teamGroupMap.keySet();
        List<CustomerReg> regs = customerRegService.list(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getTeamId, teamId));
        boolean summaryFlag = true;
        // 获取当前允许的状态
        String allowStatus = companyTeam.getAllowChangeGroupSummaryStatus();

        Map<String, Set<String>> statusMapping = new HashMap<>();
        statusMapping.put("未总检", Set.of("未总检"));
        statusMapping.put("已预检", Set.of("未总检", "已预检"));
        statusMapping.put("已初检", Set.of("未总检", "已预检", "已初检"));
        statusMapping.put("已总检", Set.of("未总检", "已预检", "已初检", "已总检"));
        statusMapping.put("审核通过", Set.of("未总检", "已预检", "已初检", "已总检", "审核通过"));

        Set<String> allowedStatuses = statusMapping.get(allowStatus);

        if (CollectionUtils.isNotEmpty(allowedStatuses)) {
            // 检查 regs 中所有对象的 summaryStatus 是否在允许的状态集合中
            summaryFlag = regs.stream()
                    .allMatch(g -> allowedStatuses.contains(g.getSummaryStatus()));
        } else {
            // 如果 allowStatus 不在映射中，默认 summaryFlag 为 false
            summaryFlag = false;
        }

        List<CustomerRegItemGroup> addGroups= Lists.newArrayList();
        boolean unCheckFlag = regs.stream().allMatch(r -> StringUtils.equals(r.getStatus(), "未登记"));
        if (CollectionUtils.isNotEmpty(regs)) {
            List<String> regIds = regs.stream().map(CustomerReg::getId).collect(Collectors.toList());
            Map<String, List<CustomerRegItemGroup>> regGroupMap = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().in(CustomerRegItemGroup::getCustomerRegId, regIds)).stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getCustomerRegId));
            for (CustomerReg reg:regs){
//                CustomerRegSummary summary = customerRegSummaryService.getByRegId(reg.getId());
                List<CustomerRegItemGroup> allRegItemGroups = CollectionUtils.isNotEmpty(regGroupMap.get(reg.getId())) ? regGroupMap.get(reg.getId()) : Lists.newArrayList();
//                boolean unPayFlag = allRegItemGroups.stream().allMatch(g -> StringUtils.equals(g.getPayStatus(), "待支付"));
                Map<String, List<CustomerRegItemGroup>> minusItemGroupMap = allRegItemGroups.stream().filter(i -> Objects.equals(i.getAddMinusFlag(), -1)).collect(Collectors.groupingBy(CustomerRegItemGroup::getItemGroupId));
                List<CustomerRegItemGroup> actualRegItemGroups = allRegItemGroups.stream().filter(i -> !Objects.equals(i.getAddMinusFlag(), -1)).collect(Collectors.toList());
//                if (Objects.isNull(summary)||StringUtils.equals(summary.getStatus(),ExConstants.SUMMARY_STATUS_已初检)&&StringUtils.equals(summary.getInitailDoctor(),"AI")){
                if (summaryFlag) {
                    Set<String> customerGroupIds = CollectionUtils.isNotEmpty(actualRegItemGroups) ? actualRegItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet()) : Sets.newHashSet();
                    Set<String> addGroupIds = new HashSet<>(teamGroupIds);
                    addGroupIds.removeAll(customerGroupIds);
                    if (CollectionUtils.isNotEmpty(addGroupIds)) {
                        List<String> addGroupIdList = new ArrayList<>(addGroupIds);
                        addGroupIdList.forEach(groupId -> {
                            CompanyTeamItemGroup companyTeamItemGroup = teamGroupMap.get(groupId).get(0);
                            List<CustomerRegItemGroup> minusGroups = minusItemGroupMap.get(groupId);
                            if (CollectionUtils.isNotEmpty(minusGroups)) {
                                CustomerRegItemGroup group = minusGroups.get(0);
                                BeanUtils.copyProperties(companyTeamItemGroup, group);
                                group.setAddMinusFlag(0);
                                addGroups.add(group);

                            } else {
                                CustomerRegItemGroup group = new CustomerRegItemGroup();
                                BeanUtils.copyProperties(companyTeamItemGroup, group);
                                group.setId(null);
                                group.setCustomerRegId(reg.getId());
                                group.setExamNo(reg.getExamNo());
                                group.setAddMinusFlag(0);
                                group.setPayStatus(ExConstants.PAY_STATUS_WAIT);
                                group.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                                addGroups.add(group);
                            }

                        });
                    }
                }

                if (unCheckFlag) {
                    Set<String> allGroupIds = allRegItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
                    Set<String> minusGroupIds = new HashSet<>(allGroupIds);
                    minusGroupIds.removeAll(teamGroupIds);
                    if (CollectionUtils.isNotEmpty(minusGroupIds)) {
                        customerRegItemGroupService.remove(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, reg.getId()).in(CustomerRegItemGroup::getItemGroupId, minusGroupIds));

                    }
                }


            }
            customerRegItemGroupService.saveOrUpdateBatch(addGroups);
        }

        }

        } catch (Exception e) {
            log.error("保存团检分组项目失败，teamId: {}, 错误信息: {}", teamId, e.getMessage(), e);
            throw new RuntimeException("保存团检分组项目失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将CompanyTeamItemGroup转换为CustomerRegItemGroup以复用现有的关系处理逻辑
     */
    private List<CustomerRegItemGroup> convertToCustomerRegItemGroups(List<CompanyTeamItemGroup> teamGroups) {
        return teamGroups.stream().map(teamGroup -> {
            CustomerRegItemGroup customerGroup = new CustomerRegItemGroup();
            customerGroup.setItemGroupId(teamGroup.getItemGroupId());
            customerGroup.setItemGroupName(teamGroup.getItemGroupName());
            customerGroup.setCheckPartId(teamGroup.getCheckPartId());
            customerGroup.setCheckPartName(teamGroup.getCheckPartName());
            customerGroup.setCheckPartCode(teamGroup.getCheckPartCode());
            customerGroup.setDepartmentId(teamGroup.getDepartmentId());
            customerGroup.setDepartmentName(teamGroup.getDepartmentName());
            customerGroup.setDepartmentCode(teamGroup.getDepartmentCode());
            customerGroup.setPrice(teamGroup.getPrice());
            customerGroup.setPriceAfterDis(teamGroup.getPriceAfterDis());
            customerGroup.setDisRate(teamGroup.getDisRate());
            customerGroup.setHisCode(teamGroup.getHisCode());
            customerGroup.setHisName(teamGroup.getHisName());
            customerGroup.setPlatCode(teamGroup.getPlatCode());
            customerGroup.setPlatName(teamGroup.getPlatName());
            customerGroup.setClassCode(teamGroup.getClassCode());
            customerGroup.setMinDiscountRate(teamGroup.getMinDiscountRate());
            customerGroup.setType(teamGroup.getItemGroupCategory()); // 字段名映射：itemGroupCategory -> type
            return customerGroup;
        }).collect(Collectors.toList());
    }

    /**
     * 将CustomerRegItemGroup转换为CompanyTeamItemGroup
     */
    private List<CompanyTeamItemGroup> convertToCompanyTeamItemGroups(List<CustomerRegItemGroup> customerGroups, String teamId) {
        return convertToCompanyTeamItemGroups(customerGroups, teamId, "main");
    }

    /**
     * 将CustomerRegItemGroup转换为CompanyTeamItemGroup（带来源类型标记）
     */
    private List<CompanyTeamItemGroup> convertToCompanyTeamItemGroups(List<CustomerRegItemGroup> customerGroups, String teamId, String sourceType) {
        // 获取团检分组信息以获取companyRegId
        CompanyTeam companyTeam = companyTeamMapper.selectById(teamId);
        String companyRegId = companyTeam != null ? companyTeam.getCompanyRegId() : null;

        return customerGroups.stream().map(customerGroup -> {
            CompanyTeamItemGroup teamGroup = new CompanyTeamItemGroup();

            // 必填字段
            teamGroup.setCompanyRegId(companyRegId); // 设置单位预约ID
            teamGroup.setTeamId(teamId);
            teamGroup.setItemGroupId(customerGroup.getItemGroupId());
            teamGroup.setItemGroupName(customerGroup.getItemGroupName());

            // 部位相关字段
            teamGroup.setCheckPartId(customerGroup.getCheckPartId());
            teamGroup.setCheckPartName(customerGroup.getCheckPartName());
            teamGroup.setCheckPartCode(customerGroup.getCheckPartCode());

            // 科室相关字段
            teamGroup.setDepartmentId(customerGroup.getDepartmentId());
            teamGroup.setDepartmentName(customerGroup.getDepartmentName());
            teamGroup.setDepartmentCode(customerGroup.getDepartmentCode());

            // 价格相关字段（确保不为null）
            teamGroup.setPrice(customerGroup.getPrice() != null ? customerGroup.getPrice() : BigDecimal.ZERO);
            teamGroup.setPriceAfterDis(customerGroup.getPriceAfterDis() != null ? customerGroup.getPriceAfterDis() : BigDecimal.ZERO);
            teamGroup.setDisRate(customerGroup.getDisRate() != null ? customerGroup.getDisRate() : BigDecimal.ONE);
            teamGroup.setMinDiscountRate(customerGroup.getMinDiscountRate() != null ? customerGroup.getMinDiscountRate() : BigDecimal.ONE);
            teamGroup.setPriceDisDiffAmount(BigDecimal.ZERO); // 默认差额为0

            // HIS相关字段
            teamGroup.setHisCode(customerGroup.getHisCode());
            teamGroup.setHisName(customerGroup.getHisName());

            // 平台相关字段
            teamGroup.setPlatCode(customerGroup.getPlatCode());
            teamGroup.setPlatName(customerGroup.getPlatName());

            // 其他字段
            teamGroup.setClassCode(customerGroup.getClassCode());
            teamGroup.setItemGroupCategory(customerGroup.getType() != null ? customerGroup.getType() : "健康项目"); // 字段名映射：type -> itemGroupCategory
            teamGroup.setAddMinusFlag(0); // 默认为正常项目
            teamGroup.setPayerType("单位"); // 团检默认单位支付
            teamGroup.setSourceType(sourceType); // 设置项目来源类型

            return teamGroup;
        }).collect(Collectors.toList());
    }



    @Override
    public List<CompanyTeamItemGroup> getItemGroupOfTeam(String teamId) {

        QueryWrapper<CompanyTeamItemGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_id", teamId);

        return companyTeamItemGroupService.list(queryWrapper);
    }

    @Override
    public CompanyTeam getCompanyTeam(String teamId) {
        CompanyTeam companyTeam = companyTeamMapper.selectById(teamId);
        if (companyTeam == null) {
            return null;
        }
        List<CompanyTeamItemGroup> itemGroupList = getItemGroupOfTeam(teamId);
        companyTeam.setItemGroupList(itemGroupList);
        //CompanyReg companyReg = companyRegMapper.selectById(companyTeam.getCompanyRegId());
        //companyTeam.setCompanyReg(companyReg);
        return companyTeam;
    }

    @Override
    public CompanyReg getCompanyRegDetail(String id) {

        CompanyReg companyReg = companyRegMapper.selectById(id);

        String companyName = commonAPI.translateDictFromTable("company", "name", "id", companyReg.getCompanyId());
        companyReg.setCompanyName(companyName);

        return companyReg;
    }

    @Override
    public List<CompanyReg> pageCompanyReg(Page<CompanyReg> page, String keyword,List<String> id) {
        return companyRegMapper.pageCompanyReg(page, keyword,id);
    }
    @Override
    public List<Company> pageCompanyByPid(Page<Company> page, String keyword, List<String> id, String pid) {
        return companyRegMapper.pageCompanyByDeptId(page, keyword,id,pid);
    }
}
