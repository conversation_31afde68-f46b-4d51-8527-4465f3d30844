package org.jeecg.modules.reg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.reg.entity.CustomerRegHealth;
import org.jeecg.modules.reg.mapper.CustomerRegHealthMapper;
import org.jeecg.modules.reg.service.ICustomerRegHealthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 健康档案
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
@Service
public class CustomerRegHealthServiceImpl extends ServiceImpl<CustomerRegHealthMapper, CustomerRegHealth> implements ICustomerRegHealthService {
    @Autowired
    private CustomerRegHealthMapper customerRegHealthMapper;

    @Override
    public CustomerRegHealth getHealthInfo(String customerId) {

        return  customerRegHealthMapper.selectHealth(customerId);
    }
}
