package org.jeecg.modules.reg.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.reg.entity.BarcodePrintItems;
import org.jeecg.modules.reg.service.IBarcodePrintItemsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 条码关联的项目组合
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Api(tags="条码关联的项目组合")
@RestController
@RequestMapping("/reg/barcodePrintItems")
@Slf4j
public class BarcodePrintItemsController extends JeecgController<BarcodePrintItems, IBarcodePrintItemsService> {
	@Autowired
	private IBarcodePrintItemsService barcodePrintItemsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param barcodePrintItems
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "条码关联的项目组合-分页列表查询")
	@ApiOperation(value="条码关联的项目组合-分页列表查询", notes="条码关联的项目组合-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BarcodePrintItems>> queryPageList(BarcodePrintItems barcodePrintItems,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<BarcodePrintItems> queryWrapper = QueryGenerator.initQueryWrapper(barcodePrintItems, req.getParameterMap());
		Page<BarcodePrintItems> page = new Page<BarcodePrintItems>(pageNo, pageSize);
		IPage<BarcodePrintItems> pageList = barcodePrintItemsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param barcodePrintItems
	 * @return
	 */
	@AutoLog(value = "条码关联的项目组合-添加")
	@ApiOperation(value="条码关联的项目组合-添加", notes="条码关联的项目组合-添加")
	@RequiresPermissions("reg:barcode_print_items:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BarcodePrintItems barcodePrintItems) {
		barcodePrintItemsService.save(barcodePrintItems);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param barcodePrintItems
	 * @return
	 */
	@AutoLog(value = "条码关联的项目组合-编辑")
	@ApiOperation(value="条码关联的项目组合-编辑", notes="条码关联的项目组合-编辑")
	@RequiresPermissions("reg:barcode_print_items:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BarcodePrintItems barcodePrintItems) {
		barcodePrintItemsService.updateById(barcodePrintItems);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "条码关联的项目组合-通过id删除")
	@ApiOperation(value="条码关联的项目组合-通过id删除", notes="条码关联的项目组合-通过id删除")
	@RequiresPermissions("reg:barcode_print_items:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		barcodePrintItemsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "条码关联的项目组合-批量删除")
	@ApiOperation(value="条码关联的项目组合-批量删除", notes="条码关联的项目组合-批量删除")
	@RequiresPermissions("reg:barcode_print_items:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.barcodePrintItemsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "条码关联的项目组合-通过id查询")
	@ApiOperation(value="条码关联的项目组合-通过id查询", notes="条码关联的项目组合-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BarcodePrintItems> queryById(@RequestParam(name="id",required=true) String id) {
		BarcodePrintItems barcodePrintItems = barcodePrintItemsService.getById(id);
		if(barcodePrintItems==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(barcodePrintItems);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param barcodePrintItems
    */
    @RequiresPermissions("reg:barcode_print_items:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BarcodePrintItems barcodePrintItems) {
        return super.exportXls(request, barcodePrintItems, BarcodePrintItems.class, "条码关联的项目组合");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("reg:barcode_print_items:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BarcodePrintItems.class);
    }

}
