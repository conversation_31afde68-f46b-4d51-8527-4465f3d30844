package org.jeecg.modules.reg.service.impl;

import org.jeecg.modules.reg.entity.BarcodePrintInfo;
import org.jeecg.modules.reg.mapper.BarcodePrintInfoMapper;
import org.jeecg.modules.reg.service.IBarcodePrintInfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 条码打印
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Service
public class BarcodePrintInfoServiceImpl extends ServiceImpl<BarcodePrintInfoMapper, BarcodePrintInfo> implements IBarcodePrintInfoService {

}
