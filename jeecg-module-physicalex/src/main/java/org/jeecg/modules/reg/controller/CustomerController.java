package org.jeecg.modules.reg.controller;

import java.util.Arrays;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;
import org.jeecg.modules.reg.service.ICustomerService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 档案表
 * @Author: jeecg-boot
 * @Date: 2024-06-14
 * @Version: V1.0
 */
@Api(tags = "档案表")
@RestController
@RequestMapping("/reg/customer")
@Slf4j
public class CustomerController extends JeecgController<Customer, ICustomerService> {
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private ICustomerAccountService customerAccountService;


    /**
     * 分页列表查询
     *
     * @param customer
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "档案表-分页列表查询")
    @ApiOperation(value = "档案表-分页列表查询", notes = "档案表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Customer>> queryPageList(Customer customer,
                                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                 HttpServletRequest req) {
        QueryWrapper<Customer> queryWrapper = QueryGenerator.initQueryWrapper(customer, req.getParameterMap());
        Page<Customer> page = new Page<Customer>(pageNo, pageSize);
        IPage<Customer> pageList = customerService.page(page, queryWrapper);
        return Result.OK(pageList);
    }


    /**
     * 添加
     *
     * @param customer
     * @return
     */
    @AutoLog(value = "档案表-添加")
    @ApiOperation(value = "档案表-添加", notes = "档案表-添加")
    @RequiresPermissions("reg:customer:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Customer customer) {
        try {
            customerService.saveCustomer(customer);
        } catch (Exception e) {
            log.error("档案表-添加异常", e);
            return Result.error("添加失败！");
        }
        return Result.OK("添加成功！", customer);
    }

    /**
     * 编辑
     *
     * @param customer
     * @return
     */
    @AutoLog(value = "档案表-编辑")
    @ApiOperation(value = "档案表-编辑", notes = "档案表-编辑")
    @RequiresPermissions("reg:customer:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody Customer customer) {
        try {
            customerService.updateCustomer(customer);
        } catch (Exception e) {
            log.error("档案表-添加异常", e);
            return Result.error("添加失败！");
        }
        return Result.OK("添加成功！", customer);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "档案表-通过id删除")
    @ApiOperation(value = "档案表-通过id删除", notes = "档案表-通过id删除")
    @RequiresPermissions("reg:customer:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "档案表-批量删除")
    @ApiOperation(value = "档案表-批量删除", notes = "档案表-批量删除")
    @RequiresPermissions("reg:customer:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "档案表-通过id查询")
    @ApiOperation(value = "档案表-通过id查询", notes = "档案表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Customer> queryById(@RequestParam(name = "id", required = true) String id) {
        Customer customer = customerService.getById(id);
        if (customer == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customer);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customer
     */
    @RequiresPermissions("reg:customer:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Customer customer) {
        return super.exportXls(request, customer, Customer.class, "档案表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("reg:customer:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Customer.class);
    }

    @ApiOperation(value = "档案表-通过身份证号查询", notes = "档案表-通过身份证号查询")
    @GetMapping(value = "/queryByIdCard4CardOrder")
    public Result<Customer> queryByIdCard4CardOrder(@RequestParam(name = "idCard", required = true) String idCard) {

        Customer customer = customerService.getByIdCard(idCard);
        if (customer == null) {
            return Result.error("未找到对应数据");
        }
        //根据生日计算年龄
        customer.setAge(DateUtil.ageOfNow(customer.getBirthday()));
        return Result.OK(customer);
    }


    @ApiOperation(value = "档案表-根据登记id查询结果和总检结论", notes = "档案表-根据登记id查询结果和总检结论")
    @GetMapping(value = "/queryResultAndSummaryByRegId")
    public Result<?> queryResultAndSummaryByRegId(@RequestParam(name = "regId", required = true) String regId) {
        try {
            Map<String, Object> resultAndSummary = customerService.queryResultAndSummaryByRegId(regId);
            return Result.OK("查询成功！", resultAndSummary);
        } catch (Exception e) {
            log.error("查询失败", e);
            return Result.error("查询失败！"+e.getMessage());
        }
    }

}
