package org.jeecg.modules.reg.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.reg.service.ICustomerRegMockService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class MockTestJob implements Job {
    private String parameter;

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }


    @Autowired
    private ICustomerRegMockService customerRegMockService;
    private final ExecutorService executorService = Executors.newFixedThreadPool(50); // Create a thread pool with 10 threads、

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                executorService.submit(() -> {
                    try {
                        customerRegMockService.generateData(this.parameter);
                    } catch (Exception e) {
                        log.error("模拟生成数据异常！", e);
                    }
                });
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("模拟生成数据任务正在执行中!");
        }
    }
}