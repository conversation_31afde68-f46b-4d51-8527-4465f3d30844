package org.jeecg.modules.reg.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.occu.entity.ZyConclusion;
import org.jeecg.modules.occu.entity.ZyConclusionDetail;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.reg.bo.DepartGroupTree;
import org.jeecg.modules.station.bo.StatusStat;
import org.jeecg.modules.summary.bo.teamreport.CompanyAbnormalItemResultVO;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Data
@TableName("customer_reg")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "customer_reg对象", description = "客户登记")
public class CustomerReg implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;

    //@Excel(name = "客户照片", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;

    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 客户照片
     */
    //@Excel(name = "客户照片", width = 15)
    @ApiModelProperty(value = "客户照片")
    private java.lang.String customerAvatar;
    /**
     * 客户ID
     */
    //@Excel(name = "客户ID", width = 15)
    @ApiModelProperty(value = "客户ID")
    private java.lang.String customerId;

    private String archivesNum;
    /**
     * 登记号
     */
    //@Excel(name = "登记号", width = 15)
    @ApiModelProperty(value = "登记号")
    private java.lang.String customerRegNum;

    /**
     * HIS编码
     */
    //@Excel(name = "HIS编码", width = 15)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty(value = "HIS编码")
    private java.lang.String hisPid;
    /**
     * HIS编码
     */
    //@Excel(name = "HIS编码", width = 15)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty(value = "HIS编码")
    private java.lang.String hisVisitNo;
    /**
     * 住院号
     */
    //@Excel(name = "住院号", width = 15)
    @ApiModelProperty(value = "住院号")
    private java.lang.String hisInpatientId;
    /**
     * 所属单位登记分组
     */
    @Excel(name = "分组名称", width = 15, orderNum = "1")
    @ApiModelProperty(value = "所属单位登记分组")
    private java.lang.String teamName;
    /**
     * 姓名
     */
    @Excel(name = "姓名", width = 15, orderNum = "20")
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
    /**
     * 证件类型
     */
    @Excel(name = "证件类型", width = 15, dicCode = "idcard_type", orderNum = "30")
    @Dict(dicCode = "idcard_type")
    @ApiModelProperty(value = "证件类型")
    private java.lang.String cardType;
    /**
     * 证件号
     */
    @Excel(name = "证件号", width = 15, orderNum = "40")
    @ApiModelProperty(value = "证件号")
    private java.lang.String idCard;
    /**
     * 性别
     */
    @Excel(name = "性别", width = 15, dicCode = "sex", orderNum = "50")
    @Dict(dicCode = "sex")
    @ApiModelProperty(value = "性别")
    private java.lang.String gender;
    /**
     * 年龄
     */
    @Excel(name = "年龄", width = 15, orderNum = "60")
    @ApiModelProperty(value = "年龄")
    private java.lang.Integer age;
    /**
     * 年龄单位
     */
    //@Excel(name = "年龄单位", width = 15, dicCode = "age_unit", orderNum = "4")
    @Dict(dicCode = "age_unit")
    @ApiModelProperty(value = "年龄单位")
    private java.lang.String ageUnit;
    /**
     * 出生日期
     */
    @Excel(name = "出生日期", width = 15, format = "yyyy-MM-dd", orderNum = "70")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
    private java.util.Date birthday;
    /**
     * 电话
     */
    @Excel(name = "电话", width = 15, orderNum = "80")
    @ApiModelProperty(value = "电话")
    private java.lang.String phone;
    /**
     * 婚姻状况
     */
    @Excel(name = "婚姻状况", width = 15, dicCode = "material_type", orderNum = "90")
    @Dict(dicCode = "material_type")
    @ApiModelProperty(value = "婚姻状况")
    private java.lang.String marriageStatus;

    /**
     * 是否备孕
     */
    @Excel(name = "是否备孕", dicCode = "yn", width = 15, orderNum = "100")
    @ApiModelProperty(value = "是否备孕")
    private java.lang.String pregnancyFlag;
    /**
     * 民族
     */
    //@Excel(name = "民族", width = 15)
    @ApiModelProperty(value = "民族")
    private java.lang.String nation;
    /**
     * 血型
     */
    //@Excel(name = "血型", width = 15, dicCode = "blood_type")
    @Dict(dicCode = "blood_type")
    @ApiModelProperty(value = "血型")
    private java.lang.String bloodType;


    /**
     * 体检卡号
     */
    //@Excel(name = "体检卡号", width = 15, orderNum = "9")
    @ApiModelProperty(value = "体检卡号")
    private java.lang.String examCardNo;
    /**
     * 医保卡号
     */
    //@Excel(name = "医保卡号", width = 15, orderNum = "10")
    @ApiModelProperty(value = "医保卡号")
    private java.lang.String medicalCardNo;
    /**
     * 健康证号
     */
    //@Excel(name = "健康证号", width = 15, orderNum = "11")
    @ApiModelProperty(value = "健康证号")
    private java.lang.String healthNo;
    /**
     * 通讯地址
     */
    //@Excel(name = "通讯地址", width = 15, orderNum = "12")
    @ApiModelProperty(value = "通讯地址")
    private java.lang.String address;
    /**
     * 省
     */
    //@Excel(name = "省", width = 15)
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 城市
     */
    //@Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 区域
     */
    //@Excel(name = "区域", width = 15)
    @ApiModelProperty(value = "区域")
    private String area;

    /**
     * 街道
     */
    // @Excel(name = "街道", width = 15)
    @ApiModelProperty(value = "街道")
    private String street;
    /**
     * 地址全称
     */
    private String addressDetail;

    /**
     * 省代码
     */
    // @Excel(name = "省代码", width = 15)
    @ApiModelProperty(value = "省代码")
    private String provinceCode;

    /**
     * 城市代码
     */
    //@Excel(name = "城市代码", width = 15)
    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    /**
     * 区域代码
     */
    // @Excel(name = "区域代码", width = 15)
    @ApiModelProperty(value = "区域代码")
    private String areaCode;

    /**
     * 街道、旗县代码
     */
    // @Excel(name = "区域代码", width = 15)
    @ApiModelProperty(value = "街道、旗县代码")
    private String streetCode;

    /**
     * 邮政编码
     */
    //@Excel(name = "邮政编码", width = 15, orderNum = "13")
    @ApiModelProperty(value = "邮政编码")
    private java.lang.String postCode;
    /**
     * 邮箱
     */
    //@Excel(name = "邮箱", width = 15, orderNum = "14")
    @ApiModelProperty(value = "邮箱")
    private java.lang.String email;
    /**
     * 客户类别
     */
    //@Excel(name = "客户类别", width = 15, dicCode = "customer_type", orderNum = "15")
    @Dict(dicCode = "customer_type")
    @ApiModelProperty(value = "客户类别")
    private java.lang.String customerCategory;
    /**
     * 体检分类
     */
    //@Excel(name = "体检分类", width = 15, dicCode = "examination_type", orderNum = "11")
    @Dict(dicCode = "examination_type")
    @ApiModelProperty(value = "体检分类")
    private java.lang.String examCategory;
    /**
     * 所属单位登记分组
     */
    @Dict(dictTable = "company_team", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "所属单位登记分组")
    private java.lang.String teamId;


    /**
     * 所属单位登记分组代码
     */
    //@Excel(name = "分组代码", width = 15, orderNum = "16")
    @ApiModelProperty(value = "所属单位登记分组代码")
    private java.lang.String teamNum;

    /**
     * 预约日期
     */
    //@Excel(name = "预约日期", width = 20, format = "yyyy-MM-dd", orderNum = "10")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "预约日期")
    private java.util.Date bookingDate;

    /**
     * 单位部门
     */
    @Excel(name = "单位部门", width = 15, dictTable = "company", dicText = "name", dicCode = "id", orderNum = "110")
    @Dict(dictTable = "company", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "单位部门ID")
    private java.lang.String companyDeptId;

    /**
     * 单位部门
     */
    @Excel(name = "单位部门名称", width = 15)
    @ApiModelProperty(value = "单位部门名称")
    private java.lang.String companyDeptName;


    /**
     * 国籍
     */
    //@Excel(name = "国籍", width = 15, dicCode = "country")
//    @Dict(dicCode = "country")
//    @Dict(dictTable = "dict_country", dicText = "name", dicCode = "code")
    @ApiModelProperty(value = "国籍")
    private java.lang.String country;

    @Dict(dictTable = "dict_country", dicText = "name", dicCode = "code")
    @ApiModelProperty(value = "国籍编码")
    private java.lang.String countryCode;
    /**
     * 危害因素
     */
    @Excel(name = "危害因素", width = 15, dictTable = "zy_risk_factor", dicText = "name", dicCode = "code", orderNum = "120")
    @Dict(dictTable = "zy_risk_factor", dicText = "name", dicCode = "code")
    @ApiModelProperty(value = "危害因素")
    private java.lang.String riskFactor;

    /**
     * 危害因素
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "接触危害因素时间")
    private Date riskStartTime;

    /**
     * 行业
     */
    //@Excel(name = "行业", width = 15, dictTable = "zy_industry", dicText = "name", dicCode = "id", orderNum = "29")
    @Dict(dictTable = "zy_industry", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "行业")
    private java.lang.String industry;

    /**
     * 工号
     */
    //@Excel(name = "工号", width = 15, orderNum="120")
    @ApiModelProperty(value = "工号")
    private java.lang.String workNo;
    /**
     * 职业
     */
    @Excel(name = "职业", width = 15, dicCode = "career", orderNum = "130")
    @Dict(dicCode = "career")
    @ApiModelProperty(value = "职业")
    private java.lang.String career;

    @TableField(exist = false)
    @ApiModelProperty(value = "职业名称")
    private java.lang.String careerName;
    /**
     * 工种
     */
    @Excel(name = "岗位类别", width = 15, dicCode = "job_status", orderNum = "140")
    @Dict(dicCode = "job_status")
    @ApiModelProperty(value = "岗位类别")
    private java.lang.String jobStatus;
    /**
     * 工种
     */
    @Excel(name = "工种", width = 15, dictTable = "zy_worktype", dicText = "name", dicCode = "code", orderNum = "150")
    @Dict(dictTable = "zy_worktype", dicText = "name", dicCode = "code")
    @ApiModelProperty(value = "工种")
    private java.lang.String workType;

    /**
     * 车间
     */
    //@Excel(name = "车间", width = 15, dictTable = "zy_work_shop", dicText = "name", dicCode = "id", orderNum = "160")
    @Dict(dictTable = "zy_work_shop", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "车间")
    private java.lang.String workShop;


    /**
     * 总工龄
     */
    @Excel(name = "总工龄（年）", width = 15, orderNum = "24")
    @ApiModelProperty(value = "总工龄")
    private java.lang.String workYears;
    /**
     * 总工龄
     */
    @Excel(name = "总工龄（月）", width = 15, orderNum = "25")
    @ApiModelProperty(value = "总工龄")
    private java.lang.String workMonths;
    /**
     * 接害工龄
     */
    @Excel(name = "接害工龄（年）", width = 15, orderNum = "26")
    @ApiModelProperty(value = "接害工龄")
    private java.lang.String riskYears;
    /**
     * 接害工龄
     */
    @Excel(name = "接害工龄（月）", width = 15, orderNum = "27")
    @ApiModelProperty(value = "接害工龄")
    private java.lang.String riskMonths;
    /**
     * 接害保护
     */
    @Excel(name = "接害保护", width = 15, dictTable = "zy_risk_protect", dicText = "name", dicCode = "code", orderNum = "150")
    @Dict(dictTable = "zy_risk_protect", dicText = "name", dicCode = "code")
    @ApiModelProperty(value = "接害保护")
    private java.lang.String riskProtect;
    /**
     * 是否复查
     */
    //@Excel(name = "是否复查", width = 15)
    @ApiModelProperty(value = "是否复查")
    private java.lang.Integer reExamStatus;
    /**
     * 复查备注
     */
    //@Excel(name = "复查备注", width = 15)
    @ApiModelProperty(value = "复查备注")
    private java.lang.String reExamRemark;
    /**
     * 监测类型
     */
    //@Excel(name = "监测类型", width = 15)
    @ApiModelProperty(value = "监测类型")
    private java.lang.String monitoringType;
    /**
     * 照射种类
     */
    //@Excel(name = "照射种类", width = 15)
    @ApiModelProperty(value = "照射种类")
    private java.lang.String occIrradiation;


    /**
     * 发票抬头
     */
    //@Excel(name = "发票抬头", width = 15)
    @ApiModelProperty(value = "发票抬头")
    private java.lang.String recipeTitle;

    /**
     * 文化程度
     */
    //@Excel(name = "文化程度", width = 15, dicCode = "edu_level")
    @Dict(dicCode = "edu_level")
    @ApiModelProperty(value = "文化程度")
    private java.lang.String eduLevel;
    /**
     * 介绍人
     */
    //@Excel(name = "介绍人", width = 15)
    @ApiModelProperty(value = "介绍人")
    private java.lang.String introducer;


    /**
     * 备注
     */
    //@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**
     * 所属单位登记
     */
    //@Excel(name = "所属单位登记", width = 15)
    @ApiModelProperty(value = "所属单位登记")
    private java.lang.String companyRegId;

    @ApiModelProperty(value = "所属预约名字")
    private java.lang.String companyRegName;

    /**
     * 检查日期
     */
    //@Excel(name = "检查日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "检查日期")
    private java.util.Date checkDate;
    /**
     * 登记员工
     */
    // @Excel(name = "登记员工", width = 15)
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "登记员工")
    private java.lang.String creatorBy;

    private java.lang.String creator;

    /**
     * 检查状态
     */
    //@Excel(name = "检查状态", width = 15)
    @ApiModelProperty(value = "检查状态")
    private String checkState;
    /**
     * 信息来源
     */
    //@Excel(name = "信息来源", width = 15)
    @ApiModelProperty(value = "信息来源")
    private java.lang.Integer infoSource;
    /**
     * 支付状态
     */
    // @Excel(name = "支付状态", width = 15)
    @ApiModelProperty(value = "支付状态")
    private java.lang.String paymentState;
    /**
     * 操作人ID
     */
    //@Excel(name = "操作人ID", width = 15)
    @ApiModelProperty(value = "操作人ID")
    private java.lang.String operId;
    /**
     * 操作人
     */
    //@Excel(name = "操作人", width = 15)
    @ApiModelProperty(value = "操作人")
    private java.lang.String operName;
    /**
     * 操作时间
     */
    //@Excel(name = "操作时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作时间")
    private java.util.Date operTime;
    /**
     * 审核日期
     */
    //@Excel(name = "审核日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核日期")
    private java.util.Date auditDate;
    /**
     * 原体检人
     */
    //@Excel(name = "原体检人", width = 15)
    @ApiModelProperty(value = "原体检人")
    private java.lang.String originCustomer;

    @ApiModelProperty(value = "原体检人证件号")
    private String originCustomerIdcard;

    @ApiModelProperty(value = "与原检人关系")
    private String originCustomerRelation;
    /**
     * 补检
     */
    //@Excel(name = "补检", width = 15)
    @ApiModelProperty(value = "补检")
    private java.lang.Integer supplyFlag;
    /**
     * 预缴
     */
    //@Excel(name = "预缴", width = 15)
    @ApiModelProperty(value = "预缴")
    private java.lang.Integer prePayFlag;
    /**
     * 报告查询码
     */
    //@Excel(name = "报告查询码", width = 15)
    @ApiModelProperty(value = "报告查询码")
    private java.lang.String webQueryCode;

    /**
     * 单位名称
     */
    //@Excel(name = "单位名称", width = 15, orderNum = "28")
    @ApiModelProperty(value = "单位名称")
    private java.lang.String companyName;


    /**
     * 保密等级
     */
    //@Excel(name = "保密等级", width = 15, orderNum = "29")
    @ApiModelProperty(value = "保密等级")
    private java.lang.String secretLevel;

    /**
     * 单位命ID
     */
    @ApiModelProperty(value = "单位ID")
    private java.lang.String companyId;

    private Date createTime;

    @ApiModelProperty(value = "登记时间")
    private Date regTime;

    @ApiModelProperty(value = "导引单打印次数")
    private Integer guidancePrintTimes;

    @ApiModelProperty(value = "登记流水号")
    private Long serialNo;

    @Excel(name = "序号", width = 15, orderNum = "0")
    @ApiModelProperty(value = "序号")
    private Long appointmentSort;


    @ApiModelProperty(value = "交表状态")
    private String retrieveStatus;

    @ApiModelProperty(value = "交表时间")
    private Date retrieveTime;

    @ApiModelProperty(value = "收表人")
    private String retrieveBy;

    @ApiModelProperty(value = "收表图片")
    private String retrieveImg;

    @ApiModelProperty(value = "组合列表")
    @TableField(exist = false)
    private List<CustomerRegItemGroup> itemGroupList;

    @ApiModelProperty(value = "所属单位预约分组")
    @TableField(exist = false)
    private CompanyTeam companyTeam;

    @ApiModelProperty(value = "所属单位预约")
    @TableField(exist = false)
    private CompanyReg companyReg;

    @ApiModelProperty(value = "包含的体检组合组合的状态统计")
    @TableField(exist = false)
    private List<StatusStat> statusStatList;

    @ApiModelProperty(value = "包含的体检组合接口状态统计")
    @TableField(exist = false)
    private List<StatusStat> interfaceStatusStatList;

    @TableField(exist = false)
    private String suitName;

    @TableLogic
    private String delFlag;

    private String summaryStatus;

    @TableField(exist = false)
    private String summaryStatusColor;

    @TableField(exist = false)
    private List<ZyRiskFactor> riskFactorList;

    @TableField(exist = false)
    private BigDecimal totalPrice;

    @TableField(exist = false)
    private BigDecimal payedAmount;

    @TableField(exist = false)
    private BigDecimal remainAmount;

    /**
     * 紧急联系人
     */
    //@Excel(name = "紧急联系人", width = 15)
    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;
    /**
     * 紧急联系人电话
     */
    //@Excel(name = "紧急联系人电话", width = 15)
    @ApiModelProperty(value = "紧急联系人电话")
    private String emergencyPhone;

    @TableField(exist = false)
    private String regDate;

    @TableField(exist = false)
    private String genderDesc;

    @ApiModelProperty(value = "散客单位")
    private String personCompanyName;

    @ApiModelProperty(value = "可用早餐")
    private String breakfirstFlag;

    @ApiModelProperty(value = "预总检状态")
    private String preSummaryStatus;

    @ApiModelProperty(value = "总检时间")
    private String summaryTime;

    @ApiModelProperty(value = "预检时间")
    private String preSummaryTime;

    @ApiModelProperty(value = "总检审核时间")
    private String summaryAuditTime;

    @ApiModelProperty(value = "总检打印时间")
    private String reportPrintTime;

    @ApiModelProperty(value = "总检报告编辑锁定标志")
    private String reportEditLockFlag;

    @ApiModelProperty(value = "总检报告编辑锁定账号")
    private String reportEditLockBy;

    @ApiModelProperty(value = "总检报告编辑锁定人")
    private String reportEditLocker;

    @ApiModelProperty(value = "总检报告编辑锁定时间")
    private Date reportEditLockTime;

    private String interfaceStatus;

    private String preSummaryBy;

    private String preSummaryMethod;

    private String initailSummaryBy;

    private String initailSummaryDoctor;

    private Date initailSummaryTime;

    private String initailSummaryMethod;

    private String eReportStatus;

    private String paperReportStatus;

    private String eReportUrl;

    private String reportVerifiedBy;

    private Date reportVerifiedTime;

    private Date paperReportTakenTime;

    private String summaryDoctorAssigned;

    private String summaryDoctorNameAssigned;

    @ApiModelProperty(value = "团检限额")
    private BigDecimal limitAmount;
    @ApiModelProperty(value = "健康问卷ID")
    private String healthQuestId;
    @ApiModelProperty(value = "报告模板ID")
    private String reportTemplateId;
    @ApiModelProperty(value = "纸质报告签名图片")
    private String paperReportSignPic;
    @ApiModelProperty(value = "团检短信通知标志")
    private String companyNotifyFlag;
    @Excel(name = "预约日期", width = 15)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "预约日期")
    private Date appointmentDate;
    //心理测评cardId
    private String psyCardId;
    //心理测评短信通知
    private String psyNotifyFlag;
    //原检人团检限额id
    private String originCustomerLimitAmountId;


    @ApiModelProperty(value = "自登记到目前的天数")
    @TableField(exist = false)
    private String daysFromReg;
    @ApiModelProperty(value = "回访状态")
    private String fellowupStatus;
    @TableField(exist = false)
    private CustomerRegItemGroup customerRegItemGroup;
    @TableField(exist = false)
    private Integer annualCount;
    @TableField(exist = false)
    private String importErrmsg;
    @TableField(exist = false)
    private String itemGroupNames;
    @TableField(exist = false)
    private BigDecimal teamPayAmount;
    @TableField(exist = false)
    private List<DepartGroupTree> departGroupTreeList;
    @TableField(exist = false)
    private List<String> regIds;
    @TableField(exist = false)
    private List<CompanyAbnormalItemResultVO> itemResults;
    @TableField(exist = false)
    private List<ZyConclusionDetail> zyConclusionDetails;
    @TableField(exist = false)
    private ZyConclusion zyConclusion;
    @TableField(exist = false)
    private List<CustomerRegItemGroup> groupList;

    @TableField(exist = false)
    private BigDecimal remainingLimitAmount;
    @TableField(exist = false)
    private String riskFactor_dictText;
    @TableField(exist = false)
    private String workType_dictText;
    @TableField(exist = false)
    private String workShop_dictText;
    @TableField(exist = false)
    private String jobStatus_dictText;
    @TableField(exist = false)
    private String occuReportResultStatus;
    @TableField(exist = false)
    private String occuReportResultMsg;
    @TableField(exist = false)
    private String occuReportCostMs;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date occuReportUploadTime;
    @TableField(exist = false)
    private CustomerOrder customerOrder;
    @TableField(exist = false)
    private String avatar;



}
