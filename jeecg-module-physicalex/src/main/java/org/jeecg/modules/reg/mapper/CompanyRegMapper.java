package org.jeecg.modules.reg.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.reg.entity.CompanyReg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;

/**
 * @Description: 单位预约
 * @Author: jeecg-boot
 * @Date: 2024-02-19
 * @Version: V1.0
 */
public interface CompanyRegMapper extends BaseMapper<CompanyReg> {

    List<CompanyTeamItemGroup> getItemGroupOfTeam(@Param("teamId") String teamId);

    List<CompanyReg> pageCompanyReg(Page<CompanyReg> page, @Param("keyword") String keyword,@Param("id") List<String>  id);

    List<Company> pageCompanyByDeptId(Page<Company> page, @Param("keyword") String keyword, @Param("id") List<String>  id, @Param("pid") String pid);
}
