package org.jeecg.modules.reg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.reg.entity.BarcodePannelSetting;
import org.jeecg.modules.reg.mapper.BarcodePannelSettingMapper;
import org.jeecg.modules.reg.service.IBarcodePannelSettingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 采血站设置
 * @Author: jeecg-boot
 * @Date: 2024-07-18
 * @Version: V1.0
 */
@Service
public class BarcodePannelSettingServiceImpl extends ServiceImpl<BarcodePannelSettingMapper, BarcodePannelSetting> implements IBarcodePannelSettingService {

    @Override
    public BarcodePannelSetting getSetting() {
        QueryWrapper<BarcodePannelSetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<BarcodePannelSetting> list = list(queryWrapper);
        if (list.isEmpty()) {
            BarcodePannelSetting barcodePannelSetting = new BarcodePannelSetting();
            barcodePannelSetting.setAutoBloodBatch("1");
            barcodePannelSetting.setAutoPrintBatch("1");
            save(barcodePannelSetting);

            return barcodePannelSetting;
        } else {
            return list.get(0);
        }
    }
}
