package org.jeecg.modules.reg.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.Customer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 档案表
 * @Author: jeecg-boot
 * @Date:   2024-06-14
 * @Version: V1.0
 */
public interface CustomerMapper extends BaseMapper<Customer> {

    Customer selectByIdCard(@Param("idCard") String idCard);

    List<Customer> getCustomersByAccountId(@Param("accountId") String accountId,@Param("relationType") String relationType);
    Customer getCustomersByAccountIdAndIdCard(@Param("accountId") String accountId,@Param("relationType") String relationType,@Param("idCard") String idCard);
    Customer getDefaultCustomerByAccountId(@Param("accountId") String accountId,  @Param("defaultState")String defaultState);

    Customer getDefaultCustomerByOpenId(@Param("openId") String openId);

}
