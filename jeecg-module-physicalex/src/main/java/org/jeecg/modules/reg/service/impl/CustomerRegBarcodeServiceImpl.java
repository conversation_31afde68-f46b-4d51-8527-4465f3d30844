package org.jeecg.modules.reg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.jms.JmsMessageSender;
import org.jeecg.modules.basicinfo.entity.BarcodeSetting;
import org.jeecg.modules.basicinfo.entity.BarcodeSettingGroup;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.service.IBarcodeSettingGroupService;
import org.jeecg.modules.basicinfo.service.IBarcodeSettingService;
import org.jeecg.modules.basicinfo.service.IItemGroupService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegBarcode;
import org.jeecg.modules.reg.entity.CustomerRegBarcodeItem;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegBarcodeMapper;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.ICustomerRegBarcodeItemService;
import org.jeecg.modules.reg.service.ICustomerRegBarcodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 体检登记条码
 * @Author: jeecg-boot
 * @Date: 2024-06-09
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustomerRegBarcodeServiceImpl extends ServiceImpl<CustomerRegBarcodeMapper, CustomerRegBarcode> implements ICustomerRegBarcodeService {
    @Autowired
    private CustomerRegBarcodeMapper customerRegBarcodeMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ICustomerRegBarcodeItemService customerRegBarcodeItemService;
    @Autowired
    private IBarcodeSettingService barcodeSettingService;
    @Autowired
    private SequenceGenerator sequenceGenerator;
    @Autowired
    private IBarcodeSettingGroupService barcodeSettingGroupService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private IItemGroupService itemGroupService;
    @Autowired
    private JmsMessageSender jmsMessageSender;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CustomerRegBarcode> listCustomerRegBarcode(String customerRegId, String includeInvalid) {
        generateBarcode(customerRegId);
        List<CustomerRegBarcode> customerRegBarcodeList = customerRegBarcodeMapper.listCustomerRegBarcode(customerRegId);
        //按照seqId排序
        customerRegBarcodeList.forEach(item -> {
            if (item.getSeq() == null) {
                item.setSeq(0);
            }
        });
        customerRegBarcodeList = customerRegBarcodeList.stream().sorted(Comparator.comparing(CustomerRegBarcode::getSeq)).collect(Collectors.toList());


        if (!StringUtils.equals(includeInvalid, "1")) {
            //过滤掉已作废的条码
            customerRegBarcodeList = customerRegBarcodeList.stream().filter(item -> item.getBloodStatus() != ExConstants.BARCODE_STATUS_已作废).collect(Collectors.toList());
        }

        //为每个条码设置BarcodeSetting
        for (CustomerRegBarcode item : customerRegBarcodeList) {
            BarcodeSetting setting = barcodeSettingService.getById(item.getBarSettingId());
            item.setBarcodeSetting(setting);
        }
        return customerRegBarcodeList;
    }

    @Override
    public List<CustomerRegBarcode> listByReg(String customerRegId) {

        return customerRegBarcodeMapper.listByReg(customerRegId);
    }

    @Override
    public void updatePrintInfo(String id) {
        String sql = "update customer_reg_barcode set print_count = print_count + 1,print_time=? where id = ?";
        jdbcTemplate.update(sql, new Date(), id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CustomerRegBarcode> generateBarcode(String customerRegId) {
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            return Collections.emptyList();
        }
        String autoCharge = sysSettingService.getValueByCode("autoCharge");
        autoCharge = StringUtils.isNotBlank(autoCharge) ? autoCharge : "0";

        List<CustomerRegBarcode> customerRegBarcodeList = new ArrayList<>();
        //根据检客的项目，获取所有条码分组Id
        List<String> settingIds = customerRegBarcodeMapper.getBarcodeSettingIds4Reg(customerRegId);
        //根据条码分组Id获取所有条码配置
        QueryWrapper<BarcodeSetting> settingQueryWrapper = new QueryWrapper<>();
        settingQueryWrapper.in("id", settingIds);
        settingQueryWrapper.orderByDesc("priority");
        List<BarcodeSetting> settingList = settingIds.isEmpty() ? Collections.emptyList() : barcodeSettingService.list(settingQueryWrapper);

        if (!settingList.isEmpty()) {
            //获取检客大项集合并过滤掉不适用的大项
            List<CustomerRegItemGroup> customerRegItemGroupList = customerRegItemGroupMapper.selectList(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerRegId));
            String finalAutoCharge = autoCharge;
            customerRegItemGroupList = customerRegItemGroupList.stream().filter(item -> {
                boolean flag;
                int abandonFlag = item.getAbandonFlag() != null ? item.getAbandonFlag() : 0;
                int addMinusFlag = item.getAddMinusFlag() != null ? item.getAddMinusFlag() : 0;
                if (StringUtils.equals(finalAutoCharge, "1")) {
                    flag = !StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATE_退款成功) && abandonFlag != 1 && addMinusFlag != -1;
                } else {
                    flag = StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATE_已支付) && abandonFlag != 1 && addMinusFlag != -1;
                }
                return flag;
            }).toList();

            List<String> customerRegItemGroupIdList = customerRegItemGroupList.stream().map(CustomerRegItemGroup::getItemGroupId).toList();
            //提前取出分组关联大项集合
            List<BarcodeSettingGroup> settingGoupList = barcodeSettingGroupService.list(new LambdaQueryWrapper<BarcodeSettingGroup>().in(BarcodeSettingGroup::getSettingId, settingIds));

            Set<String> addedGroups = new HashSet<>();

            for (BarcodeSetting setting : settingList) {

                List<BarcodeSettingGroup> groupList = settingGoupList.stream().filter(item -> StringUtils.equals(item.getSettingId(), setting.getId()) && customerRegItemGroupIdList.contains(item.getGroupId()) && !addedGroups.contains(item.getGroupId())).sorted(Comparator.comparing(BarcodeSettingGroup::getGroupId)).toList();
                if (!groupList.isEmpty()) {
                    CustomerRegBarcode customerRegBarcode = new CustomerRegBarcode();
                    customerRegBarcode.setArchivesNum(customerReg.getArchivesNum());
                    customerRegBarcode.setExamNo(customerReg.getExamNo());
                    customerRegBarcode.setBarSettingId(setting.getId());
                    customerRegBarcode.setCustomerRegId(customerRegId);
                    customerRegBarcode.setBloodStatus(ExConstants.BARCODE_STATUS_待采样);
                    customerRegBarcode.setBloodTime(null);
                    customerRegBarcode.setPrintCount(0);
                    customerRegBarcode.setPrintTime(null);
                    customerRegBarcode.setBarTemplateId(setting.getTemplateId());
                    customerRegBarcode.setSpecimenCategory(setting.getSpecimenCategory());
                    customerRegBarcode.setSpecimenCategoryCode(setting.getSpecimenCategoryCode());
                    customerRegBarcode.setSeq(setting.getSort());

                    // Barcode generation logic
                    String barNumSource = setting.getBarNumSource();
                    String barNum = "";
                    if (StringUtils.equals(barNumSource, ExConstants.BARCODE_NUM_SOURCE_自动生成)) {
                        /*barNum = sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_BARCODE_NO);
                        char firstChar = barNum.charAt(0);
                        if (Character.isDigit(firstChar)) {
                            int incrementedChar = Character.getNumericValue(firstChar) + 1;
                            barNum = incrementedChar + barNum.substring(1);
                        }*/
                    } else {
                        barNum = customerReg.getExamNo();
                    }

                    String barTextOfSetting = setting.getText();
                    if (StringUtils.isNotBlank(barTextOfSetting)) {
                        customerRegBarcode.setBarText(barTextOfSetting);
                    } else {
                        StringBuilder barTextBuilder = new StringBuilder();
                        for (BarcodeSettingGroup item : groupList) {
                            String name = StringUtils.isNotBlank(item.getShortName()) ? item.getShortName() : item.getGroupName();
                            barTextBuilder.append(name).append(" ");
                        }
                        customerRegBarcode.setBarText(barTextBuilder.toString());
                    }

                    customerRegBarcode.setBarNo(barNum);

                    List<CustomerRegBarcodeItem> barcodeItems = new ArrayList<>();
                    // Save each barcode item individually
                    for (BarcodeSettingGroup group : groupList) {
                        CustomerRegBarcodeItem item = new CustomerRegBarcodeItem();
                        item.setBarcodeId(customerRegBarcode.getId());
                        item.setBarSettingId(setting.getId());
                        item.setBarcodeNo(customerRegBarcode.getBarNo());
                        item.setArchivesNum(customerReg.getArchivesNum());
                        item.setGroupId(group.getGroupId());
                        item.setGroupName(group.getGroupName());
                        item.setShortName(group.getShortName());
                        item.setCustomerRegId(customerRegId);
                        item.setHisCode(group.getHisCode());
                        item.setHisName(group.getHisName());

                        ItemGroup itemGroup = itemGroupService.getById(group.getGroupId());
                        item.setClassCode(itemGroup.getClassCode());
                        item.setSpecimenCategory(itemGroup.getClinicalType());
                        item.setSpecimenCategoryCode(itemGroup.getClinicalTypeCode());

                        barcodeItems.add(item);


                        // Mark this group as added
                        addedGroups.add(group.getGroupId());
                    }
                    customerRegBarcode.setBarcodeItemList(barcodeItems);
                    customerRegBarcodeList.add(customerRegBarcode);
                }
            }

            List<CustomerRegBarcode> originalBarcodeList = customerRegBarcodeMapper.listBarcodeWithItemByReg(customerRegId);
            //过滤掉已作废的条码
            originalBarcodeList = originalBarcodeList.stream().filter(item -> item.getBloodStatus() != ExConstants.BARCODE_STATUS_已作废 && item.getBloodStatus() != ExConstants.BARCODE_STATUS_已放弃).toList();
            //按照barSettingId对新旧条码取并集
            Map<String, List<CustomerRegBarcode>> originalBarcodeMap = originalBarcodeList.stream().collect(Collectors.groupingBy(CustomerRegBarcode::getBarSettingId));
            Map<String, List<CustomerRegBarcode>> newBarcodeMap = customerRegBarcodeList.stream().collect(Collectors.groupingBy(CustomerRegBarcode::getBarSettingId));

            //对barSettingId相同的新旧项目，分析其所含项目来决定是否要作废旧条码，或保存新条码（保存时还要判断是否需要删除与旧条码包含的相同项目）
            // 对barSettingId相同的新旧项目，分析其所含项目来决定是否要作废旧条码，或保存新条码（保存时还要判断是否需要删除与旧条码包含的相同项目）
            Set<String> allSettingIds = new HashSet<>();
            allSettingIds.addAll(originalBarcodeMap.keySet());
            allSettingIds.addAll(newBarcodeMap.keySet());

            for (String settingId : allSettingIds) {
                List<CustomerRegBarcode> originalBarcodes = originalBarcodeMap.getOrDefault(settingId, Collections.emptyList());
                List<CustomerRegBarcode> newBarcodes = newBarcodeMap.getOrDefault(settingId, Collections.emptyList());

                // 每个barSettingId对应唯一的新旧条码
                CustomerRegBarcode originalBarcode = originalBarcodes.isEmpty() ? null : originalBarcodes.get(0);
                CustomerRegBarcode newBarcode = newBarcodes.isEmpty() ? null : newBarcodes.get(0);

                if (newBarcode == null) {
                    // 新条码不存在，删除旧条码及相关数据
                    if (originalBarcode != null) {
                        invalidBarcode(originalBarcode.getId(), "所有项目均减项或退款，作废旧条码");
                        //TODO:需要通知检验系统
                    }
                } else {
                    if (originalBarcode == null) {
                        // 新增条码，保存并关联项目组
                        saveBarcodeAndUpdateGroups(newBarcode);
                    } else {

                        // 比较新旧条码的项目组ID集合
                        Set<String> originalGroups = originalBarcode.getBarcodeItemList().stream().map(CustomerRegBarcodeItem::getGroupId).collect(Collectors.toSet());
                        Set<String> newGroups = newBarcode.getBarcodeItemList().stream().map(CustomerRegBarcodeItem::getGroupId).collect(Collectors.toSet());

                        if (originalGroups.equals(newGroups)) {
                            // 项目组未变化，保留旧条码，移除新条码避免重复插入
                            customerRegBarcodeList.remove(newBarcode);
                        } else {

                            if (originalBarcode.getBloodStatus() == ExConstants.BARCODE_STATUS_已采样) {
                                //如果新的项目多于旧的项目，则保存新条码，但是要删除旧条码的项目
                                if (newGroups.size() > originalGroups.size()) {
                                    // 保存新条码并更新关联
                                    //从新条码的项目中排除旧条码的项目
                                    List<CustomerRegBarcodeItem> newBarcodeItemList = newBarcode.getBarcodeItemList().stream().filter(item -> !originalGroups.contains(item.getGroupId())).collect(Collectors.toList());
                                    newBarcode.setBarcodeItemList(newBarcodeItemList);

                                    //需要重新生成barText
                                    StringBuilder barTextBuilder = new StringBuilder();
                                    for (CustomerRegBarcodeItem item : newBarcodeItemList) {
                                        barTextBuilder.append(item.getGroupName()).append(" ");
                                    }
                                    String finalBarText = barTextBuilder.toString().trim();
                                    newBarcode.setBarText(finalBarText);

                                    // 改为比较项目组ID集合
                                    Set<String> newBarcodeGroups = Optional.ofNullable(newBarcode.getBarcodeItemList()).orElse(Collections.emptyList()).stream().filter(Objects::nonNull) // 过滤空对象
                                            .map(CustomerRegBarcodeItem::getGroupId).filter(Objects::nonNull) // 过滤空groupId
                                            .collect(Collectors.toSet());

                                    boolean isExisted = originalBarcodes.stream().anyMatch(oldBarcode -> {
                                        try {
                                            Set<String> oldGroups = Optional.ofNullable(oldBarcode.getBarcodeItemList()).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).map(CustomerRegBarcodeItem::getGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
                                            return newBarcodeGroups.equals(oldGroups);
                                        } catch (Exception e) {
                                            log.error("条码项目组比较异常 barcodeId:{}");
                                            return false; // 异常时默认不存在
                                        }
                                    });


                                    //如果originalBarcodes里面已经有newBarcode了，则不需要插入
                                    //boolean isExisted = originalBarcodes.stream().anyMatch(item -> StringUtils.equals(StringUtils.trim(item.getBarText()), StringUtils.trim(newBarcode.getBarText())));
                                    if (!isExisted) {
                                        // 插入新条码
                                        saveBarcodeAndUpdateGroups(newBarcode);
                                    }
                                } else {
                                    // 项目组变化，作废旧条码
                                    invalidBarcode(originalBarcode.getId(), "项目组变化，作废旧条码");
                                    //TODO:需要通知检验系统

                                    saveBarcodeAndUpdateGroups(newBarcode);
                                }
                            } else {
                                // 项目组变化，替换旧条码
                                invalidBarcode(originalBarcode.getId(), "项目组变化，作废旧条码，新条码：" + newBarcode.getBarNo());
                                // 保存新条码并更新关联
                                saveBarcodeAndUpdateGroups(newBarcode);
                            }
                        }
                    }
                }
            }
        }

        return customerRegBarcodeList;
    }


    // 辅助方法：保存条码及其项目，并更新关联的项目组
    private void saveBarcodeAndUpdateGroups(CustomerRegBarcode barcode) {
        // 插入新条码
        if (StringUtils.isBlank(barcode.getBarNo())) {
            String barNum = sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_BARCODE_NO);
            char firstChar = barNum.charAt(0);
            if (Character.isDigit(firstChar)) {
                int incrementedChar = Character.getNumericValue(firstChar) + 1;
                barNum = incrementedChar + barNum.substring(1);
            }
            barcode.setBarNo(barNum);
        }
        barcode.setBloodStatus(0);
        customerRegBarcodeMapper.insert(barcode);
        // 插入关联的item
        List<CustomerRegBarcodeItem> sortedItems = barcode.getBarcodeItemList().stream().sorted(Comparator.comparing(CustomerRegBarcodeItem::getGroupId)) // 按groupId排序
                .toList();
        for (CustomerRegBarcodeItem item : sortedItems) {
            item.setBarcodeId(barcode.getId());
            item.setBarcodeNo(barcode.getBarNo());
            customerRegBarcodeItemService.save(item);
            // 按排序后的顺序更新
            jdbcTemplate.update("UPDATE customer_reg_item_group SET barcode_id=? WHERE customer_reg_id=? AND item_group_id=?", item.getBarcodeId(), item.getCustomerRegId(), item.getGroupId());
        }
    }

    private void invalidBarcode(String id, String remark) {
        LambdaUpdateWrapper<CustomerRegBarcode> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerRegBarcode::getId, id);
        updateWrapper.set(CustomerRegBarcode::getBloodStatus, ExConstants.BARCODE_STATUS_已作废);
        updateWrapper.set(CustomerRegBarcode::getRemark, remark);
        update(updateWrapper);

        // 更新旧条码关联的customer_reg_item_group的barcode_id为null
        jdbcTemplate.update("UPDATE customer_reg_item_group SET barcode_id = NULL WHERE barcode_id = ?", id);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bloodBatch(List<CustomerRegBarcode> list) throws Exception {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (list == null || list.isEmpty()) {
            return;
        }
        for (CustomerRegBarcode item : list) {
            item.setBloodStatus(ExConstants.BARCODE_STATUS_已采样);
            item.setBloodTime(new Date());
            item.setBloodBy(loginUser.getUsername());
            item.setBloodByName(loginUser.getRealname());
            updateById(item);
        }
        /*CustomerReg customerReg = customerRegMapper.selectById(list.get(0).getCustomerRegId());
        jmsMessageSender.sendExamItem(JSONObject.toJSONString(customerReg));*/
        sendBarcode2Mq(list.get(0));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void blood(CustomerRegBarcode regBarcode) throws Exception {
        if (regBarcode == null || regBarcode.getCustomerRegId() == null) {
            return;
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        regBarcode.setBloodStatus(ExConstants.BARCODE_STATUS_已采样);
        regBarcode.setBloodTime(new Date());
        regBarcode.setBloodBy(loginUser.getUsername());
        regBarcode.setBloodByName(loginUser.getRealname());
        updateById(regBarcode);

        sendBarcode2Mq(regBarcode);
    }

    @Override
    public void sendBarcode2Mq(CustomerRegBarcode regBarcode) throws Exception {
        String enable_jms_barcode = sysSettingService.getValueByCode("enable_jms_blood");
        if (!StringUtils.equals(enable_jms_barcode, "1")) {
            log.info("未开启标本采集消息队列");
            return;
        }

        CustomerReg customerReg = customerRegMapper.selectById(regBarcode.getCustomerRegId());
        jmsMessageSender.sendExamItem(JSONObject.toJSONString(customerReg));
    }


    @Override
    public void invalidateBarcode(List<String> barcodeIdList, String reason) throws Exception {
        if (barcodeIdList == null || barcodeIdList.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<CustomerRegBarcode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CustomerRegBarcode::getId, barcodeIdList);
        List<CustomerRegBarcode> barcodeList = list(wrapper);

        for (CustomerRegBarcode barcode : barcodeList) {
            barcode.setLastStatus(barcode.getBloodStatus());
            barcode.setBloodStatus(ExConstants.BARCODE_STATUS_已作废);
            barcode.setRemark("作废标本,原因：" + reason);
            updateById(barcode);
            //更新customer_reg_item_group
        }
    }

    @Override
    public void unInvalidateBarcode(List<String> barcodeIdList) throws Exception {
        if (barcodeIdList == null || barcodeIdList.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<CustomerRegBarcode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CustomerRegBarcode::getId, barcodeIdList);
        List<CustomerRegBarcode> barcodeList = list(wrapper);

        for (CustomerRegBarcode barcode : barcodeList) {
            Integer status = barcode.getLastStatus();
            if (status == null || status == ExConstants.BARCODE_STATUS_已作废) {
                status = ExConstants.BARCODE_STATUS_待采样;
            }
            barcode.setBloodStatus(status);
            barcode.setRemark("取消作废，恢复标本");
            updateById(barcode);
            //更新customer_reg_item_group
        }
    }

    @Override
    public Page<CustomerRegBarcode> pageCustomerBarcode(Page<CustomerRegBarcode> page, String examNo, String bloodStatus, String regTimeStart, String regTimeEnd, String barcode) {
        return customerRegBarcodeMapper.pageCustomerBarcode(page, examNo, bloodStatus, regTimeStart, regTimeEnd, barcode);
    }

    @Override
    public List<CustomerRegBarcode> listByRegAndBarcodeSetting(String customerRegId, String barcodeSettingId) {

        return customerRegBarcodeMapper.listByRegAndBarcodeSetting(customerRegId, barcodeSettingId);
    }

    @Override
    public List<String> listGropIdsByBarcodeId(String barcodeId) {
        return customerRegBarcodeMapper.listGropIdsByBarcodeId(barcodeId);
    }

    @Transactional
    @Override
    public void abandeBarcode(List<String> barcodeIdList) throws Exception {
        if (barcodeIdList == null || barcodeIdList.isEmpty()) {
            return;
        }

        UpdateWrapper<CustomerRegItemGroup> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("barcode_id", barcodeIdList);
        updateWrapper.set("abandon_flag", 1);
        customerRegItemGroupMapper.update(null, updateWrapper);


        LambdaQueryWrapper<CustomerRegBarcode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CustomerRegBarcode::getId, barcodeIdList);
        List<CustomerRegBarcode> barcodeList = list(wrapper);

        for (CustomerRegBarcode barcode : barcodeList) {
            barcode.setLastStatus(barcode.getBloodStatus());
            barcode.setBloodStatus(ExConstants.BARCODE_STATUS_已放弃);
            barcode.setRemark("放弃项目，作废标本");
            updateById(barcode);
            //更新customer_reg_item_group
            //jdbcTemplate.update("update customer_reg_item_group set abandon_flag=1 where barcode_id=?", barcode.getId());
        }
    }

    @Override
    public void unAbandeBarcode(List<String> barcodeIdList) throws Exception {
        if (barcodeIdList == null || barcodeIdList.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<CustomerRegBarcode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CustomerRegBarcode::getId, barcodeIdList);
        List<CustomerRegBarcode> barcodeList = list(wrapper);
        for (CustomerRegBarcode barcode : barcodeList) {
            Integer status = barcode.getLastStatus();
            if (status == null || status == ExConstants.BARCODE_STATUS_已放弃) {
                status = ExConstants.BARCODE_STATUS_待采样;
            }
            barcode.setBloodStatus(status);
            barcode.setRemark("取消放弃，恢复标本");
            updateById(barcode);
            //更新customer_reg_item_group
            jdbcTemplate.update("update customer_reg_item_group set abandon_flag=0 where barcode_id=?", barcode.getId());
        }
    }
}
