package org.jeecg.modules.system.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.system.entity.SysUser;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IXgSystemService {
    Integer queryDictItemMaxSort(String mainId);

    /**
     * 获取下一个排序号，包括
     * departOrder,
     * guideSort,
     * reportSort,
     * summarySort,
     * @return
     */
    JSONObject queryNextDepartSort();

    Set<String> queryUserRolesById(String userId);

    List<Map<String,String>> queryUserRoleRelation();

    List<SysUser> listAllUser();
}
