package org.jeecg.modules.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.mapper.XgSystemMapper;
import org.jeecg.modules.system.service.IXgSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class IXgSystemServiceImpl implements IXgSystemService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private XgSystemMapper xgSystemMapper;

    @Override
    public Integer queryDictItemMaxSort(String mainId) {

        Integer maxSort = 0;
        try {
            maxSort = jdbcTemplate.queryForObject("select max(sort_order) from sys_dict_item where dict_id = ?", Integer.class, mainId);
        } catch (Exception e) {
            maxSort = 0;
        }
        return maxSort == null ? 0 : maxSort;
    }

    @Override
    public JSONObject queryNextDepartSort() {

        return jdbcTemplate.queryForObject("select max(depart_order) as departOrder,max(guide_sort) as guideSort,max(report_sort) as reportSort,max(summary_sort) as summarySort from sys_depart", (rs, rowNum) -> {
            JSONObject json = new JSONObject();
            json.put("departOrder", rs.getString("departOrder") != null ? rs.getInt("departOrder") + 1 : 1);
            json.put("guideSort", rs.getString("guideSort") != null ? rs.getInt("guideSort") + 1 : 1);
            json.put("reportSort", rs.getString("reportSort") != null ? rs.getInt("reportSort") + 1 : 1);
            json.put("summarySort", rs.getString("summarySort") != null ? rs.getInt("summarySort") + 1 : 1);
            return json;
        });
    }

    @Override
    public Set<String> queryUserRolesById(String userId) {

        List<String> roleId = jdbcTemplate.queryForList("select distinct(role_id) from sys_user_role where user_id = ?", String.class, userId);

        return Set.copyOf(roleId);
    }

    @Override
    public List<Map<String, String>> queryUserRoleRelation() {

        return jdbcTemplate.query("select u.username,r.role_code from sys_user_role sur join sys_user u on sur.user_id=u.id join sys_role r on sur.role_id=r.id", (rs, rowNum) -> {
            return Map.of("username", rs.getString("username"), "role_code", rs.getString("role_code"));
        });
    }

    @Override
    public List<SysUser> listAllUser() {
        return xgSystemMapper.listAllUser();
    }

}
