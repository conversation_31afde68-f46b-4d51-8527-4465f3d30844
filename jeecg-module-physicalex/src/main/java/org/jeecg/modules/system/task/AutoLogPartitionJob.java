package org.jeecg.modules.system.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.service.impl.LogPartitionScheduler;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoLogPartitionJob implements Job {

    @Autowired
    private LogPartitionScheduler logPartitionScheduler;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                logPartitionScheduler.manageLogTablePartitions();
            } catch (Exception e) {
                log.error("自动对sys_log进行分区异常！", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("自动对sys_log进行分区任务正在执行中!");
        }
    }
}