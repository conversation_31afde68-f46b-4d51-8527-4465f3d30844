package org.jeecg.modules.system.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Component
public class LogPartitionScheduler {

    private final JdbcTemplate jdbcTemplate;
    // 你想操作的日志表名
    private static final String LOG_TABLE = "sys_log";

    // 构造方法注入
    public LogPartitionScheduler(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 每个月的 1 号凌晨执行一次
     * Cron 表达式示例：0 0 1 1 * ? 表示在每月1日的01:00执行
     */
    //@Scheduled(cron = "0 0 1 1 * ?")
    public void manageLogTablePartitions() {
        try {
            // 1) 创建下个月分区
            createNextMonthPartition();
            // 2) 删除过期分区（可选，看你是否需要）
            dropExpiredPartitions(6);  // 保留最近 6 个月的分区（示例）
        } catch (Exception e) {
            log.error("管理日志分区异常: ", e);
        }
    }

    /**
     * 创建下个月的分区
     * <p>
     * 例如：现在是 2025-01-15，则下个月分区命名为 p202502，对应分区边界：VALUES LESS THAN (TO_DAYS('2025-03-01'))
     */
    private void createNextMonthPartition() {
        // 获取当前日期所在月份的下一个月的第一天
        LocalDate today = LocalDate.now();          // 例如: 2025-01-15
        LocalDate nextMonthFirstDay = today.withDayOfMonth(1).plusMonths(1);
        // nextMonthFirstDay = 2025-02-01

        // 分区名约定：pYYYYMM，比如 p202502
        String partitionName = "p" + nextMonthFirstDay.format(DateTimeFormatter.ofPattern("yyyyMM"));

        // 分区范围边界：下个月的第一天再加一个月。比如 2025-03-01
        LocalDate boundaryDate = nextMonthFirstDay.plusMonths(1);
        String boundaryDateStr = boundaryDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 检查分区是否已经存在
        String partitionExistSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.PARTITIONS " +
                "WHERE TABLE_SCHEMA = DATABASE() " +
                "  AND TABLE_NAME = ? " +
                "  AND PARTITION_NAME = ?";
        Integer count = jdbcTemplate.queryForObject(partitionExistSql, Integer.class, LOG_TABLE, partitionName);
        if(count != null && count > 0) {
            log.info("分区 {} 已存在，无需新增。", partitionName);
            return;
        }

        // 构建 SQL：直接增加分区
        String addPartitionSql = String.format(
                "ALTER TABLE %s ADD PARTITION (PARTITION %s VALUES LESS THAN (TO_DAYS('%s')))",
                LOG_TABLE, partitionName, boundaryDateStr);

        log.info("即将执行SQL: {}", addPartitionSql);

        try {
            jdbcTemplate.execute(addPartitionSql);
            log.info("成功添加分区: {}", partitionName);
        } catch (Exception e) {
            // 当报错信息包含 "MAXVALUE can only be used" 时，
            // 说明当前表最后一个分区使用了 MAXVALUE，需要通过 REORGANIZE 操作拆分 pmax 分区
            String errorMsg = e.getMessage();
            log.error("添加分区失败: {}", errorMsg);
            if (errorMsg != null && errorMsg.contains("MAXVALUE can only be used")) {
                // 再次检查目标分区是否存在，避免重复创建
                count = jdbcTemplate.queryForObject(partitionExistSql, Integer.class, LOG_TABLE, partitionName);
                if(count != null && count > 0){
                    log.info("分区 {} 已存在，无需REORGANIZE。", partitionName);
                    return;
                }
                // 采用 REORGANIZE 分区的方式，将最后一个 pmax 分区拆分为两个分区
                String reorganizeSql = String.format(
                        "ALTER TABLE %s REORGANIZE PARTITION pmax INTO (" +
                                "PARTITION %s VALUES LESS THAN (TO_DAYS('%s'))," +
                                "PARTITION pmax VALUES LESS THAN (MAXVALUE))",
                        LOG_TABLE, partitionName, boundaryDateStr);
                log.info("即将执行REORGANIZE SQL: {}", reorganizeSql);
                try {
                    jdbcTemplate.execute(reorganizeSql);
                    log.info("成功添加分区: {}（通过REORGANIZE方式）", partitionName);
                } catch (Exception ex) {
                    log.error("REORGANIZE 分区失败: {}", ex.getMessage());
                }
            }
        }
    }


    /**
     * 删除过期分区
     *
     * @param monthsToKeep 保留多少个月内的分区，例如 6 表示只保留最近 6 个月
     */
    private void dropExpiredPartitions(int monthsToKeep) {
        // 计算需要保留到哪个月份： 现在的当月往前推 (monthsToKeep - 1) 个月
        // 例如今天是 2025-01-15，monthsToKeep=6，则最早保留到 2024-08-01 (含8月)
        LocalDate today = LocalDate.now().withDayOfMonth(1);
        LocalDate earliestRetainMonth = today.minusMonths(monthsToKeep - 1);

        // 查询当前表所有的分区名
        String queryPartitionsSql = "SELECT PARTITION_NAME " +
                "FROM INFORMATION_SCHEMA.PARTITIONS " +
                "WHERE TABLE_SCHEMA = DATABASE() " +
                "  AND TABLE_NAME = ? " +
                "  AND PARTITION_NAME IS NOT NULL";

        List<String> partitionNames = jdbcTemplate.queryForList(queryPartitionsSql, String.class, LOG_TABLE);

        // 根据分区命名规则 pYYYYMM，判断哪些需要删除，例如：p202310, p202311, p202312 ...
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        for (String partitionName : partitionNames) {
            // 排除 pmax 分区，不予解析或删除
            if ("pmax".equalsIgnoreCase(partitionName)) {
                continue;
            }
            if (!partitionName.startsWith("p")) {
                continue;
            }
            try {
                String yyyymm = partitionName.substring(1); // 去掉 'p'
                LocalDate partitionMonth = LocalDate.parse(yyyymm + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));

                // 如果该分区月份 < earliestRetainMonth，就删除
                if (partitionMonth.isBefore(earliestRetainMonth)) {
                    String dropSql = String.format("ALTER TABLE %s DROP PARTITION %s", LOG_TABLE, partitionName);
                    log.info("即将删除过期分区: {}", partitionName);
                    jdbcTemplate.execute(dropSql);
                }
            } catch (Exception e) {
                log.warn("解析或删除分区 {} 出错: {}", partitionName, e.getMessage());
            }
        }
    }

}
