package org.jeecg.modules.system.controller;


import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.config.shiro.ShiroRealm;
import org.jeecg.modules.system.service.ISysDictService;
import org.jeecg.modules.system.service.IXgSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
@RestController
@RequestMapping("/xg/sys")
@Slf4j
public class XgSysController {

    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private IXgSystemService xgSystemService;
    @Autowired
    public RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ShiroRealm shiroRealm;

    /**
     * 获取给定itemId的字典项目最大排序值
     *
     * @return
     */
    @RequestMapping(value = "/getNextDictSort", method = RequestMethod.GET)
    public Result<?> getNextDictSort(@RequestParam(name = "dictId") String dictId) {
        Result<Integer> result = new Result<Integer>();
        Integer maxSort = xgSystemService.queryDictItemMaxSort(dictId);
        result.setResult(maxSort + 1);
        result.setSuccess(true);
        return result;
    }

    /**
     * 查询数据 查出我的部门,并以树结构数据格式响应给前端
     *
     * @return
     */
    @RequestMapping(value = "/getNextDepartSort", method = RequestMethod.GET)
    public Result<?> getNextDepartSort() {
        return Result.ok(xgSystemService.queryNextDepartSort());
    }

}
