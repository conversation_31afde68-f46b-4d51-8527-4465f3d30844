package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.summary.entity.ReportCallback;
import org.jeecg.modules.system.entity.SysUser;

import java.util.List;

/**
 * @Description: 报告召回
 * @Author: jeecg-boot
 * @Date:   2024-09-24
 * @Version: V1.0
 */
public interface XgSystemMapper extends BaseMapper<SysUser> {

    List<SysUser> listAllUser();
}
