package org.jeecg.modules.encription.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: rsa_keys
 * @Author: jeecg-boot
 * @Date:   2024-10-08
 * @Version: V1.0
 */
@Data
@TableName("rsa_keys")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="rsa_keys对象", description="rsa_keys")
public class RsaKeys implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private java.lang.Integer id;
	/**publicKey*/
	@Excel(name = "publicKey", width = 15)
    @ApiModelProperty(value = "publicKey")
    private java.lang.String publicKey;
	/**privateKey*/
	@Excel(name = "privateKey", width = 15)
    @ApiModelProperty(value = "privateKey")
    private java.lang.String privateKey;
	/**keyVersion*/
	@Excel(name = "keyVersion", width = 15)
    @ApiModelProperty(value = "keyVersion")
    private java.lang.Integer keyVersion;
	/**activeFlag*/
	@Excel(name = "activeFlag", width = 15)
    @ApiModelProperty(value = "activeFlag")
    private java.lang.Integer activeFlag;
	/**inTransitionFlag*/
	@Excel(name = "inTransitionFlag", width = 15)
    @ApiModelProperty(value = "inTransitionFlag")
    private java.lang.Integer inTransitionFlag;
	/**createdAt*/
	@Excel(name = "createdAt", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createdAt")
    private java.util.Date createdAt;
}
