package org.jeecg.modules.encription.controller;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.encription.service.IRsaKeysService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/keys")
public class RSAKeyController {

    @Autowired
    private IRsaKeysService rsaKeyService;

    @GetMapping("/public")
    public Result<?> getPublicKey() {
        String publicKey = rsaKeyService.getActivePrivateKey();
        return Result.OK(publicKey);
    }
}

