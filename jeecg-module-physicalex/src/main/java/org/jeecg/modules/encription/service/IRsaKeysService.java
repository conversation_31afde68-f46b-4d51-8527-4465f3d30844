package org.jeecg.modules.encription.service;

import org.jeecg.modules.encription.entity.RsaKeys;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;

/**
 * @Description: rsa_keys
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
public interface IRsaKeysService extends IService<RsaKeys> {
    KeyPair generateKeyPair() throws NoSuchAlgorithmException;

    String getBase64PublicKey(KeyPair keyPair);

    String getBase64PrivateKey(KeyPair keyPair);

    String getActivePrivateKey();

    // 获取所有处于过渡期的密钥
    List<String> getTransitionalPrivateKeys();

    @Transactional
    void rotateKeys(String newPrivateKey);

    // 删除已完成过渡期的旧密钥
    @Transactional
    void deleteOldKeys();
}
