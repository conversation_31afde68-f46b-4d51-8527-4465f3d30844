import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: 'publicKey',
    align: "center",
    dataIndex: 'publicKey'
  },
  {
    title: 'privateKey',
    align: "center",
    dataIndex: 'privateKey'
  },
  {
    title: 'keyVersion',
    align: "center",
    dataIndex: 'keyVersion'
  },
  {
    title: 'isActive',
    align: "center",
    dataIndex: 'isActive'
  },
  {
    title: 'isInTransition',
    align: "center",
    dataIndex: 'isInTransition'
  },
  {
    title: 'createdAt',
    align: "center",
    dataIndex: 'createdAt',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
];

// 高级查询数据
export const superQuerySchema = {
  publicKey: {title: 'publicKey',order: 0,view: 'textarea', type: 'string',},
  privateKey: {title: 'privateKey',order: 1,view: 'textarea', type: 'string',},
  keyVersion: {title: 'keyVersion',order: 2,view: 'number', type: 'number',},
  isActive: {title: 'isActive',order: 3,view: 'number', type: 'number',},
  isInTransition: {title: 'isInTransition',order: 4,view: 'number', type: 'number',},
  createdAt: {title: 'createdAt',order: 5,view: 'date', type: 'string',},
};
