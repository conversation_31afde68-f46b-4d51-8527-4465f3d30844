package org.jeecg.modules.encription.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.jeecg.modules.encription.entity.RsaKeys;
import org.jeecg.modules.encription.mapper.RsaKeysMapper;
import org.jeecg.modules.encription.service.IRsaKeysService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: rsa_keys
 * @Author: jeecg-boot
 * @Date: 2024-10-08
 * @Version: V1.0
 */
@Service
public class RsaKeysServiceImpl extends ServiceImpl<RsaKeysMapper, RsaKeys> implements IRsaKeysService {

    @Autowired
    private RsaKeysMapper rsaKeysMapper;

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    @Override
    public KeyPair generateKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        return keyPairGenerator.generateKeyPair();
    }

    @Override
    public String getBase64PublicKey(KeyPair keyPair) {
        return Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
    }

    @Override
    public String getBase64PrivateKey(KeyPair keyPair) {
        return Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
    }

    public String getActivePrivateKey() {
        QueryWrapper<RsaKeys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("active_flag", 1);
        RsaKeys rsaKey = rsaKeysMapper.selectOne(queryWrapper);
        if (rsaKey == null) {
            throw new RuntimeException("No active RSA key found.");
        }
        return rsaKey.getPrivateKey();
    }

    // 获取所有处于过渡期的密钥
    public List<String> getTransitionalPrivateKeys() {
        QueryWrapper<RsaKeys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("in_transition_flag", 1);
        List<RsaKeys> transitionalKeys = rsaKeysMapper.selectList(queryWrapper);
        return transitionalKeys.stream().map(RsaKeys::getPrivateKey).collect(Collectors.toList());
    }

    @Transactional
    public void rotateKeys(String newPrivateKey) {
        // 获取当前的活跃密钥
        QueryWrapper<RsaKeys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("active_flag", 1);
        queryWrapper.last("limit 1");
        RsaKeys activeKey = rsaKeysMapper.selectOne(queryWrapper);

        if (activeKey != null) {
            // 将当前活跃密钥标记为过渡期密钥
            activeKey.setActiveFlag(0);
            activeKey.setInTransitionFlag(1);
            rsaKeysMapper.updateById(activeKey);
        }

        // 添加新密钥并将其设为活跃
        RsaKeys newKey = new RsaKeys();
        newKey.setPrivateKey(newPrivateKey);
        newKey.setActiveFlag(1);
        newKey.setInTransitionFlag(0);
        newKey.setKeyVersion((activeKey != null ? activeKey.getKeyVersion() : 0) + 1);
        rsaKeysMapper.insert(newKey);
    }

    // 删除已完成过渡期的旧密钥
    @Transactional
    public void deleteOldKeys() {
        QueryWrapper<RsaKeys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("in_transition_flag", 1);

        List<RsaKeys> transitionalKeys = rsaKeysMapper.selectList(queryWrapper);
        for (RsaKeys key : transitionalKeys) {
            rsaKeysMapper.deleteById(key);
        }
    }
}
