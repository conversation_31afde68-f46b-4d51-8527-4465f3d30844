package org.jeecg.modules.encription.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.encription.entity.RsaKeys;
import org.jeecg.modules.encription.service.IRsaKeysService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: rsa_keys
 * @Author: jeecg-boot
 * @Date:   2024-10-08
 * @Version: V1.0
 */
@Api(tags="rsa_keys")
@RestController
@RequestMapping("/encription/rsaKeys")
@Slf4j
public class RsaKeysController extends JeecgController<RsaKeys, IRsaKeysService> {
	@Autowired
	private IRsaKeysService rsaKeysService;

	 @GetMapping("/public")
	 public Result<?> getPublicKey() {
		 String publicKey = rsaKeysService.getActivePrivateKey();
		 return Result.OK(publicKey);
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param rsaKeys
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "rsa_keys-分页列表查询")
	@ApiOperation(value="rsa_keys-分页列表查询", notes="rsa_keys-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RsaKeys>> queryPageList(RsaKeys rsaKeys,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<RsaKeys> queryWrapper = QueryGenerator.initQueryWrapper(rsaKeys, req.getParameterMap());
		Page<RsaKeys> page = new Page<RsaKeys>(pageNo, pageSize);
		IPage<RsaKeys> pageList = rsaKeysService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rsaKeys
	 * @return
	 */
	@AutoLog(value = "rsa_keys-添加")
	@ApiOperation(value="rsa_keys-添加", notes="rsa_keys-添加")
	@RequiresPermissions("encription:rsa_keys:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RsaKeys rsaKeys) {
		rsaKeysService.save(rsaKeys);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param rsaKeys
	 * @return
	 */
	@AutoLog(value = "rsa_keys-编辑")
	@ApiOperation(value="rsa_keys-编辑", notes="rsa_keys-编辑")
	@RequiresPermissions("encription:rsa_keys:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RsaKeys rsaKeys) {
		rsaKeysService.updateById(rsaKeys);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "rsa_keys-通过id删除")
	@ApiOperation(value="rsa_keys-通过id删除", notes="rsa_keys-通过id删除")
	@RequiresPermissions("encription:rsa_keys:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rsaKeysService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "rsa_keys-批量删除")
	@ApiOperation(value="rsa_keys-批量删除", notes="rsa_keys-批量删除")
	@RequiresPermissions("encription:rsa_keys:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rsaKeysService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "rsa_keys-通过id查询")
	@ApiOperation(value="rsa_keys-通过id查询", notes="rsa_keys-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RsaKeys> queryById(@RequestParam(name="id",required=true) String id) {
		RsaKeys rsaKeys = rsaKeysService.getById(id);
		if(rsaKeys==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rsaKeys);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param rsaKeys
    */
    @RequiresPermissions("encription:rsa_keys:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RsaKeys rsaKeys) {
        return super.exportXls(request, rsaKeys, RsaKeys.class, "rsa_keys");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("encription:rsa_keys:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RsaKeys.class);
    }

}
