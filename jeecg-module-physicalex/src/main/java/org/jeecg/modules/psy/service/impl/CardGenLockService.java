package org.jeecg.modules.psy.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class CardGenLockService {

    private static final String LOCK_KEY = "gen_card_lock";
    private static final String LOCK_VALUE = "locked";

    private static final Long LOCK_EXPIRE_TIME = 300L;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public boolean tryLock() {
        Boolean result = stringRedisTemplate.opsForValue().setIfAbsent(LOCK_KEY, LOCK_VALUE, LOCK_EXPIRE_TIME, java.util.concurrent.TimeUnit.SECONDS);
        return null != result && result;
    }

    public void unlock() {
        stringRedisTemplate.delete(LOCK_KEY);
    }
}
