package org.jeecg.modules.psy.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jodd.util.StringUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.psy.entity.QuestSuit;
import org.jeecg.modules.psy.entity.SuitQuest;
import org.jeecg.modules.psy.service.IQuestSuitService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.psy.service.ISuitQuestService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: quest_suit
 * @Author: jeecg-boot
 * @Date:   2025-03-22
 * @Version: V1.0
 */
@Api(tags="quest_suit")
@RestController
@RequestMapping("/psy/questSuit")
@Slf4j
public class QuestSuitController extends JeecgController<QuestSuit, IQuestSuitService> {
	@Autowired
	private IQuestSuitService questSuitService;
	 @Autowired
	 private ISuitQuestService suitQuestService;
	
	/**
	 * 分页列表查询
	 *
	 * @param questSuit
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "quest_suit-分页列表查询")
	@ApiOperation(value="quest_suit-分页列表查询", notes="quest_suit-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<QuestSuit>> queryPageList(QuestSuit questSuit,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
//        QueryWrapper<QuestSuit> queryWrapper = QueryGenerator.initQueryWrapper(questSuit, req.getParameterMap());
		LambdaQueryWrapper<QuestSuit> queryWrapper=new LambdaQueryWrapper<>();
		String name = req.getParameter("name");
		String code = req.getParameter("code");
		String defaultFlag = req.getParameter("defaultFlag");
		if (StringUtil.isNotBlank(name)){
			queryWrapper.like(QuestSuit::getName,name);
		}
		if (StringUtil.isNotBlank(code)){
			queryWrapper.like(QuestSuit::getCode,code);
		}
		if (StringUtil.isNotBlank(defaultFlag)){
			queryWrapper.eq(QuestSuit::getDefaultFlag,defaultFlag);
		}

		Page<QuestSuit> page = new Page<QuestSuit>(pageNo, pageSize);
		IPage<QuestSuit> pageList = questSuitService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param questSuit
	 * @return
	 */
	@AutoLog(value = "quest_suit-添加")
	@ApiOperation(value="quest_suit-添加", notes="quest_suit-添加")
//	@RequiresPermissions("psy:quest_suit:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody QuestSuit questSuit) {
		questSuitService.save(questSuit);
		return Result.OK("添加成功！");
	}

	 /**
	  *   查询问卷
	  *
	  * @param
	  * @return
	  */
	 @ApiOperation(value="suit_quest-分页列表查询", notes="suit_quest-分页列表查询")
	 @GetMapping(value = "/getSuitQuestsBySuitId")
	 public Result<IPage<SuitQuest>> getSuitQuestsBySuitId(SuitQuest suitQuest,
												   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												   HttpServletRequest req) {
		 QueryWrapper<SuitQuest> queryWrapper = QueryGenerator.initQueryWrapper(suitQuest, req.getParameterMap());
		 Page<SuitQuest> page = new Page<SuitQuest>(pageNo, pageSize);
		 IPage<SuitQuest> pageList = suitQuestService.page(page, queryWrapper);
		 return Result.OK(pageList);
	 }
	 /**
	  *   添加问卷
	  *
	  * @param suitQuests
	  * @return
	  */
	 @AutoLog(value = "quest_suit-添加问卷")
	 @ApiOperation(value="quest_suit-添加问卷", notes="quest_suit-添加问卷")
//	@RequiresPermissions("psy:quest_suit:add")
	 @PostMapping(value = "/addQuest")
	 public Result<String> addQuest(@RequestBody List<SuitQuest> suitQuests) {
		 suitQuestService.saveBatch(suitQuests);
		 return Result.OK("添加成功！");
	 }

	 /**
	  *   删除问卷
	  *
	  * @param suitQuestId
	  * @return
	  */
	 @AutoLog(value = "quest_suit-删除问卷")
	 @ApiOperation(value="quest_suit-删除问卷", notes="quest_suit-删除问卷")
//	@RequiresPermissions("psy:quest_suit:add")
	 @DeleteMapping(value = "/deleteQuest")
	 public Result<String> deleteQuest(@RequestParam(name="suitQuestId",required=true) String suitQuestId) {
		 suitQuestService.removeById(suitQuestId);
		 return Result.OK("删除成功！");
	 }
	 /**
	  *   批量删除问卷
	  *
	  * @param suitQuestIds
	  * @return
	  */
	 @AutoLog(value = "quest_suit-删除问卷")
	 @ApiOperation(value="quest_suit-删除问卷", notes="quest_suit-删除问卷")
//	@RequiresPermissions("psy:quest_suit:add")
	 @DeleteMapping(value = "/deleteQuestBatch")
	 public Result<String> deleteQuestBatch(@RequestParam(name="suitQuestIds",required=true) String suitQuestIds) {
		 this.suitQuestService.removeByIds(Arrays.asList(suitQuestIds.split(",")));
		 return Result.OK("批量删除成功!");
	 }
	/**
	 *  编辑
	 *
	 * @param questSuit
	 * @return
	 */
	@AutoLog(value = "quest_suit-编辑")
	@ApiOperation(value="quest_suit-编辑", notes="quest_suit-编辑")
//	@RequiresPermissions("psy:quest_suit:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody QuestSuit questSuit) {
		questSuitService.updateById(questSuit);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "quest_suit-通过id删除")
	@ApiOperation(value="quest_suit-通过id删除", notes="quest_suit-通过id删除")
//	@RequiresPermissions("psy:quest_suit:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		questSuitService.removeById(id);
		//删除中间表
		suitQuestService.remove(new LambdaQueryWrapper<SuitQuest>().eq(SuitQuest::getSuitId,id));
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "quest_suit-批量删除")
	@ApiOperation(value="quest_suit-批量删除", notes="quest_suit-批量删除")
//	@RequiresPermissions("psy:quest_suit:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.questSuitService.removeByIds(Arrays.asList(ids.split(",")));
		//删除中间表
		suitQuestService.remove(new LambdaQueryWrapper<SuitQuest>().in(SuitQuest::getSuitId,Arrays.asList(ids.split(","))));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "quest_suit-通过id查询")
	@ApiOperation(value="quest_suit-通过id查询", notes="quest_suit-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<QuestSuit> queryById(@RequestParam(name="id",required=true) String id) {
		QuestSuit questSuit = questSuitService.getById(id);
		if(questSuit==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(questSuit);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param questSuit
    */
    @RequiresPermissions("psy:quest_suit:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, QuestSuit questSuit) {
        return super.exportXls(request, questSuit, QuestSuit.class, "quest_suit");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("psy:quest_suit:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, QuestSuit.class);
    }

}
