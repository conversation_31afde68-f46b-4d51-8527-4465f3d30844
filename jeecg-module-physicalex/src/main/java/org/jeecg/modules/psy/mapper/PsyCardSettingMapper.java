package org.jeecg.modules.psy.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.psy.entity.PsyCardSetting;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 卡片设置
 * @Author: jeecg-boot
 * @Date:   2023-12-22
 * @Version: V1.0
 */
public interface PsyCardSettingMapper extends BaseMapper<PsyCardSetting> {

    PsyCardSetting getCardSettingByTenantId(@Param("tenantId") String tenantId);

    PsyCardSetting getCardSettingByCardPrefix(@Param("cardPrefix") String cardPrefix);
}
