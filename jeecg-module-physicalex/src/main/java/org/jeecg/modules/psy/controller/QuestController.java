package org.jeecg.modules.psy.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;

import org.jeecg.modules.psy.entity.PsyCard;
import org.jeecg.modules.psy.service.IPsyCardService;
import org.jeecg.modules.psy.service.IQuestSuitService;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;

import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @Description: 问卷套餐
 * @Author: jeecg-boot
 * @Date: 2023-12-21
 * @Version: V1.0
 */
@Api(tags = "问卷套餐")
@RestController
@RequestMapping("/quest/h5")
@Slf4j
public class QuestController {
    @Autowired
    private IQuestSuitService questSuitService;
    @Autowired
    private IPsyCardService cardService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;



    /**
     * 分页列表查询
     * @return
     */
    //@AutoLog(value = "问卷套餐-分页列表查询")
    @ApiOperation(value = "问卷套餐-分页列表查询", notes = "问卷套餐-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(String cardId) {

        PsyCard card = cardService.getById(cardId);
        if (card == null) {
            return Result.error("卡号不存在");
        }
        if (StringUtils.equals(card.getStatus(), "禁用")) {
            return Result.error("账号已禁用!");
        }
        if (StringUtils.equals(card.getStatus(), "已过期")) {
            return Result.error("账号已过期!");
        }
//        String cardPrefix = StringUtils.substring(card.getAccount(), 0, 1);
//        PsyCardSetting cardSetting = cardSettingService.getByPrefix(cardPrefix);
//        if (cardSetting == null) {
//            return Result.error("测评卡配置异常！");
//        }
        CustomerReg reg = customerRegService.getOne(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getPsyCardId, cardId).last("limit 1"));
        if (Objects.isNull(reg)){
            return Result.error("未查询到登记信息!");
        }
        if (!StringUtils.equals(reg.getStatus(),"已登记")){
            return Result.error("账号未登记!");
        }
        LambdaQueryWrapper<CustomerRegItemGroup> queryWrapper = new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, reg.getId()).eq(CustomerRegItemGroup::getClassCode, "心理");
        queryWrapper.and(wq -> wq.eq(CustomerRegItemGroup::getPayStatus, "已支付")
                .or().eq(CustomerRegItemGroup::getPayerType, "单位支付"));
        List<CustomerRegItemGroup> regItemGroups = customerRegItemGroupService.list(queryWrapper);
        if (CollectionUtils.isEmpty(regItemGroups)){
            return Result.error("未查到已支付的心理测评项目");
        }
        List<String> suitCodes = regItemGroups.stream().map(CustomerRegItemGroup::getHisCode).toList();
        if (CollectionUtils.isEmpty(suitCodes)){
            return Result.error("测评套餐未配置编码");
        }
//        String questSuitId = cardSetting.getQuestSuitId();
//        QuestSuit questSuit =questSuitService.getById(questSuitId);
//        if (questSuit == null) {
//            return Result.error("测评卡配置异常,未配置测评套餐！");
//        }
        List<QuestionnaireDefinition> questList = questSuitService.getQuestListBySuitCodes(suitCodes,card.getAccount());

        return Result.OK(questList);
    }

}
