package org.jeecg.modules.psy.mapper;

import java.util.List;
import org.jeecg.modules.psy.entity.SuitQuest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: suit_quest
 * @Author: jeecg-boot
 * @Date:   2025-03-22
 * @Version: V1.0
 */
public interface SuitQuestMapper extends BaseMapper<SuitQuest> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<SuitQuest>
   */
	public List<SuitQuest> selectByMainId(@Param("mainId") String mainId);
}
