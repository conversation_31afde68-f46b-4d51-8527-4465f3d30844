package org.jeecg.modules.psy.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.psy.entity.QuestSuit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;

/**
 * @Description: quest_suit
 * @Author: jeecg-boot
 * @Date:   2025-03-22
 * @Version: V1.0
 */
public interface QuestSuitMapper extends BaseMapper<QuestSuit> {

    List<QuestionnaireDefinition> getQuestListBySuitId(@Param("suitId") String suitId);

    List<QuestionnaireDefinition> getQuestListBySuitCode(@Param("suitCode") String suitCode);

    List<QuestionnaireDefinition> getQuestListBySuitCodes(@Param("suitCodes") List<String> suitCodes);


}
