package org.jeecg.modules.psy.service;

import org.jeecg.modules.psy.entity.SuitQuest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: suit_quest
 * @Author: jeecg-boot
 * @Date:   2025-03-22
 * @Version: V1.0
 */
public interface ISuitQuestService extends IService<SuitQuest> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SuitQuest>
	 */
	public List<SuitQuest> selectByMainId(String mainId);
}
