package org.jeecg.modules.psy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.psy.entity.PsyCard;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * @Description: 心理测评卡
 * @Author: jeecg-boot
 * @Date: 2023-12-21
 * @Version: V1.0
 */
public interface IPsyCardService extends IService<PsyCard> {

    PsyCard getCardByAccount(String account) throws Exception;

    List<PsyCard> batchAddCard(Integer count, String tenantId) throws Exception;
    PsyCard generateAddCard(Integer count) throws Exception;

    ModelAndView export(List<PsyCard> psyCardList);

    void active(PsyCard psyCard);
}
