<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.psy.mapper.PsyCardSettingMapper">

    <select id="getCardSettingByTenantId" resultType="org.jeecg.modules.psy.entity.PsyCardSetting">
        SELECT * FROM psy_card_setting  <where> <if test="tenantId!=null and tenantId!=''"> tenant_id = #{tenantId}</if>  and default_flag = '1' and del_flag = '0' limit 1</where>
    </select>

    <select id="getCardSettingByCardPrefix" resultType="org.jeecg.modules.psy.entity.PsyCardSetting"
            parameterType="java.lang.String">
        SELECT * FROM psy_card_setting  WHERE card_prefix = #{cardPrefix} and del_flag = '0' limit 1
    </select>
</mapper>