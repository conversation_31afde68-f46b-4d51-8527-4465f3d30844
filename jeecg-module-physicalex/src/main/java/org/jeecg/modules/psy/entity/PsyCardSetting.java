package org.jeecg.modules.psy.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 卡片设置
 * @Author: jeecg-boot
 * @Date:   2023-12-22
 * @Version: V1.0
 */
@Data
@TableName("psy_card_setting")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="card_setting对象", description="卡片设置")
public class PsyCardSetting implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;
	/**测评套餐*/
	@Excel(name = "测评套餐", width = 15, dictTable = "quest_suit", dicText = "name", dicCode = "id")
	@Dict(dictTable = "quest_suit", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "测评套餐")
    private String questSuitId;
	/**卡号前缀*/
	@Excel(name = "卡号前缀", width = 15)
    @ApiModelProperty(value = "卡号前缀")
    private String cardPrefix;
	/**卡片标题*/
	@Excel(name = "卡片标题", width = 15)
    @ApiModelProperty(value = "卡片标题")
    private String title;
	/**描述*/
	@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String intro;
	/**卡片图标*/
	@Excel(name = "卡片图标", width = 15)
    @ApiModelProperty(value = "卡片图标")
    private String icon;
	/**是否默认*/
	@Excel(name = "是否默认", width = 15)
    @ApiModelProperty(value = "是否默认")
    private String defaultFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
	/**删除标志*/
	@Excel(name = "删除标志", width = 15)
    @ApiModelProperty(value = "删除标志")
    @TableLogic
    private String delFlag;
    /**有效期（月）*/
    @Excel(name = "有效期（月）", width = 15)
    @ApiModelProperty(value = "有效期（月）")
    private Integer durationMonths;
}
