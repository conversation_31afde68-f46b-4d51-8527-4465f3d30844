package org.jeecg.modules.psy.service.impl;

import org.jeecg.modules.psy.entity.SuitQuest;
import org.jeecg.modules.psy.mapper.SuitQuestMapper;
import org.jeecg.modules.psy.service.ISuitQuestService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: suit_quest
 * @Author: jeecg-boot
 * @Date:   2025-03-22
 * @Version: V1.0
 */
@Service
public class SuitQuestServiceImpl extends ServiceImpl<SuitQuestMapper, SuitQuest> implements ISuitQuestService {
	
	@Autowired
	private SuitQuestMapper suitQuestMapper;
	
	@Override
	public List<SuitQuest> selectByMainId(String mainId) {
		return suitQuestMapper.selectByMainId(mainId);
	}
}
