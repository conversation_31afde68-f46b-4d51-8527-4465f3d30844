package org.jeecg.modules.psy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.psy.entity.PsyCardSetting;
import org.jeecg.modules.psy.mapper.PsyCardSettingMapper;
import org.jeecg.modules.psy.service.IPsyCardSettingService;
import org.springframework.stereotype.Service;

/**
 * @Description: 卡片设置
 * @Author: jeecg-boot
 * @Date: 2023-12-22
 * @Version: V1.0
 */
@Service
public class PsyPsyCardSettingServiceImpl extends ServiceImpl<PsyCardSettingMapper, PsyCardSetting> implements IPsyCardSettingService {

    @Override
    public PsyCardSetting getByPrefix(String prefix) {
        QueryWrapper<PsyCardSetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("card_prefix", prefix);
        return getOne(queryWrapper);
    }
}
