package org.jeecg.modules.psy.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.psy.entity.PsyCard;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.psy.entity.PsyCustomer;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.util.List;

/**
 * @Description: 心理测评卡
 * @Author: jeecg-boot
 * @Date:   2023-12-21
 * @Version: V1.0
 */
public interface PsyCardMapper extends BaseMapper<PsyCard> {

    PsyCard getPsyCardByRegId(@Param("regId") String regId);

    Page<PsyCustomer> pageCustomerReg4Psy(Page<PsyCustomer> page, @Param("name")String name,@Param("examNo")String examNo,@Param("phone")String phone,@Param("gender")String gender,@Param("account")String account,@Param("cardNo")String cardNo,@Param("regTimeStart")String regTimeStart,@Param("regTimeEnd")String regTimeEnd);

}
