package org.jeecg.modules.psy.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.psy.entity.PsyCard;
import org.jeecg.modules.psy.service.IPsyCardService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 心理测评卡
 * @Author: jeecg-boot
 * @Date: 2023-12-21
 * @Version: V1.0
 */
@Api(tags = "心理测评卡")
@RestController
@RequestMapping("/psytest/card/auth")
@Slf4j
public class CardAuthController extends JeecgController<PsyCard, IPsyCardService> {
    @Autowired
    private IPsyCardService cardService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    /**
     * 核验卡号
     *
     * @return
     */
    @AutoLog(value = "心理测评卡-验证卡号")
    @ApiOperation(value = "心理测评卡-验证卡号", notes = "心理测评卡-验证卡号")
    @GetMapping(value = "/login-bycard")
    public Result<?> loginBycard(String account, String pwd) {
        try {
            PsyCard psyCard = cardService.getCardByAccount(account);
            if (psyCard == null) {
                return Result.error("卡号不存在！");
            }
            if (!psyCard.getPwd().equals(pwd)) {
                return Result.error("密码错误！");
            }
            if (StringUtils.equals(psyCard.getStatus(), "已过期")) {
                return Result.error("卡号已过期！");
            }
            if (StringUtils.equals(psyCard.getStatus(), "已禁用")) {
                return Result.error("卡号已禁用！");
            }
            CustomerReg reg = customerRegService.getOne(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getPsyCardId, psyCard.getId()));
            if (!StringUtils.equals(reg.getStatus(),"已登记")){
                return Result.error("账号未登记！");
            }
            List<CustomerRegItemGroup> groups = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, reg.getId()).eq(CustomerRegItemGroup::getClassCode, "心理"));
            boolean payFlag = groups.stream().allMatch(g -> StringUtils.equals(g.getPayStatus(), "已支付") || StringUtils.equals(g.getPayerType(), "单位支付"));
            if (!payFlag){
                return Result.error("心理测评未支付，请在支付后操作！");
            }
            return Result.OK(psyCard);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

}
