<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.psy.mapper.PsyCardMapper">

    <select id="getPsyCardByRegId" resultType="org.jeecg.modules.psy.entity.PsyCard">
        select pc.* from customer_reg r join psy_card pc on r.psy_card_id=pc.id where r.id=#{regId}
    </select>
    <select id="pageCustomerReg4Psy" resultType="org.jeecg.modules.psy.entity.PsyCustomer">
        select r.name,
               r.exam_no,
               r.id,
               c.card_no,
               c.account,
               c.pwd,
               r.gender,
               r.phone,
               c.id as cardId
        from customer_reg r join psy_card c on r.psy_card_id=c.id
         where r.del_flag=0
         <if test="name!=null and name!=''">
           and r.name like concat('%',#{name},'%')
         </if>
         <if test="examNo!=null and examNo!=''">
           and r.exam_no like concat('%',#{examNo},'%')
         </if>
        <if test="cardNo!=null and cardNo!=''">
           and c.card_no like concat('%',#{cardNo},'%')
           </if>
        <if test="account!=null and account!=''">
           and c.account like concat('%',#{account},'%')
           </if>
        <if test="phone!=null and phone!=''">
           and r.phone like concat('%',#{phone},'%')
           </if>
        <if test="gender!=null and gender!=''">
           and r.gender=#{gender}
           </if>

        <if test="regTimeStart!=null and regTimeStart!=''">
           and r.reg_time <![CDATA[>=]]> #{regTimeStart}
           </if><if test="regTimeEnd!=null and regTimeEnd!=''">
           and r.reg_time <![CDATA[<=]]> #{regTimeEnd}
        </if>
        order by r.exam_no desc

    </select>
</mapper>