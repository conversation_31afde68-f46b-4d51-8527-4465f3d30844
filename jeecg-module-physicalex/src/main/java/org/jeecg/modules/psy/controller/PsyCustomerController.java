package org.jeecg.modules.psy.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.groovy.util.Maps;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.psy.entity.PsyCustomer;
import org.jeecg.modules.psy.mapper.PsyCardMapper;
import org.jeecg.modules.psy.service.IPsyCardService;
import org.jeecg.modules.quest.entity.PersonalQuestionnaire;
import org.jeecg.modules.quest.entity.PersonalQuestionnaireResults;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;
import org.jeecg.modules.quest.entity.ReportData;
import org.jeecg.modules.quest.mapper.PersonalQuestionnaireResultsMapper;
import org.jeecg.modules.quest.service.IPersonalQuestionnaireResultsService;
import org.jeecg.modules.quest.service.IPersonalQuestionnaireService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.summary.entity.AdviceBean;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: 顾客
 * @Author: jeecg-boot
 * @Date: 2023-12-21
 * @Version: V1.0
 */
@Api(tags = "顾客")
@RestController
@RequestMapping("/psytest/customer")
@Slf4j
public class PsyCustomerController extends JeecgController<CustomerReg, ICustomerRegService> {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private PsyCardMapper psyCardMapper;
    @Autowired
    private IPersonalQuestionnaireResultsService personalQuestionnaireResultsService;
    @Autowired
    private PersonalQuestionnaireResultsMapper personalQuestionnaireResultsMapper;
    @Autowired
    private IPersonalQuestionnaireService personalQuestionnaireService;
    @Autowired
    private ICustomerRegItemResultService customerRegItemResultService;


    @GetMapping(value = "/getByCardId")
    public Result<?> getByCardId(String cardId) {
        CustomerReg reg = customerRegService.getOne(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getPsyCardId, cardId).last("limit 1"));
        PsyCustomer psyCustomer = new PsyCustomer();
        BeanUtils.copyProperties(reg,psyCustomer);
        psyCustomer.setBirthDate(reg.getBirthday());
        psyCustomer.setCardId(reg.getPsyCardId());
        return Result.OK(psyCustomer);
    }


    @PostMapping(value = "/bind")
    public Result<?> bind(@RequestBody PsyCustomer psyCustomer) {
//        customerService.saveOrUpdate(psyCustomer);
        jdbcTemplate.update("update psy_card set customer_status = ? where id = ?", "binded", psyCustomer.getCardId());
        return Result.OK("添加成功！", psyCustomer.getId());
    }

    @GetMapping(value = "/refuse")
    public Result<?> refuse(String cardId) {
        jdbcTemplate.update("update psy_card set customer_status = ? where id = ?", "refused", cardId);
        return Result.OK("操作成功！");
    }


    /**
     * 分页列表查询
     *
     * @param
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "顾客-分页列表查询")
    @ApiOperation(value = "顾客-分页列表查询", notes = "顾客-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<PsyCustomer>> queryPageList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {
        String name = req.getParameter("name");
        String examNo = req.getParameter("examNo");
        String account = req.getParameter("account");
        String cardNo = req.getParameter("cardNo");
        String phone = req.getParameter("phone");
        String gender = req.getParameter("gender");
        String regTimeEnd = req.getParameter("regTimeEnd");
        String regTimeStart = req.getParameter("regTimeStart");
        Page<PsyCustomer> page = new Page<PsyCustomer>(pageNo, pageSize);
        IPage<PsyCustomer> pageList = psyCardMapper.pageCustomerReg4Psy(page,name,examNo,phone,gender,account,cardNo,regTimeStart,regTimeEnd);
        return Result.OK(pageList);
    }
    @AutoLog(value = "获取心理测评报告")
    @ApiOperation(value = "获取心理测评报告", notes = "获取心理测评报告")
    @GetMapping(value = "/getPsyReportData")
    public Result<?> getPsyReportData(@RequestParam(name = "personalQuestionnaireId", required = true) String personalQuestionnaireId) {
        try {
            ReportData psyReportData = personalQuestionnaireResultsService.getPsyReportData(personalQuestionnaireId);
            return Result.OK(psyReportData);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            return Result.error("查询失败",e.getMessage());
        }
    }
    @AutoLog(value = "获取答题列表")
    @ApiOperation(value = "获取答题列表", notes = "获取答题列表")
    @GetMapping(value = "/getPersonalQuests")
    public Result<?> getPersonalQuests(@RequestParam(name = "customerRegId", required = true) String customerRegId) {
        try {
            List<PersonalQuestionnaire> list = personalQuestionnaireService.list(new LambdaQueryWrapper<PersonalQuestionnaire>().eq(PersonalQuestionnaire::getPersonalId, customerRegId));
            return Result.OK(list);
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.error("查询失败",e.getMessage());
        }
    }
    @AutoLog(value = "心理测评-获取问卷结果")
    @ApiOperation(value = "心理测评-获取问卷结果", notes = "心理测评-获取问卷结果")
    @GetMapping(value = "/getPersonalQuestsResults")
    public Result<?> getPersonalQuestsResults(@RequestParam(name = "personalQuestId", required = true) String personalQuestId) {
        try {
            List<PersonalQuestionnaireResults> list = personalQuestionnaireResultsService.list(new LambdaQueryWrapper<PersonalQuestionnaireResults>().eq(PersonalQuestionnaireResults::getPersonalQuestionnaireId, personalQuestId));
            CustomerRegItemResult personalQuestRets = personalQuestionnaireResultsMapper.getQuestResultByPersonalQuestId(personalQuestId);
            Map<String, Object> map = Maps.of("questResult", list, "finalResult", personalQuestRets);
            return Result.OK(map);
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.error("查询失败",e.getMessage());
        }
    }
    @AutoLog(value = "心理测评-保存最终问卷结果")
    @ApiOperation(value = "心理测评-保存最终问卷结果", notes = "心理测评-保存最终问卷结果")
    @PostMapping(value = "/savePersonalQuestsResults")
    public Result<?> savePersonalQuestsResults(@RequestBody CustomerRegItemResult result) {
        try {
            customerRegItemResultService.saveOrUpdate(result);
            personalQuestionnaireService.update(null,new LambdaUpdateWrapper<PersonalQuestionnaire>().set(PersonalQuestionnaire::getAdviseText,result.getPsyAdviceText()).set(PersonalQuestionnaire::getRefRange,result.getPsyRefRange()).eq(PersonalQuestionnaire::getId,result.getPersonalQuestId()));
            return Result.OK("保存成功");
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.error("保存失败",e.getMessage());
        }
    }

    //@AutoLog(value = "总检-分页列表查询")
    @ApiOperation(value = "心理测评-根据文本获取心理测评最终结论与建议", notes = "心理测评-根据文本获取心理测评最终结论与建议")
    @PostMapping(value = "/getPsySummaryAdviceByText")
    public Result<?> getSummaryAdviceByText(@RequestBody JSONObject jsonObject) {
        String personalQuestId = jsonObject.getString("personalQuestId");
        JSONArray questResults = jsonObject.getJSONArray("questResults");
        List<PersonalQuestionnaireResults> questionnaireResults = JSONArray.parseArray(JSONArray.toJSONString(questResults), PersonalQuestionnaireResults.class);
        QuestionnaireDefinition definition = personalQuestionnaireResultsMapper.getQuesDefinitionByPersonalQuestId(personalQuestId);
        AdviceBean adviceList = null;
        try {
            adviceList = personalQuestionnaireService.generatePsyFinalResultAndAdvice(questionnaireResults,definition);
            return Result.OK("刷新成功！", adviceList);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    /**
     * 添加
     *
     * @param psyCustomer
     * @return
     *//**//*
    @AutoLog(value = "顾客-添加")
    @ApiOperation(value = "顾客-添加", notes = "顾客-添加")
    @RequiresPermissions("psytest:customer:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody PsyCustomer psyCustomer) {
        customerService.save(psyCustomer);
        return Result.OK("添加成功！");
    }


    *//**//**
     * 编辑
     *
     * @param psyCustomer
     * @return
     *//**//*
    @AutoLog(value = "顾客-编辑")
    @ApiOperation(value = "顾客-编辑", notes = "顾客-编辑")
    @RequiresPermissions("psytest:customer:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody PsyCustomer psyCustomer) {
        customerService.updateById(psyCustomer);
        return Result.OK("编辑成功!");
    }

    *//**//**
     * 通过id删除
     *
     * @param id
     * @return
     *//**//*
    @AutoLog(value = "顾客-通过id删除")
    @ApiOperation(value = "顾客-通过id删除", notes = "顾客-通过id删除")
    @RequiresPermissions("psytest:customer:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerService.removeById(id);
        return Result.OK("删除成功!");
    }

    *//**//**
     * 批量删除
     *
     * @param ids
     * @return
     *//**//*
    @AutoLog(value = "顾客-批量删除")
    @ApiOperation(value = "顾客-批量删除", notes = "顾客-批量删除")
    @RequiresPermissions("psytest:customer:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    *//**//**
     * 通过id查询
     *
     * @param id
     * @return
     *//**//*
    //@AutoLog(value = "顾客-通过id查询")
    @ApiOperation(value = "顾客-通过id查询", notes = "顾客-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<PsyCustomer> queryById(@RequestParam(name = "id", required = true) String id) {
        PsyCustomer psyCustomer = customerService.getById(id);
        if (psyCustomer == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(psyCustomer);
    }
*//*
    *//**
     * 导出excel
     *
     * @param request
     * @param psyCustomer
     *//*
    @RequiresPermissions("psytest:customer:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PsyCustomer psyCustomer) {
        return super.exportXls(request, psyCustomer, PsyCustomer.class, "顾客");
    }

    *//**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     *//*
    @RequiresPermissions("psytest:customer:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PsyCustomer.class);
    }
*/
}
