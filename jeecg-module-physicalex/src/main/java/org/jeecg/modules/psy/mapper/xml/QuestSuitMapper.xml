<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.psy.mapper.QuestSuitMapper">
    <select id="getQuestListBySuitId" resultType="org.jeecg.modules.quest.entity.QuestionnaireDefinition"
            parameterType="java.lang.String">
        select q.id,q.name,q.pic,q.needs_minutes from quest_suit suit join suit_quest sq on suit.id = sq.suit_id join questionnaire_definition q on sq.quest_id = q.id where suit.id=#{suitId}
    </select>
    <select id="getQuestListBySuitCodes" resultType="org.jeecg.modules.quest.entity.QuestionnaireDefinition">
        select q.id,q.name,q.pic,q.needs_minutes,suit.code as suitCode,suit.id as suitId
        from quest_suit suit join suit_quest sq on suit.id = sq.suit_id
            join questionnaire_definition q on sq.quest_id = q.id
        where
        suit.code in
        <foreach collection="suitCodes" item="suitCode" open="(" separator="," close=")">
            #{suitCode}
        </foreach>

    </select>
    <select id="getQuestListBySuitCode" resultType="org.jeecg.modules.quest.entity.QuestionnaireDefinition">
        select q.id,q.name,q.pic,q.needs_minutes from quest_suit suit join suit_quest sq on suit.id = sq.suit_id join questionnaire_definition q on sq.quest_id = q.id where suit.code=#{suitCode}

    </select>

</mapper>