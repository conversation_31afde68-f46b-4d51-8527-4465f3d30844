package org.jeecg.modules.psy.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.config.TenantContext;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.psy.entity.PsyCard;
import org.jeecg.modules.psy.service.IPsyCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 心理测评卡
 * @Author: jeecg-boot
 * @Date: 2023-12-21
 * @Version: V1.0
 */
@Api(tags = "心理测评卡")
@RestController
@RequestMapping("/psy/psyCard")
@Slf4j
public class PsyCardController extends JeecgController<PsyCard, IPsyCardService> {
    @Autowired
    private IPsyCardService cardService;

    /**
     * 分页列表查询
     *
     * @param psyCard
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "心理测评卡-分页列表查询")
    @ApiOperation(value = "心理测评卡-分页列表查询", notes = "心理测评卡-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<PsyCard>> queryPageList(PsyCard psyCard,
                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                HttpServletRequest req) {
//        QueryWrapper<PsyCard> queryWrapper = QueryGenerator.initQueryWrapper(psyCard, req.getParameterMap());
        LambdaQueryWrapper<PsyCard> queryWrapper = new LambdaQueryWrapper<>();
        String cardNo = req.getParameter("cardNo");
        String account = req.getParameter("account");
        if (StringUtils.isNotBlank(cardNo)){
            queryWrapper.like(PsyCard::getCardNo,cardNo);
        }
        if (StringUtils.isNotBlank(account)){
            queryWrapper.like(PsyCard::getAccount,account);
        }
        Page<PsyCard> page = new Page<PsyCard>(pageNo, pageSize);
        IPage<PsyCard> pageList = cardService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param psyCard
     * @return
     */
    @AutoLog(value = "心理测评卡-添加")
    @ApiOperation(value = "心理测评卡-添加", notes = "心理测评卡-添加")
    @RequiresPermissions("psytest:card:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody PsyCard psyCard) {
        cardService.save(psyCard);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param psyCard
     * @return
     */
    @AutoLog(value = "心理测评卡-编辑")
    @ApiOperation(value = "心理测评卡-编辑", notes = "心理测评卡-编辑")
    @RequiresPermissions("psytest:card:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody PsyCard psyCard) {
        cardService.updateById(psyCard);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "心理测评卡-通过id删除")
    @ApiOperation(value = "心理测评卡-通过id删除", notes = "心理测评卡-通过id删除")
    @RequiresPermissions("psytest:card:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        cardService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "心理测评卡-批量删除")
    @ApiOperation(value = "心理测评卡-批量删除", notes = "心理测评卡-批量删除")
    @RequiresPermissions("psytest:card:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.cardService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "心理测评卡-通过id查询")
    @ApiOperation(value = "心理测评卡-通过id查询", notes = "心理测评卡-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<PsyCard> queryById(@RequestParam(name = "id", required = true) String id) {
        PsyCard psyCard = cardService.getById(id);
        if (psyCard == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(psyCard);
    }

    @RequiresPermissions("psytest:card:batchAdd")
    @RequestMapping(value = "/batchAdd")
    public Result<?> batchAdd(Integer count) {
        try {
            cardService.batchAddCard(count, TenantContext.getTenant());
            return Result.OK("制卡成功！");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    /**
     * 导出excel
     *
     * @param request
     * @param psyCard
     */
    @RequiresPermissions("psytest:card:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PsyCard psyCard) {
        ModelAndView modelAndView = super.exportXls(request, psyCard, PsyCard.class, "心理测评卡");
        String updateStatus = request.getParameter("updateStatus");
        if (StringUtils.equals(updateStatus, "待激活")) {
            UpdateWrapper<PsyCard> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("status", "待激活");
            updateWrapper.eq("status", "待制卡");
            cardService.update(updateWrapper);
        }

        return modelAndView;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("psytest:card:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PsyCard.class);
    }

}
