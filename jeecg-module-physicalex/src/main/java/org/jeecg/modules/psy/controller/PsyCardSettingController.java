package org.jeecg.modules.psy.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.psy.entity.PsyCardSetting;
import org.jeecg.modules.psy.service.IPsyCardSettingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 卡片设置
 * @Author: jeecg-boot
 * @Date:   2023-12-22
 * @Version: V1.0
 */
@Api(tags="卡片设置")
@RestController
@RequestMapping("/psy/cardSetting")
@Slf4j
public class PsyCardSettingController extends JeecgController<PsyCardSetting, IPsyCardSettingService> {
	@Autowired
	private IPsyCardSettingService cardSettingService;
	
	/**
	 * 分页列表查询
	 *
	 * @param psyCardSetting
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "卡片设置-分页列表查询")
	@ApiOperation(value="卡片设置-分页列表查询", notes="卡片设置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PsyCardSetting>> queryPageList(PsyCardSetting psyCardSetting,
													   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													   HttpServletRequest req) {
		QueryWrapper<PsyCardSetting> queryWrapper = QueryGenerator.initQueryWrapper(psyCardSetting, req.getParameterMap());
		Page<PsyCardSetting> page = new Page<PsyCardSetting>(pageNo, pageSize);
		IPage<PsyCardSetting> pageList = cardSettingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param psyCardSetting
	 * @return
	 */
	@AutoLog(value = "卡片设置-添加")
	@ApiOperation(value="卡片设置-添加", notes="卡片设置-添加")
//	@RequiresPermissions("psytest:card_setting:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PsyCardSetting psyCardSetting) {
		if(StringUtils.equals(psyCardSetting.getDefaultFlag(), "1")) {
			cardSettingService.update(new PsyCardSetting(), new UpdateWrapper<PsyCardSetting>().lambda().eq(PsyCardSetting::getDefaultFlag, "1").eq(PsyCardSetting::getTenantId, psyCardSetting.getTenantId()).set(PsyCardSetting::getDefaultFlag, "0"));
		}
		cardSettingService.save(psyCardSetting);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param psyCardSetting
	 * @return
	 */
	@AutoLog(value = "卡片设置-编辑")
	@ApiOperation(value="卡片设置-编辑", notes="卡片设置-编辑")
//	@RequiresPermissions("psytest:card_setting:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PsyCardSetting psyCardSetting) {
		if(StringUtils.equals(psyCardSetting.getDefaultFlag(), "1")) {
			cardSettingService.update(new PsyCardSetting(), new UpdateWrapper<PsyCardSetting>().lambda().eq(PsyCardSetting::getDefaultFlag, "1").eq(PsyCardSetting::getTenantId, psyCardSetting.getTenantId()).set(PsyCardSetting::getDefaultFlag, "0"));
		}
		cardSettingService.updateById(psyCardSetting);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "卡片设置-通过id删除")
	@ApiOperation(value="卡片设置-通过id删除", notes="卡片设置-通过id删除")
//	@RequiresPermissions("psytest:card_setting:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cardSettingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "卡片设置-批量删除")
	@ApiOperation(value="卡片设置-批量删除", notes="卡片设置-批量删除")
	@RequiresPermissions("psytest:card_setting:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cardSettingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "卡片设置-通过id查询")
	@ApiOperation(value="卡片设置-通过id查询", notes="卡片设置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PsyCardSetting> queryById(@RequestParam(name="id",required=true) String id) {
		PsyCardSetting psyCardSetting = cardSettingService.getById(id);
		if(psyCardSetting ==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(psyCardSetting);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param psyCardSetting
    */
    @RequiresPermissions("psytest:card_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PsyCardSetting psyCardSetting) {
        return super.exportXls(request, psyCardSetting, PsyCardSetting.class, "卡片设置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("psytest:card_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PsyCardSetting.class);
    }

}
