package org.jeecg.modules.psy.service;

import org.jeecg.modules.psy.entity.QuestSuit;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;

import java.util.List;

/**
 * @Description: quest_suit
 * @Author: jeecg-boot
 * @Date:   2025-03-22
 * @Version: V1.0
 */
public interface IQuestSuitService extends IService<QuestSuit> {

    List<QuestionnaireDefinition> getQuestList(String id, String account);

    List<QuestionnaireDefinition> getQuestListBySuitCodes(List<String> suitCodes, String account);

}
