package org.jeecg.modules.psy.service.impl;

import org.jeecg.modules.psy.entity.QuestSuit;
import org.jeecg.modules.psy.mapper.QuestSuitMapper;
import org.jeecg.modules.psy.service.IQuestSuitService;
import org.jeecg.modules.quest.entity.PersonalQuestionnaire;
import org.jeecg.modules.quest.entity.QuestionnaireDefinition;
import org.jeecg.modules.quest.service.IPersonalQuestionnaireService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: quest_suit
 * @Author: jeecg-boot
 * @Date:   2025-03-22
 * @Version: V1.0
 */
@Service
public class QuestSuitServiceImpl extends ServiceImpl<QuestSuitMapper, QuestSuit> implements IQuestSuitService {
    @Autowired
    private QuestSuitMapper questSuitMapper;
    @Autowired
    private IPersonalQuestionnaireService personalQuestionnaireService;
    @Override
    public List<QuestionnaireDefinition> getQuestList(String suitId, String account) {
        List<QuestionnaireDefinition> questList = questSuitMapper.getQuestListBySuitId(suitId);
        questList.forEach(quest -> {
            PersonalQuestionnaire personalQuestionnaire = personalQuestionnaireService.queryByQuestAndCard(quest.getId(), account);
            quest.setPersonalQuestionnaire(personalQuestionnaire);
        });

        return questList;
    }

    @Override
    public List<QuestionnaireDefinition> getQuestListBySuitCodes(List<String> suitCodes, String account) {
        List<QuestionnaireDefinition> questList = questSuitMapper.getQuestListBySuitCodes(suitCodes);
        questList.forEach(quest -> {
            PersonalQuestionnaire personalQuestionnaire = personalQuestionnaireService.queryByQuestAndCard(quest.getId(), account);
            quest.setPersonalQuestionnaire(personalQuestionnaire);
        });

        return questList;
    }
}
