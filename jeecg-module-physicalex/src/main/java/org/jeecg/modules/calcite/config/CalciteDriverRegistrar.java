package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.sql.Driver;
import java.sql.DriverManager;
import java.util.Enumeration;

/**
 * Calcite JDBC驱动注册器
 * 在应用启动时确保Calcite驱动已正确注册
 * 专门解决Tomcat WAR包部署时的驱动注册问题
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
//@Component
//@ConditionalOnProperty(name = "calcite.enabled", havingValue = "true", matchIfMissing = false)
//@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class CalciteDriverRegistrar implements ApplicationListener<ContextRefreshedEvent> {
    
    private static boolean driverRegistered = false;
    
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 只在根应用上下文刷新时执行一次
        if (event.getApplicationContext().getParent() == null && !driverRegistered) {
            registerCalciteDriver();
            driverRegistered = true;
        }
    }
    
    /**
     * 注册Calcite JDBC驱动
     */
    private void registerCalciteDriver() {
        try {
            log.info("Starting Calcite JDBC driver registration for Tomcat deployment...");
            
            // 记录注册前的驱动状态
            logRegisteredDrivers("Before registration");
            
            // 方法1: 使用Class.forName强制加载驱动类
            try {
                Class.forName("org.apache.calcite.jdbc.Driver", true, 
                             Thread.currentThread().getContextClassLoader());
                log.info("Calcite driver class loaded successfully");
            } catch (ClassNotFoundException e) {
                log.warn("Failed to load Calcite driver class using current context classloader", e);
                
                // 尝试使用系统类加载器
                try {
                    Class.forName("org.apache.calcite.jdbc.Driver", true, 
                                 ClassLoader.getSystemClassLoader());
                    log.info("Calcite driver class loaded using system classloader");
                } catch (ClassNotFoundException e2) {
                    log.error("Failed to load Calcite driver class", e2);
                    throw e2;
                }
            }
            
            // 方法2: 手动创建并注册驱动实例
            try {
                org.apache.calcite.jdbc.Driver calciteDriver = new org.apache.calcite.jdbc.Driver();
                DriverManager.registerDriver(calciteDriver);
                log.info("Calcite driver instance registered successfully");
            } catch (Exception e) {
                log.error("Failed to register Calcite driver instance", e);
                throw e;
            }
            
            // 方法3: 确保MySQL驱动也已注册
            try {
                Class.forName("com.mysql.cj.jdbc.Driver", true, 
                             Thread.currentThread().getContextClassLoader());
                log.info("MySQL driver verified");
            } catch (ClassNotFoundException e) {
                log.warn("MySQL driver not found, but this may not be critical", e);
            }
            
            // 记录注册后的驱动状态
            logRegisteredDrivers("After registration");
            
            // 验证Calcite驱动是否可用
            verifyCalciteDriver();
            
            log.info("Calcite JDBC driver registration completed successfully");
            
        } catch (Exception e) {
            log.error("Failed to register Calcite JDBC driver", e);
            // 不抛出异常，让应用继续启动，但记录错误
        }
    }
    
    /**
     * 验证Calcite驱动是否可用
     */
    private void verifyCalciteDriver() {
        try {
            Driver calciteDriver = DriverManager.getDriver("jdbc:calcite:");
            if (calciteDriver != null) {
                log.info("Calcite driver verification successful: {}", calciteDriver.getClass().getName());
            } else {
                log.warn("Calcite driver verification failed: driver not found");
            }
        } catch (Exception e) {
            log.warn("Calcite driver verification failed", e);
        }
    }
    
    /**
     * 记录当前已注册的JDBC驱动
     */
    private void logRegisteredDrivers(String phase) {
        try {
            log.info("JDBC drivers registered ({}): ", phase);
            Enumeration<Driver> drivers = DriverManager.getDrivers();
            int count = 0;
            while (drivers.hasMoreElements()) {
                Driver driver = drivers.nextElement();
                log.info("  [{}] {}", ++count, driver.getClass().getName());
            }
            if (count == 0) {
                log.warn("No JDBC drivers are currently registered!");
            }
        } catch (Exception e) {
            log.warn("Failed to list registered drivers during {}", phase, e);
        }
    }
}
