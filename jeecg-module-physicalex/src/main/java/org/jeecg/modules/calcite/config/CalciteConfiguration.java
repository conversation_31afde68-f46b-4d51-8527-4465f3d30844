package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.adapter.jdbc.JdbcSchema;
import org.apache.calcite.avatica.remote.Driver;
import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.schema.Schema;
import org.apache.calcite.schema.SchemaPlus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

/**
 * Apache Calcite配置类
 * 针对亿级数据和2GB内存限制优化
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@Slf4j
public class CalciteConfiguration {
    
    @Value("${spring.datasource.dynamic.datasource.master.url}")
    private String mysqlUrl;
    
    @Value("${spring.datasource.dynamic.datasource.master.username}")
    private String mysqlUsername;
    
    @Value("${spring.datasource.dynamic.datasource.master.password}")
    private String mysqlPassword;
    
    @Bean
    @Primary
    public CalciteConnection calciteConnection() throws SQLException {
        // 手动注册Calcite JDBC驱动 - 解决Tomcat部署问题
        registerCalciteDriver();

        Properties info = new Properties();
        
        // 基础配置
        info.setProperty("lex", "MYSQL");
        info.setProperty("conformance", "MYSQL_5");
        info.setProperty("caseSensitive", "false");

        // 字符集配置 - 支持中文
        info.setProperty("charset", "UTF-8");
        info.setProperty("characterEncoding", "UTF-8");
        info.setProperty("useUnicode", "true");
        info.setProperty("connectionCollation", "utf8mb4_unicode_ci");

        // 内存优化配置
        info.setProperty("streaming", "true");
        info.setProperty("memoryLimit", "2GB");
        info.setProperty("spillToDisk", "true");
        info.setProperty("tempDirectory", "./temp/calcite");

        // 创建连接
        Connection connection = DriverManager.getConnection("jdbc:calcite:", info);
        CalciteConnection calciteConn = connection.unwrap(CalciteConnection.class);
        
        // 添加MySQL数据源
        addMySQLSchema(calciteConn.getRootSchema());
        
        log.info("Apache Calcite connection established successfully");
        return calciteConn;
    }

    /**
     * 手动注册Calcite JDBC驱动
     * 解决Tomcat环境下驱动注册问题
     */
    private void registerCalciteDriver() {
        try {
            // 手动加载并注册Calcite驱动
            Class.forName("org.apache.calcite.jdbc.Driver");

            // 确保驱动已注册
            org.apache.calcite.jdbc.Driver calciteDriver = new org.apache.calcite.jdbc.Driver();
            DriverManager.registerDriver(calciteDriver);

            log.info("Calcite JDBC driver registered successfully");

        } catch (Exception e) {
            log.error("Failed to register Calcite JDBC driver", e);
            throw new RuntimeException("Failed to register Calcite JDBC driver", e);
        }
    }

    /**
     * 添加MySQL数据源到Calcite
     */
    private void addMySQLSchema(SchemaPlus rootSchema) {
        try {
            // 创建JDBC Schema连接到MySQL
            DataSource mysqlDataSource = createMySQLDataSource();
            Schema mysqlSchema = JdbcSchema.create(rootSchema, "mysql", mysqlDataSource, null, null);
            rootSchema.add("mysql", mysqlSchema);
            
            log.info("MySQL schema added to Calcite successfully");
            
        } catch (Exception e) {
            log.error("Failed to add MySQL schema to Calcite", e);
            throw new RuntimeException("Failed to configure Calcite MySQL schema", e);
        }
    }
    
    /**
     * 创建优化的MySQL数据源
     */
    private DataSource createMySQLDataSource() {
        // 使用HikariCP创建优化的MySQL连接池
        com.zaxxer.hikari.HikariConfig config = new com.zaxxer.hikari.HikariConfig();
        
        // 构建优化的JDBC URL
        String optimizedUrl = buildOptimizedMySQLUrl();
        config.setJdbcUrl(optimizedUrl);
        config.setUsername(mysqlUsername);
        config.setPassword(mysqlPassword);
        config.setDriverClassName("com.mysql.cj.jdbc.Driver");
        
        // 连接池优化配置
        config.setMaximumPoolSize(10);
        config.setMinimumIdle(2);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        
        // 字符编码配置
        config.addDataSourceProperty("useUnicode", "true");
        config.addDataSourceProperty("characterEncoding", "UTF-8");
        config.addDataSourceProperty("connectionCollation", "utf8mb4_unicode_ci");
        config.addDataSourceProperty("useOldUTF8Behavior", "false");

        // 查询优化配置
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        
        return new com.zaxxer.hikari.HikariDataSource(config);
    }
    
    /**
     * 构建优化的MySQL JDBC URL
     */
    private String buildOptimizedMySQLUrl() {
        StringBuilder urlBuilder = new StringBuilder(mysqlUrl);
        
        // 如果URL中没有参数，添加?
        if (!mysqlUrl.contains("?")) {
            urlBuilder.append("?");
        } else if (!mysqlUrl.endsWith("&")) {
            urlBuilder.append("&");
        }

        // 添加字符编码和时区配置 - 增强UTF-8支持
        urlBuilder.append("useUnicode=true&");
        urlBuilder.append("characterEncoding=UTF-8&");
        urlBuilder.append("connectionCollation=utf8mb4_unicode_ci&");
        urlBuilder.append("useOldUTF8Behavior=false&");
        urlBuilder.append("serverTimezone=Asia/Shanghai&");
        urlBuilder.append("useSSL=false&");
        urlBuilder.append("allowPublicKeyRetrieval=true&");

        // 添加性能优化参数
        urlBuilder.append("useServerPrepStmts=true&");
        urlBuilder.append("cachePrepStmts=true&");
        urlBuilder.append("prepStmtCacheSize=250&");
        urlBuilder.append("prepStmtCacheSqlLimit=2048&");
        urlBuilder.append("rewriteBatchedStatements=true&");
        urlBuilder.append("useLocalSessionState=true&");
        urlBuilder.append("elideSetAutoCommits=true&");
        urlBuilder.append("cacheResultSetMetadata=true&");
        urlBuilder.append("cacheServerConfiguration=true&");
        urlBuilder.append("maintainTimeStats=false&");
        urlBuilder.append("useStreamLengthsInPrepStmts=true&");
        urlBuilder.append("useCursorFetch=true&");
        urlBuilder.append("defaultFetchSize=1000");
        
        String result = urlBuilder.toString();
        log.debug("Optimized MySQL URL: {}", maskPassword(result));
        
        return result;
    }
    
    /**
     * 掩码密码用于日志输出
     */
    private String maskPassword(String url) {
        if (url == null) return null;
        return url.replaceAll("password=[^&]*", "password=***");
    }
}
