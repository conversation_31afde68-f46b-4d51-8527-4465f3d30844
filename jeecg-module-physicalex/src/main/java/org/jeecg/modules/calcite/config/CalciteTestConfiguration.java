package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.jdbc.CalciteConnection;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

/**
 * Calcite测试配置类
 * 用于验证Tomcat部署时的基础连接功能
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
//@Configuration
//@ConditionalOnProperty(name = "calcite.test.enabled", havingValue = "true", matchIfMissing = false)
@Slf4j
public class CalciteTestConfiguration {
    
    /**
     * 创建测试用的Calcite连接
     * 如果主配置失败，可以启用此配置进行测试
     */
    @Bean(name = "testCalciteConnection")
    public CalciteConnection testCalciteConnection() throws SQLException {
        try {
            log.info("Creating test Calcite connection...");
            
            // 强制加载驱动
            Class.forName("org.apache.calcite.jdbc.Driver");
            log.info("Calcite driver loaded successfully");
            
            // 最简单的配置
            Properties info = new Properties();
            info.setProperty("lex", "MYSQL");
            
            // 创建连接
            Connection connection = DriverManager.getConnection("jdbc:calcite:", info);

            // 转换为CalciteConnection类型
            CalciteConnection calciteConn = connection.unwrap(CalciteConnection.class);
            log.info("Test Calcite connection created successfully");

            // 测试连接
            if (!calciteConn.isClosed()) {
                log.info("Test Calcite connection is active");
            }

            return calciteConn;
            
        } catch (Exception e) {
            log.error("Failed to create test Calcite connection", e);
            throw new SQLException("Test Calcite connection failed", e);
        }
    }
}
