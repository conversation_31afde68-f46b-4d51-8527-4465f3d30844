package org.jeecg.modules.calcite.util;

import java.util.List;
import java.util.Map;

/**
 * Calcite类型转换辅助工具
 * 解决泛型类型推断问题
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class CalciteTypeHelper {
    
    /**
     * 安全地转换查询结果为Map列表
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> castToMapList(Object queryResult) {
        return (List<Map<String, Object>>) queryResult;
    }
    
    /**
     * 安全地转换查询结果为指定类型列表
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> castToList(Object queryResult, Class<T> clazz) {
        return (List<T>) queryResult;
    }
    
    /**
     * 检查对象是否为Map类型
     */
    public static boolean isMapType(Class<?> clazz) {
        return Map.class.isAssignableFrom(clazz);
    }
    
    /**
     * 检查对象是否为List类型
     */
    public static boolean isListType(Object obj) {
        return obj instanceof List;
    }
}
