package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.jdbc.CalciteConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Calcite健康检查器
 * 在应用启动后验证Calcite连接是否正常工作
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
//@Component
//@ConditionalOnProperty(name = "calcite.enabled", havingValue = "true", matchIfMissing = false)
//@Order(1000) // 在其他组件初始化后执行
@Slf4j
public class CalciteHealthChecker implements ApplicationListener<ContextRefreshedEvent> {
    
    @Autowired(required = false)
    private CalciteConnection calciteConnection;
    
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 只在根应用上下文刷新时执行
        if (event.getApplicationContext().getParent() == null) {
            performHealthCheck();
        }
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        try {
            log.info("Starting Calcite health check...");
            
            if (calciteConnection == null) {
                log.error("CalciteConnection bean is null - configuration may have failed");
                return;
            }
            
            // 检查连接状态
            if (calciteConnection.isClosed()) {
                log.error("CalciteConnection is closed");
                return;
            }
            
            // 执行简单查询测试
            String testSql = "SELECT 1 as test_value";
            try (PreparedStatement stmt = calciteConnection.prepareStatement(testSql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    int testValue = rs.getInt("test_value");
                    if (testValue == 1) {
                        log.info("✅ Calcite health check PASSED - connection is working properly");
                    } else {
                        log.warn("⚠️ Calcite health check PARTIAL - query returned unexpected value: {}", testValue);
                    }
                } else {
                    log.warn("⚠️ Calcite health check PARTIAL - query returned no results");
                }
            }
            
            // 检查连接元数据
            try {
                String databaseProductName = calciteConnection.getMetaData().getDatabaseProductName();
                String databaseProductVersion = calciteConnection.getMetaData().getDatabaseProductVersion();
                log.info("Calcite database info: {} version {}", databaseProductName, databaseProductVersion);
            } catch (Exception e) {
                log.warn("Failed to get database metadata: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("❌ Calcite health check FAILED", e);
        }
    }
}
