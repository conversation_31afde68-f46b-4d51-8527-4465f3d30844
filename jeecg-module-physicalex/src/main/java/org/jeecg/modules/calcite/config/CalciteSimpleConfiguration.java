package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.jdbc.CalciteConnection;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.PostConstruct;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

/**
 * 简化的Calcite配置类
 * 专门解决Tomcat部署时的JDBC驱动问题
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
//@Configuration
//@ConditionalOnProperty(name = "calcite.enabled", havingValue = "true", matchIfMissing = false)
@Slf4j
public class CalciteSimpleConfiguration {
    
    /**
     * 在Bean初始化之前注册驱动
     */
    @PostConstruct
    public void initializeDrivers() {
        try {
            log.info("Initializing Calcite JDBC driver for Tomcat deployment...");
            
            // 强制加载Calcite驱动类
            Class.forName("org.apache.calcite.jdbc.Driver");
            
            // 验证驱动是否可用
            try {
                java.sql.Driver driver = DriverManager.getDriver("jdbc:calcite:");
                log.info("Calcite driver verification successful: {}", driver.getClass().getName());
            } catch (SQLException e) {
                log.warn("Calcite driver verification failed, but this may be normal: {}", e.getMessage());
            }
            
            log.info("Calcite JDBC driver initialization completed");
            
        } catch (Exception e) {
            log.error("Failed to initialize Calcite JDBC driver", e);
            // 不抛出异常，让应用继续启动
        }
    }
    
    /**
     * 创建简单的Calcite连接
     */
    @Bean
    @Primary
    public CalciteConnection calciteConnection() throws SQLException {
        try {
            log.info("Creating Calcite connection for Tomcat deployment...");

            Properties info = new Properties();

            // 基础配置
            info.setProperty("lex", "MYSQL");
            info.setProperty("conformance", "MYSQL_5");
            info.setProperty("caseSensitive", "false");

            // 简化的内存配置
            info.setProperty("streaming", "false");  // 先禁用流式处理

            // 创建连接
            Connection connection = DriverManager.getConnection("jdbc:calcite:", info);

            // 转换为CalciteConnection类型
            CalciteConnection calciteConn = connection.unwrap(CalciteConnection.class);

            log.info("Calcite connection created successfully for Tomcat deployment");
            return calciteConn;

        } catch (SQLException e) {
            log.error("Failed to create Calcite connection for Tomcat deployment", e);
            throw e;
        }
    }
}
