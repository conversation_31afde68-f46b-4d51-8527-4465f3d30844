package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Apache Calcite自动配置
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@ComponentScan(basePackages = "org.jeecg.modules.calcite")
@ConditionalOnProperty(prefix = "calcite", name = "enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
public class CalciteAutoConfig {
    
    public CalciteAutoConfig() {
        log.info("Apache Calcite auto configuration enabled");
    }
}
