package org.jeecg.modules.calcite.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 体检统计数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(description = "体检统计数据")
public class ExamStatisticsDTO {
    
    @ApiModelProperty(value = "统计日期", example = "2024-01-01")
    private String createTime;
    
    @ApiModelProperty(value = "星期", example = "星期一")
    private String dayOfWeek;
    
    @ApiModelProperty(value = "格式化日期", example = "01月01日")
    private String formattedDate;
    
    // 登记统计
    @ApiModelProperty(value = "个人登记总数")
    private Integer personalRegTotal;
    
    @ApiModelProperty(value = "团体登记总数")
    private Integer companyRegTotal;
    
    @ApiModelProperty(value = "登记总数")
    private Integer regTotal;
    
    // 开始体检统计
    @ApiModelProperty(value = "个人开始体检总数")
    private Integer personalStartTotal;
    
    @ApiModelProperty(value = "团体开始体检总数")
    private Integer companyStartTotal;
    
    @ApiModelProperty(value = "开始体检总数")
    private Integer startTotal;
    
    // 总检统计
    @ApiModelProperty(value = "个人总检总数")
    private Integer personalSummaryTotal;
    
    @ApiModelProperty(value = "团体总检总数")
    private Integer companySummaryTotal;
    
    @ApiModelProperty(value = "总检总数")
    private Integer summaryTotal;
    
    // 打印统计
    @ApiModelProperty(value = "个人打印总数")
    private Integer personalPrintTotal;
    
    @ApiModelProperty(value = "团体打印总数")
    private Integer companyPrintTotal;
    
    @ApiModelProperty(value = "打印总数")
    private Integer printTotal;
    
    // 取走统计
    @ApiModelProperty(value = "个人取走总数")
    private Integer personalTakeTotal;
    
    @ApiModelProperty(value = "团体取走总数")
    private Integer companyTakeTotal;
    
    @ApiModelProperty(value = "取走总数")
    private Integer takeTotal;
    
    // 完成率统计
    @ApiModelProperty(value = "开始体检完成率", example = "85.5%")
    private String startRate;
    
    @ApiModelProperty(value = "总检完成率", example = "75.2%")
    private String summaryRate;
    
    @ApiModelProperty(value = "打印完成率", example = "65.8%")
    private String printRate;
    
    @ApiModelProperty(value = "取走完成率", example = "45.3%")
    private String takeRate;
}
