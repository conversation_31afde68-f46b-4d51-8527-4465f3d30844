package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

/**
 * Calcite字符集配置
 * 解决中文字符编码问题
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@Slf4j
public class CalciteCharsetConfiguration {
    
    @PostConstruct
    public void configureCalciteCharset() {
        try {
            // 设置Calcite相关的系统属性
            System.setProperty("calcite.default.charset", "UTF-8");
            System.setProperty("calcite.default.nationalcharset", "UTF-8");
            System.setProperty("calcite.default.collation.name", "UTF-8$en_US");
            
            // 设置文件编码
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            
            // 设置SQL解析器字符集
            System.setProperty("calcite.sql.parser.charset", "UTF-8");
            
            // 验证字符集设置
            String defaultCharset = StandardCharsets.UTF_8.name();
            log.info("Calcite字符集配置完成:");
            log.info("- Default Charset: {}", defaultCharset);
            log.info("- File Encoding: {}", System.getProperty("file.encoding"));
            log.info("- Calcite Default Charset: {}", System.getProperty("calcite.default.charset"));
            
        } catch (Exception e) {
            log.error("配置Calcite字符集失败", e);
        }
    }
}
