package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.jdbc.CalciteConnection;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

/**
 * Calcite备用配置类
 * 当主配置失败时提供基础的连接功能
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
//@Configuration
//@ConditionalOnProperty(name = "calcite.enabled", havingValue = "true", matchIfMissing = false)
@Slf4j
public class CalciteFallbackConfiguration {
    
    /**
     * 备用的Calcite连接Bean
     * 只有在主配置失败时才会创建
     */
    @Bean
    @ConditionalOnMissingBean(name = "calciteConnection")
    public CalciteConnection fallbackCalciteConnection() throws SQLException {
        log.warn("Using fallback Calcite configuration - main configuration may have failed");
        
        try {
            // 简单的驱动注册
            Class.forName("org.apache.calcite.jdbc.Driver");
            
            Properties info = new Properties();
            info.setProperty("lex", "MYSQL");
            info.setProperty("caseSensitive", "false");
            
            Connection connection = DriverManager.getConnection("jdbc:calcite:", info);

            // 转换为CalciteConnection类型
            CalciteConnection calciteConn = connection.unwrap(CalciteConnection.class);
            log.info("Fallback Calcite connection created successfully");

            return calciteConn;
            
        } catch (Exception e) {
            log.error("Fallback Calcite configuration also failed", e);
            throw new SQLException("Failed to create fallback Calcite connection", e);
        }
    }
}
