<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.advice.mapper.CustomerRegAdviceMapper">

    <select id="selectNonAdviceSummary" resultType="org.jeecg.modules.summary.entity.CustomerRegSummary">
        SELECT s.* from customer_reg_summary s left join customer_reg_advice a on s.customer_reg_id = a.customer_reg_id
        and a.id is null where s.status='审核通过'  limit 0,#{limit}
    </select>
</mapper>