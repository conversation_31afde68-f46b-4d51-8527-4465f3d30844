package org.jeecg.modules.advice.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.advice.entity.CustomerRegAdvice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.summary.entity.CustomerRegSummary;

/**
 * @Description: 健康建议
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
public interface CustomerRegAdviceMapper extends BaseMapper<CustomerRegAdvice> {

    List<CustomerRegSummary> selectNonAdviceSummary(@Param("limit") Integer limit);
}
