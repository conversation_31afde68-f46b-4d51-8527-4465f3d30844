import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '登记ID',
    align: "center",
    dataIndex: 'customerRegId'
  },
  {
    title: '健康建议',
    align: "center",
    dataIndex: 'healthAdvice'
  },
  {
    title: '就医建议',
    align: "center",
    dataIndex: 'medAdvice'
  },
];

// 高级查询数据
export const superQuerySchema = {
  customerRegId: {title: '登记ID',order: 0,view: 'text', type: 'string',},
  healthAdvice: {title: '健康建议',order: 1,view: 'text', type: 'string',},
  medAdvice: {title: '就医建议',order: 2,view: 'text', type: 'string',},
};
