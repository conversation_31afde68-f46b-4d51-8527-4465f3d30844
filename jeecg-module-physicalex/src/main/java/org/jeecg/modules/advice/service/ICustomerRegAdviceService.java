package org.jeecg.modules.advice.service;

import org.jeecg.modules.advice.entity.CustomerRegAdvice;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.summary.entity.CustomerRegSummary;

/**
 * @Description: 健康建议
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
public interface ICustomerRegAdviceService extends IService<CustomerRegAdvice> {

    void doGenerateAdvice();

    CustomerRegAdvice generate(CustomerRegSummary summary) throws Exception;

    CustomerRegAdvice getByRegId(String customerRegId);

    CustomerRegAdvice getByCustomerId(String customerId) throws Exception;
}
