package org.jeecg.modules.advice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.advice.entity.CustomerRegAdvice;
import org.jeecg.modules.advice.mapper.CustomerRegAdviceMapper;
import org.jeecg.modules.advice.service.ICustomerRegAdviceService;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

/**
 * @Description: 健康建议
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustomerRegAdviceServiceImpl extends ServiceImpl<CustomerRegAdviceMapper, CustomerRegAdvice> implements ICustomerRegAdviceService {
    @Autowired
    private CustomerRegAdviceMapper customerRegAdviceMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private AIService aiService;

    @Override
    public void doGenerateAdvice() {
        List<CustomerRegSummary> summaryList = customerRegAdviceMapper.selectNonAdviceSummary(1000);
        if (summaryList != null && !summaryList.isEmpty()) {
            for (CustomerRegSummary summary : summaryList) {
                try {
                    generate(summary);
                } catch (Exception e) {
                    log.error("生成健康建议失败", e);
                }
            }
        }
    }

    @Override
    public CustomerRegAdvice generate(CustomerRegSummary summary) throws Exception {
        CustomerRegAdvice advice = new CustomerRegAdvice();
        String healthAdvice = aiService.generateHealthAdvice(summary);
        advice.setHealthAdvice(healthAdvice);
        advice.setMedAdvice(aiService.generateMedAdvice(summary));
        advice.setCustomerRegId(summary.getCustomerRegId());
        advice.setCreateTime(new Date());
        save(advice);
        return advice;
    }

    @Override
    public CustomerRegAdvice getByRegId(String customerRegId) {
        LambdaQueryWrapper<CustomerRegAdvice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerRegAdvice::getCustomerRegId, customerRegId);
        queryWrapper.last("limit 1");

        return getOne(queryWrapper);
    }

    @Override
    public CustomerRegAdvice getByCustomerId(String customerId) throws Exception {
        log.info("开始获取客户健康建议，customerId: {}", customerId);

        // 根据customerId查询最近一次的登记记录
        CustomerReg customerReg = customerRegMapper.selectLatestReg(customerId);
        if (customerReg == null) {
            log.warn("未找到客户登记记录，customerId: {}", customerId);
            throw new Exception("未找到登记记录");
        }
        log.info("找到客户登记记录，customerRegId: {}", customerReg.getId());

        // 查询是否已存在健康建议
        LambdaQueryWrapper<CustomerRegAdvice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerRegAdvice::getCustomerRegId, customerReg.getId());
        queryWrapper.orderByDesc(CustomerRegAdvice::getCreateTime);
        queryWrapper.last("limit 1");

        CustomerRegAdvice advice = getOne(queryWrapper);
        if (advice != null) {
            log.info("找到已存在的健康建议，adviceId: {}", advice.getId());
            advice.setCustomerReg(customerReg);
            return advice;
        }

        // 没有找到健康建议，尝试通过AI生成
        log.info("未找到健康建议，开始通过AI生成，customerRegId: {}", customerReg.getId());
        CustomerRegSummary summary = customerRegSummaryMapper.getByRegId(customerReg.getId());
        if (summary == null) {
            log.warn("未找到总检报告，无法生成健康建议，customerRegId: {}", customerReg.getId());
            throw new Exception("尚未生成总检报告，无法生成健康建议");
        }

        try {
            advice = generate(summary);
            if (advice != null) {
                log.info("AI生成健康建议成功，adviceId: {}", advice.getId());
            } else {
                log.error("AI生成健康建议失败，返回结果为空，customerRegId: {}", customerReg.getId());
                throw new Exception("AI生成健康建议失败");
            }
        } catch (Exception e) {
            log.error("AI生成健康建议过程中发生异常，customerRegId: {}, error: {}", customerReg.getId(), e.getMessage(), e);
            throw new Exception("生成健康建议失败：" + e.getMessage());
        }

        advice.setCustomerReg(customerReg);
        log.info("成功获取客户健康建议，customerId: {}, adviceId: {}", customerId, advice.getId());
        return advice;
    }
}
