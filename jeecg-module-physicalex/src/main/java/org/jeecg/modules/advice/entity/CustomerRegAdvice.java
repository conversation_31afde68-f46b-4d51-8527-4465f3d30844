package org.jeecg.modules.advice.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 健康建议
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_advice")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "customer_reg_advice对象", description = "健康建议")
public class CustomerRegAdvice implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 登记ID
     */
    @Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
    /**
     * 健康建议
     */
    @Excel(name = "健康建议", width = 15)
    @ApiModelProperty(value = "健康建议")
    private java.lang.String healthAdvice;
    /**
     * 就医建议
     */
    @Excel(name = "就医建议", width = 15)
    @ApiModelProperty(value = "就医建议")
    private java.lang.String medAdvice;
    @TableField(exist = false)
    private CustomerReg customerReg;
}
