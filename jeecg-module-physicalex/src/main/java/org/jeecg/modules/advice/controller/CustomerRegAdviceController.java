package org.jeecg.modules.advice.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.advice.entity.CustomerRegAdvice;
import org.jeecg.modules.advice.service.ICustomerRegAdviceService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 健康建议
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
@Api(tags = "健康建议")
@RestController
@RequestMapping("/advice/customerRegAdvice")
@Slf4j
public class CustomerRegAdviceController extends JeecgController<CustomerRegAdvice, ICustomerRegAdviceService> {
    @Autowired
    private ICustomerRegAdviceService customerRegAdviceService;

    /**
     * 获取健康建议
     */
    @GetMapping("/getAdviceByCustomer")
    public Result<?> getAdviceByCustomer(@RequestParam String customerId) {
        CustomerRegAdvice advice = null;
        try {
            advice = customerRegAdviceService.getByCustomerId(customerId);
            return Result.OK(advice);
        } catch (Exception e) {
            return Result.error("获取健康建议失败," + e.getMessage());
        }
    }

    /**
     * 分页列表查询
     *
     * @param customerRegAdvice
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "健康建议-分页列表查询")
    @ApiOperation(value = "健康建议-分页列表查询", notes = "健康建议-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerRegAdvice>> queryPageList(CustomerRegAdvice customerRegAdvice, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CustomerRegAdvice> queryWrapper = QueryGenerator.initQueryWrapper(customerRegAdvice, req.getParameterMap());
        Page<CustomerRegAdvice> page = new Page<CustomerRegAdvice>(pageNo, pageSize);
        IPage<CustomerRegAdvice> pageList = customerRegAdviceService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param customerRegAdvice
     * @return
     */
    @AutoLog(value = "健康建议-添加")
    @ApiOperation(value = "健康建议-添加", notes = "健康建议-添加")
    @RequiresPermissions("advice:customer_reg_advice:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CustomerRegAdvice customerRegAdvice) {
        customerRegAdviceService.save(customerRegAdvice);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param customerRegAdvice
     * @return
     */
    @AutoLog(value = "健康建议-编辑")
    @ApiOperation(value = "健康建议-编辑", notes = "健康建议-编辑")
    @RequiresPermissions("advice:customer_reg_advice:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomerRegAdvice customerRegAdvice) {
        customerRegAdviceService.updateById(customerRegAdvice);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "健康建议-通过id删除")
    @ApiOperation(value = "健康建议-通过id删除", notes = "健康建议-通过id删除")
    @RequiresPermissions("advice:customer_reg_advice:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegAdviceService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "健康建议-批量删除")
    @ApiOperation(value = "健康建议-批量删除", notes = "健康建议-批量删除")
    @RequiresPermissions("advice:customer_reg_advice:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegAdviceService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "健康建议-通过id查询")
    @ApiOperation(value = "健康建议-通过id查询", notes = "健康建议-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegAdvice> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegAdvice customerRegAdvice = customerRegAdviceService.getById(id);
        if (customerRegAdvice == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegAdvice);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegAdvice
     */
    @RequiresPermissions("advice:customer_reg_advice:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegAdvice customerRegAdvice) {
        return super.exportXls(request, customerRegAdvice, CustomerRegAdvice.class, "健康建议");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("advice:customer_reg_advice:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegAdvice.class);
    }

}
