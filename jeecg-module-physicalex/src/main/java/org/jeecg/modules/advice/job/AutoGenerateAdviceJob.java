package org.jeecg.modules.advice.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.advice.service.ICustomerRegAdviceService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoGenerateAdviceJob implements Job {

    @Autowired
    private ICustomerRegAdviceService customerRegAdviceService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                customerRegAdviceService.doGenerateAdvice();
            } catch (Exception e) {
                log.error("生成建议异常！", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("生成建议任务正在执行中!");
        }
    }
}