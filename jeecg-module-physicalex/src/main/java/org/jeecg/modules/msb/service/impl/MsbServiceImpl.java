package org.jeecg.modules.msb.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.excommons.utils.ProxyOkHttpUtil;
import org.jeecg.excommons.utils.SM3Util;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.msb.bo.MsbUserInfo;
import org.jeecg.modules.msb.bo.TokenData;
import org.jeecg.modules.msb.bo.TokenResponse;
import org.jeecg.modules.msb.bo.MsbUserInfoResponse;
import org.jeecg.modules.msb.service.MsbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class MsbServiceImpl implements MsbService {

    @Autowired
    private ProxyOkHttpUtil proxyOkHttpUtil;

    @Autowired
    private ISysSettingService sysSettingService;

    @Override
    public String getToken(String code) throws Exception {
        String appId = sysSettingService.getValueByCode("msb_appid");
        if (appId == null) {
            throw new Exception("未配置蒙速办Appid");
        }
        String appSecret = sysSettingService.getValueByCode("msb_secret");
        if (appSecret == null) {
            throw new Exception("未配置蒙速办AppSecret");
        }

        String time = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString();

        Map<String, String> params = new LinkedHashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("code", code);
        params.put("app_id", appId);
        params.put("time", time);
        params.put("uuid", uuid);

        String sign = SM3Util.sm3SignTokenParam(appId, appSecret, code, time, uuid);

        params.put("sign", sign);
        log.info("获取蒙速办token请求参数: {}", JSONObject.toJSONString(params));
        String url = "https://app.zwfw.nmg.gov.cn/dgov-authing/auth2.0/token";

        String resp = proxyOkHttpUtil.post(url, JSONObject.toJSONString(params), null);
        log.info("获取蒙速办token返回结果: {}", resp);
        if (resp == null) {
            throw new Exception("获取token失败");
        }

        TokenResponse tokenResponse = JSONObject.parseObject(resp, TokenResponse.class);
        //判断tokenResponse是否为空
        if (tokenResponse == null) {
            throw new Exception("获取token失败");
        }
        TokenData data = tokenResponse.getData();
        if (data == null) {
            throw new Exception("获取token失败");
        }
        if (data.getToken() == null) {
            throw new Exception(tokenResponse.getMsg());
        }
        return data.getToken();
    }

    @Override
    public MsbUserInfo getUserInfo(String authToken) throws Exception {
        String appId = sysSettingService.getValueByCode("msb_appid");
        if (appId == null) {
            throw new Exception("未配置蒙速办Appid");
        }

        String apiBaseUrl = sysSettingService.getValueByCode("msb_api_url");
        if (apiBaseUrl == null) {
            throw new Exception("未配置蒙速办API地址");
        }
        //处理末尾防止出现多余的斜杠
        if (apiBaseUrl.endsWith("/")) {
            apiBaseUrl = apiBaseUrl.substring(0, apiBaseUrl.length() - 1);
        }

        String time = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString();

        Map<String, String> params = new LinkedHashMap<>();
        params.put("auth_token", authToken);
        params.put("app_id", appId);
        params.put("time", time);
        params.put("uuid", uuid);

        String sign = SM3Util.sm3SignUserBaseInfo(appId, sysSettingService.getValueByCode("msb_secret"), authToken, time, uuid);
        String url = String.format(apiBaseUrl + "/get_base_info?auth_token=%s&app_id=%s&time=%s&uuid=%s&sign=%s", authToken, appId, time, uuid, sign);

        String response = proxyOkHttpUtil.get(url, null);
        if (response == null) {
            throw new Exception("获取用户信息失败");
        }
        MsbUserInfoResponse msbUserInfoResponse = JSONObject.parseObject(response, MsbUserInfoResponse.class);
        if (msbUserInfoResponse == null) {
            throw new Exception("获取用户信息失败");
        }
        MsbUserInfo msbUserInfo = msbUserInfoResponse.getData();
        if (msbUserInfo == null) {
            throw new Exception("获取用户信息失败");
        }
        return msbUserInfo;
    }

}