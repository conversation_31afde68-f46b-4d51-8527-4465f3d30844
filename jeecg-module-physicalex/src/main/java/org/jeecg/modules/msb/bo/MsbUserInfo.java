package org.jeecg.modules.msb.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MsbUserInfo {
    private String birthday;
    private String registerCityCode;
    private String nickName;
    private String idCard;
    private String sex;
    private String custName;
    private String cityAccount;
    private boolean faceRealNameCheck;
    private String imgUrl;
    private String mobilePhone;
    @JsonProperty("isOcrCheck")
    private boolean ocrCheck;
    @JsonProperty("isRealName")
    private String realNameStatus;
    private Long countId;
    private String corporateCode;
    private String corporateName;
    @JsonProperty("isSixElementsCheck")
    private boolean sixElementsCheck;
    @JsonProperty("isFourElementsCheck")
    private boolean fourElementsCheck;
}
