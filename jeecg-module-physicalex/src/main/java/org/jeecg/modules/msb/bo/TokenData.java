package org.jeecg.modules.msb.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TokenData {
    private String token;
    @JsonProperty("refresh_token")
    private String refreshToken;
    @JsonProperty("token_expired_time")
    private long tokenExpiredTime;
    @JsonProperty("refresh_token_expired_time")
    private long refreshTokenExpiredTime;
    @JsonProperty("app_id")
    private String appId;
    private long time;
    private String uuid;
    private String sign;
}
