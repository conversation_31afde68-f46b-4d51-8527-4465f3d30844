package org.jeecg.modules.station.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;

import java.util.List;

/**
 * @Description: 科室小结
 * @Author: jeecg-boot
 * @Date:   2024-04-19
 * @Version: V1.0
 */
public interface CustomerRegDepartSummaryMapper extends BaseMapper<CustomerRegDepartSummary> {

    List<CustomerRegDepartSummary> listByCustomerReg(@Param("customerRegId") String customerRegId,@Param("abnormalFlag") String abnormalFlag);

    List<CustomerRegDepartSummary> listByCustomerReg4Report(@Param("customerRegId") String customerRegId,@Param("abnormalFlag") String abnormalFlag);

    List<CustomerReg> listNoSummaryCustomerReg();
}
