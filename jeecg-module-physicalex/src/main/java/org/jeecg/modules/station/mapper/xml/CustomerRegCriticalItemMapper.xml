<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.station.mapper.CustomerRegCriticalItemMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  customer_reg_critical_item 
		WHERE
			 critical_id = #{mainId} 
	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.station.entity.CustomerRegCriticalItem">
		SELECT * 
		FROM  customer_reg_critical_item
		WHERE
			 critical_id = #{mainId} 
	</select>
    <select id="listByReg" resultType="org.jeecg.modules.station.entity.CustomerRegCriticalItem">
		select * from customer_reg_critical_item where customer_reg_id=#{regId} <if test="validStatus!=null and validStatus!=''"> and valid_status=#{validStatus}</if>
	</select>
    <select id="pageCustomerReg" resultType="org.jeecg.modules.reg.entity.CustomerReg">
		select reg.* from  customer_reg reg where exists (select r.id from customer_reg r join customer_reg_critical_item c on r.id = c.customer_reg_id where r.id=reg.id
		<if test="nofityStatus!=null and nofityStatus!=''">and c.notify_status=#{nofityStatus}</if>
		<if test="postVisitStatus!=null and postVisitStatus!=''">and c.post_visit_status=#{postVisitStatus}</if>
		<if test="createStartTime!=null and createStartTime!=''">and c.create_time &gt; #{createStartTime}</if>
		<if test="createEndTime!=null and createEndTime!=''">and c.create_time &lt; #{createEndTime}</if>
		<if test="visitStartTime!=null and visitStartTime!=''">and c.post_visit_time &gt; #{visitStartTime}</if>
		<if test="visitEndTime!=null and visitEndTime!=''">and c.post_visit_time &lt; #{visitEndTime}</if>
		<if test="notifyStartTime!=null and notifyStartTime!=''">and c.notify_time &gt; #{notifyStartTime}</if>
		<if test="notifyEndTime!=null and notifyEndTime!=''">and c.notify_time &lt; #{notifyStartTime}</if>
		)
		<if test="name!=null and name!=''">and reg.name = #{name}</if>
		<if test="idCard!=null and idCard!=''">and reg.idCard=#{idCard}</if>
		<if test="phone!=null and phone!=''">and reg.phone=#{phone}</if>
		<if test="examNo!=null and examNo!=''">and reg.examNo=#{examNo}</if>
		<if test="examCardNo!=null and examCardNo!=''">and reg.examCardNo=#{examCardNo}</if> order by reg.create_time desc
	</select>
</mapper>
