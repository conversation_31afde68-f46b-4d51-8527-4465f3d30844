package org.jeecg.modules.station.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.bo.DepartSummary;
import org.jeecg.modules.station.dto.AbnormalSummary;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.jeecg.modules.summary.bo.AbnormalSummaryAndAdvice;
import org.jeecg.modules.summary.service.SystemUserUtilService;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description: 科室小结
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Api(tags = "科室小结")
@RestController
@RequestMapping("/station/customerRegDepartSummary")
@Slf4j
public class CustomerRegDepartSummaryController extends JeecgController<CustomerRegDepartSummary, ICustomerRegDepartSummaryService> {
    @Autowired
    private ICustomerRegDepartSummaryService customerRegDepartSummaryService;
    @Autowired
    private SystemUserUtilService systemUserUtilService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private AIService aiService;

    //listByCustomerReg
    @ApiOperation(value = "科室小结-通过customerRegId查询", notes = "科室小结-通过customerRegId查询")
    @GetMapping(value = "/listByCustomerReg")
    public Result<?> listByCustomerReg(String customerRegId) {
        return Result.OK(customerRegDepartSummaryService.listByCustomerReg(customerRegId, null));
    }

    @ApiOperation(value = "科室小结-通过customerRegId查询异常汇总", notes = "科室小结-通过customerRegId查询异常汇总")
    @GetMapping(value = "/listAbnormalSummaryByReg")
    public Result<?> listAbnormalSummaryByCustomerReg(String customerRegId) {

        List<String> abnormalSummaryList = customerRegDepartSummaryService.generateAbnormalSummaryList(customerRegId);
        String departSummaryText = StringUtils.join(abnormalSummaryList, "\n");
        return Result.OK("操作成功！", departSummaryText);
    }

    @ApiOperation(value = "科室小结-通过customerRegId查询异常汇总", notes = "科室小结-通过customerRegId查询异常汇总")
    @GetMapping(value = "/listAbnormalSummaryByRegV2")
    public Result<?> listAbnormalSummaryByCustomerRegV2(String customerRegId) {

        List<AbnormalSummary> abnormalSummaryList = customerRegDepartSummaryService.generateAbnormalSummaryBeanList(customerRegId);
        return Result.OK("操作成功！", abnormalSummaryList);
    }

    /**
     * 生成科室小结
     *
     * @return
     */
    @AutoLog(value = "科室小结-生成科室小结")
    @ApiOperation(value = "科室小结-生成科室小结", notes = "科室小结-生成科室小结")
    @RequiresPermissions("station:customer_reg_depart_summary:generateSummary")
    @PostMapping(value = "/generateSummary")
    public Result<?> generateSummary(@RequestBody JSONObject jsonObject) {
        JSONObject customerReg = jsonObject.getJSONObject("customerReg");
        CustomerReg reg = customerReg.toJavaObject(CustomerReg.class);
        if (customerRegDepartSummaryService.isSummaryAudited(reg.getId())) {
            return Result.error("已有总检记录，不可以修改小结！");
        }
        //JSONArray itemResultList = jsonObject.getJSONArray("itemResultList");
        String departmentId = jsonObject.getString("departmentId");
        CustomerRegDepartSummary customerRegDepartSummary = customerRegDepartSummaryService.getOneByDepartmentIdAndRegId(departmentId, reg.getId());
        if (customerRegDepartSummary != null) {
            customerRegDepartSummary.setUpdateByManual("0");
            customerRegDepartSummaryService.updateById(customerRegDepartSummary);
        }
        DepartSummary summary = customerRegDepartSummaryService.generateAndSaveSummary(departmentId, reg);
        return Result.OK("生成成功！", summary);
    }

    /**
     * 生成科室小结
     *
     * @return
     */
    @AutoLog(value = "科室小结-审核科室小结")
    @ApiOperation(value = "科室小结-审核科室小结", notes = "科室小结-审核科室小结")
    @RequiresPermissions("station:customer_reg_depart_summary:audit")
    @PostMapping(value = "/auditDepartSummary")
    public Result<?> auditDepartSummary(@RequestBody JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        String customerRegId = jsonObject.getString("customerRegId");
        String departmentId = jsonObject.getString("departmentId");
        String auditStatus = jsonObject.getString("auditStatus");
        UpdateWrapper<CustomerRegDepartSummary> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("audit_status", "通过");
        updateWrapper.set("audit_time", new Date());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        updateWrapper.set("audit_by", sysUser.getId());
        updateWrapper.set("auditor_name", sysUser.getRealname());
        customerRegDepartSummaryService.update(updateWrapper);

        //批量更新CustomerRegItemGroup的审核医生
        String auditorSignPic = systemUserUtilService.getSignPicByUsername(sysUser.getUsername());
        UpdateWrapper<CustomerRegItemGroup> itemGroupUpdateWrapper = new UpdateWrapper<>();
        itemGroupUpdateWrapper.eq("customer_reg_id", customerRegId);
        itemGroupUpdateWrapper.eq("department_id", departmentId);
        itemGroupUpdateWrapper.set("audit_doctor_code", sysUser.getUsername());
        itemGroupUpdateWrapper.set("audit_doctor_name", sysUser.getRealname());
        itemGroupUpdateWrapper.set("audit_doctor_sign_pic", auditorSignPic);

        customerRegItemGroupService.update(itemGroupUpdateWrapper);

        return Result.OK("操作成功！");
    }

    /**
     * 生成科室小结
     *
     * @return
     */
    @AutoLog(value = "科室小结-审反审科室小结")
    @ApiOperation(value = "科室小结-审反审科室小结", notes = "科室小结-审反审科室小结")
    @RequiresPermissions("station:customer_reg_depart_summary:audit")
    @PostMapping(value = "/unAuditDepartSummary")
    public Result<?> unAuditDepartSummary(@RequestBody JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        String customerRegId = jsonObject.getString("customerRegId");
        String departmentId = jsonObject.getString("departmentId");
        String auditStatus = jsonObject.getString("auditStatus");
        UpdateWrapper<CustomerRegDepartSummary> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("audit_status", "待审");
        updateWrapper.set("audit_time", null);
        updateWrapper.set("audit_by", null);
        updateWrapper.set("auditor_name", null);
        customerRegDepartSummaryService.update(updateWrapper);

        //批量更新CustomerRegItemGroup的审核医生
        UpdateWrapper<CustomerRegItemGroup> itemGroupUpdateWrapper = new UpdateWrapper<>();
        itemGroupUpdateWrapper.eq("customer_reg_id", customerRegId);
        itemGroupUpdateWrapper.eq("department_id", departmentId);
        itemGroupUpdateWrapper.set("audit_doctor_code", null);
        itemGroupUpdateWrapper.set("audit_doctor_name", null);
        itemGroupUpdateWrapper.set("audit_doctor_sign_pic", null);

        customerRegItemGroupService.update(itemGroupUpdateWrapper);

        return Result.OK("操作成功！");
    }


    /**
     * 获取科室小结
     *
     * @return
     */
    @AutoLog(value = "科室小结-获取科室小结")
    @ApiOperation(value = "科室小结-获取科室小结", notes = "科室小结-获取科室小结")
    @GetMapping(value = "/getSummary")
    public Result<?> getSummary(String departmentId, String regId) {
        CustomerRegDepartSummary summary = customerRegDepartSummaryService.getOneByDepartmentIdAndRegId(departmentId, regId);
        if (summary == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK("获取成功！", summary);
    }

    /**
     * 清除科室小结
     *
     * @return
     */
    @AutoLog(value = "科室小结-清除科室小结")
    @ApiOperation(value = "科室小结-清除科室小结", notes = "科室小结-清除科室小结")
    @GetMapping(value = "/removeSummary")
    public Result<?> removeSummary(String summaryId) {
        try {
            customerRegDepartSummaryService.removeDepartmentSummary(summaryId);
            return Result.OK("操作成功！");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    /**
     * 更新科室小结
     *
     * @return
     */
    @AutoLog(value = "科室小结-更新科室小结")
    @ApiOperation(value = "科室小结-更新科室小结", notes = "科室小结-更新科室小结")
    @RequiresPermissions("station:customer_reg_depart_summary:updateSummary")
    @PostMapping(value = "/updateSummary")
    public Result<String> updateSummary(@RequestBody JSONObject info) {
        String departmentId = info.getString("departmentId");
        String regId = info.getString("customerRegId");
        String characterSummary = info.getString("characterSummary");
        String updateByManual = info.getString("updateByManual");
        if (customerRegDepartSummaryService.isSummaryAudited(regId)) {
            return Result.error("已有总检记录，不可以修改项目结果！", "-1");
        }

        try {
            customerRegDepartSummaryService.updateSummary(departmentId, regId, characterSummary, updateByManual);
            return Result.OK("修改成功！");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    /**
     * 分页列表查询
     *
     * @param customerRegDepartSummary
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "科室小结-分页列表查询")
    @ApiOperation(value = "科室小结-分页列表查询", notes = "科室小结-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerRegDepartSummary>> queryPageList(CustomerRegDepartSummary customerRegDepartSummary, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CustomerRegDepartSummary> queryWrapper = QueryGenerator.initQueryWrapper(customerRegDepartSummary, req.getParameterMap());
        Page<CustomerRegDepartSummary> page = new Page<CustomerRegDepartSummary>(pageNo, pageSize);
        IPage<CustomerRegDepartSummary> pageList = customerRegDepartSummaryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param customerRegDepartSummary
     * @return
     */
    @AutoLog(value = "科室小结-添加")
    @ApiOperation(value = "科室小结-添加", notes = "科室小结-添加")
    @RequiresPermissions("station:customer_reg_depart_summary:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CustomerRegDepartSummary customerRegDepartSummary) {
        customerRegDepartSummaryService.save(customerRegDepartSummary);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param customerRegDepartSummary
     * @return
     */
    @AutoLog(value = "科室小结-编辑")
    @ApiOperation(value = "科室小结-编辑", notes = "科室小结-编辑")
    @RequiresPermissions("station:customer_reg_depart_summary:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomerRegDepartSummary customerRegDepartSummary) {
        customerRegDepartSummaryService.updateById(customerRegDepartSummary);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "科室小结-通过id删除")
    @ApiOperation(value = "科室小结-通过id删除", notes = "科室小结-通过id删除")
    @RequiresPermissions("station:customer_reg_depart_summary:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegDepartSummaryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "科室小结-批量删除")
    @ApiOperation(value = "科室小结-批量删除", notes = "科室小结-批量删除")
    @RequiresPermissions("station:customer_reg_depart_summary:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegDepartSummaryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "科室小结-通过id查询")
    @ApiOperation(value = "科室小结-通过id查询", notes = "科室小结-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegDepartSummary> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegDepartSummary customerRegDepartSummary = customerRegDepartSummaryService.getById(id);
        if (customerRegDepartSummary == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegDepartSummary);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegDepartSummary
     */
    @RequiresPermissions("station:customer_reg_depart_summary:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegDepartSummary customerRegDepartSummary) {
        return super.exportXls(request, customerRegDepartSummary, CustomerRegDepartSummary.class, "科室小结");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("station:customer_reg_depart_summary:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegDepartSummary.class);
    }

}
