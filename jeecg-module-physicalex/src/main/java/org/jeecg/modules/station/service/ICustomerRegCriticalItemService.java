package org.jeecg.modules.station.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 危急值记录关联项目
 * @Author: jeecg-boot
 * @Date: 2024-04-26
 * @Version: V1.0
 */
public interface ICustomerRegCriticalItemService extends IService<CustomerRegCriticalItem> {

    /**
     * 通过主表id查询子表数据
     *
     * @param mainId
     * @return List<CustomerRegCriticalItem>
     */
    public List<CustomerRegCriticalItem> selectByMainId(String mainId);

    void saveAtDepartmentSummary(String departId, String regId, List<CustomerRegCriticalItem> customerRegCriticalItemList);

    List<CustomerRegCriticalItem> listByReg(String regId, Integer validStatus);

    Page<CustomerReg> pageCustomerReg(Page<CustomerReg> page, String name, String idCard, String phone, String examNo, String examCardNo, String nofityStatus, String postVisitStatus, String timeProperty, String startTime, String endTime);

    void confirmBatch(List<CustomerRegCriticalItem> itemList);

    void notifyBatch(List<CustomerRegCriticalItem> itemList, String notifyContent, String notifyMethod) throws Exception;

    void postVisitBatch(List<CustomerRegCriticalItem> itemList, String postVisitContent, String postVisitMethod);

    void resetBatch(List<String> itemIdList, String actionType);

    List<CustomerRegCriticalItem> listByNotifyStatus(String notifyStatus, String degree);

    void doAutoNotify();

    void notifyManager();
}
