package org.jeecg.modules.station.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.station.entity.CustomerRegDepartTip;
import org.jeecg.modules.station.service.ICustomerRegDepartTipService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 科室提醒
 * @Author: jeecg-boot
 * @Date: 2024-05-08
 * @Version: V1.0
 */
@Api(tags = "科室提醒")
@RestController
@RequestMapping("/station/customerRegDepartTip")
@Slf4j
public class CustomerRegDepartTipController extends JeecgController<CustomerRegDepartTip, ICustomerRegDepartTipService> {
    @Autowired
    private ICustomerRegDepartTipService customerRegDepartTipService;
    @Autowired
    private ISysDepartService sysDepartService;

    /**
     * 分页列表查询
     *
     * @param customerRegDepartTip
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "科室提醒-分页列表查询")
    @ApiOperation(value = "科室提醒-分页列表查询", notes = "科室提醒-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerRegDepartTip>> queryPageList(CustomerRegDepartTip customerRegDepartTip,
                                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                             HttpServletRequest req) {
        QueryWrapper<CustomerRegDepartTip> queryWrapper = QueryGenerator.initQueryWrapper(customerRegDepartTip, req.getParameterMap());
        Page<CustomerRegDepartTip> page = new Page<CustomerRegDepartTip>(pageNo, pageSize);
        IPage<CustomerRegDepartTip> pageList = customerRegDepartTipService.page(page, queryWrapper);
        return Result.OK(pageList);
    }


    /**
     * 分页列表查询
     */
    //@AutoLog(value = "科室提醒-分页列表查询")
    @ApiOperation(value = "科室提醒-分页列表查询", notes = "科室提醒-分页列表查询")
    @GetMapping(value = "/listByRegId")
    public Result<?> listByRegId(String regId) {

        List<CustomerRegDepartTip> list = customerRegDepartTipService.listByRegId(regId);
        return Result.OK(list);
    }

    /**
     * 添加
     *
     * @param customerRegDepartTip
     * @return
     */
    @AutoLog(value = "科室提醒-添加")
    @ApiOperation(value = "科室提醒-添加", notes = "科室提醒-添加")
    @RequiresPermissions("station:customer_reg_depart_tip:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CustomerRegDepartTip customerRegDepartTip) {
        if (StringUtils.isNotBlank(customerRegDepartTip.getDepartmentId())) {
           SysDepart depart = sysDepartService.getById(customerRegDepartTip.getDepartmentId());
              customerRegDepartTip.setDepartmentName(depart.getDepartName());
        }
        customerRegDepartTipService.save(customerRegDepartTip);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param customerRegDepartTip
     * @return
     */
    @AutoLog(value = "科室提醒-编辑")
    @ApiOperation(value = "科室提醒-编辑", notes = "科室提醒-编辑")
    @RequiresPermissions("station:customer_reg_depart_tip:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomerRegDepartTip customerRegDepartTip) {
        if (StringUtils.isNotBlank(customerRegDepartTip.getDepartmentId())) {
            SysDepart depart = sysDepartService.getById(customerRegDepartTip.getDepartmentId());
            customerRegDepartTip.setDepartmentName(depart.getDepartName());
        }
        customerRegDepartTipService.updateById(customerRegDepartTip);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "科室提醒-通过id删除")
    @ApiOperation(value = "科室提醒-通过id删除", notes = "科室提醒-通过id删除")
    @RequiresPermissions("station:customer_reg_depart_tip:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegDepartTipService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "科室提醒-批量删除")
    @ApiOperation(value = "科室提醒-批量删除", notes = "科室提醒-批量删除")
    @RequiresPermissions("station:customer_reg_depart_tip:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegDepartTipService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "科室提醒-通过id查询")
    @ApiOperation(value = "科室提醒-通过id查询", notes = "科室提醒-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegDepartTip> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegDepartTip customerRegDepartTip = customerRegDepartTipService.getById(id);
        if (customerRegDepartTip == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegDepartTip);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegDepartTip
     */
    @RequiresPermissions("station:customer_reg_depart_tip:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegDepartTip customerRegDepartTip) {
        return super.exportXls(request, customerRegDepartTip, CustomerRegDepartTip.class, "科室提醒");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("station:customer_reg_depart_tip:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegDepartTip.class);
    }

}
