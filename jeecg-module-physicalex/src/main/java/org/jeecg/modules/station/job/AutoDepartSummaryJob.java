package org.jeecg.modules.station.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AutoDepartSummaryJob implements Job {

    @Autowired
    private ICustomerRegDepartSummaryService customerRegDepartSummaryService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        try {
            customerRegDepartSummaryService.doAutoGenerateDepartSummaryJob();
        } catch (Exception e) {
            log.error("自动生成科室小结异常", e);
        }

    }
}
