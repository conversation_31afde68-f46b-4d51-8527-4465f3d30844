package org.jeecg.modules.station.dto;

import lombok.Data;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

import java.util.List;

@Data
public class AbnormalSummary {
    private String text;
    private String title;
    private String summaryText;
    private String format;
    private String orderReason;
    private Integer severityOrder;
    private List<String> itemSummaryTextList;
    // 关联的小项列表
    private List<CustomerRegItemResult> relatedItemResults;
    // 关联的大项列表
    private List<CustomerRegItemGroup> relatedItemGroupList;
}
