package org.jeecg.modules.station.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 体检小项结果表
 * @Author: jeecg-boot
 * @Date: 2024-04-21
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_item_result")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "customer_reg_item_result对象", description = "体检小项结果表")
public class CustomerRegItemResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;

    private Integer sortNo;

    private String checkBillNo;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 档案号
     */
    @ApiModelProperty(value = "档案号")
    private String archivesNum;

    @ApiModelProperty("体检号")
    private String examNo;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;
    /**
     * 所属体检ID
     */
    @Excel(name = "所属体检ID", width = 15)
    @ApiModelProperty(value = "所属体检ID")
    private java.lang.String customerRegId;
    /**
     * 所属组合ID
     */
    @Excel(name = "所属组合ID", width = 15)
    @ApiModelProperty(value = "所属组合ID")
    private java.lang.String itemGroupId;
    /**
     * 检查科室代码
     */
    @Excel(name = "检查科室代码", width = 15)
    @ApiModelProperty(value = "检查科室代码")
    private java.lang.String checkDepartmentCode;
    /**
     * 检查科室名称
     */
    @Excel(name = "检查科室名称", width = 15)
    @ApiModelProperty(value = "检查科室名称")
    private java.lang.String checkDepartmentName;

    /**
     * 组合所属科室ID
     */
    @Excel(name = "组合所属科室ID", width = 15)
    @ApiModelProperty(value = "组合所属科室ID")
    private java.lang.String departmentId;

    /**
     * 所属组合
     */
    @Excel(name = "所属组合", width = 15)
    @ApiModelProperty(value = "所属组合")
    private java.lang.String itemGroupName;

    /**
     * 所属组合HIS代码
     */
    @Excel(name = "所属组合HIS代码", width = 15)
    @ApiModelProperty(value = "所属组合HIS代码")
    private java.lang.String groupHisCode;

    /**
     * 所属组合HIS名称
     */
    @Excel(name = "所属组合HIS名称", width = 15)
    @ApiModelProperty(value = "所属组合HIS名称")
    private java.lang.String groupHisName;

    /**
     * 小项HIS代码
     */
    @Excel(name = "小项HIS代码", width = 15)
    @ApiModelProperty(value = "小项HIS代码")
    private java.lang.String itemHisCode;
    /**
     * 小项HIS名称
     */
    @Excel(name = "小项HIS名称", width = 15)
    @ApiModelProperty(value = "小项HIS名称")
    private java.lang.String itemHisName;

    /**
     * 小项ID
     */
    @Excel(name = "小项ID", width = 15)
    @ApiModelProperty(value = "小项ID")
    private java.lang.String itemId;
    /**
     * 小项
     */
    @Excel(name = "小项", width = 15)
    @ApiModelProperty(value = "小项")
    private java.lang.String itemName;
    /**
     * 小项代码
     */
    @Excel(name = "小项代码", width = 15)
    @ApiModelProperty(value = "小项代码")
    private java.lang.String itemCode;
    /**
     * 值类型
     */
    @Excel(name = "值类型", width = 15)
    @ApiModelProperty(value = "值类型")
    private java.lang.String valueType;
    /**
     * 值
     */
    @Excel(name = "值", width = 15)
    @ApiModelProperty(value = "值")
    private java.lang.String value;
    /**
     * 值来源
     */
    @Excel(name = "值来源", width = 15)
    @ApiModelProperty(value = "值来源")
    private java.lang.String valueSource;
    /**
     * 医生ID
     */
    @Excel(name = "医生ID", width = 15)
    @ApiModelProperty(value = "医生ID")
    private java.lang.String doctorId;
    /**
     * 医生
     */
    @Excel(name = "医生", width = 15)
    @ApiModelProperty(value = "医生")
    private java.lang.String doctorName;
    /**
     * 图片
     */
    @Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class)
    private List<String> pic;
    /**
     * 仪器
     */
    @Excel(name = "仪器", width = 15)
    @ApiModelProperty(value = "仪器")
    private java.lang.String instrument;
    @Excel(name = "结果单位", width = 15)
    @ApiModelProperty(value = "结果单位")
    private java.lang.String unit;
    @Excel(name = "偏高偏低", width = 15)
    @ApiModelProperty(value = "偏高偏低")
    private java.lang.String valueIndicator;
    @Excel(name = "参考范围", width = 15)
    @ApiModelProperty(value = "参考范围")
    private java.lang.String valueRefRange;
   /* @Excel(name = "检查结论", width = 15)
    @ApiModelProperty(value = "检查结论")
    private java.lang.String checkConclusion;*/
    @Excel(name = "检查目的", width = 15)
    @ApiModelProperty(value = "检查目的")
    private java.lang.String checkPurpose;
    @Excel(name = "检查结论", width = 15)
    @ApiModelProperty(value = "检查结论")
    private String checkConclusion;
    @Excel(name = "检查部位", width = 15)
    @ApiModelProperty(value = "检查部位")
    private java.lang.String checkParts;
    @Excel(name = "检查所见", width = 15)
    @ApiModelProperty(value = "检查所见")
    private java.lang.String checkObservations;
    @Excel(name = "放弃标志", width = 15)
    @ApiModelProperty(value = "放弃标志")
    private java.lang.Integer abandonFlag;

    @Excel(name = "异常标志", width = 15)
    @ApiModelProperty(value = "异常标志")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String abnormalFlag;
    @Excel(name = "异常标志描述", width = 15)
    @ApiModelProperty(value = "异常标志描述")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String abnormalFlagDesc;
    @Excel(name = "异常标志符号", width = 15)
    @ApiModelProperty(value = "异常标志符号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String abnormalSymbol;
    @Excel(name = "危急值标志", width = 15)
    @ApiModelProperty(value = "危急值标志")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String criticalFlag;
    @Excel(name = "危急值标志", width = 15)
    @ApiModelProperty(value = "危急值分类")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String criticalDegree;
    @ApiModelProperty(value = "匹配的项目结果参考ID")
    private String matchedStandardId;

    private String reportPdf;
    @ApiModelProperty(value = "检查部位编码")
    private String checkPartCode;
    @ApiModelProperty(value = "检查部位名称")
    private String checkPartName;
    @ApiModelProperty(value = "登记大项表id")
    private String customerRegItemGroupId;
    @ApiModelProperty("科目类型")
    private String subjectClass;

    @TableField(exist = false)
    private String checkYear;
    @TableField(exist = false)
    private String valueUnit;
    @TableField(exist = false)
    private String checkStatus;
    @TableField(exist = false)
    private SysDepart depart;
    @TableField(exist = false)
    private String abnormalFlagManual;
    @TableField(exist = false)
    private String abnormalFlagDescManual;
    @TableField(exist = false)
    private Integer sumableNormalvalFlag;
    @TableField(exist = false)
    private Integer sumableFlag;
    @TableField(exist = false)
    private String psyAdviceText;
    @TableField(exist = false)
    private String personalQuestId;
    @TableField(exist = false)
    private String psyRefRange;
    @TableField(exist = false)
    private String reportPdfInterface;
    @TableField(exist = false)
    private String reportDoctorName;
    @TableField(exist = false)
    private String auditDoctorName;
    @TableField(exist = false)
    private Date checkTime;
    @TableField(exist = false)
    private Date auditTime;
    @TableField(exist = false)
    private Date reportTime;




}
