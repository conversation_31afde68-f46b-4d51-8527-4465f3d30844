package org.jeecg.modules.station.service.impl;

import org.jeecg.modules.station.entity.CriticalNotifySetting;
import org.jeecg.modules.station.mapper.CriticalNotifySettingMapper;
import org.jeecg.modules.station.service.ICriticalNotifySettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 危急值通知设置
 * @Author: jeecg-boot
 * @Date:   2025-01-11
 * @Version: V1.0
 */
@Service
public class CriticalNotifySettingServiceImpl extends ServiceImpl<CriticalNotifySettingMapper, CriticalNotifySetting> implements ICriticalNotifySettingService {

    @Autowired
    private CriticalNotifySettingMapper criticalNotifySettingMapper;

    @Override
    public CriticalNotifySetting getSetting() {
        //获取唯一的一条记录，没有则插入一条默认记录
        CriticalNotifySetting setting = criticalNotifySettingMapper.selectOne(null);
        if(setting == null){
            setting = new CriticalNotifySetting();
            criticalNotifySettingMapper.insert(setting);
        }

        return setting;
    }


}
