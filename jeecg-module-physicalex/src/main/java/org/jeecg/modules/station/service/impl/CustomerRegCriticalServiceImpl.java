package org.jeecg.modules.station.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.station.entity.CustomerRegCritical;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;
import org.jeecg.modules.station.mapper.CustomerRegCriticalItemMapper;
import org.jeecg.modules.station.mapper.CustomerRegCriticalMapper;
import org.jeecg.modules.station.service.ICustomerRegCriticalService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 危急值记录
 * @Author: jeecg-boot
 * @Date:   2024-04-26
 * @Version: V1.0
 */
@Service
public class CustomerRegCriticalServiceImpl extends ServiceImpl<CustomerRegCriticalMapper, CustomerRegCritical> implements ICustomerRegCriticalService {

	@Autowired
	private CustomerRegCriticalMapper customerRegCriticalMapper;
	@Autowired
	private CustomerRegCriticalItemMapper customerRegCriticalItemMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		customerRegCriticalItemMapper.deleteByMainId(id);
		customerRegCriticalMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			customerRegCriticalItemMapper.deleteByMainId(id.toString());
			customerRegCriticalMapper.deleteById(id);
		}
	}
}
