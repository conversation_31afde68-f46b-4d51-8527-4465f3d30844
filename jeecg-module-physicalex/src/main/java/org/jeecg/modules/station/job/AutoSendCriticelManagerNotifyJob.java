package org.jeecg.modules.station.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.station.service.ICustomerRegCriticalItemService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoSendCriticelManagerNotifyJob implements Job {

    @Autowired
    private ICustomerRegCriticalItemService customerRegCriticalItemService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                customerRegCriticalItemService.notifyManager();
            } catch (Exception e) {
                log.error("向危急值管理人员发送短信通知异常", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("向危急值管理人员发送短信通知异常！");
        }
    }
}
