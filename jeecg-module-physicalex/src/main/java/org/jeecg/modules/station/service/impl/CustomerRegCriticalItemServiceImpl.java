package org.jeecg.modules.station.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.MustacheUtil;
import org.jeecg.modules.basicinfo.entity.AutoSmsSetting;
import org.jeecg.modules.basicinfo.service.IAutoSmsSettingService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.sms.bo.SmsResult;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.jeecg.modules.station.entity.CriticalDealRecord;
import org.jeecg.modules.station.entity.CriticalNotifySetting;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;
import org.jeecg.modules.station.mapper.CriticalDealRecordMapper;
import org.jeecg.modules.station.mapper.CriticalNotifySettingMapper;
import org.jeecg.modules.station.mapper.CustomerRegCriticalItemMapper;
import org.jeecg.modules.station.service.ICriticalNotifySettingService;
import org.jeecg.modules.station.service.ICustomerRegCriticalItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 危急值记录关联项目
 * @Author: jeecg-boot
 * @Date: 2024-04-26
 * @Version: V1.0
 */
@Service
public class CustomerRegCriticalItemServiceImpl extends ServiceImpl<CustomerRegCriticalItemMapper, CustomerRegCriticalItem> implements ICustomerRegCriticalItemService {

    @Autowired
    private CustomerRegCriticalItemMapper customerRegCriticalItemMapper;
    @Autowired
    private CriticalDealRecordMapper criticalDealRecordMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ISmsRecordsService smsRecordsService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private IAutoSmsSettingService autoSmsSettingService;
    @Autowired
    private ICriticalNotifySettingService criticalNotifySettingService;

    @Override
    public List<CustomerRegCriticalItem> selectByMainId(String mainId) {
        return customerRegCriticalItemMapper.selectByMainId(mainId);
    }

    @Override
    public void saveAtDepartmentSummary(String departId, String regId, List<CustomerRegCriticalItem> customerRegCriticalItemList) {

        jdbcTemplate.update("delete from customer_reg_critical_item where customer_reg_id = ? and department_id = ? and (confirm_status=0 or confirm_status is null)", regId, departId);

        for (CustomerRegCriticalItem criticalItem : customerRegCriticalItemList) {
            customerRegCriticalItemMapper.insert(criticalItem);
        }
    }

    @Override
    public List<CustomerRegCriticalItem> listByReg(String regId, Integer validStatus) {
        LambdaQueryWrapper<CustomerRegCriticalItem> queryWrapper = new LambdaQueryWrapper<CustomerRegCriticalItem>().eq(CustomerRegCriticalItem::getCustomerRegId, regId);
        if (Objects.nonNull(validStatus)) {
            queryWrapper.eq(CustomerRegCriticalItem::getValidStatus, validStatus);
        }
//        return customerRegCriticalItemMapper.listByReg(regId, validStatus);
        return customerRegCriticalItemMapper.selectList(queryWrapper);
    }

    @Override
    public Page<CustomerReg> pageCustomerReg(Page<CustomerReg> page, String name, String idCard, String phone, String examNo, String examCardNo, String nofityStatus, String postVisitStatus, String timeProperty, String startTime, String endTime) {
        String createTimeStart = null;
        String createTimeEnd = null;
        if ("createTime".equals(timeProperty)) {
            createTimeStart = startTime;
            createTimeEnd = endTime;
        }
        String postVisitTimeStart = null;
        String postVisitTimeEnd = null;
        if ("postVisitTime".equals(timeProperty)) {
            postVisitTimeStart = startTime;
            postVisitTimeEnd = endTime;
        }

        String notifyTimeStart = null;
        String notifyTimeEnd = null;
        if ("notifyTime".equals(timeProperty)) {
            notifyTimeStart = startTime;
            notifyTimeEnd = endTime;
        }
        return customerRegCriticalItemMapper.pageCustomerReg(page, name, idCard, phone, examNo, examCardNo, nofityStatus, postVisitStatus, createTimeStart, createTimeEnd, postVisitTimeStart, postVisitTimeEnd, notifyTimeStart, notifyTimeEnd);
    }

    @Override
    public void confirmBatch(List<CustomerRegCriticalItem> itemList) {
        if (itemList == null || itemList.size() == 0) {
            return;
        }
        List<String> ids = itemList.stream().map(CustomerRegCriticalItem::getId).collect(Collectors.toList());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        UpdateWrapper<CustomerRegCriticalItem> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids);
        updateWrapper.set("confirm_status", 1);
        updateWrapper.set("confirm_time", new Date());
        updateWrapper.set("confirm_by", sysUser.getUsername());
        updateWrapper.set("confirmer", sysUser.getRealname());
        customerRegCriticalItemMapper.update(null, updateWrapper);

        //生成操作记录
        CustomerRegCriticalItem criticalItem = itemList.get(0);
        CriticalDealRecord criticalDealRecord = new CriticalDealRecord();
        criticalDealRecord.setCustomerRegId(criticalItem.getCustomerRegId());
        criticalDealRecord.setAction(ExConstants.CRITICAL_DEAL_ACTION_CONFIRM);
        criticalDealRecord.setCriticalItemIds(StringUtils.join(ids, ","));
        criticalDealRecord.setCriticalItems(itemList.stream().map(CustomerRegCriticalItem::getItemName).collect(Collectors.joining(",")));
        criticalDealRecord.setCreateBy(sysUser.getUsername());
        criticalDealRecord.setCreateTime(new Date());
        criticalDealRecord.setCreator(sysUser.getRealname());
        criticalDealRecordMapper.insert(criticalDealRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void notifyBatch(List<CustomerRegCriticalItem> itemList, String notifyContent, String notifyMethod) throws Exception {
        if (itemList == null || itemList.isEmpty()) {
            return;
        }
        List<String> ids = itemList.stream().map(CustomerRegCriticalItem::getId).collect(Collectors.toList());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        UpdateWrapper<CustomerRegCriticalItem> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids);
        updateWrapper.set("notify_status", 1);
        updateWrapper.set("notify_time", new Date());
        updateWrapper.set("notify_by", sysUser.getUsername());
        updateWrapper.set("notifier", sysUser.getRealname());
        customerRegCriticalItemMapper.update(null, updateWrapper);

        //生成操作记录
        CustomerRegCriticalItem criticalItem = itemList.get(0);
        CriticalDealRecord criticalDealRecord = new CriticalDealRecord();
        criticalDealRecord.setContent(notifyContent);
        criticalDealRecord.setActionMethod(notifyMethod);
        criticalDealRecord.setCustomerRegId(criticalItem.getCustomerRegId());
        criticalDealRecord.setAction(ExConstants.CRITICAL_DEAL_ACTION_NOTIFY);
        criticalDealRecord.setCriticalItemIds(StringUtils.join(ids, ","));
        criticalDealRecord.setCriticalItems(itemList.stream().map(CustomerRegCriticalItem::getItemName).collect(Collectors.joining(",")));
        criticalDealRecord.setCreateBy(sysUser.getUsername());
        criticalDealRecord.setCreateTime(new Date());
        criticalDealRecord.setCreator(sysUser.getRealname());
        criticalDealRecordMapper.insert(criticalDealRecord);

        if (StringUtils.equals(notifyMethod, "短信")) {
            //获取CustomerReg
            String regId = criticalItem.getCustomerRegId();
            CustomerReg customerReg = customerRegMapper.selectById(regId);
            if (customerReg == null) {
                throw new Exception("未找到体检登记记录！");
            }

            SmsResult smsResult = smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "0", customerReg.getPhone(), notifyContent, criticalItem.getId(), ExConstants.SMS_BIZ_TYPE_危急值通知);
            if (!smsResult.isSuccess()) {
                throw new Exception("短信发送失败：" + smsResult.getMsg());
            }
        }
    }

    @Override
    public void postVisitBatch(List<CustomerRegCriticalItem> itemList, String postVisitContent, String postVisitMethod) {
        if (itemList == null || itemList.isEmpty()) {
            return;
        }
        List<String> ids = itemList.stream().map(CustomerRegCriticalItem::getId).collect(Collectors.toList());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        UpdateWrapper<CustomerRegCriticalItem> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids);
        updateWrapper.set("post_visit_status", 1);
        updateWrapper.set("post_visit_time", new Date());
        updateWrapper.set("post_visit_by", sysUser.getUsername());
        updateWrapper.set("post_visitor", sysUser.getRealname());
        customerRegCriticalItemMapper.update(null, updateWrapper);

        //生成操作记录
        CustomerRegCriticalItem criticalItem = itemList.get(0);
        CriticalDealRecord criticalDealRecord = new CriticalDealRecord();
        criticalDealRecord.setCustomerRegId(criticalItem.getCustomerRegId());
        criticalDealRecord.setContent(postVisitContent);
        criticalDealRecord.setAction(ExConstants.CRITICAL_DEAL_ACTION_VISIT);
        criticalDealRecord.setActionMethod(postVisitMethod);
        criticalDealRecord.setCriticalItemIds(StringUtils.join(ids, ","));
        criticalDealRecord.setCriticalItems(itemList.stream().map(CustomerRegCriticalItem::getItemName).collect(Collectors.joining(",")));
        criticalDealRecord.setCreateBy(sysUser.getUsername());
        criticalDealRecord.setCreateTime(new Date());
        criticalDealRecord.setCreator(sysUser.getRealname());
        criticalDealRecordMapper.insert(criticalDealRecord);

        //发送短信
        if (StringUtils.equals(postVisitMethod, "短信")) {
            //获取CustomerReg
            String regId = criticalItem.getCustomerRegId();
            CustomerReg customerReg = customerRegMapper.selectById(regId);
            if (customerReg == null) {
                return;
            }

            SmsResult smsResult = smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "0", customerReg.getPhone(), postVisitContent, criticalItem.getId(), ExConstants.SMS_BIZ_TYPE_危急值通知);
            if (!smsResult.isSuccess()) {
                throw new RuntimeException("短信发送失败：" + smsResult.getMsg());
            }
        }

    }

    @Override
    public void resetBatch(List<String> itemIdList, String actionType) {
        if (itemIdList == null || itemIdList.isEmpty()) {
            return;
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        UpdateWrapper<CustomerRegCriticalItem> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", itemIdList);
        if ("confirm".equals(actionType)) {
            updateWrapper.set("confirm_status", 0);
            updateWrapper.set("confirm_time", null);
            updateWrapper.set("confirm_by", null);
            updateWrapper.set("confirmer", null);
        }
        if ("notify".equals(actionType)) {
            updateWrapper.set("notify_status", 0);
            updateWrapper.set("notify_time", null);
            updateWrapper.set("notify_by", null);
            updateWrapper.set("notifier", null);
        }
        if ("postVisit".equals(actionType)) {
            updateWrapper.set("post_visit_status", 0);
            updateWrapper.set("post_visit_time", null);
            updateWrapper.set("post_visit_by", null);
            updateWrapper.set("post_visitor", null);
        }
        customerRegCriticalItemMapper.update(null, updateWrapper);
    }

    @Override
    public List<CustomerRegCriticalItem> listByNotifyStatus(String notifyStatus, String degree) {
        LambdaQueryWrapper<CustomerRegCriticalItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerRegCriticalItem::getNotifyStatus, notifyStatus).eq(CustomerRegCriticalItem::getSeverityDegree, degree);
        return customerRegCriticalItemMapper.selectList(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doAutoNotify() {
        List<AutoSmsSetting> autoSmsSettings = autoSmsSettingService.listAutoSmsSetting(ExConstants.SMS_BIZ_TYPE_危急值通知);
        if (autoSmsSettings == null || autoSmsSettings.isEmpty()) {
            return;
        }
        AutoSmsSetting autoSmsSetting = autoSmsSettings.get(0);
        String msgTemplate = autoSmsSetting.getTemplateContent();
        List<CustomerRegCriticalItem> criticalItems = listByNotifyStatus("0", autoSmsSetting.getSeverityDegree());
        if (criticalItems == null || criticalItems.isEmpty()) {
            return;
        }
        for (CustomerRegCriticalItem criticalItem : criticalItems) {
            try {
                Date createTime = criticalItem.getCreateTime();
                if (!autoSmsSetting.canSend(createTime)) {
                    continue;
                }

                CustomerReg customerReg = customerRegMapper.getLiteById(criticalItem.getCustomerRegId());
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("name", customerReg.getName());
                dataMap.put("phone", customerReg.getPhone());
                dataMap.put("itemName", criticalItem.getItemName());
                dataMap.put("itemValue", criticalItem.getItemValue());
                dataMap.put("degree", criticalItem.getSeverityDegree());
                dataMap.put("genderTitle", StringUtils.equals(customerReg.getGender(), "男") ? "先生" : (StringUtils.equals(customerReg.getGender(), "女") ? "女士" : ""));
                String smsContent = MustacheUtil.render(msgTemplate, dataMap);
                SmsResult smsResult = smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "1", customerReg.getPhone(), smsContent, criticalItem.getId(), ExConstants.SMS_BIZ_TYPE_危急值通知);
                LambdaUpdateWrapper<CustomerRegCriticalItem> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(CustomerRegCriticalItem::getId, criticalItem.getId());
                if (smsResult.isSuccess()) {
                    updateWrapper.set(CustomerRegCriticalItem::getNotifyStatus, 1);
                    updateWrapper.set(CustomerRegCriticalItem::getNotifyTime, new Date());
                    updateWrapper.set(CustomerRegCriticalItem::getNotifyBy, "system");
                    updateWrapper.set(CustomerRegCriticalItem::getNotifier, "系统");
                    update(updateWrapper);
                }
            } catch (Exception e) {
                log.error("自动通知失败", e);
            }
        }

    }

    @Override
    public void notifyManager() {
        CriticalNotifySetting setting = criticalNotifySettingService.getSetting();
        if (setting == null) {
            return;
        }
        if (!StringUtils.equals(setting.getEnableFlag(), "1")) {
            return;
        }
        String smsTemplate = "有危急值待处理！检客信息：{{name}} {{genderTitle}}，联系电话:{{phone}}，{{{itemInfo}}}。";
        if (StringUtils.isNotBlank(setting.getSmsTemplate())) {
            smsTemplate = setting.getSmsTemplate();
        }
        if (StringUtils.isBlank(setting.getNotifyPhones())) {
            return;
        }
        String phones = StringUtils.replace(setting.getNotifyPhones(), "，", ",");
        List<String> managerPhones = List.of(phones.split(","));
        if (managerPhones.isEmpty()) {
            return;
        }
        //对managerPhones去重，trim
        managerPhones = managerPhones.stream().map(String::trim).distinct().toList();


        String itemInfoTemplate = "危急值类别：{{degree}}，项目名称：{{itemName}}，项目值：{{{itemValue}}}";

        //查询所有未通知的危急值记录
        LambdaQueryWrapper<CustomerRegCriticalItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerRegCriticalItem::getManagerNotified, "0");

        String degree = setting.getDegree();
        if (StringUtils.isNotBlank(degree)) {
            //对degree进行分割,可能是中文逗号，英文逗号
            degree = StringUtils.replace(degree, "，", ",");
            List<String> degreeList = List.of(degree.split(","));
            if (!degreeList.isEmpty()) {
                queryWrapper.in(CustomerRegCriticalItem::getSeverityDegree, degreeList);
            }
        }


        List<CustomerRegCriticalItem> criticalItems = customerRegCriticalItemMapper.selectList(queryWrapper);
        if (criticalItems == null || criticalItems.isEmpty()) {
            return;
        }

        //按照customerRegId进行分组,按照一个人一个人的通知
        Map<String, List<CustomerRegCriticalItem>> regMap = criticalItems.stream().collect(Collectors.groupingBy(CustomerRegCriticalItem::getCustomerRegId));
        for (Map.Entry<String, List<CustomerRegCriticalItem>> entry : regMap.entrySet()) {
            String regId = entry.getKey();
            List<CustomerRegCriticalItem> itemList = entry.getValue();
            CustomerReg customerReg = customerRegMapper.getLiteById(regId);
            if (customerReg == null) {
                continue;
            }
            Map<String, Object> smsData = new HashMap<>();
            smsData.put("name", customerReg.getName());
            smsData.put("phone", customerReg.getPhone());
            smsData.put("genderTitle", StringUtils.equals(customerReg.getGender(), "男") ? "先生" : (StringUtils.equals(customerReg.getGender(), "女") ? "女士" : ""));
            //拼接危急值内容
            StringBuilder itemInfo = new StringBuilder();
            for (CustomerRegCriticalItem criticalItem : itemList) {
                Map<String, Object> itemData = new HashMap<>();
                itemData.put("itemName", criticalItem.getItemName());
                itemData.put("itemValue", criticalItem.getItemValue());
                itemData.put("degree", criticalItem.getSeverityDegree());
                itemInfo.append(MustacheUtil.render(itemInfoTemplate, itemData));
            }
            smsData.put("itemInfo", itemInfo.toString());
            String smsContent = MustacheUtil.render(smsTemplate, smsData);

            for (String phone : managerPhones) {
                SmsResult smsResult = smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "1", phone, smsContent, regId, ExConstants.SMS_BIZ_TYPE_危急值通知);
                if (smsResult.isSuccess()) {
                    LambdaUpdateWrapper<CustomerRegCriticalItem> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(CustomerRegCriticalItem::getCustomerRegId, regId);
                    updateWrapper.set(CustomerRegCriticalItem::getManagerNotified, "1");
                    update(updateWrapper);
                }
            }
        }
    }


}
