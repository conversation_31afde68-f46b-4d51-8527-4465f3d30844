package org.jeecg.modules.station.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.station.entity.CriticalDealRecord;
import org.jeecg.modules.station.service.ICriticalDealRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 危急值处理记录
 * @Author: jeecg-boot
 * @Date:   2024-05-14
 * @Version: V1.0
 */
@Api(tags="危急值处理记录")
@RestController
@RequestMapping("/station/criticalDealRecord")
@Slf4j
public class CriticalDealRecordController extends JeecgController<CriticalDealRecord, ICriticalDealRecordService> {
	@Autowired
	private ICriticalDealRecordService criticalDealRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param criticalDealRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "危急值处理记录-分页列表查询")
	@ApiOperation(value="危急值处理记录-分页列表查询", notes="危急值处理记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CriticalDealRecord>> queryPageList(CriticalDealRecord criticalDealRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CriticalDealRecord> queryWrapper = QueryGenerator.initQueryWrapper(criticalDealRecord, req.getParameterMap());
		Page<CriticalDealRecord> page = new Page<CriticalDealRecord>(pageNo, pageSize);
		if(StringUtils.isBlank(criticalDealRecord.getCustomerRegId()))
		{
			return Result.OK(page);
		}
		IPage<CriticalDealRecord> pageList = criticalDealRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param criticalDealRecord
	 * @return
	 */
	@AutoLog(value = "危急值处理记录-添加")
	@ApiOperation(value="危急值处理记录-添加", notes="危急值处理记录-添加")
	//@RequiresPermissions("station:critical_deal_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CriticalDealRecord criticalDealRecord) {
		criticalDealRecordService.save(criticalDealRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param criticalDealRecord
	 * @return
	 */
	@AutoLog(value = "危急值处理记录-编辑")
	@ApiOperation(value="危急值处理记录-编辑", notes="危急值处理记录-编辑")
	@RequiresPermissions("station:critical_deal_record:edit")
	//@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CriticalDealRecord criticalDealRecord) {
		criticalDealRecordService.updateById(criticalDealRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "危急值处理记录-通过id删除")
	@ApiOperation(value="危急值处理记录-通过id删除", notes="危急值处理记录-通过id删除")
	//@RequiresPermissions("station:critical_deal_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		criticalDealRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "危急值处理记录-批量删除")
	@ApiOperation(value="危急值处理记录-批量删除", notes="危急值处理记录-批量删除")
	@RequiresPermissions("station:critical_deal_record:deleteBatch")
	//@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.criticalDealRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "危急值处理记录-通过id查询")
	@ApiOperation(value="危急值处理记录-通过id查询", notes="危急值处理记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CriticalDealRecord> queryById(@RequestParam(name="id",required=true) String id) {
		CriticalDealRecord criticalDealRecord = criticalDealRecordService.getById(id);
		if(criticalDealRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(criticalDealRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param criticalDealRecord
    */
    @RequiresPermissions("station:critical_deal_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CriticalDealRecord criticalDealRecord) {
        return super.exportXls(request, criticalDealRecord, CriticalDealRecord.class, "危急值处理记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("station:critical_deal_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CriticalDealRecord.class);
    }

}
