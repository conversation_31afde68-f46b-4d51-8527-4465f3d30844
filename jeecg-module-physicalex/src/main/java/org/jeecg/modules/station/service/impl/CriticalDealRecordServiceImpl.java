package org.jeecg.modules.station.service.impl;

import org.jeecg.modules.station.entity.CriticalDealRecord;
import org.jeecg.modules.station.mapper.CriticalDealRecordMapper;
import org.jeecg.modules.station.service.ICriticalDealRecordService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 危急值处理记录
 * @Author: jeecg-boot
 * @Date:   2024-05-14
 * @Version: V1.0
 */
@Service
public class CriticalDealRecordServiceImpl extends ServiceImpl<CriticalDealRecordMapper, CriticalDealRecord> implements ICriticalDealRecordService {

}
