package org.jeecg.modules.station.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 危急值处理记录
 * @Author: jeecg-boot
 * @Date: 2024-05-14
 * @Version: V1.0
 */
@Data
@TableName("critical_deal_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "critical_deal_record对象", description = "危急值处理记录")
public class CriticalDealRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String creator;
    /**
     * 创建人账号
     */
    @ApiModelProperty(value = "创建人账号")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 登记档案ID
     */
    @Excel(name = "登记档案ID", width = 15)
    @ApiModelProperty(value = "登记档案ID")
    private java.lang.String customerRegId;
    /**
     * 内容
     */
    @Excel(name = "内容", width = 15)
    @ApiModelProperty(value = "内容")
    private java.lang.String content;
    /**
     * 动作
     */
    @Excel(name = "动作", width = 15)
    @ApiModelProperty(value = "动作")
    private java.lang.String action;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**
     * 关联危急值项目ID
     */
    @Excel(name = "关联危急值项目ID", width = 15)
    @ApiModelProperty(value = "关联危急值项目ID")
    private java.lang.String criticalItemIds;

    /**
     * 关联危急值项目ID
     */
    @Excel(name = "关联危急值项目", width = 15)
    @ApiModelProperty(value = "关联危急值项目")
    private java.lang.String criticalItems;
    /**
     * 采用方式
     */
    @Excel(name = "采用方式", width = 15)
    @ApiModelProperty(value = "采用方式")
    private java.lang.String actionMethod;
}
