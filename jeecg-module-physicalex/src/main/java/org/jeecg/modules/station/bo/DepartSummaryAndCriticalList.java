package org.jeecg.modules.station.bo;

import lombok.Data;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;

import java.util.List;

@Data
public class DepartSummaryAndCriticalList {
    private List<String> summaryList;
    private List<String> pureSummaryList;
    private List<CustomerRegCriticalItem> criticalItemList;
    private String summaryAbnormalFlag;
    //private List<ItemStandard> matchedStandardList;
}
