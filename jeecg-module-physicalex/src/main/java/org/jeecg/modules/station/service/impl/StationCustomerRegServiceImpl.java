package org.jeecg.modules.station.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.reg.bo.DepartAndGroupBean;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.station.bo.DepartStat;
import org.jeecg.modules.station.bo.StatusStat;
import org.jeecg.modules.station.mapper.StationCustomerRegMapper;
import org.jeecg.modules.station.service.IStationCustomerRegService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Service
public class StationCustomerRegServiceImpl extends ServiceImpl<CustomerRegMapper, CustomerReg> implements IStationCustomerRegService {
    @Autowired
    private StationCustomerRegMapper stationCustomerRegMapper;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private CommonAPI commonAPI;
    @Autowired
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;

    @Override
    public void pageCustomerReg(Page<CustomerReg> page, List<String> departmentIds, String name, String gender, String idCard, String phone, String regDateStart, String regDateEnd, String examNo, String examCardNo, String checkState, String companyRegId, String teamId, String itemGroupId, String itemId, String changeApplyStatus) {
        stationCustomerRegMapper.pageCustomerReg(page, departmentIds, name, gender, idCard, phone, regDateStart, regDateEnd, examNo, examCardNo, checkState, companyRegId, teamId, itemGroupId, itemId, changeApplyStatus);

        if (page.getRecords().isEmpty()) {
            return;
        }
        List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("checkStatus");

        page.getRecords().forEach(customerReg -> {
            if (StringUtils.isNotBlank(customerReg.getRiskFactor())) {
                List<String> riskFactorIds = Arrays.asList(customerReg.getRiskFactor().split(","));
                List<ZyRiskFactor> zyRiskFactors = zyRiskFactorMapper.selectBatchIds(riskFactorIds);
                customerReg.setRiskFactorList(zyRiskFactors);
            }

            List<StatusStat> statusStatList = customerRegItemGroupMapper.statCustomerRegItemGroupStatus(customerReg.getId(), departmentIds);
            StatusStat abandoned = customerRegItemGroupMapper.statCustomerRegItemGroupAbandoned(customerReg.getId(), departmentIds);
            if (abandoned.getCount() > 0) {
                statusStatList.add(abandoned);
            }

            statusStatList.forEach(statusStat -> {
                String valueToSearch = statusStat.getStatus(); // 替换为你要搜索的值
                Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> valueToSearch.equals(dictModel.getValue())).findFirst();

                if (optionalDictModel.isPresent()) {
                    String color = optionalDictModel.get().getColor();
                    statusStat.setColor(color);
                }
            });

            customerReg.setStatusStatList(statusStatList);
        });
    }

    @Override
    public CustomerReg getRegById(String regId, List<String> departmentIds) {

        CustomerReg customerReg = customerRegMapper.selectById(regId);
        if (StringUtils.isNotBlank(customerReg.getRiskFactor())) {
            List<String> riskFactorIds = Arrays.asList(customerReg.getRiskFactor().split(","));
            List<ZyRiskFactor> zyRiskFactors = zyRiskFactorMapper.selectBatchIds(riskFactorIds);
            customerReg.setRiskFactorList(zyRiskFactors);
        }

        List<StatusStat> statusStatList = customerRegItemGroupMapper.statCustomerRegItemGroupStatus(customerReg.getId(), departmentIds);
        StatusStat abandoned = customerRegItemGroupMapper.statCustomerRegItemGroupAbandoned(customerReg.getId(), departmentIds);
        if (abandoned.getCount() > 0) {
            statusStatList.add(abandoned);
        }

        List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("checkStatus");
        statusStatList.forEach(statusStat -> {
            String valueToSearch = statusStat.getStatus(); // 替换为你要搜索的值
            Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> valueToSearch.equals(dictModel.getValue())).findFirst();

            if (optionalDictModel.isPresent()) {
                String color = optionalDictModel.get().getColor();
                statusStat.setColor(color);
            }
        });

        customerReg.setStatusStatList(statusStatList);

        return customerReg;
    }

    @Override
    public List<SysDepart> getDepartsByUser(String userId) {

        return customerRegMapper.getDepartsByUser(userId);
    }

    @Override
    public List<DepartStat> checkStatGroupByDepart(String customerRegId) {

        List<DepartStat> list = customerRegItemGroupMapper.checkStatGroupByDepart(customerRegId);
        for (DepartStat departStat : list) {
            SysDepart depart = sysDepartMapper.selectById(departStat.getDepartmentId());
            departStat.setDepart(depart);
        }
        //按照depart的guideOrder排序
        list.sort(Comparator.comparingInt(o -> o.getDepart().getGuideSort()));

        return list;
    }
}
