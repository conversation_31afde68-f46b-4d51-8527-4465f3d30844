package org.jeecg.modules.station.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 科室提醒
 * @Author: jeecg-boot
 * @Date:   2024-05-08
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_depart_tip")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer_reg_depart_tip对象", description="科室提醒")
public class CustomerRegDepartTip implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人账号*/
    @ApiModelProperty(value = "创建人账号")
    private java.lang.String createBy;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private java.lang.String creator;
	/**创建科室*/
	@Excel(name = "创建科室", width = 15)
    @ApiModelProperty(value = "创建科室")
    private java.lang.String departmentName;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**提醒内容*/
	@Excel(name = "提醒内容", width = 15)
    @ApiModelProperty(value = "提醒内容")
    private java.lang.String content;
	/**创建科室ID*/
	@Excel(name = "创建科室ID", width = 15)
    @ApiModelProperty(value = "创建科室ID")
    private java.lang.String departmentId;
	/**登记记录ID*/
	@Excel(name = "登记记录ID", width = 15)
    @ApiModelProperty(value = "登记记录ID")
    private java.lang.String customerRegId;
}
