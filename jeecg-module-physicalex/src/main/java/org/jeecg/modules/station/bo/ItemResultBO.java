package org.jeecg.modules.station.bo;

import lombok.Data;
import org.jeecg.modules.basicinfo.entity.DiagnosisComplex;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.entity.ItemStandard;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

@Data
public class ItemResultBO {
    private CustomerRegItemResult customerRegItemResult;
    private ItemStandard itemStandard;
    private ItemInfo itemInfo;
    //private DiagnosisComplex diagnosisComplex;
}
