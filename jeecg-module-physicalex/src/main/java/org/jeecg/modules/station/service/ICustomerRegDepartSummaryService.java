package org.jeecg.modules.station.service;

import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.station.bo.ComplexDiagnosticResult;
import org.jeecg.modules.station.bo.DepartSummary;
import org.jeecg.modules.station.bo.DepartSummaryAndCriticalList;
import org.jeecg.modules.station.dto.AbnormalSummary;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

import java.util.List;

/**
 * @Description: 科室小结
 * @Author: jeecg-boot
 * @Date:   2024-04-19
 * @Version: V1.0
 */
public interface ICustomerRegDepartSummaryService extends IService<CustomerRegDepartSummary> {

    void removeDepartmentSummary(String id) throws Exception;

    List<ComplexDiagnosticResult> getDiagnosticByComplex(String departmentId, CustomerReg reg, List<CustomerRegItemResult> itemResultList,String severityDegree);

    DepartSummary generateSummary(String departmentId, CustomerReg reg, List<CustomerRegItemResult> itemResultList);

    List<String> generateAbnormalSummaryList(String customerRegId);

    List<AbnormalSummary> generateAbnormalSummaryBeanList(String customerRegId);

    DepartSummary generateAndSaveSummary(String departmentId, CustomerReg reg);

    CustomerRegDepartSummary getOneByDepartmentIdAndRegId(String departmentId, String regId);

    void updateSummary(String departmentId, String customerRegId, String characterSummary,String updateByManual) throws Exception;

    DepartSummaryAndCriticalList genetateDepartSummaryAndCriticalItem(String departmentId, CustomerReg reg, List<CustomerRegItemResult> itemResultList);

    List<CustomerRegCriticalItem> generateCriticalItem(String departmentId, CustomerReg reg, List<CustomerRegItemResult> itemResultList);

    List<CustomerRegDepartSummary> listByCustomerReg(String customerRegId,String abnormalFlag);

    boolean isSummaryAudited(String customerRegId);

    void doAutoGenerateDepartSummaryJob() throws Exception;
}
