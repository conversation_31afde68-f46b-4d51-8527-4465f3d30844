package org.jeecg.modules.station.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.station.entity.CriticalNotifySetting;
import org.jeecg.modules.station.service.ICriticalNotifySettingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 危急值通知设置
 * @Author: jeecg-boot
 * @Date:   2025-01-11
 * @Version: V1.0
 */
@Api(tags="危急值通知设置")
@RestController
@RequestMapping("/station/criticalNotifySetting")
@Slf4j
public class CriticalNotifySettingController extends JeecgController<CriticalNotifySetting, ICriticalNotifySettingService> {
	@Autowired
	private ICriticalNotifySettingService criticalNotifySettingService;

	 /**
	  * 获取唯一的一条记录
	  */
	 @AutoLog(value = "危急值通知设置-获取唯一的一条记录")
	 @ApiOperation(value="危急值通知设置-获取唯一的一条记录", notes="危急值通知设置-获取唯一的一条记录")
	 @GetMapping(value = "/getSetting")
	 public Result<CriticalNotifySetting> getSetting() {
		 CriticalNotifySetting setting = criticalNotifySettingService.getSetting();
		 return Result.OK(setting);
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param criticalNotifySetting
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "危急值通知设置-分页列表查询")
	@ApiOperation(value="危急值通知设置-分页列表查询", notes="危急值通知设置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CriticalNotifySetting>> queryPageList(CriticalNotifySetting criticalNotifySetting,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CriticalNotifySetting> queryWrapper = QueryGenerator.initQueryWrapper(criticalNotifySetting, req.getParameterMap());
		Page<CriticalNotifySetting> page = new Page<CriticalNotifySetting>(pageNo, pageSize);
		IPage<CriticalNotifySetting> pageList = criticalNotifySettingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param criticalNotifySetting
	 * @return
	 */
	@AutoLog(value = "危急值通知设置-添加")
	@ApiOperation(value="危急值通知设置-添加", notes="危急值通知设置-添加")
	@RequiresPermissions("station:critical_notify_setting:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CriticalNotifySetting criticalNotifySetting) {
		criticalNotifySettingService.save(criticalNotifySetting);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param criticalNotifySetting
	 * @return
	 */
	@AutoLog(value = "危急值通知设置-编辑")
	@ApiOperation(value="危急值通知设置-编辑", notes="危急值通知设置-编辑")
	@RequiresPermissions("station:critical_notify_setting:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CriticalNotifySetting criticalNotifySetting) {
		criticalNotifySettingService.updateById(criticalNotifySetting);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "危急值通知设置-通过id删除")
	@ApiOperation(value="危急值通知设置-通过id删除", notes="危急值通知设置-通过id删除")
	@RequiresPermissions("station:critical_notify_setting:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		criticalNotifySettingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "危急值通知设置-批量删除")
	@ApiOperation(value="危急值通知设置-批量删除", notes="危急值通知设置-批量删除")
	@RequiresPermissions("station:critical_notify_setting:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.criticalNotifySettingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "危急值通知设置-通过id查询")
	@ApiOperation(value="危急值通知设置-通过id查询", notes="危急值通知设置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CriticalNotifySetting> queryById(@RequestParam(name="id",required=true) String id) {
		CriticalNotifySetting criticalNotifySetting = criticalNotifySettingService.getById(id);
		if(criticalNotifySetting==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(criticalNotifySetting);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param criticalNotifySetting
    */
    @RequiresPermissions("station:critical_notify_setting:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CriticalNotifySetting criticalNotifySetting) {
        return super.exportXls(request, criticalNotifySetting, CriticalNotifySetting.class, "危急值通知设置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("station:critical_notify_setting:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CriticalNotifySetting.class);
    }

}
