package org.jeecg.modules.station.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.station.service.ICustomerRegCriticalItemService;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoSendCriticelNotifyJob implements Job {

    @Autowired
    private ICustomerRegCriticalItemService customerRegCriticalItemService;

    private final Lock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                customerRegCriticalItemService.doAutoNotify();
            } catch (Exception e) {
                log.error("向检客发送危急值通知短信异常", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("向检客发送危急值通知短信异常！");
        }
    }
}
