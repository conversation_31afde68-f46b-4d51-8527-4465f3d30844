package org.jeecg.modules.station.service;

import org.jeecg.modules.station.entity.CustomerRegCriticalItem;
import org.jeecg.modules.station.entity.CustomerRegCritical;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.beans.factory.annotation.Autowired;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 危急值记录
 * @Author: jeecg-boot
 * @Date:   2024-04-26
 * @Version: V1.0
 */
public interface ICustomerRegCriticalService extends IService<CustomerRegCritical> {

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

}
