package org.jeecg.modules.station.bo;

import lombok.Data;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;

import java.util.List;

@Data
public class AbnormalSummaryBean {
    private String title;
    private Integer order;
    private String format;
    private String summaryText;
    private String itemGroupFormat;
    private List<CustomerRegItemGroup> itemGroupList;
    private Boolean abnormalSettingFlag;
    private String itemOrGroup;
    private String abnormalJudgeExpression;
    private String disgnoseSeparator;
}
