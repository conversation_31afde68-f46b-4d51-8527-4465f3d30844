package org.jeecg.modules.station.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 危急值记录关联项目
 * @Author: jeecg-boot
 * @Date: 2024-04-26
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_critical_item")
@ApiModel(value = "customer_reg_critical_item对象", description = "危急值记录关联项目")
public class CustomerRegCriticalItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
    /**
     * 体检登记
     */
    @Excel(name = "体检登记", width = 15)
    @ApiModelProperty(value = "体检登记")
    private java.lang.String customerRegId;
    /**
     * 所属记录ID
     */
    @ApiModelProperty(value = "所属记录ID")
    private java.lang.String criticalId;
    /**
     * 触发类型：复合判断、项目参考值
     */
    @Excel(name = "触发类型", width = 15)
    @ApiModelProperty(value = "触发类型")
    private java.lang.String triggerType;
    /**
     * 所属复合判断
     */
    @Excel(name = "所属复合判断", width = 15)
    @ApiModelProperty(value = "所属复合判断")
    private java.lang.String diagnosisComplexId;
    /**
     * 复合判断关联的项目结果
     */
    @Excel(name = "复合判断关联的项目结果", width = 15)
    @ApiModelProperty(value = "复合判断关联的项目结果")
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler.class)
    private List<CustomerRegItemResult> complexItemResults;
    /**
     * 所属组合ID
     */
    @Excel(name = "所属组合ID", width = 15)
    @ApiModelProperty(value = "所属组合ID")
    private java.lang.String itemGroupId;
    /**
     * 所属组合
     */
    @Excel(name = "所属组合", width = 15)
    @ApiModelProperty(value = "所属组合")
    private java.lang.String itemGroupName;
    /**
     * 所属项目ID
     */
    @Excel(name = "所属项目ID", width = 15)
    @ApiModelProperty(value = "所属项目ID")
    private java.lang.String itemId;
    /**
     * 所属项目
     */
    @Excel(name = "所属项目", width = 15)
    @ApiModelProperty(value = "所属项目")
    private java.lang.String itemName;
    /**
     * 项目结果ID
     */
    @Excel(name = "项目结果ID", width = 15)
    @ApiModelProperty(value = "项目结果ID")
    private java.lang.String itemResultId;
    /**
     * 项目结果值
     */
    @Excel(name = "项目结果值", width = 15)
    @ApiModelProperty(value = "项目结果值")
    private java.lang.String itemValue;
    /**
     * 项目结果参考ID
     */
    @Excel(name = "项目结果参考ID", width = 15)
    @ApiModelProperty(value = "项目结果参考ID")
    private java.lang.String itemStandardId;
    /**
     * 来源
     */
    @Excel(name = "来源", width = 15)
    @ApiModelProperty(value = "来源")
    private java.lang.String source;
    /**
     * 程度分类
     */
    @Excel(name = "程度分类", width = 15)
    @Dict(dicCode = "severity_degree")
    @ApiModelProperty(value = "程度分类")
    private java.lang.String severityDegree;
    /**
     * 建议
     */
    @Excel(name = "建议", width = 15)
    @ApiModelProperty(value = "建议")
    private java.lang.String advice;
    /**
     * 确认状态
     */
    @Excel(name = "确认状态", width = 15)
    @ApiModelProperty(value = "确认状态")
    private java.lang.String confirmStatus;
    /**
     * 确认时间
     */
    @Excel(name = "确认时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "确认时间")
    private java.util.Date confirmTime;
    /**
     * 确认人账号
     */
    @Excel(name = "确认人账号", width = 15)
    @ApiModelProperty(value = "确认人账号")
    private java.lang.String confirmBy;
    /**
     * 确认人
     */
    @Excel(name = "确认人", width = 15)
    @ApiModelProperty(value = "确认人")
    private java.lang.String confirmer;

    /**
     * 通知状态
     */
    @Excel(name = "通知状态", width = 15)
    @ApiModelProperty(value = "通知状态")
    private java.lang.String notifyStatus;
    /**
     * 通知时间
     */
    @Excel(name = "通知时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "通知时间")
    private java.util.Date notifyTime;
    /**
     * 通知人账号
     */
    @Excel(name = "通知人账号", width = 15)
    @ApiModelProperty(value = "通知人账号")
    private java.lang.String notifyBy;
    /**
     * 通知人
     */
    @Excel(name = "通知人", width = 15)
    @ApiModelProperty(value = "通知人")
    private java.lang.String notifier;
    /**
     * 通知方式
     */
    @Excel(name = "通知方式", width = 15)
    @ApiModelProperty(value = "通知方式")
    private java.lang.String notifyMethod;
    /**
     * 通知内容
     */
    @Excel(name = "通知内容", width = 15)
    @ApiModelProperty(value = "通知内容")
    private java.lang.String notifyContent;


    /**
     * 回访时间
     */
    @Excel(name = "回访时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "回访时间")
    private java.util.Date postVisitTime;
    /**
     * 回访状态
     */
    @Excel(name = "回访状态", width = 15)
    @ApiModelProperty(value = "回访状态")
    private java.lang.String postVisitStatus;
    /**
     * 回访结果
     */
    @Excel(name = "回访结果", width = 15)
    @ApiModelProperty(value = "回访结果")
    private java.lang.String postVistResult;
    /**
     * 回访人账号
     */
    @Excel(name = "回访人账号", width = 15)
    @ApiModelProperty(value = "回访人账号")
    private java.lang.String postVisitBy;
    /**
     * 回访人
     */
    @Excel(name = "回访人", width = 15)
    @ApiModelProperty(value = "回访人")
    private java.lang.String postVisitor;
    /**
     * 依据
     */
    @Excel(name = "依据", width = 15)
    @ApiModelProperty(value = "依据")
    private java.lang.String baseOn;

    /**
     * 有效状态
     */
    @Excel(name = "有效状态", width = 15)
    private String validStatus;

    @TableLogic
    private Integer delFlag;

    /**
     * 项目所属科室ID
     */
    @Excel(name = "项目所属科室ID", width = 15)
    @ApiModelProperty(value = "项目所属科室ID")
    private String departmentId;

    /**
     * 体检号
     */
    @Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private String examNo;

    /**
     * 是否已通知管理人员
     */
    @Excel(name = "是否已通知管理人员", width = 15)
    @ApiModelProperty(value = "是否已通知管理人员")
    private String managerNotified;

    /**
     * 检查部位编码
     */
    @Excel(name = "检查部位编码", width = 15)
    @ApiModelProperty(value = "检查部位编码")
    private String checkPartCode;

    /**
     * 检查部位名称
     */
    @Excel(name = "检查部位名称", width = 15)
    @ApiModelProperty(value = "检查部位名称")
    private String checkPartName;

}
