<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.station.mapper.CustomerRegDepartSummaryMapper">

    <select id="listByCustomerReg" resultType="org.jeecg.modules.station.entity.CustomerRegDepartSummary">
        select * from customer_reg_depart_summary s join sys_depart d on s.department_id = d.id where s.customer_reg_id = #{customerRegId} <if test="abnormalFlag!=null and abnormalFlag!=''"> and s.abnormal_flag=#{abnormalFlag}</if> order by d.summary_sort
    </select>

    <select id="listByCustomerReg4Report" resultType="org.jeecg.modules.station.entity.CustomerRegDepartSummary">
        select * from customer_reg_depart_summary s join sys_depart d on s.department_id = d.id where s.customer_reg_id = #{customerRegId} <if test="abnormalFlag!=null and abnormalFlag!=''"> and s.abnormal_flag=#{abnormalFlag}</if> order by d.summary_sort
    </select>
    <select id="listNoSummaryCustomerReg" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select * from customer_reg  c where id not in (select customer_reg_id from customer_reg_depart_summary ds join sys_depart d on ds.department_id = d.id where ds.customer_reg_id = c.id  and d.auto_summary   = 1)
    </select>
</mapper>