package org.jeecg.modules.station.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 科室小结
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_depart_summary")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "customer_reg_depart_summary对象", description = "科室小结")
public class CustomerRegDepartSummary implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 体检登记ID
     */
    @Excel(name = "体检登记ID", width = 15)
    @ApiModelProperty(value = "体检登记ID")
    private java.lang.String customerRegId;
    /**
     * 科室ID
     */
    @Excel(name = "科室ID", width = 15)
    @ApiModelProperty(value = "科室ID")
    private java.lang.String departmentId;
    /**
     * 体征总结
     */
    @Excel(name = "体征总结", width = 15)
    @ApiModelProperty(value = "体征总结")
    private java.lang.String characterSummary;
    private String pureSummary;
    /**
     * 诊断总结
     */
    @Excel(name = "诊断总结", width = 15)
    @ApiModelProperty(value = "诊断总结")
    private java.lang.String diagnosisSummary;
    /**
     * 科室名称
     */
    @Excel(name = "科室名称", width = 15)
    @ApiModelProperty(value = "科室名称")
    private java.lang.String departmentName;
    /**
     * 小结医生ID
     */
    @ApiModelProperty(value = "小结医生ID")
    private java.lang.String createBy;

    @TableField(exist = false)
    private java.lang.String creatorSignPic;
    /**
     * 小结医生
     */
    @Excel(name = "小结医生", width = 15)
    @ApiModelProperty(value = "小结医生")
    private java.lang.String creatorName;
    /**
     * 检查时间
     */
    @Excel(name = "检查时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "检查时间")
    private java.util.Date checkDate;
    /**
     * 检查医生ID
     */
    @Excel(name = "检查医生ID", width = 15)
    @ApiModelProperty(value = "检查医生ID")
    private java.lang.String checkBy;
    /**
     * 检查医生
     */
    @Excel(name = "检查医生", width = 15)
    @ApiModelProperty(value = "检查医生")
    private java.lang.String checkerName;
    /**
     * 审核医生ID
     */
    @Excel(name = "审核医生ID", width = 15)
    @ApiModelProperty(value = "审核医生ID")
    private java.lang.String auditBy;
    /**
     * 审核状态
     */
    @Excel(name = "审核状态", width = 15)
    @ApiModelProperty(value = "审核状态")
    private java.lang.String auditStatus;
    /**
     * 审核医生
     */
    @Excel(name = "审核医生", width = 15)
    @ApiModelProperty(value = "审核医生")
    private java.lang.String auditorName;

    @TableField(exist = false)
    private java.lang.String auditorSignPic;
    /**
     * 审核时间
     */
    @Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private java.util.Date auditTime;

    private java.util.Date createTime;

    private java.util.Date updateTime;

    private java.lang.String updateBy;

    private String updateByManual;

    private String abnormalFlag;

    @TableField(exist = false)
    private Integer sort;

}
