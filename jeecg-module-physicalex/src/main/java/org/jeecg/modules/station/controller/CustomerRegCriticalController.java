package org.jeecg.modules.station.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.station.entity.CustomerRegCritical;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;
import org.jeecg.modules.station.service.ICustomerRegCriticalItemService;
import org.jeecg.modules.station.service.ICustomerRegCriticalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 危急值记录
 * @Author: jeecg-boot
 * @Date: 2024-04-26
 * @Version: V1.0
 */
@Api(tags = "危急值记录")
@RestController
@RequestMapping("/station/customerRegCritical")
@Slf4j
public class CustomerRegCriticalController extends JeecgController<CustomerRegCritical, ICustomerRegCriticalService> {

    @Autowired
    private ICustomerRegCriticalService customerRegCriticalService;

    @Autowired
    private ICustomerRegCriticalItemService customerRegCriticalItemService;


    /*---------------------------------主表处理-begin-------------------------------------*/

    /**
     * 查询有危急值的登记列表
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "危急值记录-查询有危急值的登记列表", notes = "危急值记录-查询有危急值的登记列表")
    @GetMapping(value = "/customerRegList")
    public Result<IPage<CustomerReg>> customerRegList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);

        String name = StringUtils.trimToNull(req.getParameter("name"));
        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String phone = StringUtils.trimToNull(req.getParameter("phone"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String examCardNo = StringUtils.trimToNull(req.getParameter("examCardNo"));
        String nofityStatus = StringUtils.trimToNull(req.getParameter("nofityStatus"));
        String postVisitStatus = StringUtils.trimToNull(req.getParameter("postVisitStatus"));
        String timeProperty = StringUtils.trimToNull(req.getParameter("timeProperty"));
        String startTime = StringUtils.trimToNull(req.getParameter("startTime"));
        String endTime = StringUtils.trimToNull(req.getParameter("endTime"));

        customerRegCriticalItemService.pageCustomerReg(page, name, idCard, phone, examNo, examCardNo, nofityStatus, postVisitStatus, timeProperty, startTime, endTime);
        return Result.OK(page);
    }

    /**
     * 通过登记ID查询
     *
     * @return
     */
    //@AutoLog(value = "危急值记录关联项目-通过主表ID查询")
    @ApiOperation(value = "危急值记录关联项目-通过登记ID查询", notes = "危急值记录关联项目-通过登记ID查询")
    @GetMapping(value = "/pageCriticalItemByRegId")
    public Result<IPage<CustomerRegCriticalItem>> pageCriticalItemByRegId(CustomerRegCriticalItem customerRegCriticalItem, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CustomerRegCriticalItem> queryWrapper = QueryGenerator.initQueryWrapper(customerRegCriticalItem, req.getParameterMap());
        Page<CustomerRegCriticalItem> page = new Page<CustomerRegCriticalItem>(pageNo, pageSize);
        if (StringUtils.isBlank(customerRegCriticalItem.getCustomerRegId())) {
            return Result.OK(page);
        }
        IPage<CustomerRegCriticalItem> pageList = customerRegCriticalItemService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "危急值记录关联项目-通过登记ID查询", notes = "危急值记录关联项目-通过登记ID查询")
    @GetMapping(value = "/listCriticalItemByRegId")
    public Result<?> listCriticalItemByRegId(CustomerRegCriticalItem customerRegCriticalItem, HttpServletRequest req) {
        QueryWrapper<CustomerRegCriticalItem> queryWrapper = QueryGenerator.initQueryWrapper(customerRegCriticalItem, req.getParameterMap());
        if (StringUtils.isBlank(customerRegCriticalItem.getCustomerRegId())) {
            return Result.OK(Collections.emptyList());
        }
        List<CustomerRegCriticalItem> list = customerRegCriticalItemService.list(queryWrapper);
        return Result.OK(list);
    }

    /**
     * 根据登记ID查询
     *
     * @return
     */
    //@AutoLog(value = "危急值记录-分页列表查询")
    @ApiOperation(value = "危急值记录-根据登记ID查询", notes = "危急值记录-根据登记ID查询")
    @GetMapping(value = "/listByReg")
    public Result<?> listByReg(String regId) {
        List<CustomerRegCriticalItem> criticalItems = customerRegCriticalItemService.listByReg(regId, 1);
        return Result.OK(criticalItems);
    }


    /**
     * 批量确认
     *
     * @return
     */
    @AutoLog(value = "危急值记录-确认")
    @ApiOperation(value = "危急值记录-确认", notes = "危急值记录-确认")
    //@RequiresPermissions("station:customer_reg_critical:confirm")
    @PostMapping(value = "/confirm")
    public Result<String> confirm(@RequestBody JSONObject info) {
        JSONArray itemList = info.getJSONArray("itemList");
        if (itemList == null || itemList.size() == 0) {
            return Result.OK("没有需要确认的危急值！");
        }
        List<CustomerRegCriticalItem> items = itemList.toJavaList(CustomerRegCriticalItem.class);
        customerRegCriticalItemService.confirmBatch(items);
        return Result.OK("操作成功！");
    }


    @AutoLog(value = "危急值记录-通知")
    @ApiOperation(value = "危急值记录-通知", notes = "危急值记录-通知")
    @PostMapping(value = "/notify")
    public Result<String> notify(@RequestBody JSONObject info) {
        JSONArray itemList = info.getJSONArray("itemList");
        String content = info.getString("content");
        String method = info.getString("method");
        if (itemList == null || itemList.isEmpty()) {
            return Result.OK("没有需要通知的危急值！");
        }
        List<CustomerRegCriticalItem> items = itemList.toJavaList(CustomerRegCriticalItem.class);
        try {
            customerRegCriticalItemService.notifyBatch(items, content, method);
            return Result.OK("操作成功！");
        } catch (Exception e) {
            return Result.error("操作失败！详细原因：" + e.getMessage());
        }
    }

    @AutoLog(value = "危急值记录-访问")
    @ApiOperation(value = "危急值记录-访问", notes = "危急值记录-访问")
    @PostMapping(value = "/visit")
    public Result<String> visit(@RequestBody JSONObject info) {
        JSONArray itemList = info.getJSONArray("itemList");
        if (itemList == null || itemList.isEmpty()) {
            return Result.OK("没有需要访问的危急值！");
        }
        String content = info.getString("content");
        String method = info.getString("method");
        List<CustomerRegCriticalItem> items = itemList.toJavaList(CustomerRegCriticalItem.class);
        customerRegCriticalItemService.postVisitBatch(items, content, method);
        return Result.OK("操作成功！");
    }

    //resetBatch
    @AutoLog(value = "危急值记录-重置")
    @ApiOperation(value = "危急值记录-重置", notes = "危急值记录-重置")
    @PostMapping(value = "/reset")
    public Result<String> reset(@RequestBody JSONObject info) {
        JSONArray itemList = info.getJSONArray("itemList");
        if (itemList == null || itemList.size() == 0) {
            return Result.OK("没有需要重置的危急值！");
        }
        List<String> itemIdList = itemList.toJavaList(String.class);
        String actionType = info.getString("action");
        customerRegCriticalItemService.resetBatch(itemIdList, actionType);
        return Result.OK("操作成功！");
    }


}
