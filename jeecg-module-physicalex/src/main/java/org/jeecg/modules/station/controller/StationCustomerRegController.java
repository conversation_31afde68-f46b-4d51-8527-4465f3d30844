package org.jeecg.modules.station.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.bo.DepartStat;
import org.jeecg.modules.station.service.IStationCustomerRegService;
import org.jeecg.modules.system.entity.SysDepart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 工作站客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Api(tags = "工作站客户登记")
@RestController
@RequestMapping("/station/reg")
@Slf4j
public class StationCustomerRegController extends JeecgController<CustomerReg, ICustomerRegService> {
    @Autowired
    private IStationCustomerRegService stationCustomerRegService;
    @Autowired
    private ICustomerRegService customerRegService;

    /**
     * checkStatGroupByDepart
     *
     * @return
     */
    @RequestMapping(value = "/checkStatGroupByDepart", method = RequestMethod.GET)
    public Result<?> checkStatGroupByDepart(String customerRegId) {
        List<DepartStat> departStats = stationCustomerRegService.checkStatGroupByDepart(customerRegId);
        return Result.ok(departStats);
    }


    /**
     * 查询数据 查出我的部门
     *
     * @return
     */
    @RequestMapping(value = "/getDepartOfCurrentUser", method = RequestMethod.GET)
    public Result<?> getMyDeptList() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysDepart> myDepartList = stationCustomerRegService.getDepartsByUser(loginUser.getId());

        return Result.ok(myDepartList);
    }

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "客户登记-分页列表查询", notes = "客户登记-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerReg>> queryPageList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String gender = StringUtils.trimToNull(req.getParameter("gender"));
        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String phone = StringUtils.trimToNull(req.getParameter("phone"));
        String regDateStart = StringUtils.trimToNull(req.getParameter("regDateStart"));
        String regDateEnd = StringUtils.trimToNull(req.getParameter("regDateEnd"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String examCardNo = StringUtils.trimToNull(req.getParameter("examCardNo"));
        String checkState = StringUtils.trimToNull(req.getParameter("checkState"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String itemGroupId = StringUtils.trimToNull(req.getParameter("itemGroupId"));
        String itemId = StringUtils.trimToNull(req.getParameter("itemId"));
        String changeApplyStatus = StringUtils.trimToNull(req.getParameter("changeApplyStatus"));
        String ignoreDepartment = StringUtils.trimToNull(req.getParameter("ignoreDepartment"));
        String departmentId = StringUtils.trimToNull(req.getParameter("departmentId"));

        //如果examNo不为空，忽略时间段
        if (StringUtils.isNotBlank(examNo)) {
            regDateStart = null;
            regDateEnd = null;
        }


        if (!StringUtils.equals(ignoreDepartment, "1") && StringUtils.isBlank(departmentId)) {
            return Result.OK(page);
        }
        List<String> departmentIds = null;
        if (StringUtils.isNotBlank(departmentId)) {
            departmentIds = Arrays.asList(StringUtils.split(departmentId, ","));
        }


        stationCustomerRegService.pageCustomerReg(page, departmentIds, name, gender, idCard, phone, regDateStart, regDateEnd, examNo, examCardNo, checkState, companyRegId, teamId, itemGroupId, itemId, changeApplyStatus);
        return Result.OK(page);
    }

    @ApiOperation(value = "客户登记-根据id获取登记记录", notes = "客户登记-根据id获取登记记录")
    @GetMapping(value = "/getRegById")
    public Result<CustomerReg> getRegById(String regId, String departmentId) {
        List<String> departmentIds = null;
        if (StringUtils.isNotBlank(departmentId)) {
            departmentIds = Arrays.asList(StringUtils.split(departmentId, ","));
        }
        CustomerReg customerReg = stationCustomerRegService.getRegById(regId, departmentIds);
        return Result.OK(customerReg);
    }
}
