package org.jeecg.modules.station.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.station.entity.CustomerRegDepartTip;
import org.jeecg.modules.station.mapper.CustomerRegDepartTipMapper;
import org.jeecg.modules.station.service.ICustomerRegDepartTipService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 科室提醒
 * @Author: jeecg-boot
 * @Date:   2024-05-08
 * @Version: V1.0
 */
@Service
public class CustomerRegDepartTipServiceImpl extends ServiceImpl<CustomerRegDepartTipMapper, CustomerRegDepartTip> implements ICustomerRegDepartTipService {

    @Override
    public List<CustomerRegDepartTip> listByRegId(String regId) {
        QueryWrapper<CustomerRegDepartTip> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_reg_id", regId);
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }
}
