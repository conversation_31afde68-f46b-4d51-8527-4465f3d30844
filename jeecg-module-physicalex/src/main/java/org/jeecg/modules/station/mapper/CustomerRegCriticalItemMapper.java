package org.jeecg.modules.station.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;

import java.util.List;

/**
 * @Description: 危急值记录关联项目
 * @Author: jeecg-boot
 * @Date: 2024-04-26
 * @Version: V1.0
 */
public interface CustomerRegCriticalItemMapper extends BaseMapper<CustomerRegCriticalItem> {

    /**
     * 通过主表id删除子表数据
     *
     * @param mainId 主表id
     * @return boolean
     */
    public boolean deleteByMainId(@Param("mainId") String mainId);

    /**
     * 通过主表id查询子表数据
     *
     * @param mainId 主表id
     * @return List<CustomerRegCriticalItem>
     */
    public List<CustomerRegCriticalItem> selectByMainId(@Param("mainId") String mainId);

    List<CustomerRegCriticalItem> listByReg(@Param("regId") String regId, @Param("validStatus") Integer validStatus);

    Page<CustomerReg> pageCustomerReg(Page<CustomerReg> page, @Param("name") String name, @Param("idCard") String idCard, @Param("phone") String phone, @Param("examNo") String examNo, @Param("examCardNo") String examCardNo, @Param("nofityStatus") String nofityStatus, @Param("postVisitStatus") String postVisitStatus, @Param("createStartTime") String createStartTime, @Param("createEndTime") String createEndTime, @Param("visitStartTime") String visitStartTime, @Param("visitEndTime") String visitEndTime, @Param("notifyStartTime") String notifyStartTime, @Param("notifyEndTime") String notifyEndTime);

}
