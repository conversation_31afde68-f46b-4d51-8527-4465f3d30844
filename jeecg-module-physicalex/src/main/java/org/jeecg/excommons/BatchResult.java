package org.jeecg.excommons;

import lombok.Data;

import java.util.List;

@Data
public class BatchResult<T>{

    @Data
    public static class FailureResult<T> {
        private T item;
        private String reason;

        public FailureResult(T item, String reason) {
            this.item = item;
            this.reason = reason;
        }
    }

    private List<T> successResults;
    private List<FailureResult<T>> failureResults;
    private int totalProcessed;
    private int successCount;
    private int failureCount;

    public void setSuccessResults(List<T> successResults) {
        this.successResults = successResults;
        this.successCount = successResults.size();
    }

    public void setFailureResults(List<FailureResult<T>> failureResults) {
        this.failureResults = failureResults;
        this.failureCount = failureResults.size();
    }

}