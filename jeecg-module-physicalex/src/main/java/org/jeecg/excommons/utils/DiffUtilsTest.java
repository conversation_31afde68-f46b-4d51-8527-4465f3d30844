package org.jeecg.excommons.utils;

import com.github.difflib.DiffUtils;
import com.github.difflib.patch.AbstractDelta;
import com.github.difflib.patch.Patch;
import org.junit.jupiter.api.Test;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class DiffUtilsTest {

    @Test
    public void testDiff() {
        // 原始字符串
        String original = "Hello world!\nThis is a test.\nGoodbye world!";
        // 修改后的字符串
        String revised = "Hello world!\nThis is a diff test.\nGoodbye world!";

        // 将字符串转换为行列表
        List<String> originalLines = Arrays.asList(original.split("\n"));
        List<String> revisedLines = Arrays.asList(revised.split("\n"));

        // 计算差异
        Patch<String> patch = DiffUtils.diff(originalLines, revisedLines);

        // 输出差异
        for (AbstractDelta<String> delta : patch.getDeltas()) {
            System.out.println(delta.getType() + ":");
            System.out.println("Original: " + delta.getSource());
            System.out.println("Revised: " + delta.getTarget());
        }

        // 断言差异不为空
        assertFalse(patch.getDeltas().isEmpty());
    }
}
