package org.jeecg.excommons.utils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;

import java.util.concurrent.TimeUnit;

public class GroovyUtil {

    private static volatile GroovyUtil instance;
    private final Cache<String, Script> scriptCache;

    private GroovyUtil() {
        this.scriptCache = CacheBuilder.newBuilder()
                .expireAfterAccess(60, TimeUnit.MINUTES) // 设置缓存项在60分钟内没有被读/写访问就会过期
                .build();
    }

    public static GroovyUtil getInstance() {
        if (instance == null) {
            synchronized (GroovyUtil.class) {
                if (instance == null) {
                    instance = new GroovyUtil();
                }
            }
        }
        return instance;
    }

    public boolean executeExpression(String expression, Object itemResult) throws GroovyResultException {
        try {
            Script script = scriptCache.get(expression, () -> new GroovyShell().parse(expression));
            Binding binding = new Binding();
            binding.setVariable("itemResult", itemResult);
            script.setBinding(binding);
            Object result = script.run();
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                throw new GroovyResultException("表达式结果不是布尔值");
            }
        } catch (Exception e) {
            throw new GroovyResultException("运行表达式出错");
        }
    }

    public static void main(String[] args) {
        try {
            long start = System.currentTimeMillis();
            for(int i = 0; i < 100; i++) {
                boolean resutl = GroovyUtil.getInstance().executeExpression("itemResult == '1'", "1");
                System.out.println(resutl);
            }
            System.out.println("耗时：" + (System.currentTimeMillis() - start));
        } catch (GroovyResultException e) {
            throw new RuntimeException(e);
        }
    }
}