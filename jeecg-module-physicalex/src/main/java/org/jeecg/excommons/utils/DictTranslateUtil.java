package org.jeecg.excommons.utils;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

@Component
public class DictTranslateUtil {
    @Autowired
    private ISysDictService sysDictService; // 系统字典服务

    @Autowired
    private JdbcTemplate jdbcTemplate;      // 数据库查询工具

    /**
     * 通用字典翻译方法
     * @param object 需要翻译的对象
     */
    public void translate(Object object) {
        Class<?> clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            Dict dictAnnotation = field.getAnnotation(Dict.class);
            if (dictAnnotation != null) {
                translateField(object, clazz, field, dictAnnotation);
            }
        }
    }

    private void translateField(Object object, Class<?> clazz, Field field, Dict dictAnnotation) {
        try {
            field.setAccessible(true);
            Object codeValue = field.get(object);
            if (codeValue == null || "".equals(codeValue)) return;

            String dictText = getDictText(dictAnnotation, codeValue.toString());
            setDictTextField(object, clazz, field.getName(), dictText);
        } catch (Exception e) {
            e.printStackTrace(); // 实际项目中应记录日志
        }
    }

    private String getDictText(Dict dict, String codeValue) {
        if (StringUtils.isNotBlank(dict.dictTable())) {
            return queryTableDict(dict.dictTable(), dict.dicCode(), dict.dicText(), codeValue);
        } else {
            return querySysDict(dict.dicCode(), codeValue);
        }
    }

    private String querySysDict(String dictCode, String codeValue) {
        return sysDictService.queryDictTextByKey(dictCode, codeValue);
    }

    private String queryTableDict(String table, String codeField, String textField, String codeValue) {
        String sql = String.format("SELECT %s FROM %s WHERE %s = ?", textField, table, codeField);
        try {
            return jdbcTemplate.queryForObject(sql, String.class, codeValue);
        } catch (Exception e) {
            return null;
        }
    }

    private void setDictTextField(Object object, Class<?> clazz, String fieldName, String dictText) {
        try {
            String dictTextFieldName = fieldName + "_dictText";
            Field dictTextField = clazz.getDeclaredField(dictTextFieldName);
            dictTextField.setAccessible(true);
            dictTextField.set(object, dictText);
        } catch (NoSuchFieldException e) {
            // 忽略未找到 _dictText 字段的情况
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
