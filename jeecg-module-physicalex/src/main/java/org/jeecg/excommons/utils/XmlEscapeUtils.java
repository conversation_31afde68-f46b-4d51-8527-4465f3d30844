package org.jeecg.excommons.utils;

import java.util.HashMap;
import java.util.Map;

public class XmlEscapeUtils {

    private static final Map<Character, String> escapeMap = new HashMap<>();
    private static final Map<String, Character> unescapeMap = new HashMap<>();

    static {
        escapeMap.put('&', "&amp;");
        escapeMap.put('<', "&lt;");
        escapeMap.put('>', "&gt;");
        escapeMap.put('"', "&quot;");
        escapeMap.put('\'', "&apos;");

        unescapeMap.put("&amp;", '&');
        unescapeMap.put("&lt;", '<');
        unescapeMap.put("&gt;", '>');
        unescapeMap.put("&quot;", '"');
        unescapeMap.put("&apos;", '\'');
    }

    // Escapes XML special characters in the input string
    public static String escapeXml(String input) {
        if (input == null) {
            return null;
        }

        StringBuilder escaped = new StringBuilder();
        for (char c : input.toCharArray()) {
            String escape = escapeMap.get(c);
            if (escape != null) {
                escaped.append(escape);
            } else {
                escaped.append(c);
            }
        }
        return escaped.toString();
    }

    // Unescapes XML special characters in the input string
    public static String unescapeXml(String input) {
        if (input == null) {
            return null;
        }

        StringBuilder unescaped = new StringBuilder();
        int i = 0;
        while (i < input.length()) {
            if (input.charAt(i) == '&') {
                int semicolonIndex = input.indexOf(';', i);
                if (semicolonIndex > i) {
                    String entity = input.substring(i, semicolonIndex + 1);
                    Character unescapedChar = unescapeMap.get(entity);
                    if (unescapedChar != null) {
                        unescaped.append(unescapedChar);
                        i = semicolonIndex + 1;
                        continue;
                    }
                }
            }
            unescaped.append(input.charAt(i));
            i++;
        }
        return unescaped.toString();
    }

    public static void main(String[] args) {
        // Test escaping
        String original = "1、眼科检查 : (1)眼科检查：右眼视力:1.0 ；矫正视力(左):未矫 ；矫正视力(右):未矫 \n" +
                "2、检验检查项目 : (1)尿液分析：酸碱度偏低:5.0 ；(2)血常规(五分类)：红细胞偏高:5.19 10^12/L；血红蛋白偏高:153 g/L；红细胞压积偏高:46.70 %；(3)生化（肝肾血糖血脂）：高密度脂蛋白偏高:1.83 mmol/L\n" +
                "3、超声医学科 : (1)肝胆胰脾双肾彩超：肝胆胰脾双肾彩超：肝脏：大小正常，包膜光滑，实质回声细密，分布均匀，肝内管状结构清晰，肝内胆管未见扩张。门静脉主干不扩张。&amp;#10;胆囊：大小30x10mm，壁毛糙，内透声清晰，囊内未见明显异常回声。胆总管内径不扩张，显示段未见明显异常回声。&amp;#10;胰腺：形态大小正常，实质回声细密，分布均匀，胰管未见扩张。&amp;#10;脾脏：形态大小正常，实质回声均匀，脾门静脉未见扩张。&amp;#10;肾脏：双肾位置、大小、形态正常，被膜平滑，实质回声均匀，皮髓质分界清晰，双肾盂未见分离。CDFI:双肾内血流充盈好。\n" +
                "4、心电图 : (1)心电图：1.窦性心律，2.非特异性T波异常（Ⅱ，Ⅲ，aVF,V5,V6)(下壁。前壁心肌缺血） \n";
        String escaped = escapeXml(original);
        System.out.println("Original: " + original);
        System.out.println("Escaped: " + escaped);

        // Test unescaping
        String toUnescape = "1、眼科检查 : (1)眼科检查：右眼视力:1.0 ；矫正视力(左):未矫 ；矫正视力(右):未矫 \\n\" +\n" +
                "                \"2、检验检查项目 : (1)尿液分析：酸碱度偏低:5.0 ；(2)血常规(五分类)：红细胞偏高:5.19 10^12/L；血红蛋白偏高:153 g/L；红细胞压积偏高:46.70 %；(3)生化（肝肾血糖血脂）：高密度脂蛋白偏高:1.83 mmol/L\\n\" +\n" +
                "                \"3、超声医学科 : (1)肝胆胰脾双肾彩超：肝胆胰脾双肾彩超：肝脏：大小正常，包膜光滑，实质回声细密，分布均匀，肝内管状结构清晰，肝内胆管未见扩张。门静脉主干不扩张。&amp;#10;胆囊：大小30x10mm，壁毛糙，内透声清晰，囊内未见明显异常回声。胆总管内径不扩张，显示段未见明显异常回声。&amp;#10;胰腺：形态大小正常，实质回声细密，分布均匀，胰管未见扩张。&amp;#10;脾脏：形态大小正常，实质回声均匀，脾门静脉未见扩张。&amp;#10;肾脏：双肾位置、大小、形态正常，被膜平滑，实质回声均匀，皮髓质分界清晰，双肾盂未见分离。CDFI:双肾内血流充盈好。\\n\" +\n" +
                "                \"4、心电图 : (1)心电图：1.窦性心律，2.非特异性T波异常（Ⅱ，Ⅲ，aVF,V5,V6)(下壁。前壁心肌缺血） \\n";
        String unescaped = unescapeXml(toUnescape);
        System.out.println("To Unescape: " + toUnescape);
        System.out.println("Unescaped: " + unescaped);
    }


}