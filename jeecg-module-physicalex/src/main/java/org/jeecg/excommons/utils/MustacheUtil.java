package org.jeecg.excommons.utils;

import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.Map;

public class MustacheUtil {
    public static String render(String template, Map<String, Object> values) {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(template), template);
        StringWriter writer = new StringWriter();
        mustache.execute(writer, values);

        return writer.toString();
    }
}