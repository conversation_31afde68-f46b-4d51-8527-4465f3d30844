package org.jeecg.excommons.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;

public class InitialUtil {

    private static final HanyuPinyinOutputFormat PINYIN_FORMAT = new HanyuPinyinOutputFormat();
    public static String generateInitial(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }

        // 去除前后空格
        name = name.trim();
        StringBuilder initial = new StringBuilder();

        // 处理每个字符
        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);

            // 中文处理
            if (isChinese(c)) {
                initial.append(getPinyinInitial(c));
            }
            // 英文字母处理
            else if (Character.isLetter(c)) {
                initial.append(Character.toUpperCase(c));
            }else{
                initial.append(c);
            }
            // 其他字符跳过
        }

        return initial.toString();
    }

    /**
     * 使用pinyin4j获取汉字拼音首字母
     */
    private static char getPinyinInitial(char c) {
        try {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, PINYIN_FORMAT);
            if (pinyinArray != null && pinyinArray.length > 0) {
                return pinyinArray[0].charAt(0);
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            // 转换失败时返回原字符的大写形式
        }
        return Character.toUpperCase(c);
    }

    /**
     * 判断字符是否是中文
     */
    private static boolean isChinese(char c) {
        return (c >= 0x4E00 && c <= 0x9FA5);
    }
}
