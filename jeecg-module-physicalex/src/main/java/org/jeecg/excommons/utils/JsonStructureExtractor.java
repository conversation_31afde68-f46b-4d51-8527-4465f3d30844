package org.jeecg.excommons.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JsonStructureExtractor {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    // 修改正则表达式以匹配键名后可能存在的空格
    private static final Pattern JSON_PROPERTY_PATTERN = Pattern.compile("([a-zA-Z0-9_]+)\\s*:");

    /**
     * 提取并返回输入字符串中的第一个有效 JSON 结构（可能是数组或对象）。
     *
     * @param input 包含 JSON 结构的字符串
     * @return 首个有效 JSON 结构（字符串形式）；若未找到则返回 null
     */
    public static String extractJsonStructure(String input) {
        // 移除代码块标记
        input = removeCodeBlockMarkers(input);

        // 尝试修复：给未被双引号包围的属性名加上双引号，替换单引号为双引号，移除尾随逗号
        input = fixJsonFormat(input);

        // 是否在引号内的标记（用于忽略字符串中的符号）
        boolean inString = false;

        // 用于记录 JSON 结构的起始位置
        int startIndex = -1;
        // 用于记录当前匹配到的括号层级
        int braceCount = 0;

        // 记录当前遇到的起始括号/大括号
        char openingChar = '\0';
        char closingChar = '\0';

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);

            // 简单判断是否进出字符串
            if (c == '"') {
                inString = !inString;
            }

            // 只有在不在字符串内时，才判断括号
            if (!inString) {
                // 如果当前还未开始匹配 JSON 结构，就看是否遇到 '[' 或 '{'
                if (braceCount == 0) {
                    if (c == '[') {
                        startIndex = i;
                        braceCount = 1;
                        openingChar = '[';
                        closingChar = ']';
                    } else if (c == '{') {
                        startIndex = i;
                        braceCount = 1;
                        openingChar = '{';
                        closingChar = '}';
                    }
                }
                // 如果已经开始匹配了，就继续累加或累减
                else {
                    if (c == openingChar) {
                        braceCount++;
                    } else if (c == closingChar) {
                        braceCount--;
                        // 当 braceCount 回到 0，说明找到了一个完整的 JSON 结构
                        if (braceCount == 0 && startIndex != -1) {
                            String potentialJson = input.substring(startIndex, i + 1);
                            if (isValidJson(potentialJson)) {
                                return potentialJson;
                            }
                            // 如果解析失败，就重置继续寻找
                            startIndex = -1;
                            openingChar = '\0';
                            closingChar = '\0';
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 移除代码块标记，例如 ```json 和 ```
     *
     * @param input 原始输入字符串
     * @return 移除代码块标记后的字符串
     */
    private static String removeCodeBlockMarkers(String input) {
        // 移除 ```json 和 ```
        return input.replaceAll("```json\\s*", "")
                .replaceAll("```", "")
                .trim();
    }

    /**
     * 给 JSON 的 key 补上双引号（例如把  name: 变为 "name":），替换单引号为双引号，并移除尾随逗号。
     * 注意：如果原字符串中有更复杂的结构，或者本来就存在带双引号的情况，需要进一步优化。
     *
     * @param input 原始输入字符串
     * @return 修复后的字符串
     */
    private static String fixJsonFormat(String input) {
        // 替换未被双引号包围的属性名为带双引号的属性名
        Matcher matcher = JSON_PROPERTY_PATTERN.matcher(input);
        StringBuffer buffer = new StringBuffer();
        while (matcher.find()) {
            // 只给 key 加双引号
            matcher.appendReplacement(buffer, "\"" + matcher.group(1) + "\":");
        }
        matcher.appendTail(buffer);
        // 替换单引号为双引号
        String result = buffer.toString().replaceAll("'", "\"");
        // 移除对象或数组中的尾随逗号
        result = result.replaceAll(",(?=\\s*[}\\]])", "");
        return result;
    }

    /**
     * 判断给定字符串是否为有效的 JSON（对象或数组）。
     */
    private static boolean isValidJson(String json) {
        try {
            JsonNode node = objectMapper.readTree(json);
            // 只要是对象或数组，即判定为有效
            return node.isObject() || node.isArray();
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) {
        // 示例1：输入是一个 JSON 数组
        String inputArray = "```json\n" +
                "[\n" +
                "    {\n" +
                "        \"title\": \"红细胞压积\",\n" +
                "        \"summaryText\": \"红细胞压积偏高(45.10%)\",\n" +
                "        \"severityOrder\": 1,\n" +
                "        \"orderReason\": \"轻度异常，需关注\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"title\": \"总蛋白\",\n" +
                "        \"summaryText\": \"总蛋白偏高(82.6g/L)\",\n" +
                "        \"severityOrder\": 2,\n" +
                "        \"orderReason\":\"轻度异常，需关注\"\n" +
                "    },\n" +
                "    {\n" +
                "      \t\"title\":\"低密度脂蛋白\", \n" +
                "      \t\"summaryText\":\"低密度脂蛋白偏高(5.03mmol/L)\", \n" +
                "      \t\"severityOrder\":3, \n" +
                "      \t\"orderReason\":\"心血管风险因素之一，需要重视并进一步检查或干预治疗。\"\n" +
                "   },  \n" +
                "   {  \n" +
                "     \t  title : '彩超',  \n" +
                "     \t  summaryText : '右乳实性结节 BI-RADS 3类建议复查（见图1）双侧乳腺轻度增生（见图2-3）左肾下极实质内偏囊性包块倾向良性囊肿？建议CT或超声随诊（见图2-3）肝、胆、胰、脾及右肾与双侧输尿管目前未见异常.',   \n" +
                "     \t  severityOrder :4 ,   \n" +
                "     \t  orderReason:'涉及多个器官系统问题且包含潜在恶性病变可能性较高者优先处理.'\n" +
                "   },  \n" +
                "   {  \n" +
                "    \t    title:'耳鼻喉检查',    \n" +
                "    \t    summaryText:'粘膜慢性充血.',    \n" +
                "    \t    severityOrder:5,     \n" +
                "    \t order_reason:\"常见症状但可能影响生活质量.\"\n" +
                "   },  \n" +
                "\n" +
                "]\n" +
                "```";
        String extractedArray = extractJsonStructure(inputArray);
        System.out.println("Extracted JSON Array: " + extractedArray);

        // 示例2：输入是一个 JSON 对象
        String inputObject = "{seq:1,title:\"耳鼻喉科情况\",content:\"这里是内容...\"} 不相关内容";
        String extractedObject = extractJsonStructure(inputObject);
        System.out.println("Extracted JSON Object: " + extractedObject);

        // 示例3：输入包含多个 JSON 结构，本示例仅提取第一个有效的
        String complexInput = "前面无关内容 [{a:1},{b:2}] {x:1} 后面无关内容";
        String firstJson = extractJsonStructure(complexInput);
        System.out.println("Extracted First JSON Structure: " + firstJson);
    }
}
