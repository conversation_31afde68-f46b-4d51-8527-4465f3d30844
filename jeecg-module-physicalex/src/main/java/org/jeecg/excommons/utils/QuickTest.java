package org.jeecg.excommons.utils;

public class QuickTest {
    public static void main(String[] args) {
        String input = "2、残留胸腺可能，请结合临床，建议复查。。";
        String result = SmartPunctuationProcessor.normalize(input);
        System.out.println("Input: " + input);
        System.out.println("Output: " + result);
        System.out.println("Expected: 2、残留胸腺可能，请结合临床，建议复查。");
        System.out.println("Match: " + result.equals("2、残留胸腺可能，请结合临床，建议复查。"));
        
        // Test individual methods
        System.out.println("\n=== Step by step ===");
        String step1 = input.replace("\n", "，").replace("\r", "");
        System.out.println("Step1 (replace newlines): " + step1);
        
        String step2 = SmartPunctuationProcessor.normalizeMiddlePunctuation(step1);
        System.out.println("Step2 (normalizeMiddlePunctuation): " + step2);
        
        String step3 = SmartPunctuationProcessor.normalizeTrailingPunctuation(step2);
        System.out.println("Step3 (normalizeTrailingPunctuation): " + step3);
        
        String step4 = step3.replaceAll("\\s+", " ").trim();
        System.out.println("Step4 (trim spaces): " + step4);
        
        // Test regex directly
        System.out.println("\n=== Direct regex test ===");
        String directTest = "test。。";
        String directResult = directTest.replaceAll("。{2,}", "。");
        System.out.println("Direct test '。。' -> '" + directResult + "'");
    }
}
