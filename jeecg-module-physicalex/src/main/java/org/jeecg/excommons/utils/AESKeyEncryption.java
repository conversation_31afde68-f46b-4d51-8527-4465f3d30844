package org.jeecg.excommons.utils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class AESKeyEncryption {

    // 生成AES对称密钥
    public static SecretKey generateAESKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(256); // 设置AES密钥长度 (128, 192, 256 位)
        return keyGenerator.generateKey();
    }

    // 使用AES加密私钥
    public static String encryptPrivateKey(String privateKey, SecretKey secretKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(privateKey.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    // 使用AES解密私钥
    public static String decryptPrivateKey(String encryptedPrivateKey, SecretKey secretKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedPrivateKey));
        return new String(decryptedBytes);
    }

    public static void main(String[] args) {
        try {
            String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDll5xlM3FpICAIcP1MQJqpLwDQh+aT9KlGoPbKyPcwDP4Bcjy+XHSxmPQI1mH+D/GNAA29B/4T6ZEnyW3WBAB/OvitNivEZyQz3cMLGU+abB564qFTUm3KnZ2WYOh01vAiKVSXWDut5BaDynZS8SyhOM2ILh6g1VvErjA671I+nM0LVcYX/rMYvPosAJz3uKPp1XgTqk19OV3Uc/BEehd5oBdbWUyFaEI1rKHd0h2sVuPF4T/2CU6C9psOtFsPebKXJZ2hqnbPJ3BeigvovKiafUPaDu/Rw8GoctGd5FYaBxD+zNEkJjrseTzW9NW7urb1WwQFme9INhIPJfHaPw4FAgMBAAECggEAALPt+WT6eRGRdEEPELvVvjGkS+gn1G8ShqqvWmtW5hMoATDl43NZfp3X1JZThym/kA5WTnsiAC5mSgnuKwh+IPlGQVUUxobto3t4gXMaUpPQcB/XkEGLQQnbbyUL2pEMFhhTweClngV4LkcuYSpKBgiVQ+C0D69KzqPH31M8+beZ6ivDVf3FMGoQ6y2Et/d4i/NeAMh4D41WE9LdXzYB/Pksfy6WxKZEDN4ek5WTMYXVZadIT5H73yVM6j0PqPz01UWh7wilav0hbFMGwyAF67x02J5aSX7UPCSNPCx16RstKu8yA9pzFZFNez0oyaWjgkZYRLpDxGaY3uPVySaDmwKBgQDzFx6Yj58IoLfYhLTTLWgA37zOzYO7cykUHwwAVT2u8ZVoyljz/UpXM6uuhzz9U6cW0ijqt1XV8DW8Q8Tq0QNDcc4H6E0Ws7I9SP91yPYYs4nmFUECwxP9eYGcMY1nkvZWA1U21FJrmUc3nXW8U114Wf4hc/r18UWJcJ+dyXK6BwKBgQDxyPsyjruRoSoFau4mvnmIij9DBO5IVQESjhqe+dvym6l3bLj0MWd5cBQlM6z7rQwzGNaX6lJ86Bc3fWiGem7PXy6OWL7WeLDK87n7cAcvvbPz0Jc+gXWgn1aTsJ4EoAQgzkdcDaKumgrxJfbgsXOSYGVWIjlLO89KJQ8s1tDkkwKBgQDem6piDQuWzA7vkT9CL8+RFVKCkwLycpgInBHScDznasXmy35AGRCt4Fx0m9XHKobVyCi+IJvTKYEb4Xd/jV4OsjX5WcrWYkJ0CtgdlaV/o1JBx+7bx6cTtORzE5GpapR42d58Ml0zuY7VKDW7+x+aadbl7Z7BAGwsW11gEi0kmwKBgQC1ePj5QGIN8t3cRR7MW6hSUBkdT0Udaxer+paGfdiU5jqy1RqYxb/5SKMGLRwHpiGC9ICUcx5Ie9Z5IppiXOBwqusBkiBiTyG6omYUD0iFj6fSNtxd4MMNtKpBnY1swUF38ftjXyScUigmSS1cBjRNSCanGHbSPejeqKQULoOJGwKBgAxzg79HRQ/g5/Np57O5vi+M994Jn+UlLFF2HVTmGfLUrFZGuDos06qjba6DZ2lHvtj0BoK2yZ9V7SCAInGh4zbQWtl5Ho5idHpmMrU5BU3JjcTWf8HonEGBOEEeXw4Ix74WEzrVO+4k5+9Zew0jpisezqyJB9haEfbw2VGQzvMw";

            // 生成AES对称密钥
            SecretKey secretKey = generateAESKey();

            // 加密私钥
            String encryptedPrivateKey = encryptPrivateKey(privateKey, secretKey);
            System.out.println("加密后的私钥: " + encryptedPrivateKey);

            // 解密私钥
            String decryptedPrivateKey = decryptPrivateKey(encryptedPrivateKey, secretKey);
            System.out.println("解密后的私钥: " + decryptedPrivateKey);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

