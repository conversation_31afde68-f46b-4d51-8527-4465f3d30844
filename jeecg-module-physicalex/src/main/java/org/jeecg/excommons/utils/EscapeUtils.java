package org.jeecg.excommons.utils;

import org.apache.commons.lang.StringEscapeUtils;

public class EscapeUtils {

    public static String unescape(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        String tmp = StringEscapeUtils.unescapeHtml(input);
        tmp = StringEscapeUtils.unescapeXml(tmp);
        tmp = StringEscapeUtils.unescapeJava(tmp);
        tmp = StringEscapeUtils.unescapeJavaScript(tmp);
        return tmp;
    }


    // 简单测试示例
    public static void main(String[] args) {
        String testStr = "Hello &lt;b&gt;world&lt;/b&gt; &amp; &#x1F600; &#128516; and &quot;quotes&quot; &lt;";
        System.out.println("原始字符串: " + testStr);
        System.out.println("解析后: " + unescape(testStr));
    }
}


