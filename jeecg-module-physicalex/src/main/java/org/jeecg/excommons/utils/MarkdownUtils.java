package org.jeecg.excommons.utils;

import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.text.TextContentRenderer;

public class MarkdownUtils {
    public static String removeMarkdownTags(String markdownText) {
        Parser parser = Parser.builder().build();
        Node document = parser.parse(markdownText);
        // HtmlRenderer renderer = HtmlRenderer.builder().build(); //可以markdown转html
        TextContentRenderer renderer = TextContentRenderer.builder().build();
        return renderer.render(document);
    }

    public static void main(String[] args) {
        String markdownText = "\n" +
                "* [x] \uD83E\uDD5B Core: The core of Milkdown, which provides the plugin loading system with the editor concepts.\n" +
                "* [x] \uD83E\uDDC7 Plugins: A set of plugins that can be used to extend the functionalities of the editor..";
        String plainText = removeMarkdownTags(markdownText);
        System.out.println(plainText);
    }
}
