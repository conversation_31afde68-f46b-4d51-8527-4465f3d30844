package org.jeecg.excommons.utils;

/**
 * SmartPunctuationProcessor 演示类
 * 用于验证标点符号处理功能
 */
public class SmartPunctuationProcessorDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 智能标点符号处理器演示 ===");

        System.out.println(removeLeadingSeparatorsSimple("2、残留胸腺可能，请结合临床，建议复查。。"));
        
        // 测试用例1：保留正常括号
        String test1 = "：检查结果(正常)";
        String result1 = removeLeadingSeparatorsSimple(test1);
        System.out.println("测试1 - 保留正常括号:");
        System.out.println("  输入: '" + test1 + "'");
        System.out.println("  输出: '" + result1 + "'");
        System.out.println("  预期: '检查结果(正常)'");
        System.out.println("  结果: " + (result1.equals("检查结果(正常)") ? "✓ 通过" : "✗ 失败"));
        System.out.println();
        
        // 测试用例2：保留引号
        String test2 = "；\"重要\"提示";
        String result2 = removeLeadingSeparatorsSimple(test2);
        System.out.println("测试2 - 保留引号:");
        System.out.println("  输入: '" + test2 + "'");
        System.out.println("  输出: '" + result2 + "'");
        System.out.println("  预期: '\"重要\"提示'");
        System.out.println("  结果: " + (result2.equals("\"重要\"提示") ? "✓ 通过" : "✗ 失败"));
        System.out.println();
        
        // 测试用例3：保留感叹号
        String test3 = "，。!重要提醒";
        String result3 = removeLeadingSeparatorsSimple(test3);
        System.out.println("测试3 - 保留感叹号:");
        System.out.println("  输入: '" + test3 + "'");
        System.out.println("  输出: '" + result3 + "'");
        System.out.println("  预期: '!重要提醒'");
        System.out.println("  结果: " + (result3.equals("!重要提醒") ? "✓ 通过" : "✗ 失败"));
        System.out.println();
        
        // 测试用例4：保留方括号
        String test4 = "：；【注意】事项";
        String result4 = removeLeadingSeparatorsSimple(test4);
        System.out.println("测试4 - 保留方括号:");
        System.out.println("  输入: '" + test4 + "'");
        System.out.println("  输出: '" + result4 + "'");
        System.out.println("  预期: '【注意】事项'");
        System.out.println("  结果: " + (result4.equals("【注意】事项") ? "✓ 通过" : "✗ 失败"));
        System.out.println();
        
        // 测试用例5：连续标点处理
        String test5 = "检查结果：，，，异常。。。";
        String result5 = normalizeSimple(test5);
        System.out.println("测试5 - 连续标点处理:");
        System.out.println("  输入: '" + test5 + "'");
        System.out.println("  输出: '" + result5 + "'");
        System.out.println("  预期: '检查结果，异常。'");
        System.out.println("  结果: " + (result5.equals("检查结果，异常。") ? "✓ 通过" : "✗ 失败"));
        System.out.println();
        
        System.out.println("=== 演示完成 ===");
    }
    
    /**
     * 简化版的开头分隔符删除方法（不依赖 StringUtils）
     */
    private static String removeLeadingSeparatorsSimple(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        // 只删除开头的分隔符和空白字符，保留有意义的标点符号
        return text.replaceFirst("^[：；，。、\\s]+", "");
    }
    
    /**
     * 简化版的标点符号规范化方法
     */
    private static String normalizeSimple(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        String processed = text;
        
        // 1. 处理换行符
        processed = processed.replace("\n", "，").replace("\r", "");
        
        // 2. 处理连续的逗号
        processed = processed.replaceAll("，{2,}", "，");
        processed = processed.replaceAll(",{2,}", "，");

        // 3. 处理连续的句号
        processed = processed.replaceAll("。{2,}", "。");
        processed = processed.replaceAll("\\.{2,}", "。");

        // 4. 处理混合连续标点
        processed = processed.replaceAll("[，,]{2,}", "，");
        processed = processed.replaceAll("[。.]{2,}", "。");

        // 5. 处理冒号后紧跟的逗号（特殊情况）
        processed = processed.replaceAll("：，+", "：");
        processed = processed.replaceAll("：,+", "：");

        // 6. 处理逗号+句号的组合
        processed = processed.replaceAll("，。", "。");
        processed = processed.replaceAll(",。", "。");
        processed = processed.replaceAll("，\\.", "。");
        
        // 7. 处理末尾的多个混合标点，替换为单个句号
        processed = processed.replaceAll("[，。,.;；、\\s]+$", "。");
        
        // 8. 处理多余的空格
        processed = processed.replaceAll("\\s+", " ").trim();
        
        return processed;
    }
}
