package org.jeecg.excommons.utils;

import org.apache.lucene.analysis.Tokenizer;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;

import java.io.IOException;
import java.text.BreakIterator;
import java.util.Locale;

public class PunctuationTokenizer extends Tokenizer {
    private final CharTermAttribute termAttr = addAttribute(CharTermAttribute.class);
    private String[] tokens = null;
    private int index = 0;

    @Override
    public boolean incrementToken() throws IOException {
        if (tokens == null) {
            // 读取输入文本
            StringBuilder textBuilder = new StringBuilder();
            char[] buffer = new char[1024];
            int length;
            while ((length = input.read(buffer)) != -1) {
                textBuilder.append(buffer, 0, length);
            }
            String content = textBuilder.toString();

            // 按照标点符号进行分割
            tokens = splitByPunctuation(content);
            index = 0;
        }

        if (index < tokens.length) {
            clearAttributes();
            termAttr.append(tokens[index].trim());
            index++;
            return true;
        } else {
            return false;
        }
    }

    private String[] splitByPunctuation(String text) {
        // 定义中文标点符号
        String punctuation = "，。？！；：、（）《》“”‘’【】—…";
        // 使用正则表达式进行分割
        String regex = "[" + punctuation + "\\p{Punct}\\s]+";
        return text.split(regex);
    }

    @Override
    public void reset() throws IOException {
        super.reset();
        tokens = null;
        index = 0;
    }
}

