package org.jeecg.excommons.utils;

import com.alibaba.fastjson.JSONObject;
import org.bouncycastle.crypto.digests.SM3Digest;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

public class SM3Util {

    public static String sm3SignTokenParam(String appId, String appSecret, String code, String time, String uuid) {
        String data = "app_id=" + appId + "&app_secret=" + appSecret + "&code=" + code
                + "&grant_type=authorization_code&time=" + time + "&uuid=" + uuid;
        System.out.println("sign data: " + data);
        return sm3(data);
    }

    public static String sm3SignUserBaseInfo(String appId, String appSecret, String authToken, String time, String uuid) {
        String data = "app_id=" + appId + "&app_secret=" + appSecret + "&auth_token=" + authToken
                + "&time=" + time + "&uuid=" + uuid;
        System.out.println("sign data: " + data);
        return sm3(data);
    }

    private static String sm3(String data) {
        SM3Digest digest = new SM3Digest();
        byte[] bytes = data.getBytes(StandardCharsets.UTF_8);
        digest.update(bytes, 0, bytes.length);
        byte[] result = new byte[digest.getDigestSize()];
        digest.doFinal(result, 0);
        return bytesToHex(result);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static void main(String[] args) {
        String json = "{\"grant_type\":\"authorization_code\",\"code\":\"792f49b4-1fc7-4451-8ae5-c44af40fb890-1744251165678\",\"app_id\":\"9cdabb3f-4562-46c4-833c-336722d7e458\",\"time\":\"1744251162891\",\"uuid\":\"1401cbf6-58fd-41c9-8ed9-5e86d4c85eb6\",\"sign\":\"11016ffbb5a8b1baf0af25cc1c02ab36e41ee05c9a05945403999f78097924a9\"}";
        JSONObject jsonObject = JSONObject.parseObject(json);
        String appId = jsonObject.getString("app_id");
        String appSecret = "122c6735-4d92-452c-90c7-3f439afa9356";
        String code = jsonObject.getString("code");
        String time = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString();

        String sign = sm3SignTokenParam(appId, appSecret, code, time, uuid);
        System.out.println("SM3 Sign: " + sign);
    }
}