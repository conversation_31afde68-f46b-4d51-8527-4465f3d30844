package org.jeecg.excommons.utils;

import java.util.regex.Pattern;

public class TextCleanUtils {

    // 默认的标点符号正则表达式（包含中英文标点）
    private static final String DEFAULT_PUNCTUATION_REGEX = "[\\p{P}]+";
    private static final String DEFAULT_PUNCTUATION_WITH_LINE_BREAKS_REGEX = "[\\p{P}\\n\\r]+";

    /**
     * 移除字符串首尾的标点符号和换行符
     *
     * @param text 需要处理的文本
     * @return 处理后的文本
     */
    public static String trimPunctuation(String text) {
        return trimPunctuation(text, true);
    }

    /**
     * 移除字符串首尾的标点符号，可选是否移除换行符
     *
     * @param text 需要处理的文本
     * @param removeLineBreaks 是否移除换行符
     * @return 处理后的文本
     */
    public static String trimPunctuation(String text, boolean removeLineBreaks) {
        return trimPunctuation(text, removeLineBreaks ? DEFAULT_PUNCTUATION_WITH_LINE_BREAKS_REGEX : DEFAULT_PUNCTUATION_REGEX);
    }

    /**
     * 使用自定义正则表达式移除字符串首尾的标点符号和换行符
     *
     * @param text 需要处理的文本
     * @param punctuationRegex 自定义的标点符号正则表达式
     * @return 处理后的文本
     */
    public static String trimPunctuation(String text, String punctuationRegex) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 编译正则表达式，只匹配末尾的标点符号
        Pattern pattern = Pattern.compile(punctuationRegex + "$");

        // 处理末尾的标点符号
        String result = pattern.matcher(text).replaceAll("");

        // 移除末尾空白字符
        result = result.replaceAll("\\s+$", "");

        return result;
    }

    /**
     * 规范化句尾的标点符号，将多个连续的标点符号替换为单个标点
     *
     * @param text 需要处理的文本
     * @return 处理后的文本
     */
    public static String normalizePunctuation(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 将文本按行分割
        String[] lines = text.split("\\r\\n|\\r|\\n");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (!line.isEmpty()) {
                // 处理句尾的连续标点符号
                // 1. 如果包含句号，统一使用句号结尾
                // 2. 如果包含分号，统一使用句号结尾
                // 3. 其他情况使用句号结尾
                line = line.replaceAll("[。；，、！？\\\\.,!?]+$", "。");
                result.append(line);
            }

            // 在非最后一行添加换行符
            if (i < lines.length - 1) {
                result.append(System.lineSeparator());
            }
        }

        return result.toString();
    }

    /**
     * 规范化换行符（统一使用系统换行符）
     *
     * @param text 需要处理的文本
     * @return 处理后的文本
     */
    public static String normalizeLineEndings(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 将所有类型的换行符转换为系统默认换行符
        return text.replaceAll("\\r\\n|\\r|\\n", System.lineSeparator());
    }

    /**
     * 移除多余的换行符，保留单个换行符
     *
     * @param text 需要处理的文本
     * @return 处理后的文本
     */
    public static String removeExtraLineBreaks(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 将连续的换行符替换为单个换行符
        return text.replaceAll("[\\r\\n]+", System.lineSeparator());
    }

    /**
     * 修正文本中的行号
     * 将每行开头的数字序号修正为正确的顺序
     *
     * @param text 需要处理的文本
     * @return 修正后的文本
     */
    public static String fixLineNumbers(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 将文本分割成行
        String[] lines = text.split("\\r\\n|\\r|\\n");
        StringBuilder result = new StringBuilder();
        int expectedNumber = 1;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            // 匹配行首的数字（可能后跟顿号、点号等）
            if (line.matches("^\\d+[、.].*")) {
                // 替换行首的数字为期望的序号
                line = line.replaceFirst("^\\d+", String.valueOf(expectedNumber));
                expectedNumber++;
            }

            result.append(line);
            if (i < lines.length - 1) {
                result.append(System.lineSeparator());
            }
        }

        return result.toString();
    }

    /**
     * 规范化文本中的标点。将每行末尾的“；”、“。”等组合规范化为单个句号“。”，
     * 如果行末没有标点，也可以选择添加一个句号（可根据需要调整）。
     *
     * @param input 原始文本
     * @return 规范化后的文本
     */
    public static String normalize(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        // 按行分割文本（兼容 \n 或 \r\n）
        String[] lines = input.split("\\r?\\n");
        StringBuilder sb = new StringBuilder();
        for (String line : lines) {
            String trimmed = line.trim();
            if (trimmed.isEmpty()) {
                sb.append("\n");
                continue;
            }
            // 使用正则表达式将行末连续的“；”或“。”替换为一个“。”
            String normalized = trimmed.replaceAll("[；。]+$", "。");
            // 如果行末没有标点符号（可选逻辑），则追加句号
            if (!normalized.matches(".*[。；，、！？]$")) {
                //normalized += "。";
            }
            sb.append(normalized).append("\n");
        }
        // 返回去除末尾多余换行后的文本
        return sb.toString().trim();
    }


    // 测试用例
    public static void main(String[] args) {
        // 测试移除标点符号
        String text1 = "...Hello, World!...";
        System.out.println("原文本：" + text1);
        System.out.println("处理后（移除换行符）：" + trimPunctuation(text1));
        System.out.println("处理后（保留换行符）：" + trimPunctuation(text1, false));

        // 测试处理换行符
        String text2 = "Line1\\r\\nLine2\\rLine3\\nLine4";
        System.out.println("\\n原文本换行：" + text2);
        System.out.println("规范化换行：" + normalizeLineEndings(text2));

        // 测试移除多余换行符
        String text3 = "Line1\\n\\n\\nLine2\\n\\nLine3";
        System.out.println("\\n原文本多余换行：" + text3);
        System.out.println("移除多余换行：" + removeExtraLineBreaks(text3));

        // 测试中文标点
        String text4 = "。。。你好，世界！。。。\\n第二行";
        System.out.println("\\n原文本中文标点：" + text4);
        System.out.println("处理后（移除换行符）：" + trimPunctuation(text4));
        System.out.println("处理后（保留换行符）：" + trimPunctuation(text4, false));

        // 测试修正行号
        String text5 = "1、谷草转氨酶偏低1290UL谷丙转氨酶偏低800UL谷草谷丙偏高16UL。\\n" +
                "2、尿酸偏高460umolL。\\n" +
                "3、甘油三脂偏高318mmolL总胆固醇偏高533mmolL高密度脂蛋白偏低082mmolL载脂蛋白A1B偏低11。\\n" +
                "4、同型半胱氨酸偏高1770umolL。\\n" +
                "2、双侧上颌窦及筛窦炎症。";
        System.out.println("\\n原文本行号：\\n" + text5);
        System.out.println("修正后的行号：\\n" + fixLineNumbers(text5));

        // 测试修正句尾标点
        String original = "19、冠状动脉硬化；。\n20、双肺多发结节，建议复查；";
        String normalized = normalize(original);
        System.out.println("原文本标点：\n" + original);
        System.out.println("规范化标点后：\n" + normalized);
    }
}