package org.jeecg.excommons.utils;

import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.Tokenizer;

public class PunctuationAnalyzer extends Analyzer {
    @Override
    protected TokenStreamComponents createComponents(String fieldName) {
        Tokenizer tokenizer = new PunctuationTokenizer();
        TokenStream tokenStream = tokenizer;
        return new TokenStreamComponents(tokenizer, tokenStream);
    }
}
