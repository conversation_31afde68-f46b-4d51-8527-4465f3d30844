package org.jeecg.excommons.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class DiagnosisSplitUtils {

    /**
     * 根据自定义分隔符拆分诊断结论
     *
     * @param text      原始文本
     * @param separator 分隔符字符串（每个字符视为独立分隔符）
     * @return 分割后的非空结果列表
     */
    public static List<String> split(String text, String separator) {
        // 处理空值边界情况
        if (StringUtils.isBlank(text)) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(separator)) {
            return Collections.emptyList();
        }

        // 添加换行符到分隔符中
        separator = separator + "\r\n";
        //separator = separator + "\r\n \t";

        // 生成安全的正则表达式
        String regex = buildSafeRegex(separator);

        // 执行分割并处理结果
        return Arrays.stream(text.split(regex))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 构建安全的字符类正则表达式
     * 特点：
     * 1. 自动转义正则特殊字符
     * 2. 正确处理连字符位置
     * 3. 支持任意字符作为分隔符（包括换行符）
     */
    private static String buildSafeRegex(String separator) {
        StringBuilder regexBuilder = new StringBuilder("[");
        boolean hasHyphen = false;

        // 预处理每个字符
        for (int i = 0; i < separator.length(); i++) {
            char c = separator.charAt(i);
            if (c == '\r' && i + 1 < separator.length() && separator.charAt(i + 1) == '\n') {
                regexBuilder.append("\\r\\n");
                i++; // 跳过下一个字符
                continue;
            }
            if (c == '\n') {
                regexBuilder.append("\\n");
                continue;
            }
            if (c == '\r') {
                regexBuilder.append("\\r");
                continue;
            }
            if (c == '-') {
                hasHyphen = true; // 特殊处理连字符
                continue;
            }
            regexBuilder.append(escapeRegexChar(c));
        }

        // 处理连字符（放在字符类末尾）
        if (hasHyphen) {
            regexBuilder.append("-");
        }

        regexBuilder.append("]+"); // 匹配一个或多个分隔符
        return regexBuilder.toString();
    }

    /**
     * 转义单个正则特殊字符
     * 规则：
     * - 基础正则元字符必须转义
     * - Unicode字符直接使用
     */
    private static String escapeRegexChar(char c) {
        switch (c) {
            case '\\':
            case '^':
            case '$':
            case '.':
            case '|':
            case '?':
            case '*':
            case '+':
            case '(':
            case ')':
            case '[':
            case ']':
            case '{':
            case '}':
                return "\\" + c;
            default:
                return String.valueOf(c);
        }
    }

    // 测试用例
    public static void main(String[] args) {
        // 测试1: 基础分隔符
        String text1 = "血压偏高;血糖异常，血脂过高。心电图正常\r\n心电图正常2";
        List<String> result1 = split(text1, ";，。");
        System.out.println(result1);
        // 输出: [血压偏高, 血糖异常, 血脂过高, 心电图正常, 心电图正常2]

        // 测试2: 包含特殊字符
        String text2 = "A*B?C|D+E-F";
        List<String> result2 = split(text2, "*?|+-");
        System.out.println(result2);
        // 输出: [A, B, C, D, E, F]

        // 测试3: 中英文混合分隔符
        String text3 = "异常1；异常2,异常3.异常4\n异常5";
        List<String> result3 = split(text3, "；,.");
        System.out.println(result3);
        // 输出: [异常1, 异常2, 异常3, 异常4, 异常5]

        // 测试4: 空值处理
        List<String> result4 = split("", ",");
        System.out.println(result4); // 输出: []

        // 测试5: 连字符处理
        String text5 = "A-B-C-D";
        List<String> result5 = split(text5, "-");
        System.out.println(result5);
        // 输出: [A, B, C, D]

        // 测试6: 纯换行符分割
        String text6 = "胆囊息肉\n" +
                "双侧甲状腺不均质改变\n" +
                "肝胰脾 双肾膀胱前列腺、颈动脉未见异常                     \n";
        List<String> result6 = split(text6, "\\r\\n,，；;。.");
        System.out.println(result6);
        // 输出: [行1, 行2, 行3, 行4]
    }
}