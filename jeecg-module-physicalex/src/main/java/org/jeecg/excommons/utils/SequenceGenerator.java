package org.jeecg.excommons.utils;

import org.jeecg.common.util.RedisUtil;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Component
public class SequenceGenerator {

    private RedisUtil redisUtil;
    private RedisLockUtil redisLockUtil;
    private JdbcTemplate jdbcTemplate;

    @Autowired
    public SequenceGenerator(RedisUtil redisUtil, RedisLockUtil redisLockUtil, JdbcTemplate jdbcTemplate) {
        this.redisUtil = redisUtil;
        this.redisLockUtil = redisLockUtil;
        this.jdbcTemplate = jdbcTemplate;
    }

    // 获取按今天日期格式化的序列号（如：YYMMDD格式）
    public String getFormatedSerialNumBaseToday(String businessCode) {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        return getFormattedSerialNum(businessCode, today);
    }

    // 获取按今天日期的序列号（如：YYMMDD格式）
    public Long getSerialNoBaseToday(String businessCode) {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        return getNextSequence(businessCode, today);
    }

    // 获取按订单号格式化的序列号（如：DDMMYYYY格式）
    public String getFormatedOrderNoBaseToday(String businessCode) {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("ddMMyyyy"));
        return getFormattedSerialNum(businessCode, today);
    }

    // 获取指定日期的序列号
    public Long getSerialNoBaseDate(String businessCode, LocalDate date) {
        String formattedDate = date.format(DateTimeFormatter.ofPattern("yyMMdd"));
        return getNextSequence(businessCode, formattedDate);
    }

    // 获取格式化的序列号（例如：日期+序列号）
    private String getFormattedSerialNum(String businessCode, String date) {
        Long seq = getNextSequence(businessCode, date);
        String formattedSeq = String.format("%04d", seq);
        if (seq > 9999) {
            formattedSeq = String.valueOf(seq);
        }
        return date + formattedSeq;
    }


    // 获取并生成下一个序列号
    public Long getNextSequence(String businessCode, String date) {
        String lockName = "sequence_lock:" + businessCode + ":" + date;
        RLock lock = redisLockUtil.getLock(lockName);

        try {
            // 尝试获取分布式锁，避免多个实例同时更新序列号
            boolean locked = lock.tryLock(100, 10, TimeUnit.SECONDS);  // 锁过期时间为 10 秒
            if (!locked) {
                throw new RuntimeException("获取序列号时，无法获取分布式锁！");
            }

            String redisKey = "sequence:" + businessCode + ":" + date;
            long seq = redisUtil.incr(redisKey, 1); // 使用 Redis 原子自增操作

            // 如果 Redis 中没有序列号，尝试从数据库加载
//            if (seq == 1) {  // 假设序列号从 1 开始
//                //从数据库获取序列号，保证序列号的唯一性
//                seq = getSequenceFromDB(businessCode, date) + 1;
//                redisUtil.set(redisKey, seq); // 保存序列号到 Redis
//            }
//            updateSequenceAsync(businessCode, date, seq);  // 异步更新数据库中的序列号
            return seq;
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();  // This releases the lock
            }
        }
    }

    private Long getSequenceFromDB(String businessCode, String date) {
        Long seq = null;
        try {
            seq = jdbcTemplate.queryForObject("SELECT sequence_number FROM sequence_table WHERE business_code = ? AND own_date = ?", Long.class, businessCode, date);
        } catch (Exception ignored) {
        }

        if (seq == null) {
            jdbcTemplate.update("INSERT INTO sequence_table (business_code, own_date, sequence_number) VALUES (?, ?, ?)", businessCode, date, 0);
            seq = 0L;
        }

        return seq;
    }


    // 异步更新数据库中的序列号
    @Async("taskExecutor")
    protected void updateSequenceAsync(String businessCode, String date, Long seq) {
        jdbcTemplate.update("UPDATE sequence_table SET sequence_number = ? WHERE business_code = ? AND own_date = ?", seq, businessCode, date);
    }
}
