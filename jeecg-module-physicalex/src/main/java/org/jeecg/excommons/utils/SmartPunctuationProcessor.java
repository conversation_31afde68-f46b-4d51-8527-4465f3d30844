package org.jeecg.excommons.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 智能标点符号处理器
 * 提供更精确、更通用的标点符号处理方法
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class SmartPunctuationProcessor {

    /**
     * 常见的分隔符标点（通常在开头时是无意义的）
     */
    private static final String SEPARATOR_PUNCTUATION = "：；，。、";
    
    /**
     * 有意义的标点符号（即使在开头也应该保留）
     */
    private static final String MEANINGFUL_PUNCTUATION = "()（）[]【】\"'《》<>!！?？";

    /**
     * 所有中文标点符号
     */
    private static final String CHINESE_PUNCTUATION = "，。？！；：、（）《》【】—…";
    
    /**
     * 所有英文标点符号
     */
    private static final String ENGLISH_PUNCTUATION = ",.?!;:()\"'[]{}/-";

    /**
     * 智能处理开头的标点符号
     * 只删除无意义的分隔符，保留有意义的标点符号
     * 
     * @param text 待处理的文本
     * @return 处理后的文本
     */
    public static String removeLeadingSeparators(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        
        // 只删除开头的分隔符和空白字符，保留有意义的标点符号
        return text.replaceFirst("^[" + SEPARATOR_PUNCTUATION + "\\s]+", "");
    }
    
    /**
     * 智能处理末尾的标点符号
     * 规范化末尾的标点符号，避免连续重复
     * 
     * @param text 待处理的文本
     * @return 处理后的文本
     */
    public static String normalizeTrailingPunctuation(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        
        String processed = text;
        
        // 1. 处理末尾的连续标点符号，替换为单个句号
        processed = processed.replaceAll("[，。,.;；、\\s]+$", "。");
        
        // 2. 如果末尾没有标点符号，不自动添加（保持原有逻辑）
        
        return processed;
    }
    
    /**
     * 智能处理中间的连续标点符号
     * 
     * @param text 待处理的文本
     * @return 处理后的文本
     */
    public static String normalizeMiddlePunctuation(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        
        String processed = text;
        
        // 处理连续的逗号
        processed = processed.replaceAll("，{2,}", "，");
        processed = processed.replaceAll(",{2,}", "，");
        
        // 处理连续的句号
        processed = processed.replaceAll("。{2,}", "。");
        processed = processed.replaceAll("\\.{2,}", "。");
        
        // 处理混合连续标点
        processed = processed.replaceAll("[，,]{2,}", "，");
        processed = processed.replaceAll("[。.]{2,}", "。");

        // 处理冒号后紧跟的逗号（特殊情况）
        processed = processed.replaceAll("：，+", "：");
        processed = processed.replaceAll("：,+", "：");
        processed = processed.replaceAll("；，+", "；");
        processed = processed.replaceAll("；,+", "；");

        // 处理逗号+句号的组合
        processed = processed.replaceAll("，。", "。");
        processed = processed.replaceAll(",。", "。");
        processed = processed.replaceAll("，\\.", "。");
        
        return processed;
    }
    
    /**
     * 全面的标点符号规范化处理
     * 结合开头、中间、末尾的处理逻辑
     * 
     * @param text 待处理的文本
     * @return 处理后的文本
     */
    public static String normalize(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        
        String processed = text;
        
        // 1. 处理换行符
        processed = processed.replace("\n", "，").replace("\r", "");
        
        // 2. 处理中间的连续标点符号
        processed = normalizeMiddlePunctuation(processed);
        
        // 3. 处理末尾的标点符号
        processed = normalizeTrailingPunctuation(processed);
        
        // 4. 处理多余的空格
        processed = processed.replaceAll("\\s+", " ").trim();
        
        return processed;
    }
    
    /**
     * 专门用于处理格式化后文本开头的标点符号
     * 这是针对模板渲染后可能出现的开头分隔符问题的专用方法
     * 
     * @param formattedText 模板渲染后的文本
     * @return 处理后的文本
     */
    public static String cleanFormattedTextLeading(String formattedText) {
        return removeLeadingSeparators(formattedText);
    }
    
    /**
     * 判断字符是否为分隔符标点
     * 
     * @param c 字符
     * @return 是否为分隔符标点
     */
    public static boolean isSeparatorPunctuation(char c) {
        return SEPARATOR_PUNCTUATION.indexOf(c) >= 0;
    }
    
    /**
     * 判断字符是否为有意义的标点符号
     * 
     * @param c 字符
     * @return 是否为有意义的标点符号
     */
    public static boolean isMeaningfulPunctuation(char c) {
        return MEANINGFUL_PUNCTUATION.indexOf(c) >= 0;
    }
    
    /**
     * 判断字符是否为标点符号
     * 
     * @param c 字符
     * @return 是否为标点符号
     */
    public static boolean isPunctuation(char c) {
        return CHINESE_PUNCTUATION.indexOf(c) >= 0 || ENGLISH_PUNCTUATION.indexOf(c) >= 0;
    }
    
    /**
     * 移除连续的标点符号，只保留最后一个
     * 这是一个兜底方法，确保不会有连续标点符号遗漏
     * 
     * @param text 待处理的文本
     * @return 处理后的文本
     */
    public static String removeConsecutivePunctuation(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        
        StringBuilder result = new StringBuilder();
        char[] chars = text.toCharArray();
        
        for (int i = 0; i < chars.length; i++) {
            char currentChar = chars[i];
            
            // 判断当前字符是否为标点符号
            if (isPunctuation(currentChar)) {
                // 查找连续标点符号的结束位置
                int j = i;
                while (j < chars.length && isPunctuation(chars[j])) {
                    j++;
                }
                
                // 只保留最后一个标点符号
                if (j > i + 1) {
                    result.append(chars[j - 1]);
                    i = j - 1; // 跳过已处理的连续标点
                } else {
                    result.append(currentChar);
                }
            } else {
                result.append(currentChar);
            }
        }
        
        return result.toString();
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试用例
        System.out.println("=== 测试开头分隔符处理 ===");
        System.out.println("原文: '：检查结果(正常)' -> '" + removeLeadingSeparators("：检查结果(正常)") + "'");
        System.out.println("原文: '；，。异常发现' -> '" + removeLeadingSeparators("；，。异常发现") + "'");
        System.out.println("原文: '(正常)检查结果' -> '" + removeLeadingSeparators("(正常)检查结果") + "'");
        System.out.println("原文: '\"重要\"提示' -> '" + removeLeadingSeparators("\"重要\"提示") + "'");
        
        System.out.println("\n=== 测试全面规范化处理 ===");
        System.out.println("原文: '检查结果：，，，异常发现。。。' -> '" + normalize("检查结果：，，，异常发现。。。") + "'");
        System.out.println("原文: '：；，血压偏高，，，建议复查。。。' -> '" + normalize("：；，血压偏高，，，建议复查。。。") + "'");
        System.out.println("原文: '(重要)心电图正常，，，。。。无异常' -> '" + normalize("(重要)心电图正常，，，。。。无异常") + "'");
    }
}
