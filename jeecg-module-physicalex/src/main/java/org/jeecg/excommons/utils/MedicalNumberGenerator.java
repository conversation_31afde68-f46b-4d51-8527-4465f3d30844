package org.jeecg.excommons.utils;

import org.jeecg.excommons.ExConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.jeecg.modules.basicinfo.service.ISysSettingService;

@Component
public class MedicalNumberGenerator {

    @Autowired
    private ISysSettingService sysSettingService;

    @Autowired
    private SequenceGenerator sequenceGenerator;

    public String generateMedicalNumber() {
        // Query the SysSetting for the prefix and length configuration
        String prefix = sysSettingService.getValueByCode("exam_no_prefix");

        // Generate a unique number using SequenceGenerator
        String serialNumber = sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_REG_SERIAL_NO);

        return prefix + serialNumber;
    }

    public String generateBarcodeNumber() {
        // Query the SysSetting for the prefix and length configuration
        String prefix = sysSettingService.getValueByCode("barcode_number_prefix");
        String lengthStr = sysSettingService.getValueByCode("barcode_number_length");
        int length = Integer.parseInt(lengthStr);

        // Generate a unique number using SequenceGenerator
        Long serialNumber = sequenceGenerator.getSerialNoBaseToday("barcode_number");
        String uniqueNumber = String.format("%0" + (length - prefix.length()) + "d", serialNumber);

        return prefix + uniqueNumber;
    }
}