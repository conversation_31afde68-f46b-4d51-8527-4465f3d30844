package org.jeecg.excommons.utils;

import java.util.ArrayList;
import java.util.List;

public class TextUtils {
    public static List<String> extractAbnormalPhrases(String text) {
        List<String> abnormalPhrases = new ArrayList<>();
        // 1、2. 3, 4)等，考虑中英文逗号、点、右括号
        String regex = "(\\d+[、.．,，\\)])";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(text);

        List<Integer> startList = new ArrayList<>();
        List<Integer> endList = new ArrayList<>();
        List<String> phrases = new ArrayList<>();

        while (matcher.find()) {
            startList.add(matcher.start());
            endList.add(matcher.end());
        }

        // 没有编号就整体返回
        if (startList.isEmpty()) {
            String cleaned = text.replaceAll("[（(][^）)]*[）)]", "").trim();
            if (!cleaned.isEmpty()) abnormalPhrases.add(cleaned.replaceAll("[，,。；！？：:;!?、]+$", ""));
            return abnormalPhrases;
        }

        // 逐段切分
        for (int i = 0; i < startList.size(); i++) {
            int contentStart = endList.get(i);
            int contentEnd = (i == startList.size() - 1) ? text.length() : startList.get(i + 1);
            String phrase = text.substring(contentStart, contentEnd).trim();

            // 去除首尾标点和换行
            phrase = phrase.replaceAll("^[：:，,。；!?！、\\s]*", "").replaceAll("[，,。；!?！、\\s]+$", "");
            // 去括号说明
            phrase = phrase.replaceAll("[（(][^）)]*[）)]", "").trim();

            if (!phrase.isEmpty()) abnormalPhrases.add(phrase);
        }
        return abnormalPhrases;
    }



    public static void main(String[] args) {
        List<String> abnormalPhrases;

        // 1. 标准编号，空格分隔
        abnormalPhrases = extractAbnormalPhrases("1、体重指数BMI偏高 2、血压偏高 3、高密度胆固醇代谢异常。");
        System.out.println(abnormalPhrases);
        // [体重指数BMI偏高, 血压偏高, 高密度胆固醇代谢异常]

        // 2. 换行分隔
        abnormalPhrases = extractAbnormalPhrases("1、体重指数BMI偏高\n2、血压偏高\n3、高密度胆固醇代谢异常。");
        System.out.println(abnormalPhrases);
        // [体重指数BMI偏高, 血压偏高, 高密度胆固醇代谢异常]

        // 3. 中文顿号、逗号、分号、句号分隔
        abnormalPhrases = extractAbnormalPhrases("1、体重指数BMI偏高，2、血压偏高；3、高密度胆固醇代谢异常。");
        System.out.println(abnormalPhrases);
        // [体重指数BMI偏高, 血压偏高, 高密度胆固醇代谢异常]

        // 4. 编号后无内容
        abnormalPhrases = extractAbnormalPhrases("1、 2、血压偏高 3、高密度胆固醇代谢异常。");
        System.out.println(abnormalPhrases);
        // [血压偏高, 高密度胆固醇代谢异常]

        // 5. 英文标点的编号
        abnormalPhrases = extractAbnormalPhrases("1.体重指数BMI偏高 2) 血压偏高 3。高密度胆固醇代谢异常。");
        System.out.println(abnormalPhrases);
        // [体重指数BMI偏高, 血压偏高, 高密度胆固醇代谢异常]

        // 6. 无任何编号
        abnormalPhrases = extractAbnormalPhrases("血压偏高，高密度胆固醇代谢异常。");
        System.out.println(abnormalPhrases);
        // [血压偏高，高密度胆固醇代谢异常]

        // 7. 内容包含数字但不是编号
        abnormalPhrases = extractAbnormalPhrases("1、白细胞3.5，2、血小板140，3、血红蛋白100mg偏低。");
        System.out.println(abnormalPhrases);
        // [白细胞3.5, 血小板140, 血红蛋白100mg偏低]

        // 8. 括号说明
        abnormalPhrases = extractAbnormalPhrases("1、血糖偏高（进食后测定） 2、尿酸偏高(空腹) 3、血压升高");
        System.out.println(abnormalPhrases);
        // [血糖偏高, 尿酸偏高, 血压升高]

        // 9. 中间夹英文
        abnormalPhrases = extractAbnormalPhrases("1、LDL-C升高, 2、HDL-C偏低, 3、Triglyceride高。");
        System.out.println(abnormalPhrases);
        // [LDL-C升高, HDL-C偏低, Triglyceride高]

        // 10. 末尾有结尾标点但无最后编号
        abnormalPhrases = extractAbnormalPhrases("1、体重指数BMI偏高 2、血压偏高。");
        System.out.println(abnormalPhrases);
        // [体重指数BMI偏高, 血压偏高]

        // 11. 多位数字编号
        abnormalPhrases = extractAbnormalPhrases("10、胆固醇偏高 11、肝酶偏高 12、血糖偏低。");
        System.out.println(abnormalPhrases);
        // [胆固醇偏高, 肝酶偏高, 血糖偏低]

        // 12. 有多余空格
        abnormalPhrases = extractAbnormalPhrases(" 1、 体重指数BMI偏高   2、血压偏高    3、 血糖偏低 ");
        System.out.println(abnormalPhrases);
        // [体重指数BMI偏高, 血压偏高, 血糖偏低]

        // 13. 特殊中括号、小圆圈编号（如有拓展正则可测）
        abnormalPhrases = extractAbnormalPhrases("①血压偏高 ②血糖偏高 ③血脂异常");
        System.out.println(abnormalPhrases);
        // [①血压偏高 ②血糖偏高 ③血脂异常]  // 当前版本如需兼容可扩展正则

        // 14. 特殊中括号、小圆圈编号（如有拓展正则可测）
        abnormalPhrases = extractAbnormalPhrases("1、血常规：平均血红蛋白浓度偏高(355g/L)，血小板体积分布宽度偏低(8.9fL)，大型血小板比率偏低(16.40%)，中性粒细胞百分比偏低(39.1%)，淋巴细胞百分比偏高(52.20%)。\n" + "2、肝功全项：球蛋白偏低(17.70g/L)，白球比偏高(2.5)，谷丙转氨酶偏低(6.10U/L)，谷草/谷丙偏高(2.2U/L)，前白蛋白偏低(18.650mg/dL)。\n" + "3、肾功全项：二氧化碳结合力偏低(20.70mmol/L)。\n" + "4、尿常规（常规+有形成分）：隐血阳性(3+)，红细胞偏高(418/ul)，非鳞状上皮细胞阳性(+/ul)，小圆形上皮细胞偏高(6.7/ul)。");
        System.out.println(abnormalPhrases);
    }
}