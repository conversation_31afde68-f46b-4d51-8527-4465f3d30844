package org.jeecg.excommons.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 身份证工具类，用于解析身份证号码中的信息，如性别、年龄和出生日期。
 */
public class IdCardUtils {

    /**
     * 验证身份证号码是否合法。
     *
     * @param idCard 身份证号码
     * @return 如果合法返回 true，否则返回 false
     */
    public static boolean isValidCard(String idCard) {
        if (idCard == null || (idCard.length() != 15 && idCard.length() != 18)) {
            return false;
        }
        String idCard18;
        if (idCard.length() == 15) {
            idCard18 = convert15To18(idCard);
        } else {
            idCard18 = idCard;
        }
        return validateChecksum(idCard18);
    }

    /**
     * 获取出生日期。
     *
     * @param idCard 身份证号码
     * @return 出生日期，如果身份证号码不合法，返回 null
     */
    public static Date getBirthDate(String idCard) {
        if (!isValidCard(idCard)) {
            return null;
        }
        String birthStr;
        if (idCard.length() == 18) {
            birthStr = idCard.substring(6, 14);
        } else {
            birthStr = "19" + idCard.substring(6, 12);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            return sdf.parse(birthStr);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取年龄。
     *
     * @param idCard 身份证号码
     * @return 年龄，如果身份证号码不合法，返回 -1
     */
    public static int getAge(String idCard) {
        Date birthDate = getBirthDate(idCard);
        if (birthDate == null) {
            return -1;
        }
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthDate)) {
            return -1;
        }
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

        cal.setTime(birthDate);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

        int age = yearNow - yearBirth;

        if (monthNow < monthBirth || (monthNow == monthBirth && dayOfMonthNow < dayOfMonthBirth)) {
            age--;
        }
        return age;
    }

    /**
     * 获取性别。
     *
     * @param idCard 身份证号码
     * @return 性别，"男" 或 "女"，如果身份证号码不合法，返回 null
     */
    public static String getGender(String idCard) {
        if (!isValidCard(idCard)) {
            return null;
        }
        int genderCode;
        if (idCard.length() == 18) {
            genderCode = Character.getNumericValue(idCard.charAt(16));
        } else {
            genderCode = Character.getNumericValue(idCard.charAt(14));
        }
        if (genderCode % 2 == 0) {
            return "女";
        } else {
            return "男";
        }
    }

    /**
     * 将15位身份证号码转换为18位。
     *
     * @param idCard15 15位身份证号码
     * @return 18位身份证号码
     */
    private static String convert15To18(String idCard15) {
        String idCard17 = idCard15.substring(0, 6) + "19" + idCard15.substring(6);
        char[] chars = idCard17.toCharArray();
        int[] coefs = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        char[] code = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        int sum = 0;
        for (int i = 0; i < coefs.length; i++) {
            sum += coefs[i] * Character.getNumericValue(chars[i]);
        }
        char lastChar = code[sum % 11];
        return idCard17 + lastChar;
    }

    /**
     * 校验18位身份证号码的校验位是否正确。
     *
     * @param idCard18 18位身份证号码
     * @return 如果校验位正确返回 true，否则返回 false
     */
    private static boolean validateChecksum(String idCard18) {
        char[] chars = idCard18.toCharArray();
        int[] coefs = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        char[] code = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        int sum = 0;
        for (int i = 0; i < coefs.length; i++) {
            if (!Character.isDigit(chars[i])) {
                return false;
            }
            sum += coefs[i] * Character.getNumericValue(chars[i]);
        }
        char expectedCode = code[sum % 11];
        char actualCode = chars[17];
        // 处理可能的小写 x
        if (actualCode == 'x') {
            actualCode = 'X';
        }
        return expectedCode == actualCode;
    }

    /**
     * 示例主函数，演示如何使用工具类。
     */
    public static void main(String[] args) {
        String idCard = "11010519491231002x"; // 示例身份证号码，请替换为实际号码

        // 验证身份证号是否合法
        boolean isValid = IdCardUtils.isValidCard(idCard);
        System.out.println("身份证号是否合法: " + isValid);

        if (isValid) {
            // 获取出生日期
            Date birthDate = IdCardUtils.getBirthDate(idCard);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            System.out.println("出生日期: " + sdf.format(birthDate));

            // 获取年龄
            int age = IdCardUtils.getAge(idCard);
            System.out.println("年龄: " + age);

            // 获取性别
            String gender = IdCardUtils.getGender(idCard);
            System.out.println("性别: " + gender);
        } else {
            System.out.println("身份证号码不合法，无法解析信息。");
        }
    }
}

