package org.jeecg.excommons.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ColonContentParser {

    /**
     * 解析输入字符串，若包含冒号则解析冒号后面的内容，若不包含则直接分隔整个字符串
     *
     * @param input 输入字符串
     * @return 解析后的词列表
     */
    public static List<String> parseColonSeparatedContent(String input) {
        List<String> result = new ArrayList<>();

        // 匹配冒号及其后面的内容
        String pattern = ".*?：(.*)";
        Matcher matcher = Pattern.compile(pattern).matcher(input);

        String contentToSplit;

        if (matcher.find()) {
            // 如果匹配到冒号后面的内容
            contentToSplit = matcher.group(1).trim();
        } else {
            // 如果没有冒号，直接使用整个输入字符串
            contentToSplit = input.trim();
        }

        // 根据标点符号分割（逗号、顿号、分号、空格等）
        String[] items = contentToSplit.split("[，,、；;\\s]+");

        for (String item : items) {
            if (!item.isEmpty()) {
                result.add(item.trim());
            }
        }

        return result;
    }

    public static void main(String[] args) {
        // 示例 1: 包含冒号
        String input1 = "4、血常规(五分类)：中性粒细胞百分比偏低(35.10%)，淋巴细胞百分比偏高(56.20%)，左侧扁桃体肥大。";
        List<String> parsedContent1 = parseColonSeparatedContent(input1);

        System.out.println("解析结果（包含冒号）：");
        for (String item : parsedContent1) {
            System.out.println(item);
        }

        // 示例 2: 不包含冒号
        String input2 = "中性粒细胞百分比偏低(35.10%)，淋巴细胞百分比偏高(56.20%)，左侧扁桃体肥大";
        List<String> parsedContent2 = parseColonSeparatedContent(input2);

        System.out.println("解析结果（不包含冒号）：");
        for (String item : parsedContent2) {
            System.out.println(item);
        }
    }
}
