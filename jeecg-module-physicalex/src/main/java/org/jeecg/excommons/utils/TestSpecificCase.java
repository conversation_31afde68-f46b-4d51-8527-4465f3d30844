package org.jeecg.excommons.utils;

/**
 * 测试特定的标点符号处理问题
 */
public class TestSpecificCase {
    
    public static void main(String[] args) {
        System.out.println("=== 测试特定的连续句号问题 ===");
        
        // 测试原始问题
        String input = "2、残留胸腺可能，请结合临床，建议复查。。";
        String expected = "2、残留胸腺可能，请结合临床，建议复查。";
        
        System.out.println("原始输入: '" + input + "'");
        System.out.println("期望输出: '" + expected + "'");
        
        // 测试各个方法
        System.out.println("\n=== 分步测试 ===");
        
        // 1. 测试 normalizeMiddlePunctuation
        String step1 = normalizeMiddlePunctuationSimple(input);
        System.out.println("Step 1 (normalizeMiddlePunctuation): '" + step1 + "'");
        
        // 2. 测试 normalizeTrailingPunctuation
        String step2 = normalizeTrailingPunctuationSimple(step1);
        System.out.println("Step 2 (normalizeTrailingPunctuation): '" + step2 + "'");
        
        // 3. 测试完整的 normalize
        String result = normalizeSimple(input);
        System.out.println("Final result (normalize): '" + result + "'");
        
        // 验证结果
        boolean success = expected.equals(result);
        System.out.println("\n结果: " + (success ? "✅ 成功" : "❌ 失败"));
        
        if (!success) {
            System.out.println("需要修复连续句号处理逻辑");
        }
        
        // 测试其他连续标点情况
        System.out.println("\n=== 其他连续标点测试 ===");
        testCase("连续逗号: ", "测试，，，结果", "测试，结果");
        testCase("连续句号: ", "测试。。。结果", "测试。结果");
        testCase("混合标点: ", "测试，。。，结果", "测试。结果");
        testCase("末尾连续: ", "测试结果。。。", "测试结果。");
    }
    
    private static void testCase(String desc, String input, String expected) {
        String result = normalizeSimple(input);
        boolean success = expected.equals(result);
        System.out.println(desc + "'" + input + "' -> '" + result + "' " + (success ? "✅" : "❌"));
    }
    
    /**
     * 简化版的中间标点处理
     */
    private static String normalizeMiddlePunctuationSimple(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        String processed = text;
        
        // 处理连续的逗号
        processed = processed.replaceAll("，{2,}", "，");
        processed = processed.replaceAll(",{2,}", "，");
        
        // 处理连续的句号
        processed = processed.replaceAll("。{2,}", "。");
        processed = processed.replaceAll("\\.{2,}", "。");
        
        // 处理混合连续标点
        processed = processed.replaceAll("[，,]{2,}", "，");
        processed = processed.replaceAll("[。.]{2,}", "。");
        
        // 处理逗号+句号的组合
        processed = processed.replaceAll("，。", "。");
        processed = processed.replaceAll(",。", "。");
        processed = processed.replaceAll("，\\.", "。");
        
        return processed;
    }
    
    /**
     * 简化版的末尾标点处理
     */
    private static String normalizeTrailingPunctuationSimple(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        String processed = text;
        
        // 处理末尾的连续标点符号，替换为单个句号
        processed = processed.replaceAll("[，。,.;；、\\s]+$", "。");
        
        return processed;
    }
    
    /**
     * 简化版的完整规范化
     */
    private static String normalizeSimple(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        String processed = text;
        
        // 1. 处理换行符
        processed = processed.replace("\n", "，").replace("\r", "");
        
        // 2. 处理中间的连续标点符号
        processed = normalizeMiddlePunctuationSimple(processed);
        
        // 3. 处理末尾的标点符号
        processed = normalizeTrailingPunctuationSimple(processed);
        
        // 4. 处理多余的空格
        processed = processed.replaceAll("\\s+", " ").trim();
        
        return processed;
    }
}
