package org.jeecg.excommons.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringNormalizer {

    /**
     * 规范化处理字符串
     * 1. 处理异常序号：保留首个数字序号，移除后续所有数字序号
     * 2. 规范标点符号：处理连续句号、逗号结尾等异常标点
     *
     * @param input 原始字符串
     * @return 规范后的字符串
     */
    public static String normalize(String input) {
        String processed = processAbnormalNumber(input);
        processed = processPunctuation(processed);
        return processed;
    }

    /**
     * 处理异常序号（保留首个数字序号）
     */
    private static String processAbnormalNumber(String input) {
        // 匹配首个数字序号，并移除后续所有数字序号
        String regex = "^(\\d+、)(.*)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String firstNumber = matcher.group(1); // 提取首个序号
            String remaining = matcher.group(2)
                    .replaceAll("\\d+、", ""); // 移除后续所有数字序号
            return firstNumber + remaining;
        }
        return input;
    }

    /**
     * 规范标点符号处理
     */
    private static String processPunctuation(String input) {
        String processed = input;

        // 1. 处理文本中间的连续标点符号（保留一个最合适的）
        // 连续的逗号 -> 单个逗号
        processed = processed.replaceAll("，{2,}", "，");
        processed = processed.replaceAll(",{2,}", "，");

        // 连续的句号 -> 单个句号
        processed = processed.replaceAll("。{2,}", "。");
        processed = processed.replaceAll("\\.{2,}", "。");

        // 2. 处理混合连续标点（中间位置）
        processed = processed.replaceAll("[，,]{2,}", "，");
        processed = processed.replaceAll("[。.]{2,}", "。");

        // 3. 处理逗号+句号的组合（中间和末尾）
        processed = processed.replaceAll("，。", "。");
        processed = processed.replaceAll(",。", "。");
        processed = processed.replaceAll("，\\.", "。");

        // 4. 处理末尾的多个混合标点，替换为单个句号
        processed = processed.replaceAll("[，。,.;；、\\s]+$", "。");

        // 5. 处理多余的空格
        processed = processed.replaceAll("\\s+", " ").trim();

        // 6. 兜底处理：任何连续的标点符号都只保留最后一个
        processed = removeConsecutivePunctuation(processed);

        return processed;
    }

    /**
     * 兜底处理方法：移除连续的标点符号，只保留最后一个
     * 这是最后的保险措施，确保不会有任何连续标点符号遗漏
     */
    private static String removeConsecutivePunctuation(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder result = new StringBuilder();
        char[] chars = input.toCharArray();

        for (int i = 0; i < chars.length; i++) {
            char currentChar = chars[i];

            // 判断当前字符是否为标点符号
            if (isPunctuation(currentChar)) {
                // 查找连续标点符号的结束位置
                int j = i;
                while (j < chars.length && isPunctuation(chars[j])) {
                    j++;
                }

                // 只保留最后一个标点符号
                if (j > i + 1) {
                    result.append(chars[j - 1]);
                    i = j - 1; // 跳过已处理的连续标点
                } else {
                    result.append(currentChar);
                }
            } else {
                result.append(currentChar);
            }
        }

        return result.toString();
    }

    /**
     * 判断字符是否为标点符号
     */
    private static boolean isPunctuation(char c) {
        // 使用字符串包含的方式来避免字符编码问题
        String punctuations = "，。,.;；、!！?？:：\"\"\"''（）()[]【】";
        return punctuations.indexOf(c) >= 0;
    }

    public static void main(String[] args) {
        // 测试用例
        String test1 = "6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类,。。。";
        String test2 = "6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类，,。";
        String test3 = "6、2、双侧乳腺实性结节-增生结节  符合BI-RADS 3类，。。";
        String test4 = "6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类,。";
        String test5 = "6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类。末尾的标点符号处理仍不能满足";
        // 新增测试用例 - 测试连续标点符号
        String test6 = "检查结果：，，，异常发现";
        String test7 = "血压偏高。。。。建议复查";
        String test8 = "心电图正常，，，。。。无异常";
        String test9 = "体重指数：：：：正常范围";
        String test10 = "建议：！！！注意饮食？？？";

        System.out.println("原始测试用例：");
        System.out.println(normalize(test1)); // 6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类。
        System.out.println(normalize(test2)); // 6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类。
        System.out.println(normalize(test3)); // 6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类。
        System.out.println(normalize(test4)); // 6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类。
        System.out.println(normalize(test5)); // 6、双侧乳腺实性结节-增生结节  符合BI-RADS 3类。末尾的标点符号处理仍不能满足

        System.out.println("\n连续标点符号测试用例：");
        System.out.println("test6: " + normalize(test6)); // 检查结果：异常发现
        System.out.println("test7: " + normalize(test7)); // 血压偏高。建议复查
        System.out.println("test8: " + normalize(test8)); // 心电图正常。无异常
        System.out.println("test9: " + normalize(test9)); // 体重指数：正常范围
        System.out.println("test10: " + normalize(test10)); // 建议？注意饮食？
    }
}