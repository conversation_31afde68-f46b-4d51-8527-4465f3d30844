package org.jeecg.excommons.utils;

import java.util.LinkedHashMap;
import java.util.Map;

public class ScriptCache extends LinkedHashMap<String, Class<?>> {
    private final int maxCapacity;

    public ScriptCache(int maxCapacity) {
        // 第三个参数 true 表示按访问顺序排序
        super(maxCapacity, 0.75f, true);
        this.maxCapacity = maxCapacity;
    }

    @Override
    protected boolean removeEldestEntry(Map.Entry<String, Class<?>> eldest) {
        // 当缓存大小超过最大容量时，移除最老的条目
        return size() > maxCapacity;
    }
}
