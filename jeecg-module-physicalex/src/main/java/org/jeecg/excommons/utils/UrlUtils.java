package org.jeecg.excommons.utils;

import org.apache.commons.lang3.StringUtils;

public class UrlUtils {
    /**
     * 拼接两个 URL 部分，确保中间只有一个斜线。
     *
     * @param baseUrl 基础 URL
     * @param path    追加的路径
     * @return 拼接后的完整 URL
     */
    public static String concatenateUrl(String baseUrl, String path) {
        if (StringUtils.isBlank(baseUrl)) {
            return path;
        }
        if (StringUtils.isBlank(path)) {
            return baseUrl;
        }
        return StringUtils.removeEnd(baseUrl, "/") + "/" + StringUtils.removeStart(path, "/");
    }
}

