package org.jeecg.excommons.utils;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RedisLockUtil {
    @Autowired
    private RedissonClient redissonClient;


    // 获取分布式锁
    public RLock getLock(String lockName) {
        return redissonClient.getLock(lockName);
    }

    // 关闭连接
    public void close() {
        redissonClient.shutdown();
    }
}
