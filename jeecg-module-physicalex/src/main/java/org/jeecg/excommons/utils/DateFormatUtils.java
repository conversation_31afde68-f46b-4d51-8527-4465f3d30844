package org.jeecg.excommons.utils;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateFormatUtils {
    // 计算两个日期之间的年、月、日差异
    public static String formatBetween(LocalDate targetDate, LocalDate currentDate) {
        Period period = Period.between(currentDate, targetDate);

        int years = period.getYears();
        int months = period.getMonths();
        int days = period.getDays();

        StringBuilder result = new StringBuilder();
        if (years != 0) {
            result.append(Math.abs(years)).append("年");
        }
        /*if (years != 0 && months != 0) {
            result.append("零");
        }*/
        if (months != 0) {
            result.append(Math.abs(months)).append("个月");
        }
        /*if (months != 0 && days != 0) {
            result.append("零");
        }*/
        if (days != 0) {
            result.append(Math.abs(days)).append("天");
        }

        if (targetDate.isBefore(currentDate)) {
            result.append("前");
        } else {
            result.append("后");
        }

        return result.toString();
    }

    // 重载方法，入参是Date类型
    public static String formatBetween(Date targetDate, Date currentDate) {
        LocalDate targetLocalDate = targetDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate currentLocalDate = currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return formatBetween(targetLocalDate, currentLocalDate);
    }
    public static void main(String[] args) {
        // 示例：日期之间的差异
        LocalDate target = LocalDate.parse("2022-12-05", DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate current = LocalDate.now();

        String targetDateText = formatBetween(target, current);
        System.out.println(targetDateText); // 输出格式：x年x月x天
    }
}
