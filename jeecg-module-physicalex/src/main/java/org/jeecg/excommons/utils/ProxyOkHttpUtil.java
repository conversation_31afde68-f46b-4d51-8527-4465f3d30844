package org.jeecg.excommons.utils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jeecg.config.ProxyProperties;
import org.jeecg.modules.ai.service.StreamResponseCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ProxyOkHttpUtil {

    private final OkHttpClient client;
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");


    @Autowired
    public ProxyOkHttpUtil(ProxyProperties proxyProperties) {

        boolean proxyEnabled = proxyProperties.isEnabled();
        OkHttpClient.Builder builder = new OkHttpClient.Builder().connectTimeout(60, TimeUnit.SECONDS)  // Set connection timeout
                .readTimeout(600, TimeUnit.SECONDS)    // Set read timeout
                .retryOnConnectionFailure(true)        // Enable retry on connection failure
                .connectionPool(new ConnectionPool(5, 5, TimeUnit.MINUTES)); // Set connection pool

        if (proxyEnabled) {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyProperties.getHost(), proxyProperties.getPort()));
            builder.proxy(proxy);

            // If proxy requires authentication, set proxy authenticator
            Authenticator proxyAuthenticator = new Authenticator() {
                @Override
                public okhttp3.Request authenticate(Route route, Response response) throws IOException {
                    String credential = Credentials.basic(proxyProperties.getUsername(), proxyProperties.getPassword());
                    return response.request().newBuilder().header("Proxy-Authorization", credential).build();
                }
            };
            builder.proxyAuthenticator(proxyAuthenticator);
        }

        // Build OkHttpClient instance
        this.client = builder.build();
    }

    // Perform a GET request with optional custom headers
    public String get(String url, Map<String, String> customHeaders) throws IOException {
        Request.Builder requestBuilder = new Request.Builder().url(url);

        // Add custom headers if any
        if (customHeaders != null) {
            for (Map.Entry<String, String> entry : customHeaders.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return response.body().string();
        }
    }

    // Perform a POST request with optional custom headers
    public String post(String url, String json, Map<String, String> customHeaders) throws IOException {
        RequestBody body = RequestBody.create(json, MediaType.get("application/json; charset=utf-8"));
        Request.Builder requestBuilder = new Request.Builder().url(url).post(body);

        // Add custom headers if any
        if (customHeaders != null) {
            for (Map.Entry<String, String> entry : customHeaders.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException(url + " Unexpected code " + response.code() + ": " + response.message() + " - " + response.body().string());
            }
            assert response.body() != null;
            return response.body().string();
        }
    }

    /**
     * 执行流式POST请求
     *
     * @param url           请求URL
     * @param json          请求体JSON字符串
     * @param customHeaders 自定义请求头
     * @param callback      流式响应回调
     */
    public void postStream(String url, String json, Map<String, String> customHeaders, StreamResponseCallback callback) {
        RequestBody body = RequestBody.create(json, JSON);
        Request.Builder requestBuilder = new Request.Builder().url(url).post(body);

        // 添加自定义请求头
        if (customHeaders != null) {
            for (Map.Entry<String, String> entry : customHeaders.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();

        log.info("Making stream POST request to: {}", url);
        log.debug("Request body: {}", json.length() > 200 ? json.substring(0, 200) + "..." : json);

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                log.error("HTTP request failed: {}", e.getMessage(), e);
                callback.onError(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                log.info("Received HTTP response: {} {}", response.code(), response.message());
                log.debug("Response headers: {}", response.headers());

                try {
                    if (!response.isSuccessful()) {
                        String errorBody = "";
                        if (response.body() != null) {
                            errorBody = response.body().string();
                        }
                        log.error("HTTP request failed: {} {} - {}", response.code(), response.message(), errorBody);
                        throw new IOException(url + " Unexpected code " + response.code() + ": " + response.message() + " - " + errorBody);
                    }

                    ResponseBody responseBody = response.body();
                    if (responseBody == null) {
                        log.error("Response body is null");
                        throw new IOException("Response body is null");
                    }

                    log.info("Starting to read stream response...");
                    try (BufferedReader reader = new BufferedReader(responseBody.charStream())) {
                        String line;
                        int lineCount = 0;
                        while ((line = reader.readLine()) != null) {
                            lineCount++;

                            // 记录前几行的完整内容用于调试
                            if (lineCount <= 3) {
                                log.debug("Stream line {}: [{}]", lineCount, line);
                            } else {
                                log.trace("Received line {}: {}", lineCount, line.length() > 100 ? line.substring(0, 100) + "..." : line);
                            }

                            if (line.trim().isEmpty()) {
                                continue; // 跳过空行
                            }

                            callback.onMessage(line);
                        }
                        log.info("Stream reading completed, total lines: {}", lineCount);
                    }
                    callback.onComplete();
                } catch (Exception e) {
                    log.error("Error processing stream response", e);
                    callback.onError(e);
                } finally {
                    response.close();
                }
            }
        });
    }



    /**
     * 执行流式 POST 请求，并将响应体按行通过 SseEmitter 推送。
     * 假设上游服务返回的是基于文本的、按行分隔的数据流。
     *
     * @param url           目标请求 URL
     * @param jsonPayload   请求体的 JSON 字符串
     * @param customHeaders 自定义请求头 Map (可为 null)
     * @param emitter       用于向客户端推送 SSE 事件的 SseEmitter 实例
     */
    public void postStream(String url, String jsonPayload, Map<String, String> customHeaders, SseEmitter emitter) {
        // 1. 构建请求体
        RequestBody body = RequestBody.create(jsonPayload, JSON);

        // 2. 构建请求 Builder
        Request.Builder requestBuilder = new Request.Builder().url(url).post(body);

        // 3. 添加自定义请求头
        if (customHeaders != null) {
            customHeaders.forEach(requestBuilder::header); // 使用 forEach 简化添加
        }

        // 4. 构建最终请求对象
        Request request = requestBuilder.build();
        log.info("发起流式 POST 请求至: {}", url);

        // 5. 异步执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                // 处理网络层面的失败（例如，无法连接）
                log.error("流式 POST 请求失败 (网络层面). URL: {}, Error: {}", url, e.getMessage(), e);
                // 通过 emitter 通知客户端错误
                emitter.completeWithError(new IOException("上游服务请求失败: " + e.getMessage(), e));
            }

            @Override
            public void onResponse(Call call, Response response) {
                // 使用 try-with-resources 确保 ResponseBody 总是被关闭
                try (ResponseBody responseBody = response.body()) {
                    // 6. 检查 HTTP 响应状态码
                    if (!response.isSuccessful()) {
                        String errorBodyContent = responseBody != null ? responseBody.string() : "[无响应体]";
                        log.error("流式 POST 请求返回失败状态码. URL: {}, Status: {} {}, Body: {}",
                                url, response.code(), response.message(), errorBodyContent);
                        emitter.completeWithError(new IOException("上游服务返回错误状态 " + response.code() + ": " + response.message()));
                        return; // 状态码非 2xx，直接返回
                    }

                    // 7. 检查响应体是否为空
                    if (responseBody == null) {
                        log.warn("流式 POST 请求成功但响应体为空. URL: {}", url);
                        emitter.complete(); // 响应成功但无内容，正常完成
                        return;
                    }

                    log.info("流式 POST 请求成功，开始读取并推送响应流. URL: {}", url);

                    // 8. 按行读取并推送 SSE 事件
                    // 使用 InputStreamReader 指定字符集，BufferedReader 提高效率
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream(), StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            // 检查当前线程是否被中断 (对于长时间运行的任务是个好习惯)
                            if (Thread.currentThread().isInterrupted()) {
                                log.warn("流式读取线程被中断. URL: {}", url);
                                throw new IOException("读取线程被中断");
                            }

                            try {
                                // 发送 SSE 事件，包含数据行
                                // 使用 name("update") 或其他有意义的名称，方便前端处理
                                // log.trace("推送 SSE 数据行: {}", line); // Trace 级别日志记录推送内容
                                emitter.send(SseEmitter.event()
                                        .name("update") // 定义事件类型
                                        .data(line)     // 设置数据
                                        .reconnectTime(5000)); // 设置建议重连时间 (ms)
                            } catch (IOException sendEx) {
                                // 发送时发生 IO 异常，通常意味着客户端断开连接
                                log.warn("向客户端推送 SSE 数据时发生 IO 异常 (可能客户端已断开). URL: {}, Error: {}", url, sendEx.getMessage());
                                break; // 中断读取和推送循环
                            } catch (IllegalStateException stateEx) {
                                // Emitter 状态异常，通常意味着它已经被完成 (可能由超时、错误或客户端关闭触发)
                                log.warn("向客户端推送 SSE 数据时发生状态异常 (Emitter 可能已完成). URL: {}, Error: {}", url, stateEx.getMessage());
                                break; // 中断读取和推送循环
                            }
                        }

                        // 9. 循环正常结束，表示流已读完
                        log.info("成功读取并推送完响应流. URL: {}", url);
                        emitter.complete(); // 正常完成 SSE 连接

                    } catch (IOException readEx) {
                        // 读取响应体时发生 IO 异常
                        log.error("读取上游响应流时发生 IO 异常. URL: {}, Error: {}", url, readEx.getMessage(), readEx);
                        emitter.completeWithError(new IOException("读取上游响应流失败: " + readEx.getMessage(), readEx));
                    }
                } catch (Exception e) {
                    // 捕获处理响应过程中的任何其他未预料异常
                    log.error("处理流式响应时发生未预料异常. URL: {}, Error: {}", url, e.getMessage(), e);
                    // 确保在这种情况下也通知 emitter 错误状态
                    // 检查 emitter 是否已完成，避免重复调用
                    try {
                        if (emitter != null) { // 理论上 emitter 不会是 null，但做个检查无妨
                            emitter.completeWithError(e);
                        }
                    } catch (Exception completionEx) {
                        log.error("尝试完成 emitter 出错状态时再次发生异常. URL: {}, Error: {}", url, completionEx.getMessage());
                    }
                }
            }
        });
    }

    /**
     * 执行流式POST请求
     *
     * @param url           请求URL
     * @param json          请求体JSON字符串
     * @param customHeaders 自定义请求头
     * @param emitter       流式响应回调
     */
    // 修改后的 SSE 版本流式请求
    /*public void postStream(String url, String json, Map<String, String> customHeaders, SseEmitter emitter) {
        RequestBody body = RequestBody.create(json, JSON);
        Request.Builder requestBuilder = new Request.Builder().url(url).post(body);

        // 添加自定义请求头（保持不变）
        if (customHeaders != null) {
            for (Map.Entry<String, String> entry : customHeaders.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
        }

        client.newCall(requestBuilder.build()).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                emitter.completeWithError(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (ResponseBody responseBody = response.body(); BufferedReader reader = new BufferedReader(responseBody.charStream())) {

                    String line;
                    while ((line = reader.readLine()) != null) {
                        // 通过 SSE 发送每行数据
                        emitter.send(SseEmitter.event().data(line).reconnectTime(3000));
                    }
                    emitter.complete();
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
            }
        });
    }*/


}
