package org.jeecg.excommons.utils;

import org.jeecg.excommons.ExConstants;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class AESKeyUtil {

    // 生成AES对称密钥
    public static SecretKey generateAESKey() throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(256); // 设置AES密钥长度为256位
        return keyGen.generateKey();
    }

    // 使用AES密钥加密
    public static String encrypt(String plainText, SecretKey secretKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes); // Base64编码输出
    }

    // 使用AES密钥解密
    public static String decrypt(String encryptedText, SecretKey secretKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decryptedBytes);
    }

    // 将SecretKey转换为Base64字符串
    public static String encodeKeyToBase64(SecretKey secretKey) {
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    // 从Base64字符串恢复SecretKey
    public static SecretKey decodeKeyFromBase64(String base64Key) {
        byte[] decodedKey = Base64.getDecoder().decode(base64Key);
        return new SecretKeySpec(decodedKey, 0, decodedKey.length, "AES");
    }

    public static void main(String[] args) {
        try {
            // 生成AES密钥
            SecretKey secretKey = generateAESKey();
            String base64Key = encodeKeyToBase64(secretKey);
            System.out.println("生成的AES密钥 (Base64 编码): " + base64Key);

            // 加密
            SecretKey decodedKey = decodeKeyFromBase64(ExConstants.ENC_SECRET_KEY);
            String plainText = "1000001:qwe12c";
            String encryptedText = encrypt(plainText, decodedKey);
            System.out.println("加密后的文本: " + encryptedText);

            // 解密
            String decryptedText = decrypt(encryptedText, decodedKey);
            System.out.println("解密后的文本: " + decryptedText);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}