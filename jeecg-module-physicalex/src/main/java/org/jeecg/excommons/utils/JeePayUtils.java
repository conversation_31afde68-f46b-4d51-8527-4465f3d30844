package org.jeecg.excommons.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Iterator;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class JeePayUtils {
    public static JSONObject reqParam2JSON(HttpServletRequest request) throws Exception {

        JSONObject returnObject = new JSONObject();

        if (isConvertJSON(request)) {

            String body = "";
            try {
                body = request.getReader().lines().collect(Collectors.joining(""));
                if (StringUtils.isEmpty(body)) {
                    return returnObject;
                }
                return JSONObject.parseObject(body);

            } catch (Exception e) {
                log.error("请求参数转换异常！ params=[{}]", body);
                throw new Exception("转换异常");
            }
        }

        // 参数Map
        Map<String, String[]> properties = request.getParameterMap();

        // 返回值Map
        Iterator<Map.Entry<String, String[]>> entries = properties.entrySet().iterator();
        Map.Entry entry;
        String name;
        String value = "";
        while (entries.hasNext()) {
            entry = entries.next();
            name = (String) entry.getKey();
            Object valueObj = entry.getValue();
            if (null == valueObj) {
                value = "";
            } else if (valueObj instanceof String[] values) {
                for (String s : values) {
                    value = s + ",";
                }
                value = value.substring(0, value.length() - 1);
            } else {
                value = valueObj.toString();
            }

            if (!name.contains("[")) {
                returnObject.put(name, value);
                continue;
            }
            //添加对json对象解析的支持  example: {ps[abc] : 1}
            String mainKey = name.substring(0, name.indexOf("["));
            String subKey = name.substring(name.indexOf("[") + 1, name.indexOf("]"));
            JSONObject subJson = new JSONObject();
            if (returnObject.get(mainKey) != null) {
                subJson = (JSONObject) returnObject.get(mainKey);
            }
            subJson.put(subKey, value);
            returnObject.put(mainKey, subJson);
        }
        return returnObject;

    }

    private static boolean isConvertJSON(HttpServletRequest request) {

        String contentType = request.getContentType();

        //有contentType  && json格式，  get请求不转换
        if (contentType != null && contentType.toLowerCase().contains("application/json") && !request.getMethod().equalsIgnoreCase("GET")) { //application/json 需要转换为json格式；
            return true;
        }

        return false;
    }
}
