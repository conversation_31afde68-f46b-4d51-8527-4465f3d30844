package org.jeecg.excommons.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JsonExtractorAndParser {

    public static String extractAndParseJson(String input) {
        try {
            // Step 1: 使用正则表达式提取可能的 JSON 部分
            String potentialJson = extractJson(input);

            // Step 2: 对提取的部分进行清理
            String cleanedJson = cleanJsonString(potentialJson);

            // Step 3: 尝试解析为合法的 JSON 格式
            Object parsed = parseJson(cleanedJson);

            // Step 4: 返回清理后的合法 JSON 字符串
            return JSON.toJSONString(parsed);
        } catch (Exception e) {
            // Step 5: 如果出错，返回错误信息
            return "{\"error\": \"Invalid JSON format: " + e.getMessage() + "\"}";
        }
    }

    private static String extractJson(String input) {
        // 正则表达式匹配以 { 或 [ 开始的 JSON 结构
        Pattern pattern = Pattern.compile("\\{[^\\{\\}]*\\}|\\[[^\\[\\]]*\\]");
        Matcher matcher = pattern.matcher(input);

        // 如果匹配到 JSON 部分，则提取并返回
        if (matcher.find()) {
            return matcher.group();
        } else {
            throw new IllegalArgumentException("No valid JSON structure found");
        }
    }

    private static String cleanJsonString(String input) {
        // 1. 修复中文引号（“”）替换为标准双引号（"）
        input = input.replaceAll("[“”]", "\"");

        // 2. 修复尾部的逗号问题（JSON 中最后一个元素后不应有逗号）
        input = input.replaceAll(",\\s*([}\\]])", "$1");

        // 3. 去除多余的空格和换行符
        input = input.replaceAll("\\s+", " ");

        // 4. 确保字符串的边界符合 JSON 格式
        if (!input.startsWith("[") && !input.startsWith("{")) {
            input = "[" + input + "]";
        }

        return input;
    }

    private static Object parseJson(String input) {
        try {
            // 使用 FastJson 解析 JSON 对象或数组
            if (input.startsWith("[")) {
                return JSON.parseArray(input);
            } else if (input.startsWith("{")) {
                return JSON.parseObject(input);
            } else {
                throw new JSONException("Invalid JSON structure");
            }
        } catch (JSONException e) {
            throw new IllegalArgumentException("Invalid JSON format", e);
        }
    }

    public static void main(String[] args) {
        String inputJson = "Some random text here...``` [ {\"seq\":1,\"title\":\"体重指数BMI 超重\",\"content\":\"体重指数（BMI）为27.2kg/m2，属于超重范围。超重可能增加心血管疾病、糖尿病、高血压等慢性病的风险。建议采取均衡饮食和适量运动，以减轻体重，降低患病风险。\"}, {\"seq\":2,\"title\":\"46、47三度松动\",\"content\":\"牙齿46和47存在三度松动，可能是牙周病或牙齿损伤所致。建议尽快就诊口腔科，进行详细检查和治疗，以防止牙齿进一步松动甚至脱落。\"}, {\"seq\":3,\"title\":\"牙结石\",\"content\":\"存在牙结石，可能导致牙龈炎和牙周病。建议定期进行口腔清洁，去除牙结石，维护口腔健康。\"}, {\"seq\":4,\"title\":\"窦性心动过缓\",\"content\":\"心率54次/分，诊断为窦性心动过缓。心动过缓可能导致乏力、头晕等症状，严重时可能影响心脏功能。建议进一步评估心脏状况，必要时考虑安装心脏起搏器。\"} ] ```more random text...";

        String result = extractAndParseJson(inputJson);
        System.out.println(result);
    }
}

