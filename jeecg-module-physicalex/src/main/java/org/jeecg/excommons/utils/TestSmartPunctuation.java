package org.jeecg.excommons.utils;

/**
 * 简单的测试类，验证SmartPunctuationProcessor的功能
 * 可以直接运行main方法进行测试
 */
public class TestSmartPunctuation {
    
    public static void main(String[] args) {
        System.out.println("=== SmartPunctuationProcessor Test ===");
        
        int passCount = 0;
        int totalCount = 0;
        
        // Test 1: 保留正常括号
        totalCount++;
        String test1Input = ":result(normal)";
        String test1Expected = "result(normal)";
        String test1Result = SmartPunctuationProcessor.removeLeadingSeparators(test1Input);
        boolean test1Pass = test1Expected.equals(test1Result);
        if (test1Pass) passCount++;
        
        System.out.println("Test 1 - Keep brackets:");
        System.out.println("  Input: '" + test1Input + "'");
        System.out.println("  Expected: '" + test1Expected + "'");
        System.out.println("  Actual: '" + test1Result + "'");
        System.out.println("  Result: " + (test1Pass ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test 2: 保留引号
        totalCount++;
        String test2Input = ";\"important\"note";
        String test2Expected = "\"important\"note";
        String test2Result = SmartPunctuationProcessor.removeLeadingSeparators(test2Input);
        boolean test2Pass = test2Expected.equals(test2Result);
        if (test2Pass) passCount++;
        
        System.out.println("Test 2 - Keep quotes:");
        System.out.println("  Input: '" + test2Input + "'");
        System.out.println("  Expected: '" + test2Expected + "'");
        System.out.println("  Actual: '" + test2Result + "'");
        System.out.println("  Result: " + (test2Pass ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test 3: 保留感叹号
        totalCount++;
        String test3Input = ",.!important";
        String test3Expected = "!important";
        String test3Result = SmartPunctuationProcessor.removeLeadingSeparators(test3Input);
        boolean test3Pass = test3Expected.equals(test3Result);
        if (test3Pass) passCount++;
        
        System.out.println("Test 3 - Keep exclamation:");
        System.out.println("  Input: '" + test3Input + "'");
        System.out.println("  Expected: '" + test3Expected + "'");
        System.out.println("  Actual: '" + test3Result + "'");
        System.out.println("  Result: " + (test3Pass ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test 4: 连续标点处理
        totalCount++;
        String test4Input = "result:,,,abnormal...";
        String test4Expected = "result:abnormal.";
        String test4Result = SmartPunctuationProcessor.normalize(test4Input);
        boolean test4Pass = test4Expected.equals(test4Result);
        if (test4Pass) passCount++;
        
        System.out.println("Test 4 - Consecutive punctuation:");
        System.out.println("  Input: '" + test4Input + "'");
        System.out.println("  Expected: '" + test4Expected + "'");
        System.out.println("  Actual: '" + test4Result + "'");
        System.out.println("  Result: " + (test4Pass ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test 5: 综合测试 - 模板渲染后的处理
        totalCount++;
        String test5Input = ":blood pressure(high),,,suggest...";
        String test5Expected = "blood pressure(high),suggest.";
        String test5Step1 = SmartPunctuationProcessor.cleanFormattedTextLeading(test5Input);
        String test5Result = SmartPunctuationProcessor.normalize(test5Step1);
        boolean test5Pass = test5Expected.equals(test5Result);
        if (test5Pass) passCount++;
        
        System.out.println("Test 5 - Combined processing:");
        System.out.println("  Input: '" + test5Input + "'");
        System.out.println("  Step1 (cleanLeading): '" + test5Step1 + "'");
        System.out.println("  Expected: '" + test5Expected + "'");
        System.out.println("  Actual: '" + test5Result + "'");
        System.out.println("  Result: " + (test5Pass ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test 6: 测试字符判断方法
        totalCount++;
        boolean test6Pass = true;
        
        // 测试分隔符标点
        if (!SmartPunctuationProcessor.isSeparatorPunctuation(':')) test6Pass = false;
        if (!SmartPunctuationProcessor.isSeparatorPunctuation(';')) test6Pass = false;
        if (!SmartPunctuationProcessor.isSeparatorPunctuation(',')) test6Pass = false;
        if (!SmartPunctuationProcessor.isSeparatorPunctuation('.')) test6Pass = false;
        
        // 测试有意义标点
        if (!SmartPunctuationProcessor.isMeaningfulPunctuation('(')) test6Pass = false;
        if (!SmartPunctuationProcessor.isMeaningfulPunctuation(')')) test6Pass = false;
        if (!SmartPunctuationProcessor.isMeaningfulPunctuation('!')) test6Pass = false;
        if (!SmartPunctuationProcessor.isMeaningfulPunctuation('?')) test6Pass = false;
        
        // 测试非分隔符标点不应该被识别为分隔符
        if (SmartPunctuationProcessor.isSeparatorPunctuation('(')) test6Pass = false;
        if (SmartPunctuationProcessor.isSeparatorPunctuation('!')) test6Pass = false;
        
        if (test6Pass) passCount++;
        
        System.out.println("Test 6 - Character classification:");
        System.out.println("  Testing isSeparatorPunctuation and isMeaningfulPunctuation");
        System.out.println("  Result: " + (test6Pass ? "PASS" : "FAIL"));
        System.out.println();
        
        // 总结
        System.out.println("=== Test Summary ===");
        System.out.println("Total tests: " + totalCount);
        System.out.println("Passed: " + passCount);
        System.out.println("Failed: " + (totalCount - passCount));
        System.out.println("Success rate: " + (passCount * 100 / totalCount) + "%");
        
        if (passCount == totalCount) {
            System.out.println("🎉 All tests PASSED! The fix is working correctly.");
        } else {
            System.out.println("❌ Some tests FAILED. Please check the implementation.");
        }
    }
}
