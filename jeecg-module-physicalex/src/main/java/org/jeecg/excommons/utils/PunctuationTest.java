package org.jeecg.excommons.utils;

/**
 * Simple punctuation test without Chinese characters
 */
public class PunctuationTest {
    
    public static void main(String[] args) {
        System.out.println("=== Punctuation Processing Test ===");
        
        // Test 1: Keep normal brackets
        String test1 = ":result(normal)";
        String result1 = removeLeadingSeparators(test1);
        System.out.println("Test 1 - Keep brackets:");
        System.out.println("  Input: '" + test1 + "'");
        System.out.println("  Output: '" + result1 + "'");
        System.out.println("  Expected: 'result(normal)'");
        System.out.println("  Result: " + (result1.equals("result(normal)") ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test 2: Keep quotes
        String test2 = ";\"important\"note";
        String result2 = removeLeadingSeparators(test2);
        System.out.println("Test 2 - Keep quotes:");
        System.out.println("  Input: '" + test2 + "'");
        System.out.println("  Output: '" + result2 + "'");
        System.out.println("  Expected: '\"important\"note'");
        System.out.println("  Result: " + (result2.equals("\"important\"note") ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test 3: Keep exclamation
        String test3 = ",.!important";
        String result3 = removeLeadingSeparators(test3);
        System.out.println("Test 3 - Keep exclamation:");
        System.out.println("  Input: '" + test3 + "'");
        System.out.println("  Output: '" + result3 + "'");
        System.out.println("  Expected: '!important'");
        System.out.println("  Result: " + (result3.equals("!important") ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test 4: Consecutive punctuation processing
        String test4 = "result:,,,abnormal...";
        String result4 = normalize(test4);
        System.out.println("Test 4 - Consecutive punctuation:");
        System.out.println("  Input: '" + test4 + "'");
        System.out.println("  Output: '" + result4 + "'");
        System.out.println("  Expected: 'result:abnormal.'");
        System.out.println("  Result: " + (result4.equals("result:abnormal.") ? "PASS" : "FAIL"));
        System.out.println();
        
        System.out.println("=== Test Complete ===");
    }
    
    /**
     * Remove leading separators only
     */
    private static String removeLeadingSeparators(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        // Only remove leading separators and whitespace, keep meaningful punctuation
        return text.replaceFirst("^[:;,.\\s]+", "");
    }
    
    /**
     * Normalize punctuation
     */
    private static String normalize(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        String processed = text;
        
        // 1. Handle line breaks
        processed = processed.replace("\n", ",").replace("\r", "");
        
        // 2. Handle consecutive commas
        processed = processed.replaceAll(",{2,}", ",");
        
        // 3. Handle consecutive periods
        processed = processed.replaceAll("\\.{2,}", ".");
        
        // 4. Handle mixed consecutive punctuation
        processed = processed.replaceAll("[,]{2,}", ",");
        processed = processed.replaceAll("[.]{2,}", ".");
        
        // 5. Handle colon followed by commas (special case)
        processed = processed.replaceAll(":,+", ":");
        
        // 6. Handle comma+period combinations
        processed = processed.replaceAll(",\\.", ".");
        
        // 7. Handle trailing mixed punctuation, replace with single period
        processed = processed.replaceAll("[,.;\\s]+$", ".");
        
        // 8. Handle extra spaces
        processed = processed.replaceAll("\\s+", " ").trim();
        
        return processed;
    }
}
