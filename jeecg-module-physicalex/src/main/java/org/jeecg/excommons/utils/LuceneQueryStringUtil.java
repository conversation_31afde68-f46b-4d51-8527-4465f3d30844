package org.jeecg.excommons.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LuceneQueryStringUtil {

    /**
     * 转义Lucene查询字符串中的特殊字符。
     *
     * @param input 原始查询字符串
     * @return 转义后的查询字符串
     */
    public static String escapeLuceneQuery(String input) {
        StringBuilder escapedString = new StringBuilder();
        for (char c : input.toCharArray()) {
            // List of special characters that need to be escaped
            if (c == '\\' || c == '+' || c == '-' || c == '!' || c == '(' || c == ')' || c == ':'
                    || c == '^' || c == '[' || c == ']' || c == '\"' || c == '{' || c == '}'
                    || c == '~' || c == '*' || c == '?' || c == '|' || c == '&' || c == '/') {
                escapedString.append('\\');
            }
            escapedString.append(c);
        }
        return escapedString.toString();
    }

    /**
     * 清理查询字符串中的不可见字符。
     *
     * @param input 原始查询字符串
     * @return 清理后的查询字符串
     */
    public static String cleanInvisibleChars(String input) {
        // 只保留可见字符，移除不可见字符
        return input.replaceAll("[\\p{C}]", "");
    }

    /**
     * 移除查询字符串中包含 "未见异常" 或 "未见明显异常" 的词。
     *
     * @param input 原始查询字符串
     * @return 处理后的查询字符串
     */
    public static String removeUnwantedPhrases(String input) {
        String regex = "\\b(未见异常|未见明显异常)\\b";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        return matcher.replaceAll("");
    }

    /**
     * 检查并处理查询字符串中的非法字符。
     *
     * @param input 原始查询字符串
     * @return 处理后的查询字符串
     */
    public static String handleIllegalChars(String input) {
        // 此处可以添加针对特定非法字符的处理逻辑
        // 例如替换非法字符或移除它们
        // 此示例中只是简单地移除不可见字符并转义特殊字符
        String cleanedInput = cleanInvisibleChars(input);
        String withoutUnwantedPhrases = removeUnwantedPhrases(cleanedInput);
        return escapeLuceneQuery(withoutUnwantedPhrases);
    }

    public static void main(String[] args) {
        // 示例用法
        String queryString = "患者情况：未见异常，检查结果：未见明显异常";
        String handledQuery = LuceneQueryStringUtil.handleIllegalChars(queryString);

        System.out.println("Original Query: " + queryString);
        System.out.println("Handled Query: " + handledQuery);
    }
}
