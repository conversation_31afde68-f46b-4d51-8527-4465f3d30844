package org.jeecg.excommons.sms;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "biz.sms.md")
@Configuration
@Data
public class MDProperties {
    private String serviceURL;
    private String sn;// 序列号
    private String account;
    private String password;
    private String signCode;
    private String extno;
}