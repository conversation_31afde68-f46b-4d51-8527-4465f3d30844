package org.jeecg.excommons.jms;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.config.MqConstants;
import org.jeecg.modules.fee.bo.ReceiptStatus;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Session;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;

@Slf4j
@Component
public class JmsMessageReceiver {
    @Autowired
    private IFeePayRecordService feePayRecordService;

    @JmsListener(destination = MqConstants.TOPIC_RECEIPT_STATE, containerFactory = "durableJmsListenerContainerFactory")
    public void receiveReceiptState(String message, Message jmsMessage, Session session) {
        log.info("接收到HIS支付状态消息：" + message);

        try {
            JAXBContext context = JAXBContext.newInstance(ReceiptStatus.class);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            StringReader reader = new StringReader(message);
            ReceiptStatus receiptStatus = (ReceiptStatus) unmarshaller.unmarshal(reader);

            feePayRecordService.updateFeeState(receiptStatus);
            jmsMessage.acknowledge();
        } catch (Exception e) {
            log.error("处理HIS支付状态异常：" + e.getMessage());
            try {
                session.recover(); // 让消息重新回到队列，稍后重新处理
            } catch (JMSException ex) {
                log.error("从体检主应用接受到申请单信息,将消息放回队列异常:" + e.getMessage());
            }
        }
    }

}

