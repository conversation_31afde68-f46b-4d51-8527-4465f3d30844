package org.jeecg.excommons.jms;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.jms.Topic;

@Service
public class JmsMessageSender {

    @Autowired
    private JmsTemplate jmsTemplate;

    @Resource(name = "applyTopic")
    private Topic applyTopic;

    @Resource(name = "applyTopicUpdate")
    private Topic applyTopicUpdate;

    @Resource(name = "archiveTopic")
    private Topic archiveTopic;

    @Resource(name = "archiveTopicUpdate")
    private Topic archiveTopicUpdate;

    @Resource(name = "paymentTopic")
    private Topic paymentTopic;

    @Resource(name = "refundTopic")
    private Topic refundTopic;

    @Resource(name = "receiptAddTopic")
    private Topic receiptAddTopic;

    @Resource(name = "receiptRefundTopic")
    private Topic receiptRefundTopic;

    @Resource(name = "patientTopic")
    private Topic patientTopic;

    @Resource(name = "dictDepart")
    private Topic dictDepart;

    @Resource(name = "dictUser")
    private Topic dictUser;

    @Resource(name = "dictItem")
    private Topic dictItem;

    @Resource(name = "addExam")
    private Topic addExam;

    public void sendApplyTopicMessage(String message) {
        jmsTemplate.convertAndSend(applyTopic, message);
    }

    public void sendPatientTopicMessage(String message) {

        jmsTemplate.convertAndSend(patientTopic, message);
    }

    public void sendArchiveTopicMessage(String message) {

        jmsTemplate.convertAndSend(archiveTopic, message);
    }

    public void updateArchiveTopicMessage(String message) {
        jmsTemplate.convertAndSend(archiveTopicUpdate, message);
    }

    public void updateApply(String message) {
        jmsTemplate.convertAndSend(applyTopicUpdate, message);
    }

    public void sendJmsMessage(Topic topic, String message) {
        jmsTemplate.convertAndSend(topic, message);
    }

    public void sendPayTopicMessage(String message) {
        jmsTemplate.convertAndSend(receiptAddTopic, message);
    }

    public void sendRefundTopicMessage(String message) {
        jmsTemplate.convertAndSend(receiptRefundTopic, message);
    }


    public void sendDictUserMessage(String message) {
        jmsTemplate.convertAndSend(dictUser, message);
    }


    public void sendDictItemMessage(String message) {
        jmsTemplate.convertAndSend(dictItem, message);
    }


    public void sendDictDepartMessage(String message) {
        jmsTemplate.convertAndSend(dictDepart, message);
    }

    public void sendExamItem(String message) {
        jmsTemplate.convertAndSend(addExam, message);
    }

    public void sendPaymentMessage(String message) {
        jmsTemplate.convertAndSend(paymentTopic, message);
    }

    public void sendRefundMessage(String message) {
        jmsTemplate.convertAndSend(refundTopic, message);
    }

}

