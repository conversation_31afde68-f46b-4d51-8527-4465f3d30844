package org.jeecg.excommons;

import org.jeecg.excommons.utils.MustacheUtil;

import java.util.HashMap;
import java.util.Map;

public class TempTest {
    //写一个测试Groovy字符串模版和mustche性能对比的方法
    public static void main(String[] args) {

        //Groovy字符串模版
        Map<String, Object> values = new HashMap<>();
        values.put("No", "1");
        values.put("name", "丙肝三项");
        values.put("value", "阳性");
        String template = "{{No}}.{{name}}：{{value}}";
        String result = MustacheUtil.render(template, values);
        System.out.println(result);

    }
}
