package org.jeecg.excommons;

import java.nio.charset.Charset;
import java.nio.charset.CharsetEncoder;
import java.nio.charset.CodingErrorAction;

public class GBKChecker {

    /**
     * 判断字符串中的所有字符是否都可以用GBK编码表示
     *
     * @param str 要检查的字符串
     * @return 如果所有字符都可以用GBK编码表示，则返回true；否则返回false
     */
    public static boolean isAllCharactersGBK(String str) {
        if (str == null) {
            // 可以根据需求决定是否将null视为不符合
            return false;
        }

        // 获取GBK编码的Charset实例
        Charset gbkCharset = Charset.forName("GBK");

        // 创建一个CharsetEncoder，用于检测编码能力
        CharsetEncoder encoder = gbkCharset.newEncoder();

        // 设置在遇到无法编码的字符时，抛出异常
        encoder.onUnmappableCharacter(CodingErrorAction.REPORT);
        encoder.onMalformedInput(CodingErrorAction.REPORT);

        try {
            // 尝试编码整个字符串
            encoder.encode(java.nio.CharBuffer.wrap(str));
            // 如果没有抛出异常，说明所有字符都可以被GBK编码
            return true;
        } catch (Exception e) {
            // 如果编码过程中出现异常，说明存在无法被GBK编码的字符
            return false;
        }
    }

    public static boolean isAllGBK(String str) {
        try {
            // 将字符串编码为 GBK 字节数组，然后再解码回来
            byte[] bytes = str.getBytes("GBK");
            String decoded = new String(bytes, "GBK");
            // 如果解码后的字符串与原字符串一致，说明所有字符都能被 GBK 表示
            return str.equals(decoded);
        } catch (Exception e) {
            // 如果编码/解码过程中出现异常，说明存在不可编码的字符
            return false;
        }
    }

    // 示例用法
    public static void main(String[] args) {
        String testString1 = "康有䘵"; // 所有字符都在GBK范围内
        String testString2 = "康有禄"; // 包含Emoji，不在GBK范围内

        System.out.println(isAllCharactersGBK(testString1)); // 输出: true
        System.out.println(isAllCharactersGBK(testString2)); // 输出: false

        System.out.println(isAllGBK(testString1)); // 输出: true
        System.out.println(isAllGBK(testString2)); // 输出: true
    }
}
