#!/bin/bash

echo "========================================"
echo "Chrome进程紧急清理脚本"
echo "时间: $(date)"
echo "========================================"

echo
echo "1. 检查当前Chrome进程..."
chrome_processes=$(ps aux | grep -i chrome | grep -v grep)
if [ -n "$chrome_processes" ]; then
    echo "发现Chrome进程:"
    echo "$chrome_processes"
else
    echo "未发现Chrome进程"
    exit 0
fi

echo
echo "2. 统计Chrome进程数量..."
chrome_count=$(ps aux | grep -i chrome | grep -v grep | wc -l)
echo "当前Chrome进程数量: $chrome_count"

echo
echo "3. 强制终止所有Chrome进程..."

# 方法1: 使用pkill
echo "使用pkill终止Chrome进程..."
pkill -f chrome
sleep 2

# 方法2: 使用killall (如果可用)
if command -v killall &> /dev/null; then
    echo "使用killall终止Chrome进程..."
    killall chrome 2>/dev/null
    sleep 2
fi

# 方法3: 强制终止 (SIGKILL)
echo "强制终止残留的Chrome进程..."
pkill -9 -f chrome
sleep 2

echo
echo "4. 清理ChromeDriver进程..."
pkill -f chromedriver
pkill -9 -f chromedriver 2>/dev/null

echo
echo "5. 等待进程完全退出..."
sleep 3

echo
echo "6. 验证清理结果..."
remaining_chrome=$(ps aux | grep -i chrome | grep -v grep)
if [ -n "$remaining_chrome" ]; then
    echo "警告: 仍有Chrome进程残留:"
    echo "$remaining_chrome"
else
    echo "所有Chrome进程已清理完成"
fi

echo
echo "7. 清理临时文件..."
temp_dirs=$(find /tmp -name "chrome_*" -type d 2>/dev/null)
if [ -n "$temp_dirs" ]; then
    echo "清理Chrome临时目录..."
    echo "$temp_dirs" | xargs rm -rf
fi

# 清理用户临时目录中的Chrome文件
if [ -d "$HOME/.config/google-chrome" ]; then
    echo "清理用户Chrome配置目录中的临时文件..."
    find "$HOME/.config/google-chrome" -name "*.tmp" -delete 2>/dev/null
fi

echo
echo "8. 调用应用程序重启WebDriver池..."
if command -v curl &> /dev/null; then
    response=$(curl -s -X POST "http://localhost:8080/jeecg-boot/webdriver/management/pool/restart" \
                    -H "Content-Type: application/json" \
                    -w "%{http_code}")
    
    if [[ "$response" == *"200"* ]]; then
        echo "WebDriver池重启请求发送成功"
    else
        echo "WebDriver池重启请求发送失败，HTTP状态码: $response"
        echo "请手动重启应用或调用管理接口"
    fi
else
    echo "curl命令不可用，请手动调用重启接口:"
    echo "curl -X POST http://localhost:8080/jeecg-boot/webdriver/management/pool/restart"
fi

echo
echo "9. 最终状态检查..."
final_chrome_count=$(ps aux | grep -i chrome | grep -v grep | wc -l)
echo "最终Chrome进程数量: $final_chrome_count"

if [ "$final_chrome_count" -eq 0 ]; then
    echo "✅ 清理成功: 所有Chrome进程已终止"
    exit_code=0
elif [ "$final_chrome_count" -lt "$chrome_count" ]; then
    echo "⚠️  部分清理成功: Chrome进程从 $chrome_count 减少到 $final_chrome_count"
    exit_code=1
else
    echo "❌ 清理失败: Chrome进程数量未减少"
    exit_code=2
fi

echo
echo "========================================"
echo "清理完成: $(date)"
echo "退出码: $exit_code"
echo "========================================"

exit $exit_code
