@echo off
echo ========================================
echo Chrome进程紧急清理脚本
echo 时间: %date% %time%
echo ========================================

echo.
echo 1. 检查当前Chrome进程...
tasklist | findstr chrome.exe
if %errorlevel% == 0 (
    echo 发现Chrome进程
) else (
    echo 未发现Chrome进程
    goto :end
)

echo.
echo 2. 统计Chrome进程数量...
for /f %%i in ('tasklist ^| findstr chrome.exe ^| find /c "chrome.exe"') do set chrome_count=%%i
echo 当前Chrome进程数量: %chrome_count%

echo.
echo 3. 强制终止所有Chrome进程...
taskkill /F /IM chrome.exe /T
if %errorlevel% == 0 (
    echo Chrome进程终止成功
) else (
    echo Chrome进程终止失败，尝试备用方法...
    wmic process where "name='chrome.exe'" delete
)

echo.
echo 4. 清理ChromeDriver进程...
taskkill /F /IM chromedriver.exe /T
if %errorlevel% == 0 (
    echo ChromeDriver进程终止成功
) else (
    echo ChromeDriver进程终止失败或不存在
)

echo.
echo 5. 等待进程完全退出...
timeout /t 3 /nobreak

echo.
echo 6. 验证清理结果...
tasklist | findstr chrome.exe
if %errorlevel% == 0 (
    echo 警告: 仍有Chrome进程残留
    tasklist | findstr chrome.exe
) else (
    echo 所有Chrome进程已清理完成
)

echo.
echo 7. 清理临时文件...
if exist "%TEMP%\chrome_*" (
    echo 清理Chrome临时目录...
    rmdir /s /q "%TEMP%\chrome_*" 2>nul
)

echo.
echo 8. 调用应用程序重启WebDriver池...
curl -X POST "http://localhost:8080/jeecg-boot/webdriver/management/pool/restart" -H "Content-Type: application/json"
if %errorlevel% == 0 (
    echo WebDriver池重启请求发送成功
) else (
    echo WebDriver池重启请求发送失败，请手动重启应用
)

:end
echo.
echo ========================================
echo 清理完成: %date% %time%
echo ========================================
pause
