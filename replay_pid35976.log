JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 409 ciObject found
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass lombok/core/configuration/ConfigurationSource
instanceKlass lombok/core/TypeResolver
instanceKlass lombok/core/AST$FieldAccess
instanceKlass lombok/core/LombokImmutableList$1
instanceKlass lombok/javac/JavacImportList
instanceKlass lombok/javac/PackageName
instanceKlass lombok/core/configuration/FileSystemSourceCache$Content
instanceKlass lombok/core/configuration/ConfigurationFile
instanceKlass lombok/core/configuration/BubblingConfigurationResolver
instanceKlass lombok/core/LombokConfiguration$3
instanceKlass lombok/core/configuration/FileSystemSourceCache$1
instanceKlass lombok/core/configuration/ConfigurationProblemReporter$1
instanceKlass lombok/core/configuration/ConfigurationProblemReporter
instanceKlass lombok/core/configuration/ConfigurationParser
instanceKlass lombok/core/configuration/ConfigurationFileToSource
instanceKlass lombok/core/configuration/FileSystemSourceCache
instanceKlass lombok/core/LombokConfiguration$1
instanceKlass lombok/core/configuration/ConfigurationResolverFactory
instanceKlass lombok/core/configuration/ConfigurationResolver
instanceKlass lombok/core/LombokConfiguration
instanceKlass sun/nio/fs/WindowsUriSupport
instanceKlass lombok/core/ImportList
instanceKlass lombok/javac/JavacAST$ErrorLog
instanceKlass java/util/IdentityHashMap$EntryIterator$Entry
instanceKlass com/sun/tools/javac/model/JavacElements$1
instanceKlass javax/annotation/processing/SupportedOptions
instanceKlass com/sun/tools/javac/util/MatchingUtils
instanceKlass javax/annotation/processing/SupportedAnnotationTypes
instanceKlass lombok/javac/HandlerLibrary$VisitorContainer
instanceKlass lombok/experimental/WithBy
instanceKlass lombok/With
instanceKlass lombok/Value
instanceKlass lombok/javac/JavacASTAdapter
instanceKlass lombok/experimental/UtilityClass
instanceKlass lombok/ToString
instanceKlass lombok/Synchronized
instanceKlass lombok/experimental/SuperBuilder
instanceKlass lombok/javac/handlers/JavacSingularsRecipes$StatementMaker
instanceKlass lombok/javac/handlers/JavacSingularsRecipes$ExpressionMaker
instanceKlass lombok/javac/handlers/HandleBuilder$BuilderJob
instanceKlass lombok/experimental/StandardException
instanceKlass lombok/SneakyThrows
instanceKlass lombok/Singular
instanceKlass lombok/Setter
instanceKlass lombok/core/PrintAST
instanceKlass lombok/NonNull
instanceKlass lombok/extern/slf4j/XSlf4j
instanceKlass lombok/extern/slf4j/Slf4j
instanceKlass lombok/extern/log4j/Log4j
instanceKlass lombok/extern/log4j/Log4j2
instanceKlass lombok/extern/java/Log
instanceKlass lombok/extern/jbosslog/JBossLog
instanceKlass lombok/extern/flogger/Flogger
instanceKlass lombok/CustomLog
instanceKlass lombok/extern/apachecommons/CommonsLog
instanceKlass lombok/extern/jackson/Jacksonized
instanceKlass lombok/experimental/Helper
instanceKlass lombok/Getter
instanceKlass lombok/experimental/FieldNameConstants
instanceKlass lombok/core/LombokImmutableList
instanceKlass lombok/core/JavaIdentifiers
instanceKlass lombok/experimental/ExtensionMethod
instanceKlass lombok/EqualsAndHashCode
instanceKlass lombok/experimental/Delegate
instanceKlass lombok/Data
instanceKlass lombok/RequiredArgsConstructor
instanceKlass lombok/NoArgsConstructor
instanceKlass lombok/AllArgsConstructor
instanceKlass lombok/Cleanup
instanceKlass lombok/Builder$Default
instanceKlass lombok/Builder
instanceKlass lombok/javac/handlers/HandleConstructor
instanceKlass lombok/core/LombokInternalAliasing
instanceKlass lombok/core/AlreadyHandledAnnotations
instanceKlass lombok/javac/ResolutionResetNeeded
instanceKlass lombok/core/HandlerPriority
instanceKlass lombok/javac/HandlerLibrary$AnnotationHandlerContainer
instanceKlass lombok/experimental/Accessors
instanceKlass lombok/javac/JavacAnnotationHandler
instanceKlass lombok/core/SpiLoadUtil$1$1
instanceKlass lombok/core/SpiLoadUtil$1
instanceKlass java/util/Vector$1
instanceKlass lombok/core/SpiLoadUtil
instanceKlass lombok/core/configuration/ConfigurationKeysLoader
instanceKlass lombok/core/configuration/CheckerFrameworkVersion
instanceKlass lombok/core/configuration/TypeName
instanceKlass lombok/core/configuration/LogDeclaration
instanceKlass lombok/core/configuration/IdentifierName
instanceKlass lombok/core/configuration/ConfigurationDataType$6
instanceKlass lombok/core/configuration/ConfigurationDataType$7
instanceKlass lombok/core/configuration/NullAnnotationLibrary
instanceKlass lombok/core/configuration/ConfigurationValueType
instanceKlass lombok/core/configuration/ConfigurationDataType$5
instanceKlass lombok/core/configuration/ConfigurationDataType$4
instanceKlass lombok/core/configuration/ConfigurationDataType$3
instanceKlass lombok/core/configuration/ConfigurationDataType$2
instanceKlass lombok/core/configuration/ConfigurationDataType$1
instanceKlass lombok/core/configuration/ConfigurationValueParser
instanceKlass lombok/core/configuration/ConfigurationDataType
instanceKlass lombok/core/configuration/ConfigurationKey
instanceKlass lombok/ConfigurationKeys
instanceKlass lombok/core/configuration/ConfigurationKeysLoader$LoaderLoader
instanceKlass lombok/core/TypeLibrary
instanceKlass lombok/javac/HandlerLibrary
instanceKlass lombok/javac/JavacASTVisitor
instanceKlass lombok/javac/JavacTransformer
instanceKlass com/sun/tools/javac/api/JavacScope
instanceKlass java/text/BreakIterator
instanceKlass com/sun/source/util/DocTreePath
instanceKlass lombok/core/AST
instanceKlass lombok/core/LombokNode
instanceKlass lombok/javac/handlers/JavacHandlerUtil
instanceKlass lombok/javac/JavacTreeMaker$FieldId
instanceKlass lombok/javac/JavacTreeMaker$MethodId
instanceKlass lombok/javac/JavacTreeMaker
instanceKlass java/util/regex/Pattern$1
instanceKlass javax/lang/model/type/TypeVisitor
instanceKlass lombok/javac/JavacTreeMaker$SchroedingerType
instanceKlass lombok/javac/Javac
instanceKlass lombok/javac/apt/Java9Compiler
instanceKlass lombok/javac/apt/LombokFileObjects$Compiler
instanceKlass lombok/javac/apt/LombokFileObject
instanceKlass lombok/javac/apt/LombokFileObjects
instanceKlass lombok/javac/apt/MessagerDiagnosticsReceiver
instanceKlass javax/tools/ForwardingJavaFileManager
instanceKlass lombok/permit/dummy/Parent
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass java/lang/Class$EnclosingMethodInfo
instanceKlass lombok/core/CleanupRegistry
instanceKlass lombok/permit/Permit$Fake
instanceKlass lombok/permit/Permit
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/AnnotationProcessorHider$AstModificationNotifierData
instanceKlass lombok/core/AnnotationProcessor$ProcessorDescriptor
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass java/util/WeakHashMap$HashIterator
instanceKlass java/net/URLEncoder
instanceKlass java/net/URLDecoder
instanceKlass lombok/launch/PackageShader
instanceKlass lombok/launch/Main
instanceKlass com/sun/tools/javac/processing/JavacProcessingEnvironment$ProcessorState
instanceKlass com/sun/tools/javac/processing/JavacRoundEnvironment
instanceKlass javax/lang/model/util/AbstractElementVisitor6
instanceKlass javax/lang/model/element/ElementVisitor
instanceKlass com/sun/tools/javac/processing/JavacProcessingEnvironment$Round
instanceKlass com/sun/tools/javac/code/TypeAnnotations$1
instanceKlass com/sun/tools/javac/comp/ConstFold$1
instanceKlass com/sun/tools/javac/code/Types$TypePair
instanceKlass com/sun/tools/javac/code/Types$UniqueType
instanceKlass com/sun/tools/javac/code/Flags
instanceKlass com/sun/tools/javac/comp/Resolve$MethodResolutionContext$Candidate
instanceKlass com/sun/tools/javac/code/Types$ImplementationCache$Entry
instanceKlass com/sun/tools/javac/comp/Resolve$LookupFilter
instanceKlass com/sun/tools/javac/comp/Resolve$5
instanceKlass com/sun/tools/javac/comp/Resolve$MethodResolutionContext
instanceKlass com/sun/tools/javac/code/TypeAnnotationPosition$TypePathEntry
instanceKlass com/sun/tools/javac/code/Scope$ImportScope$1
instanceKlass com/sun/tools/javac/jvm/ClassReader$TypeAnnotationProxy
instanceKlass com/sun/tools/javac/code/TypeAnnotationPosition
instanceKlass com/sun/tools/javac/code/Types$25
instanceKlass com/sun/tools/javac/tree/TreeMaker$2
instanceKlass com/sun/tools/javac/comp/TypeEnter$BasicConstructorHelper
instanceKlass com/sun/tools/javac/util/Iterators$2
instanceKlass com/sun/tools/javac/comp/TypeEnter$1
instanceKlass com/sun/tools/javac/code/TypeTag$1
instanceKlass com/sun/tools/javac/jvm/ClassReader$28
instanceKlass com/sun/tools/javac/jvm/ClassReader$ParameterAnnotations
instanceKlass com/sun/tools/javac/code/SymbolMetadata
instanceKlass com/sun/tools/javac/jvm/ClassReader$CompleterDeproxy
instanceKlass com/sun/tools/javac/jvm/ClassReader$AnnotationDeproxy
instanceKlass com/sun/tools/javac/jvm/ClassReader$ProxyVisitor
instanceKlass com/sun/tools/javac/file/JRTIndex$CtSym
instanceKlass com/sun/tools/javac/file/JRTIndex$Entry
instanceKlass com/sun/tools/javac/jvm/Code$1
instanceKlass com/sun/tools/javac/comp/MatchBindingsComputer$1
instanceKlass com/sun/tools/javac/comp/Attr$13
instanceKlass com/sun/tools/javac/code/Scope$FilterImportScope$SymbolImporter
instanceKlass com/sun/tools/javac/code/Scope$ImportFilter
instanceKlass com/sun/tools/javac/code/Scope$ScopeImpl$2
instanceKlass java/nio/file/FileTreeWalker$1
instanceKlass com/sun/tools/javac/comp/Check$5
instanceKlass com/sun/tools/javac/comp/AttrContext
instanceKlass com/sun/tools/javac/code/Scope$ScopeImpl$1
instanceKlass com/sun/tools/javac/code/ClassFinder$2
instanceKlass com/sun/tools/javac/code/ClassFinder$1
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass java/util/ImmutableCollections$Access$1
instanceKlass jdk/internal/access/JavaUtilCollectionAccess
instanceKlass java/util/ImmutableCollections$Access
instanceKlass java/nio/file/Files$2
instanceKlass java/util/ComparableTimSort
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass java/nio/file/Files$3
instanceKlass java/nio/file/FileTreeWalker$Event
instanceKlass jdk/nio/zipfs/ZipDirectoryStream$1
instanceKlass java/nio/file/FileTreeWalker$DirectoryNode
instanceKlass jdk/nio/zipfs/ZipDirectoryStream
instanceKlass java/nio/file/FileTreeWalker
instanceKlass java/nio/file/SimpleFileVisitor
instanceKlass java/time/chrono/AbstractChronology
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass java/time/ZoneId
instanceKlass java/time/LocalTime
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/temporal/TemporalField
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass jdk/nio/zipfs/ZipUtils
instanceKlass jdk/nio/zipfs/ZipFileSystem$END
instanceKlass jdk/nio/zipfs/ZipConstants
instanceKlass sun/nio/fs/WindowsChannelFactory$2
instanceKlass jdk/nio/zipfs/ZipFileAttributeView
instanceKlass jdk/nio/zipfs/ZipPath
instanceKlass jdk/nio/zipfs/ZipCoder
instanceKlass sun/nio/fs/WindowsSecurity
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass java/nio/file/attribute/GroupPrincipal
instanceKlass java/nio/file/attribute/UserDefinedFileAttributeView
instanceKlass java/nio/file/attribute/PosixFileAttributeView
instanceKlass sun/nio/fs/WindowsUserPrincipals$User
instanceKlass sun/nio/fs/WindowsUserPrincipals
instanceKlass sun/nio/fs/AbstractAclFileAttributeView
instanceKlass sun/nio/fs/FileOwnerAttributeViewImpl
instanceKlass java/nio/file/attribute/AclFileAttributeView
instanceKlass java/nio/file/attribute/DosFileAttributeView
instanceKlass java/nio/file/attribute/FileOwnerAttributeView
instanceKlass java/nio/file/attribute/UserPrincipal
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass java/nio/file/attribute/PosixFileAttributes
instanceKlass jdk/nio/zipfs/ZipFileAttributes
instanceKlass jdk/nio/zipfs/ZipFileSystem$IndexNode
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass java/nio/file/FileVisitor
instanceKlass com/sun/tools/javac/file/JavacFileManager$ArchiveContainer
instanceKlass com/sun/tools/javac/file/JavacFileManager$PathAndContainer
instanceKlass java/util/RegularEnumSet$EnumSetIterator
instanceKlass javax/lang/model/element/ModuleElement$OpensDirective
instanceKlass com/sun/tools/javac/jvm/ClassReader$InterimUsesDirective
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass com/sun/tools/javac/jvm/ClassReader$UsesProvidesCompleter
instanceKlass com/sun/tools/javac/jvm/ClassReader$InterimProvidesDirective
instanceKlass javax/lang/model/element/ModuleElement$ExportsDirective
instanceKlass java/util/function/IntFunction
instanceKlass com/sun/tools/javac/util/Name$NameMapper
instanceKlass com/sun/tools/javac/jvm/ClassReader$SourceFileObject
instanceKlass com/sun/tools/javac/jvm/PoolReader$ImmutablePoolHelper
instanceKlass com/sun/tools/javac/jvm/PoolReader
instanceKlass com/sun/tools/javac/comp/Modules$3
instanceKlass com/sun/tools/javac/code/ModuleFinder$1
instanceKlass jdk/internal/jrtfs/JrtFileAttributes
instanceKlass java/util/function/BooleanSupplier
instanceKlass java/util/stream/StreamSpliterators
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass jdk/internal/jrtfs/JrtDirectoryStream$1
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass jdk/internal/jimage/ImageReader$SharedImageReader$LocationVisitor
instanceKlass jdk/internal/jrtfs/JrtDirectoryStream
instanceKlass com/sun/tools/javac/file/JavacFileManager$DirectoryContainer
instanceKlass com/sun/tools/javac/tree/JCTree$1
instanceKlass com/sun/tools/javac/parser/JavacParser$LambdaClassifier
instanceKlass com/sun/tools/javac/parser/UnicodeReader$1
instanceKlass com/sun/tools/javac/util/LayoutCharacters
instanceKlass com/sun/tools/javac/tree/TreeInfo$2
instanceKlass com/sun/tools/javac/tree/TreeInfo
instanceKlass java/util/BitSet
instanceKlass com/sun/tools/javac/util/Position$LineMapImpl
instanceKlass com/sun/tools/javac/util/Position$LineMap
instanceKlass com/sun/tools/javac/util/Position
instanceKlass com/sun/tools/javac/parser/LazyDocCommentTable$Entry
instanceKlass com/sun/tools/javac/parser/JavacParser$1
instanceKlass com/sun/tools/javac/parser/JavadocTokenizer$OffsetMap
instanceKlass com/sun/tools/javac/resources/CompilerProperties$Errors
instanceKlass com/sun/tools/javac/util/IntHashTable
instanceKlass com/sun/tools/javac/parser/LazyDocCommentTable
instanceKlass com/sun/tools/javac/parser/JavacParser$ErrorRecoveryAction
instanceKlass com/sun/tools/javac/parser/JavacParser$AbstractEndPosTable
instanceKlass com/sun/tools/javac/tree/EndPosTable
instanceKlass com/sun/tools/javac/parser/JavacParser
instanceKlass com/sun/tools/javac/parser/Scanner
instanceKlass com/sun/source/tree/LineMap
instanceKlass com/sun/tools/javac/file/BaseFileManager$ContentCacheEntry
instanceKlass com/sun/tools/javac/util/DiagnosticSource
instanceKlass javax/annotation/processing/AbstractProcessor
instanceKlass com/sun/tools/javac/processing/JavacProcessingEnvironment$DiscoveredProcessors$ProcessorStateIterator
instanceKlass com/sun/tools/javac/processing/JavacProcessingEnvironment$DiscoveredProcessors
instanceKlass com/sun/tools/javac/util/Iterators$CompoundIterator
instanceKlass com/sun/tools/javac/util/Iterators$1
instanceKlass com/sun/tools/javac/util/Iterators
instanceKlass javax/annotation/processing/Processor
instanceKlass com/sun/tools/javac/processing/JavacProcessingEnvironment$ServiceIterator
instanceKlass com/sun/tools/javac/file/JavacFileManager$3
instanceKlass javax/tools/StandardLocation$2
instanceKlass com/sun/tools/javac/model/JavacTypes
instanceKlass com/sun/tools/javac/processing/JavacMessager
instanceKlass com/sun/tools/javac/processing/JavacFiler
instanceKlass com/sun/tools/javac/util/ForwardingDiagnosticFormatter$ForwardingConfiguration
instanceKlass com/sun/tools/javac/code/Types$DefaultSymbolVisitor
instanceKlass com/sun/tools/javac/util/ForwardingDiagnosticFormatter
instanceKlass com/sun/tools/javac/code/ModuleFinder$ModuleNameFromSourceReader
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass com/sun/tools/javac/comp/Modules$PackageNameFinder
instanceKlass com/sun/tools/javac/api/MultiTaskListener
instanceKlass com/sun/tools/javac/parser/UnicodeReader
instanceKlass com/sun/tools/javac/parser/ScannerFactory
instanceKlass com/sun/tools/javac/parser/Tokens$Token
instanceKlass com/sun/tools/javac/parser/Tokens
instanceKlass com/sun/tools/javac/parser/ReferenceParser
instanceKlass com/sun/tools/javac/model/JavacElements
instanceKlass com/sun/tools/javac/tree/DocCommentTable
instanceKlass com/sun/source/tree/Scope
instanceKlass com/sun/source/util/DocSourcePositions
instanceKlass com/sun/source/doctree/DocTreeVisitor
instanceKlass com/sun/source/util/SourcePositions
instanceKlass com/sun/source/doctree/AuthorTree
instanceKlass com/sun/source/doctree/InheritDocTree
instanceKlass com/sun/source/doctree/AttributeTree
instanceKlass com/sun/source/doctree/DeprecatedTree
instanceKlass com/sun/source/doctree/IdentifierTree
instanceKlass com/sun/source/doctree/EntityTree
instanceKlass com/sun/source/doctree/HiddenTree
instanceKlass com/sun/source/doctree/DocTypeTree
instanceKlass com/sun/source/doctree/IndexTree
instanceKlass com/sun/source/doctree/EndElementTree
instanceKlass com/sun/source/doctree/ErroneousTree
instanceKlass com/sun/source/doctree/CommentTree
instanceKlass com/sun/source/doctree/DocRootTree
instanceKlass com/sun/source/doctree/SinceTree
instanceKlass com/sun/source/doctree/SummaryTree
instanceKlass com/sun/source/doctree/VersionTree
instanceKlass com/sun/source/doctree/ReferenceTree
instanceKlass com/sun/source/doctree/ValueTree
instanceKlass com/sun/source/doctree/UsesTree
instanceKlass com/sun/source/doctree/LinkTree
instanceKlass com/sun/source/doctree/LiteralTree
instanceKlass com/sun/source/doctree/ParamTree
instanceKlass com/sun/source/doctree/SeeTree
instanceKlass com/sun/source/doctree/ProvidesTree
instanceKlass com/sun/source/doctree/ThrowsTree
instanceKlass com/sun/source/doctree/SerialTree
instanceKlass com/sun/source/doctree/SerialFieldTree
instanceKlass com/sun/source/doctree/SerialDataTree
instanceKlass com/sun/source/doctree/TextTree
instanceKlass com/sun/source/doctree/ReturnTree
instanceKlass com/sun/tools/javac/parser/Tokens$Comment
instanceKlass com/sun/source/doctree/DocCommentTree
instanceKlass com/sun/source/doctree/StartElementTree
instanceKlass com/sun/source/doctree/UnknownInlineTagTree
instanceKlass com/sun/source/doctree/UnknownBlockTagTree
instanceKlass com/sun/source/doctree/BlockTagTree
instanceKlass com/sun/source/doctree/SystemPropertyTree
instanceKlass com/sun/source/doctree/InlineTagTree
instanceKlass com/sun/source/doctree/DocTree
instanceKlass com/sun/tools/javac/tree/DocTreeMaker
instanceKlass com/sun/source/util/DocTreeFactory
instanceKlass com/sun/tools/javac/parser/Lexer
instanceKlass com/sun/tools/javac/parser/ParserFactory
instanceKlass jdk/internal/jimage/ImageReader$Node
instanceKlass jdk/internal/jrtfs/SystemImage$2
instanceKlass jdk/internal/jrtfs/SystemImage
instanceKlass jdk/internal/jrtfs/JrtPath
instanceKlass com/sun/tools/javac/file/JRTIndex
instanceKlass com/sun/tools/javac/main/DelegatingJavaFileManager
instanceKlass javax/lang/model/element/RecordComponentElement
instanceKlass com/sun/tools/javac/jvm/ClassReader$AttributeReader
instanceKlass com/sun/tools/javac/code/Preview$1
instanceKlass com/sun/tools/javac/comp/Analyzer$2
instanceKlass com/sun/tools/javac/comp/Analyzer$1
instanceKlass com/sun/tools/javac/comp/Analyzer$StatementAnalyzer
instanceKlass com/sun/tools/javac/comp/Analyzer$DeferredAnalysisHelper
instanceKlass com/sun/tools/javac/comp/Analyzer
instanceKlass com/sun/tools/javac/code/Symtab$2
instanceKlass com/sun/tools/javac/code/Symtab$1
instanceKlass com/sun/tools/javac/jvm/JNIWriter
instanceKlass com/sun/tools/javac/jvm/Code
instanceKlass com/sun/tools/javac/jvm/PoolWriter$WriteablePoolHelper
instanceKlass com/sun/tools/javac/code/Types$SignatureGenerator
instanceKlass com/sun/tools/javac/jvm/PoolWriter
instanceKlass com/sun/tools/javac/comp/ConstFold
instanceKlass com/sun/tools/javac/comp/Operators$OperatorHelper
instanceKlass com/sun/tools/javac/comp/Operators
instanceKlass com/sun/tools/javac/jvm/PoolConstant$Dynamic
instanceKlass com/sun/tools/javac/jvm/StringConcat
instanceKlass com/sun/tools/javac/jvm/Gen$GenFinalizer
instanceKlass com/sun/tools/javac/jvm/Items$Item
instanceKlass com/sun/tools/javac/jvm/ClassWriter$AttributeWriter
instanceKlass com/sun/tools/javac/jvm/ClassFile
instanceKlass com/sun/tools/javac/code/ModuleFinder$ModuleLocationIterator
instanceKlass com/sun/tools/javac/code/ModuleFinder
instanceKlass com/sun/tools/javac/comp/Flow
instanceKlass com/sun/tools/javac/comp/Infer$GraphStrategy
instanceKlass com/sun/tools/javac/comp/InferenceContext
instanceKlass javax/lang/model/element/TypeParameterElement
instanceKlass com/sun/tools/javac/comp/Infer$AbstractIncorporationEngine
instanceKlass com/sun/tools/javac/code/Type$UndetVar$UndetVarListener
instanceKlass com/sun/tools/javac/comp/Infer
instanceKlass com/sun/tools/javac/util/MandatoryWarningHandler
instanceKlass com/sun/tools/javac/code/Preview
instanceKlass com/sun/tools/javac/util/Dependencies
instanceKlass com/sun/tools/javac/comp/TypeEnvs
instanceKlass com/sun/tools/javac/code/TypeAnnotations
instanceKlass com/sun/tools/javac/code/DeferredLintHandler$1
instanceKlass com/sun/tools/javac/code/DeferredLintHandler
instanceKlass com/sun/tools/javac/comp/TypeEnter$DefaultConstructorHelper
instanceKlass com/sun/tools/javac/util/GraphUtils$DependencyKind
instanceKlass com/sun/tools/javac/comp/TypeEnter$Phase
instanceKlass com/sun/tools/javac/comp/TypeEnter
instanceKlass java/util/function/BiPredicate
instanceKlass com/sun/tools/javac/code/Types$CandidatesCache
instanceKlass com/sun/tools/javac/code/Types$ImplementationCache
instanceKlass com/sun/tools/javac/code/Types$3
instanceKlass com/sun/tools/javac/code/Types$DescriptorCache
instanceKlass com/sun/tools/javac/code/Types
instanceKlass com/sun/tools/javac/tree/TreeMaker$AnnotationBuilder
instanceKlass com/sun/tools/javac/tree/TreeMaker
instanceKlass com/sun/tools/javac/tree/JCTree$Factory
instanceKlass com/sun/tools/javac/comp/DeferredAttr$4
instanceKlass com/sun/tools/javac/tree/TreeCopier
instanceKlass com/sun/tools/javac/comp/DeferredAttr$DeferredAttrContext
instanceKlass com/sun/tools/javac/comp/DeferredAttr$DeferredStuckPolicy
instanceKlass com/sun/tools/javac/comp/AttrRecover
instanceKlass com/sun/tools/javac/comp/Resolve$ReferenceLookupResult
instanceKlass com/sun/tools/javac/api/Formattable$LocalizedString
instanceKlass com/sun/tools/javac/comp/Resolve$9
instanceKlass com/sun/tools/javac/comp/Resolve$8
instanceKlass com/sun/tools/javac/comp/Resolve$7
instanceKlass com/sun/tools/javac/comp/Resolve$6
instanceKlass com/sun/tools/javac/comp/Env
instanceKlass com/sun/tools/javac/comp/Resolve$AbstractMethodCheck
instanceKlass com/sun/tools/javac/comp/Resolve$2
instanceKlass com/sun/tools/javac/code/Scope$ScopeListener
instanceKlass com/sun/tools/javac/comp/Resolve$LookupHelper
instanceKlass com/sun/tools/javac/comp/Resolve$ReferenceChooser
instanceKlass com/sun/tools/javac/comp/Resolve$LogResolveHelper
instanceKlass com/sun/tools/javac/comp/Resolve$RecoveryLoadClass
instanceKlass com/sun/tools/javac/comp/Resolve
instanceKlass com/sun/tools/javac/comp/Check$1
instanceKlass com/sun/tools/javac/util/Warner
instanceKlass com/sun/tools/javac/comp/Check
instanceKlass com/sun/tools/javac/comp/Modules$1
instanceKlass com/sun/tools/javac/resources/CompilerProperties$Fragments
instanceKlass com/sun/tools/javac/code/Directive
instanceKlass javax/lang/model/element/ModuleElement$RequiresDirective
instanceKlass javax/lang/model/element/ModuleElement$Directive
instanceKlass com/sun/tools/javac/code/Scope$ScopeListenerList
instanceKlass com/sun/tools/javac/code/Scope$Entry
instanceKlass com/sun/tools/javac/comp/Annotate$AnnotationTypeMetadata
instanceKlass com/sun/tools/javac/api/Formattable
instanceKlass com/sun/tools/javac/code/Kinds$KindSelector
instanceKlass com/sun/tools/javac/code/MissingInfoHandler
instanceKlass com/sun/tools/javac/code/TypeMetadata
instanceKlass javax/lang/model/type/NullType
instanceKlass com/sun/tools/javac/code/Symtab
instanceKlass com/sun/tools/javac/comp/MatchBindingsComputer$MatchBindings
instanceKlass com/sun/source/util/SimpleTreeVisitor
instanceKlass javax/lang/model/type/UnionType
instanceKlass com/sun/tools/javac/comp/Resolve$MethodCheck
instanceKlass javax/lang/model/type/IntersectionType
instanceKlass com/sun/tools/javac/comp/Check$NestedCheckContext
instanceKlass com/sun/tools/javac/comp/Attr$ResultInfo
instanceKlass com/sun/tools/javac/code/Types$DefaultTypeVisitor
instanceKlass com/sun/tools/javac/comp/Annotate$2
instanceKlass com/sun/tools/javac/comp/Check$CheckContext
instanceKlass com/sun/tools/javac/code/TypeMetadata$Entry
instanceKlass javax/lang/model/element/AnnotationMirror
instanceKlass com/sun/tools/javac/comp/Annotate
instanceKlass com/sun/tools/javac/util/ByteBuffer
instanceKlass com/sun/tools/javac/code/Attribute
instanceKlass javax/lang/model/element/AnnotationValue
instanceKlass javax/lang/model/type/PrimitiveType
instanceKlass com/sun/tools/javac/comp/Annotate$AnnotationTypeCompleter
instanceKlass com/sun/tools/javac/jvm/ClassReader
instanceKlass com/sun/tools/javac/code/Scope
instanceKlass com/sun/tools/javac/code/ClassFinder
instanceKlass com/sun/tools/javac/util/Convert
instanceKlass com/sun/tools/javac/util/ArrayUtils
instanceKlass com/sun/tools/javac/util/Name
instanceKlass javax/lang/model/element/Name
instanceKlass com/sun/tools/javac/util/Name$Table
instanceKlass com/sun/tools/javac/util/Names
instanceKlass com/sun/tools/javac/code/Symbol$Completer$1
instanceKlass javax/lang/model/element/ModuleElement
instanceKlass javax/lang/model/element/PackageElement
instanceKlass com/sun/tools/javac/main/JavaCompiler
instanceKlass com/sun/tools/javac/code/Lint$AugmentVisitor
instanceKlass com/sun/tools/javac/code/Attribute$Visitor
instanceKlass javax/lang/model/element/TypeElement
instanceKlass javax/lang/model/element/QualifiedNameable
instanceKlass com/sun/source/tree/MemberSelectTree
instanceKlass com/sun/source/tree/ParameterizedTypeTree
instanceKlass com/sun/source/tree/ArrayTypeTree
instanceKlass com/sun/source/tree/UnionTypeTree
instanceKlass com/sun/source/tree/MemberReferenceTree
instanceKlass com/sun/source/tree/IdentifierTree
instanceKlass com/sun/source/tree/PrimitiveTypeTree
instanceKlass com/sun/source/tree/ArrayAccessTree
instanceKlass com/sun/source/tree/GuardedPatternTree
instanceKlass com/sun/source/tree/ConditionalExpressionTree
instanceKlass com/sun/source/tree/CompoundAssignmentTree
instanceKlass com/sun/source/tree/AssignmentTree
instanceKlass com/sun/source/tree/LambdaExpressionTree
instanceKlass com/sun/source/tree/ExpressionStatementTree
instanceKlass com/sun/source/tree/ParenthesizedTree
instanceKlass com/sun/source/tree/InstanceOfTree
instanceKlass com/sun/source/tree/MethodInvocationTree
instanceKlass com/sun/source/tree/EmptyStatementTree
instanceKlass com/sun/source/tree/VariableTree
instanceKlass com/sun/source/tree/MethodTree
instanceKlass com/sun/source/tree/ClassTree
instanceKlass com/sun/source/tree/LabeledStatementTree
instanceKlass com/sun/source/tree/EnhancedForLoopTree
instanceKlass com/sun/source/tree/ModuleTree
instanceKlass com/sun/source/tree/DoWhileLoopTree
instanceKlass com/sun/source/tree/PackageTree
instanceKlass com/sun/source/tree/DefaultCaseLabelTree
instanceKlass com/sun/source/tree/SwitchExpressionTree
instanceKlass com/sun/source/tree/BindingPatternTree
instanceKlass com/sun/source/tree/ParenthesizedPatternTree
instanceKlass com/sun/source/tree/PatternTree
instanceKlass com/sun/source/tree/IntersectionTypeTree
instanceKlass com/sun/source/tree/ExportsTree
instanceKlass com/sun/source/tree/AnnotatedTypeTree
instanceKlass com/sun/source/tree/YieldTree
instanceKlass com/sun/source/tree/UsesTree
instanceKlass com/sun/source/tree/RequiresTree
instanceKlass com/sun/source/tree/OpensTree
instanceKlass com/sun/source/tree/ErroneousTree
instanceKlass com/sun/source/tree/ProvidesTree
instanceKlass com/sun/source/tree/DirectiveTree
instanceKlass com/sun/source/tree/TypeParameterTree
instanceKlass com/sun/source/tree/LiteralTree
instanceKlass com/sun/source/tree/ModifiersTree
instanceKlass com/sun/source/tree/UnaryTree
instanceKlass com/sun/source/tree/TypeCastTree
instanceKlass com/sun/source/tree/NewClassTree
instanceKlass com/sun/source/tree/BinaryTree
instanceKlass com/sun/source/tree/NewArrayTree
instanceKlass com/sun/source/tree/BreakTree
instanceKlass com/sun/source/tree/SynchronizedTree
instanceKlass com/sun/source/tree/CatchTree
instanceKlass com/sun/source/tree/CaseTree
instanceKlass com/sun/source/tree/TryTree
instanceKlass com/sun/source/tree/IfTree
instanceKlass com/sun/source/tree/ReturnTree
instanceKlass com/sun/source/tree/AssertTree
instanceKlass com/sun/source/tree/ThrowTree
instanceKlass com/sun/source/tree/ContinueTree
instanceKlass com/sun/source/tree/ForLoopTree
instanceKlass com/sun/source/tree/ImportTree
instanceKlass com/sun/source/tree/SwitchTree
instanceKlass com/sun/source/tree/WhileLoopTree
instanceKlass com/sun/source/tree/BlockTree
instanceKlass com/sun/source/tree/StatementTree
instanceKlass com/sun/source/tree/WildcardTree
instanceKlass com/sun/source/tree/AnnotationTree
instanceKlass javax/annotation/processing/Messager
instanceKlass javax/annotation/processing/Filer
instanceKlass javax/annotation/processing/RoundEnvironment
instanceKlass com/sun/tools/javac/tree/JCTree$Visitor
instanceKlass com/sun/tools/javac/processing/JavacProcessingEnvironment
instanceKlass javax/annotation/processing/ProcessingEnvironment
instanceKlass com/sun/tools/javac/platform/PlatformDescription
instanceKlass java/lang/invoke/MethodHandles$1
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass com/sun/tools/javac/code/Source$1
instanceKlass com/sun/tools/javac/util/Pair
instanceKlass com/sun/tools/javac/code/DeferredCompletionFailureHandler$FlipSymbolDescription
instanceKlass com/sun/tools/javac/code/DeferredCompletionFailureHandler$3
instanceKlass com/sun/tools/javac/code/DeferredCompletionFailureHandler$2
instanceKlass com/sun/tools/javac/code/Symbol$Completer
instanceKlass com/sun/tools/javac/code/DeferredCompletionFailureHandler$1
instanceKlass com/sun/tools/javac/code/DeferredCompletionFailureHandler$Handler
instanceKlass com/sun/tools/javac/code/DeferredCompletionFailureHandler
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass com/sun/tools/javac/parser/Parser
instanceKlass com/sun/tools/javac/api/JavacTaskImpl$Filter
instanceKlass com/sun/tools/javac/main/Arguments$ErrorReporter
instanceKlass java/util/AbstractMap$SimpleEntry
instanceKlass jdk/internal/jimage/ImageBufferCache$2
instanceKlass jdk/internal/jimage/ImageBufferCache
instanceKlass java/nio/file/FileStore
instanceKlass java/nio/channels/AsynchronousFileChannel
instanceKlass java/nio/channels/AsynchronousChannel
instanceKlass java/nio/file/spi/FileSystemProvider$1
instanceKlass com/sun/tools/javac/util/StringUtils
instanceKlass com/sun/tools/javac/file/BaseFileManager$3
instanceKlass com/sun/tools/doclint/DocLint$1
instanceKlass com/sun/source/tree/CompilationUnitTree
instanceKlass com/sun/source/util/TreePath
instanceKlass javax/lang/model/util/Types
instanceKlass javax/lang/model/util/Elements
instanceKlass com/sun/source/util/Trees
instanceKlass com/sun/source/util/TreeScanner
instanceKlass com/sun/source/tree/TreeVisitor
instanceKlass java/util/ServiceLoader$ProviderSpliterator
instanceKlass com/sun/tools/doclint/DocLint
instanceKlass com/sun/source/util/Plugin
instanceKlass com/sun/tools/javac/util/ListBuffer$1
instanceKlass com/sun/tools/javac/main/Arguments
instanceKlass com/sun/tools/javac/api/ClientCodeWrapper$WrappedDiagnosticListener
instanceKlass com/sun/tools/javac/api/ClientCodeWrapper$Trusted
instanceKlass com/sun/source/util/TaskListener
instanceKlass com/sun/tools/javac/api/ClientCodeWrapper
instanceKlass com/sun/tools/javac/file/PathFileObject
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass com/sun/tools/javac/util/JCDiagnostic
instanceKlass javax/lang/model/element/ExecutableElement
instanceKlass javax/lang/model/element/Parameterizable
instanceKlass javax/lang/model/element/VariableElement
instanceKlass javax/lang/model/type/ExecutableType
instanceKlass javax/lang/model/type/ErrorType
instanceKlass javax/lang/model/type/DeclaredType
instanceKlass javax/lang/model/type/ArrayType
instanceKlass com/sun/tools/javac/jvm/PoolConstant$LoadableConstant
instanceKlass javax/lang/model/type/NoType
instanceKlass javax/lang/model/type/WildcardType
instanceKlass javax/lang/model/type/TypeVariable
instanceKlass javax/lang/model/type/ReferenceType
instanceKlass javax/lang/model/type/TypeMirror
instanceKlass com/sun/tools/javac/code/AnnoConstruct
instanceKlass javax/lang/model/element/Element
instanceKlass javax/lang/model/AnnotatedConstruct
instanceKlass com/sun/tools/javac/jvm/PoolConstant
instanceKlass com/sun/tools/javac/util/AbstractDiagnosticFormatter$SimpleConfiguration
instanceKlass com/sun/source/tree/ExpressionTree
instanceKlass com/sun/source/tree/CaseLabelTree
instanceKlass com/sun/tools/javac/tree/JCTree
instanceKlass com/sun/source/tree/Tree
instanceKlass com/sun/tools/javac/api/DiagnosticFormatter$Configuration
instanceKlass com/sun/tools/javac/code/Printer
instanceKlass com/sun/tools/javac/code/Symbol$Visitor
instanceKlass com/sun/tools/javac/code/Type$Visitor
instanceKlass com/sun/tools/javac/util/AbstractDiagnosticFormatter
instanceKlass com/sun/tools/javac/util/Options
instanceKlass java/util/ResourceBundle$3
instanceKlass java/util/ResourceBundle$CacheKeyReference
instanceKlass java/util/ResourceBundle$CacheKey
instanceKlass com/sun/tools/javac/util/List$3
instanceKlass com/sun/tools/javac/util/JavacMessages$ResourceBundleHelper
instanceKlass com/sun/tools/javac/util/List$2
instanceKlass com/sun/tools/javac/util/JavacMessages
instanceKlass com/sun/tools/javac/api/Messages
instanceKlass com/sun/tools/javac/util/JCDiagnostic$DiagnosticInfo
instanceKlass com/sun/tools/javac/util/JCDiagnostic$Factory
instanceKlass java/util/JumboEnumSet$EnumSetIterator
instanceKlass com/sun/tools/javac/file/Locations$ModuleTable
instanceKlass javax/tools/StandardJavaFileManager$PathFactory
instanceKlass sun/nio/fs/WindowsPath$1
instanceKlass com/sun/tools/javac/file/Locations$LocationHandler
instanceKlass com/sun/tools/javac/file/Locations
instanceKlass com/sun/tools/javac/file/BaseFileManager$ByteBufferCache
instanceKlass com/sun/tools/javac/file/JavacFileManager$1
instanceKlass com/sun/tools/javac/code/Lint
instanceKlass com/sun/tools/javac/util/Assert
instanceKlass com/sun/tools/javac/file/RelativePath
instanceKlass javax/tools/JavaFileObject
instanceKlass javax/tools/FileObject
instanceKlass javax/tools/JavaFileManager$Location
instanceKlass com/sun/tools/javac/file/JavacFileManager$Container
instanceKlass com/sun/tools/javac/main/OptionHelper
instanceKlass com/sun/tools/javac/file/FSInfo
instanceKlass com/sun/tools/javac/api/DiagnosticFormatter
instanceKlass com/sun/tools/javac/util/Log$DiagnosticHandler
instanceKlass com/sun/tools/javac/util/JCDiagnostic$DiagnosticPosition
instanceKlass com/sun/tools/javac/util/AbstractLog
instanceKlass com/sun/tools/javac/util/Context$Factory
instanceKlass com/sun/tools/javac/util/Context$Key
instanceKlass javax/tools/DiagnosticCollector
instanceKlass org/codehaus/plexus/compiler/javac/JavaxToolsCompiler$1
instanceKlass org/codehaus/plexus/util/StringUtils
instanceKlass org/apache/maven/shared/utils/io/SelectorUtils
instanceKlass org/apache/maven/shared/utils/io/MatchPattern
instanceKlass org/apache/maven/shared/utils/io/MatchPatterns
instanceKlass org/apache/maven/shared/utils/io/DirectoryScanner
instanceKlass sun/nio/ch/IOStatus
instanceKlass java/nio/channels/Channels
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass sun/nio/fs/WindowsSecurityDescriptor
instanceKlass org/apache/maven/shared/utils/io/FileUtils
instanceKlass org/apache/maven/monitor/event/EventDispatcher
instanceKlass org/apache/maven/artifact/repository/RepositoryCache
instanceKlass org/apache/maven/shared/incremental/IncrementalBuildHelperRequest
instanceKlass org/codehaus/plexus/util/SelectorUtils
instanceKlass org/codehaus/plexus/util/MatchPatterns
instanceKlass org/codehaus/plexus/util/MatchPattern
instanceKlass org/codehaus/plexus/util/AbstractScanner
instanceKlass org/codehaus/plexus/util/Scanner
instanceKlass org/codehaus/plexus/compiler/util/scan/mapping/SuffixMapping
instanceKlass org/codehaus/plexus/compiler/util/scan/AbstractSourceInclusionScanner
instanceKlass org/apache/maven/shared/incremental/IncrementalBuildHelper
instanceKlass org/apache/maven/shared/utils/StringUtils
instanceKlass com/sun/tools/javac/file/BaseFileManager
instanceKlass com/sun/tools/javac/util/Context
instanceKlass javax/tools/StandardJavaFileManager
instanceKlass com/sun/source/util/JavacTask
instanceKlass javax/tools/JavaCompiler$CompilationTask
instanceKlass com/sun/tools/javac/api/JavacTool
instanceKlass javax/tools/ToolProvider
instanceKlass org/codehaus/plexus/languages/java/jpms/ResolvePathsRequest
instanceKlass org/codehaus/plexus/languages/java/jpms/ResolvePathResult
instanceKlass org/codehaus/plexus/languages/java/jpms/ResolvePathRequest
instanceKlass org/codehaus/plexus/languages/java/jpms/ManifestModuleNameExtractor
instanceKlass org/codehaus/plexus/languages/java/jpms/SourceModuleInfoParser
instanceKlass org/codehaus/plexus/languages/java/jpms/ModuleInfoParser
instanceKlass org/codehaus/plexus/languages/java/jpms/ModuleNameExtractor
instanceKlass org/codehaus/plexus/compiler/CompilerMessage
instanceKlass org/codehaus/plexus/util/cli/StreamConsumer
instanceKlass org/codehaus/plexus/compiler/CompilerOutputStyle
instanceKlass javax/tools/Diagnostic
instanceKlass javax/tools/JavaCompiler
instanceKlass javax/tools/Tool
instanceKlass javax/tools/JavaFileManager
instanceKlass javax/tools/OptionChecker
instanceKlass javax/tools/DiagnosticListener
instanceKlass org/codehaus/plexus/languages/java/jpms/JavaModuleDescriptor
instanceKlass org/codehaus/plexus/languages/java/jpms/ResolvePathsResult
instanceKlass org/codehaus/plexus/compiler/CompilerResult
instanceKlass org/codehaus/plexus/compiler/util/scan/SourceInclusionScanner
instanceKlass org/apache/maven/shared/utils/logging/MessageBuilder
instanceKlass org/codehaus/plexus/compiler/CompilerConfiguration
instanceKlass org/codehaus/plexus/compiler/util/scan/mapping/SourceMapping
instanceKlass org/codehaus/plexus/languages/java/jpms/LocationManager
instanceKlass org/codehaus/plexus/compiler/javac/InProcessCompiler
instanceKlass org/codehaus/plexus/compiler/Compiler
instanceKlass org/codehaus/plexus/compiler/manager/CompilerManager
instanceKlass org/apache/maven/artifact/resolver/filter/AbstractScopeArtifactFilter
instanceKlass org/sonatype/plexus/build/incremental/EmptyScanner
instanceKlass org/apache/maven/shared/utils/io/IOUtil
instanceKlass org/codehaus/plexus/interpolation/PrefixAwareRecursionInterceptor
instanceKlass org/codehaus/plexus/interpolation/SimpleRecursionInterceptor
instanceKlass org/apache/maven/shared/filtering/BaseFilter$1
instanceKlass org/codehaus/plexus/interpolation/SingleResponseValueSource
instanceKlass org/codehaus/plexus/interpolation/PrefixedValueSourceWrapper
instanceKlass org/codehaus/plexus/interpolation/FeedbackEnabledValueSource
instanceKlass org/codehaus/plexus/interpolation/AbstractDelegatingValueSource
instanceKlass org/codehaus/plexus/interpolation/QueryEnabledValueSource
instanceKlass org/codehaus/plexus/interpolation/multi/DelimiterSpecification
instanceKlass org/codehaus/plexus/interpolation/multi/MultiDelimiterStringSearchInterpolator
instanceKlass org/apache/maven/shared/utils/StringUtils
instanceKlass org/apache/maven/shared/utils/io/FileUtils
instanceKlass org/codehaus/plexus/util/SelectorUtils
instanceKlass org/codehaus/plexus/util/MatchPatterns
instanceKlass org/codehaus/plexus/util/MatchPattern
instanceKlass org/codehaus/plexus/util/AbstractScanner
instanceKlass org/codehaus/plexus/interpolation/RecursionInterceptor
instanceKlass org/codehaus/plexus/interpolation/AbstractValueSource
instanceKlass org/apache/maven/plugins/resources/MavenBuildTimestamp
instanceKlass org/apache/maven/shared/utils/io/FileUtils$FilterWrapper
instanceKlass org/codehaus/plexus/util/StringUtils
instanceKlass org/sonatype/plexus/components/cipher/PBECipher
instanceKlass org/codehaus/plexus/util/Scanner
instanceKlass javax/annotation/meta/TypeQualifier
instanceKlass javax/annotation/Nonnull
instanceKlass org/codehaus/plexus/interpolation/Interpolator
instanceKlass org/codehaus/plexus/interpolation/BasicInterpolator
instanceKlass org/codehaus/plexus/interpolation/InterpolationPostProcessor
instanceKlass org/codehaus/plexus/interpolation/ValueSource
instanceKlass org/sonatype/plexus/components/sec/dispatcher/model/SettingsSecurity
instanceKlass org/apache/maven/shared/filtering/AbstractMavenFilteringRequest
instanceKlass org/sonatype/plexus/build/incremental/BuildContext
instanceKlass org/apache/maven/shared/filtering/MavenResourcesFiltering
instanceKlass org/apache/maven/shared/filtering/MavenReaderFilter
instanceKlass org/apache/maven/shared/filtering/MavenFileFilter
instanceKlass org/apache/maven/shared/filtering/DefaultFilterInfo
instanceKlass org/sonatype/plexus/components/cipher/PlexusCipher
instanceKlass org/sonatype/plexus/components/sec/dispatcher/SecDispatcher
instanceKlass org/apache/maven/repository/internal/DefaultVersionResolver$Record
instanceKlass org/apache/maven/repository/internal/DefaultVersionResolver$VersionInfo
instanceKlass org/eclipse/aether/resolution/ResolutionErrorPolicyRequest
instanceKlass org/eclipse/aether/repository/AuthenticationDigest
instanceKlass org/eclipse/aether/util/StringUtils
instanceKlass org/eclipse/aether/repository/LocalMetadataResult
instanceKlass org/eclipse/aether/repository/LocalMetadataRequest
instanceKlass org/eclipse/aether/resolution/MetadataResult
instanceKlass org/eclipse/aether/resolution/MetadataRequest
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass org/apache/maven/model/merge/ModelMerger$1
instanceKlass org/eclipse/aether/util/graph/selector/ExclusionDependencySelector$ExclusionComparator
instanceKlass org/eclipse/aether/collection/DependencyManagement
instanceKlass org/eclipse/aether/graph/Dependency$Exclusions$1
instanceKlass org/eclipse/aether/util/artifact/ArtifactIdUtils
instanceKlass org/apache/maven/project/DefaultDependencyResolutionRequest
instanceKlass org/apache/maven/lifecycle/internal/LifecycleDependencyResolver$ReactorDependencyFilter
instanceKlass org/apache/maven/artifact/resolver/filter/ExclusionArtifactFilter
instanceKlass org/apache/maven/artifact/repository/metadata/AbstractRepositoryMetadata
instanceKlass org/eclipse/aether/repository/LocalMetadataRegistration
instanceKlass org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Writer
instanceKlass org/codehaus/plexus/util/WriterFactory
instanceKlass org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$1
instanceKlass org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$ContentTransformer
instanceKlass org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader
instanceKlass org/eclipse/aether/metadata/AbstractMetadata
instanceKlass org/eclipse/aether/metadata/MergeableMetadata
instanceKlass org/eclipse/aether/repository/LocalArtifactRegistration
instanceKlass org/apache/maven/repository/internal/VersionsMetadataGenerator
instanceKlass org/apache/maven/repository/internal/LocalSnapshotMetadataGenerator
instanceKlass org/eclipse/aether/internal/impl/Utils
instanceKlass org/eclipse/sisu/plexus/CompositeBeanHelper$1
instanceKlass org/codehaus/plexus/util/introspection/MethodMap
instanceKlass org/codehaus/plexus/util/introspection/ClassMap$CacheMiss
instanceKlass org/codehaus/plexus/util/introspection/ClassMap
instanceKlass org/codehaus/plexus/util/introspection/ReflectionValueExtractor$Tokenizer
instanceKlass org/codehaus/plexus/util/introspection/ReflectionValueExtractor
instanceKlass org/eclipse/sisu/plexus/CompositeBeanHelper
instanceKlass org/apache/maven/plugin/internal/ValidatingConfigurationListener
instanceKlass org/apache/maven/plugin/DebugConfigurationListener
instanceKlass org/codehaus/plexus/component/configurator/converters/ParameterizedConfigurationConverter
instanceKlass org/codehaus/plexus/component/configurator/converters/AbstractConfigurationConverter
instanceKlass org/codehaus/plexus/component/configurator/converters/ConfigurationConverter
instanceKlass org/codehaus/plexus/component/configurator/converters/lookup/DefaultConverterLookup
instanceKlass org/codehaus/plexus/component/configurator/expression/DefaultExpressionEvaluator
instanceKlass org/apache/maven/plugin/PluginParameterExpressionEvaluator
instanceKlass org/codehaus/plexus/component/configurator/expression/TypeAwareExpressionEvaluator
instanceKlass org/apache/maven/monitor/logging/DefaultLog
instanceKlass com/google/common/collect/Iterables
instanceKlass com/google/inject/internal/Messages$Converter
instanceKlass com/google/inject/internal/Messages
instanceKlass org/w3c/dom/Element
instanceKlass org/w3c/dom/Document
instanceKlass org/w3c/dom/Node
instanceKlass org/apache/maven/plugin/install/InstallRequest
instanceKlass org/apache/maven/plugin/install/DualDigester
instanceKlass java/security/CodeSigner
instanceKlass java/util/jar/JarVerifier
instanceKlass org/eclipse/sisu/space/FileEntryIterator
instanceKlass org/eclipse/sisu/space/ResourceEnumeration
instanceKlass org/eclipse/sisu/plexus/ComponentDescriptorBeanModule$PlexusDescriptorBeanSource
instanceKlass org/eclipse/sisu/plexus/ComponentDescriptorBeanModule$ComponentMetadata
instanceKlass org/apache/maven/plugin/AbstractMojo
instanceKlass org/apache/maven/plugin/ContextEnabled
instanceKlass org/apache/maven/plugin/Mojo
instanceKlass org/eclipse/sisu/plexus/ComponentDescriptorBeanModule
instanceKlass org/apache/maven/classrealm/ArtifactClassRealmConstituent
instanceKlass org/eclipse/aether/util/graph/visitor/TreeDependencyVisitor
instanceKlass org/eclipse/aether/util/graph/visitor/FilteringDependencyVisitor
instanceKlass org/eclipse/aether/internal/impl/ArtifactRequestBuilder
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$NodeInfo
instanceKlass org/eclipse/aether/util/graph/transformer/NearestVersionSelector$ConflictGroup
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictItem
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeContext
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ConflictContext
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$State
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictIdSorter$RootQueue
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictIdSorter$ConflictId
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictMarker$ConflictGroup
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictMarker$Key
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictMarker
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictIdSorter
instanceKlass org/eclipse/aether/util/graph/transformer/TransformationContextKeys
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyGraphTransformationContext
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$GraphKey
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$Descriptor
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$Constraint$VersionRepo
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$Constraint
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$ConstraintKey
instanceKlass org/eclipse/aether/util/graph/manager/ClassicDependencyManager$Key
instanceKlass org/eclipse/aether/graph/DependencyCycle
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext
instanceKlass org/eclipse/aether/internal/impl/collect/NodeStack
instanceKlass org/eclipse/aether/internal/impl/collect/ObjectPool
instanceKlass org/eclipse/aether/internal/impl/collect/CachingArtifactTypeRegistry
instanceKlass org/apache/maven/plugin/internal/WagonExcluder
instanceKlass org/eclipse/aether/util/filter/ScopeDependencyFilter
instanceKlass org/apache/maven/plugin/internal/PlexusUtilsInjector
instanceKlass org/eclipse/aether/util/filter/AndDependencyFilter
instanceKlass org/apache/maven/plugin/CacheUtils
instanceKlass org/apache/maven/plugin/DefaultPluginRealmCache$CacheKey
instanceKlass java/lang/Character$CharacterCache
instanceKlass org/apache/maven/project/artifact/DefaultProjectArtifactsCache$CacheKey
instanceKlass org/apache/maven/lifecycle/internal/ExecutionPlanItem
instanceKlass org/apache/maven/plugin/MavenPluginValidator
instanceKlass org/codehaus/plexus/component/repository/ComponentDependency
instanceKlass org/codehaus/plexus/component/repository/ComponentRequirement
instanceKlass org/apache/maven/plugin/descriptor/Parameter
instanceKlass org/codehaus/plexus/configuration/DefaultPlexusConfiguration
instanceKlass org/apache/maven/repository/internal/ArtifactDescriptorReaderDelegate
instanceKlass org/apache/maven/repository/internal/DefaultModelCache$Key
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3Reader$1
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3Reader$ContentTransformer
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3Reader
instanceKlass org/apache/maven/repository/internal/DefaultModelResolver
instanceKlass org/apache/maven/repository/internal/DefaultModelCache
instanceKlass org/apache/maven/plugin/DefaultPluginDescriptorCache$CacheKey
instanceKlass org/apache/maven/lifecycle/internal/GoalTask
instanceKlass org/apache/maven/execution/ProjectExecutionEvent
instanceKlass org/apache/maven/lifecycle/internal/CompoundProjectExecutionListener
instanceKlass org/apache/maven/lifecycle/internal/LifecycleTask
instanceKlass org/slf4j/helpers/FormattingTuple
instanceKlass org/slf4j/helpers/MessageFormatter
instanceKlass org/eclipse/aether/util/repository/ChainedWorkspaceReader
instanceKlass java/util/LinkedList$ListItr
instanceKlass org/codehaus/plexus/util/dag/TopologicalSorter
instanceKlass org/codehaus/plexus/util/dag/CycleDetector
instanceKlass org/codehaus/plexus/util/dag/Vertex
instanceKlass org/codehaus/plexus/util/dag/DAG
instanceKlass org/apache/maven/project/ProjectSorter
instanceKlass org/apache/maven/graph/DefaultProjectDependencyGraph
instanceKlass org/apache/maven/project/DefaultProjectBuildingResult
instanceKlass org/apache/maven/model/merge/ModelMerger$NotifierKeyComputer
instanceKlass org/apache/maven/model/ActivationFile
instanceKlass org/apache/maven/model/Notifier
instanceKlass org/apache/maven/model/Site
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecyclePluginAnalyzer$1
instanceKlass org/apache/maven/lifecycle/mapping/LifecycleMojo
instanceKlass org/apache/maven/lifecycle/mapping/Lifecycle
instanceKlass org/apache/maven/model/building/DefaultModelBuildingEvent
instanceKlass org/apache/maven/model/building/ModelBuildingEventCatapult$1
instanceKlass org/apache/maven/model/building/DefaultModelProblem
instanceKlass org/apache/maven/model/building/ModelProblemCollectorRequest
instanceKlass org/apache/maven/project/DefaultProjectBuilder$InterimResult
instanceKlass org/apache/commons/lang3/Validate
instanceKlass org/apache/maven/artifact/versioning/Restriction
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion$StringItem
instanceKlass org/apache/maven/artifact/ArtifactUtils
instanceKlass org/apache/maven/artifact/DefaultArtifact
instanceKlass java/lang/Byte$ByteCache
instanceKlass java/lang/Short$ShortCache
instanceKlass java/lang/Long$LongCache
instanceKlass org/apache/commons/lang3/math/NumberUtils
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion$IntItem
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion$Item
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion
instanceKlass org/apache/maven/artifact/versioning/DefaultArtifactVersion
instanceKlass org/apache/maven/model/Extension
instanceKlass org/codehaus/plexus/interpolation/util/StringUtils
instanceKlass org/apache/maven/model/MailingList
instanceKlass org/apache/maven/model/Organization
instanceKlass org/apache/maven/model/Prerequisites
instanceKlass org/apache/maven/model/CiManagement
instanceKlass org/apache/maven/model/IssueManagement
instanceKlass org/codehaus/plexus/interpolation/reflection/MethodMap
instanceKlass org/codehaus/plexus/interpolation/reflection/ClassMap$CacheMiss
instanceKlass org/codehaus/plexus/interpolation/reflection/ClassMap
instanceKlass org/codehaus/plexus/interpolation/reflection/ReflectionValueExtractor$Tokenizer
instanceKlass org/codehaus/plexus/interpolation/reflection/ReflectionValueExtractor
instanceKlass org/codehaus/plexus/interpolation/util/ValueSourceUtils
instanceKlass org/apache/maven/model/interpolation/StringVisitorModelInterpolator$ModelVisitor
instanceKlass org/apache/maven/model/interpolation/StringVisitorModelInterpolator$1
instanceKlass org/codehaus/plexus/interpolation/PrefixAwareRecursionInterceptor
instanceKlass org/codehaus/plexus/interpolation/StringSearchInterpolator
instanceKlass org/apache/maven/model/interpolation/UrlNormalizingPostProcessor
instanceKlass org/apache/maven/model/interpolation/PathTranslatingPostProcessor
instanceKlass java/text/DontCareFieldPosition$1
instanceKlass java/text/Format$FieldDelegate
instanceKlass sun/util/resources/Bundles$2
instanceKlass sun/util/resources/LocaleData$LocaleDataResourceBundleProvider
instanceKlass java/util/spi/ResourceBundleProvider
instanceKlass org/apache/maven/model/interpolation/MavenBuildTimestamp
instanceKlass org/apache/maven/model/interpolation/ProblemDetectingValueSource
instanceKlass org/codehaus/plexus/interpolation/PrefixedValueSourceWrapper
instanceKlass org/codehaus/plexus/interpolation/FeedbackEnabledValueSource
instanceKlass org/codehaus/plexus/interpolation/AbstractDelegatingValueSource
instanceKlass org/codehaus/plexus/interpolation/QueryEnabledValueSource
instanceKlass org/apache/maven/model/merge/ModelMerger$ExtensionKeyComputer
instanceKlass org/apache/maven/model/merge/ModelMerger$ResourceKeyComputer
instanceKlass org/apache/maven/model/Scm
instanceKlass org/apache/maven/model/License
instanceKlass org/apache/maven/model/building/FilterModelBuildingRequest
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel$1
instanceKlass sun/nio/ch/Interruptible
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass java/nio/channels/FileLock
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass org/eclipse/aether/repository/LocalArtifactRequest
instanceKlass org/apache/maven/repository/internal/DefaultVersionResolver$Key
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher$1
instanceKlass org/eclipse/aether/RepositoryEvent$Builder
instanceKlass org/eclipse/aether/internal/impl/DefaultSyncContextFactory$DefaultSyncContext
instanceKlass org/apache/maven/project/ReactorModelPool$CacheKey
instanceKlass org/eclipse/aether/util/version/GenericVersion$Item
instanceKlass org/eclipse/aether/util/version/GenericVersion$Tokenizer
instanceKlass org/eclipse/aether/util/version/GenericVersion
instanceKlass org/eclipse/aether/util/version/GenericVersionConstraint
instanceKlass org/eclipse/aether/version/VersionConstraint
instanceKlass org/eclipse/aether/version/VersionRange
instanceKlass org/eclipse/aether/util/version/GenericVersionScheme
instanceKlass org/eclipse/aether/artifact/AbstractArtifact
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass java/util/Formatter
instanceKlass org/apache/maven/project/ReactorModelCache$CacheKey
instanceKlass org/apache/maven/model/building/ModelCacheTag$2
instanceKlass org/apache/maven/model/building/ModelCacheTag$1
instanceKlass org/apache/maven/repository/internal/ArtifactDescriptorUtils
instanceKlass org/apache/maven/model/merge/ModelMerger$SourceDominant
instanceKlass org/apache/maven/model/merge/ModelMerger$DependencyKeyComputer
instanceKlass org/apache/maven/model/ActivationProperty
instanceKlass org/apache/maven/model/building/ModelProblemUtils
instanceKlass org/apache/maven/model/DistributionManagement
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$Xpp3DomBuilderInputLocationBuilder
instanceKlass org/apache/maven/model/Exclusion
instanceKlass org/apache/maven/model/DependencyManagement
instanceKlass org/apache/maven/model/Parent
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$1
instanceKlass org/codehaus/plexus/util/xml/Xpp3DomBuilder$InputLocationBuilder
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$ContentTransformer
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx
instanceKlass org/apache/maven/model/building/ModelSource2
instanceKlass org/apache/maven/model/building/DefaultModelBuildingResult
instanceKlass org/apache/maven/model/building/AbstractModelBuildingListener
instanceKlass org/apache/maven/project/ProjectModelResolver
instanceKlass org/apache/maven/model/building/DefaultModelBuildingRequest
instanceKlass org/apache/maven/artifact/repository/LegacyLocalRepositoryManager
instanceKlass org/apache/maven/project/DefaultProjectBuildingRequest
instanceKlass org/slf4j/impl/OutputChoice$1
instanceKlass org/apache/maven/shared/utils/logging/AnsiMessageBuilder
instanceKlass org/apache/maven/shared/utils/logging/MessageBuilder
instanceKlass org/jetbrains/maven/server/EventInfoPrinter
instanceKlass org/apache/maven/lifecycle/internal/DefaultExecutionEventCatapult$1
instanceKlass org/apache/maven/lifecycle/internal/DefaultExecutionEvent
instanceKlass org/apache/maven/AbstractMavenLifecycleParticipant
instanceKlass org/apache/maven/settings/RuntimeInfo
instanceKlass org/eclipse/aether/repository/RemoteRepository$Builder
instanceKlass org/eclipse/aether/AbstractRepositoryListener
instanceKlass org/eclipse/aether/util/repository/DefaultAuthenticationSelector
instanceKlass org/eclipse/aether/util/repository/DefaultProxySelector
instanceKlass org/eclipse/aether/util/repository/DefaultMirrorSelector$MirrorDef
instanceKlass org/eclipse/aether/util/repository/DefaultMirrorSelector
instanceKlass org/apache/maven/settings/crypto/DefaultSettingsDecryptionResult
instanceKlass org/apache/maven/settings/crypto/DefaultSettingsDecryptionRequest
instanceKlass org/eclipse/aether/internal/impl/TrackingFileManager
instanceKlass org/eclipse/aether/internal/impl/SimpleLocalRepositoryManager
instanceKlass java/util/ArrayList$SubList$1
instanceKlass org/eclipse/aether/internal/impl/PrioritizedComponent
instanceKlass org/eclipse/sisu/wire/EntrySetAdapter$ValueIterator
instanceKlass org/eclipse/aether/util/ConfigUtils
instanceKlass org/eclipse/aether/internal/impl/PrioritizedComponents
instanceKlass org/apache/maven/RepositoryUtils$MavenArtifactTypeRegistry
instanceKlass org/apache/maven/RepositoryUtils
instanceKlass org/eclipse/aether/util/repository/SimpleResolutionErrorPolicy
instanceKlass org/eclipse/aether/util/repository/SimpleArtifactDescriptorPolicy
instanceKlass org/eclipse/aether/artifact/DefaultArtifactType
instanceKlass org/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry
instanceKlass org/eclipse/aether/util/graph/transformer/JavaDependencyContextRefiner
instanceKlass org/eclipse/aether/util/graph/transformer/ChainedDependencyGraphTransformer
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver
instanceKlass org/eclipse/aether/graph/Exclusion
instanceKlass org/eclipse/aether/util/graph/selector/ExclusionDependencySelector
instanceKlass org/eclipse/aether/util/graph/selector/OptionalDependencySelector
instanceKlass org/eclipse/aether/util/graph/selector/ScopeDependencySelector
instanceKlass org/eclipse/aether/util/graph/selector/AndDependencySelector
instanceKlass org/eclipse/aether/util/graph/manager/ClassicDependencyManager
instanceKlass org/eclipse/aether/util/graph/traverser/FatArtifactTraverser
instanceKlass org/eclipse/aether/DefaultSessionData
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullFileTransformerManager
instanceKlass org/eclipse/aether/transform/FileTransformerManager
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullArtifactTypeRegistry
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullAuthenticationSelector
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullProxySelector
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullMirrorSelector
instanceKlass org/eclipse/aether/SessionData
instanceKlass org/eclipse/aether/artifact/ArtifactTypeRegistry
instanceKlass org/eclipse/aether/artifact/ArtifactType
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver
instanceKlass org/apache/maven/repository/internal/MavenRepositorySystemUtils
instanceKlass org/apache/maven/execution/DefaultMavenExecutionResult
instanceKlass org/apache/maven/artifact/repository/MavenArtifactRepository
instanceKlass org/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout2
instanceKlass org/apache/maven/execution/AbstractExecutionListener
instanceKlass org/eclipse/aether/transfer/AbstractTransferListener
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuildingResult
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuilder$1
instanceKlass org/apache/maven/toolchain/model/io/xpp3/MavenToolchainsXpp3Writer
instanceKlass org/apache/maven/toolchain/model/io/xpp3/MavenToolchainsXpp3Reader$1
instanceKlass org/apache/maven/toolchain/model/io/xpp3/MavenToolchainsXpp3Reader$ContentTransformer
instanceKlass org/apache/maven/toolchain/model/io/xpp3/MavenToolchainsXpp3Reader
instanceKlass org/apache/maven/building/DefaultProblemCollector
instanceKlass org/apache/maven/building/ProblemCollectorFactory
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuildingRequest
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuildingResult
instanceKlass org/codehaus/plexus/interpolation/SimpleRecursionInterceptor
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuilder$1
instanceKlass org/codehaus/plexus/interpolation/os/OperatingSystemUtils$DefaultEnvVarSource
instanceKlass org/codehaus/plexus/interpolation/os/OperatingSystemUtils$EnvVarSource
instanceKlass org/codehaus/plexus/interpolation/os/OperatingSystemUtils
instanceKlass org/codehaus/plexus/interpolation/AbstractValueSource
instanceKlass org/codehaus/plexus/interpolation/RegexBasedInterpolator
instanceKlass org/codehaus/plexus/util/xml/pull/MXSerializer
instanceKlass org/codehaus/plexus/util/xml/pull/XmlSerializer
instanceKlass org/apache/maven/settings/io/xpp3/SettingsXpp3Writer
instanceKlass org/codehaus/plexus/util/xml/pull/EntityReplacementMap
instanceKlass org/apache/maven/settings/io/xpp3/SettingsXpp3Reader$1
instanceKlass org/apache/maven/settings/io/xpp3/SettingsXpp3Reader$ContentTransformer
instanceKlass org/apache/maven/settings/io/xpp3/SettingsXpp3Reader
instanceKlass org/apache/maven/building/FileSource
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuildingRequest
instanceKlass org/apache/maven/plugin/CompoundMojoExecutionListener
instanceKlass org/apache/maven/project/RepositorySessionDecorator
instanceKlass com/google/inject/internal/BytecodeGen
instanceKlass com/google/inject/internal/DelegatingInvocationHandler
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass java/security/Key
instanceKlass org/sonatype/plexus/components/sec/dispatcher/PasswordDecryptor
instanceKlass org/apache/maven/artifact/resolver/DefaultArtifactResolver$DaemonThreadCreator
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass org/codehaus/plexus/classworlds/realm/Entry
instanceKlass org/eclipse/sisu/inject/Guice4$1
instanceKlass org/apache/maven/model/PatternSet
instanceKlass org/apache/maven/model/Contributor
instanceKlass org/apache/maven/model/merge/ModelMerger$KeyComputer
instanceKlass org/apache/maven/model/merge/ModelMerger$Remapping
instanceKlass org/apache/maven/cli/event/DefaultEventSpyContext
instanceKlass org/eclipse/sisu/wire/EntryListAdapter$ValueIterator
instanceKlass org/apache/maven/cli/logging/Slf4jLogger
instanceKlass org/eclipse/sisu/inject/LazyBeanEntry$JsrNamed
instanceKlass org/eclipse/sisu/inject/LazyBeanEntry
instanceKlass org/eclipse/sisu/inject/Implementations
instanceKlass org/eclipse/sisu/plexus/LazyPlexusBean
instanceKlass org/eclipse/sisu/inject/RankedSequence$Itr
instanceKlass org/eclipse/sisu/inject/RankedBindings$Itr
instanceKlass org/eclipse/sisu/inject/LocatedBeans$Itr
instanceKlass org/eclipse/sisu/plexus/RealmFilteredBeans$FilteredItr
instanceKlass org/eclipse/sisu/plexus/DefaultPlexusBeans$Itr
instanceKlass org/eclipse/sisu/plexus/DefaultPlexusBeans
instanceKlass org/eclipse/sisu/plexus/RealmFilteredBeans
instanceKlass org/eclipse/sisu/inject/LocatedBeans
instanceKlass org/eclipse/sisu/inject/MildElements$Indexable
instanceKlass com/google/inject/internal/ProviderInternalFactory$1
instanceKlass com/google/inject/internal/ConstructorInjector$1
instanceKlass org/eclipse/sisu/inject/MildValues$ValueItr
instanceKlass org/eclipse/sisu/inject/RankedSequence$Content
instanceKlass com/google/inject/internal/CircularDependencyProxy
instanceKlass org/eclipse/sisu/inject/InjectorBindings
instanceKlass com/google/inject/spi/ProvisionListener$ProvisionInvocation
instanceKlass com/google/inject/internal/MembersInjectorImpl$1
instanceKlass com/google/inject/internal/InternalContext
instanceKlass com/google/inject/internal/Initializer$1
instanceKlass com/google/common/collect/TransformedIterator
instanceKlass com/google/common/collect/CompactHashMap$Itr
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$AsMap$AsMapIterator
instanceKlass com/google/inject/internal/SingleMethodInjector$1
instanceKlass com/google/inject/internal/InjectorImpl$MethodInvoker
instanceKlass com/google/inject/internal/SingleMethodInjector
instanceKlass com/google/inject/internal/InjectorImpl$ProviderBindingImpl$1
instanceKlass com/google/inject/internal/InjectorImpl$1
instanceKlass com/google/inject/internal/SingleFieldInjector
instanceKlass com/google/inject/internal/SingleParameterInjector
instanceKlass org/eclipse/sisu/plexus/PlexusConfigurations$ConfigurationProvider
instanceKlass javax/annotation/PreDestroy
instanceKlass javax/annotation/PostConstruct
instanceKlass org/eclipse/sisu/bean/BeanPropertySetter
instanceKlass com/google/inject/internal/MembersInjectorImpl
instanceKlass org/eclipse/sisu/bean/BeanInjector
instanceKlass org/eclipse/sisu/plexus/PlexusLifecycleManager$2
instanceKlass org/eclipse/sisu/bean/PropertyBinder$1
instanceKlass org/eclipse/sisu/plexus/ProvidedPropertyBinding
instanceKlass org/eclipse/sisu/plexus/PlexusRequirements$AbstractRequirementProvider
instanceKlass org/eclipse/sisu/bean/BeanPropertyField
instanceKlass org/eclipse/sisu/bean/DeclaredMembers$MemberIterator
instanceKlass org/eclipse/sisu/bean/BeanPropertyIterator
instanceKlass org/eclipse/sisu/bean/DeclaredMembers
instanceKlass org/eclipse/sisu/bean/IgnoreSetters
instanceKlass org/eclipse/sisu/bean/BeanProperties
instanceKlass org/eclipse/sisu/plexus/PlexusRequirements
instanceKlass org/eclipse/sisu/plexus/PlexusConfigurations
instanceKlass org/eclipse/sisu/plexus/PlexusPropertyBinder
instanceKlass org/eclipse/sisu/bean/BeanLifecycle
instanceKlass com/google/inject/internal/EncounterImpl
instanceKlass com/google/inject/internal/AbstractBindingProcessor$Processor$1
instanceKlass org/apache/maven/session/scope/internal/SessionScope$2
instanceKlass org/apache/maven/execution/scope/internal/MojoExecutionScope$2
instanceKlass com/google/inject/internal/ProviderInternalFactory
instanceKlass com/google/inject/internal/InternalProviderInstanceBindingImpl$Factory
instanceKlass com/google/inject/internal/FactoryProxy
instanceKlass com/google/inject/internal/InternalFactoryToProviderAdapter
instanceKlass com/google/inject/internal/ConstructionContext
instanceKlass com/google/inject/internal/SingletonScope$1
instanceKlass com/google/inject/internal/ProviderToInternalFactoryAdapter
instanceKlass com/google/inject/internal/CycleDetectingLock$CycleDetectingLockFactory$ReentrantCycleDetectingLock
instanceKlass com/google/inject/internal/Initializer$InjectableReference
instanceKlass com/google/common/collect/Collections2
instanceKlass com/google/inject/internal/ProvisionListenerStackCallback
instanceKlass com/google/common/cache/LocalCache$AbstractReferenceEntry
instanceKlass com/google/inject/internal/ProvisionListenerCallbackStore$KeyBinding
instanceKlass com/google/inject/internal/util/Classes
instanceKlass com/google/inject/spi/ExposedBinding
instanceKlass com/google/inject/internal/CreationListener
instanceKlass com/google/inject/internal/InjectorShell$LoggerFactory
instanceKlass com/google/inject/internal/InjectorShell$InjectorFactory
instanceKlass com/google/inject/internal/Initializables$1
instanceKlass com/google/inject/internal/Initializables
instanceKlass com/google/inject/internal/ConstantFactory
instanceKlass com/google/inject/internal/InjectorShell
instanceKlass com/google/inject/internal/ProvisionListenerCallbackStore
instanceKlass com/google/inject/internal/SingleMemberInjector
instanceKlass com/google/inject/spi/TypeEncounter
instanceKlass com/google/inject/internal/MembersInjectorStore
instanceKlass com/google/inject/internal/TypeConverterBindingProcessor$4
instanceKlass com/google/inject/internal/TypeConverterBindingProcessor$2
instanceKlass com/google/inject/internal/TypeConverterBindingProcessor$1
instanceKlass com/google/inject/internal/TypeConverterBindingProcessor$5
instanceKlass com/google/inject/internal/FailableCache
instanceKlass com/google/inject/internal/ConstructorInjectorStore
instanceKlass com/google/inject/internal/DeferredLookups
instanceKlass com/google/inject/spi/ConvertedConstantBinding
instanceKlass com/google/inject/spi/ProviderBinding
instanceKlass com/google/common/collect/ListMultimap
instanceKlass com/google/inject/internal/InjectorImpl
instanceKlass com/google/inject/internal/Lookups
instanceKlass com/google/inject/internal/InjectorImpl$InjectorOptions
instanceKlass org/eclipse/sisu/wire/BeanProviders$3
instanceKlass org/sonatype/inject/BeanEntry
instanceKlass org/eclipse/sisu/BeanEntry
instanceKlass org/eclipse/sisu/wire/BeanProviders$4
instanceKlass org/eclipse/sisu/wire/PlaceholderBeanProvider
instanceKlass com/google/inject/internal/ProvisionListenerStackCallback$ProvisionCallback
instanceKlass com/google/inject/internal/ConstructorInjector
instanceKlass com/google/inject/internal/DefaultConstructionProxyFactory$ReflectiveProxy
instanceKlass com/google/inject/internal/ConstructionProxy
instanceKlass com/google/inject/internal/DefaultConstructionProxyFactory
instanceKlass com/google/inject/internal/ConstructionProxyFactory
instanceKlass com/google/inject/internal/ConstructorBindingImpl$Factory
instanceKlass org/eclipse/sisu/inject/TypeArguments$Implicit
instanceKlass org/eclipse/sisu/wire/BeanProviders$7
instanceKlass org/eclipse/sisu/wire/BeanProviders$1
instanceKlass com/google/inject/spi/ProviderLookup$1
instanceKlass com/google/inject/spi/ProviderWithDependencies
instanceKlass com/google/inject/spi/ProviderLookup
instanceKlass org/eclipse/sisu/wire/BeanProviders
instanceKlass org/eclipse/sisu/inject/HiddenSource
instanceKlass org/eclipse/sisu/wire/LocatorWiring
instanceKlass com/google/inject/ProvidedBy
instanceKlass com/google/inject/ImplementedBy
instanceKlass org/apache/maven/settings/crypto/SettingsDecryptionResult
instanceKlass org/apache/maven/settings/building/DefaultSettingsProblemCollector
instanceKlass org/apache/maven/settings/merge/MavenSettingsMerger
instanceKlass org/apache/maven/settings/building/SettingsBuildingResult
instanceKlass org/apache/maven/settings/building/SettingsProblemCollector
instanceKlass org/eclipse/aether/impl/MetadataGenerator
instanceKlass org/apache/maven/model/Relocation
instanceKlass org/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionGroup
instanceKlass org/eclipse/aether/repository/LocalArtifactResult
instanceKlass com/google/common/base/ExtraObjectsMethodsForWeb
instanceKlass org/eclipse/aether/transform/FileTransformer
instanceKlass org/eclipse/aether/version/Version
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCollector$PremanagedDependency
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool
instanceKlass org/eclipse/aether/graph/DefaultDependencyNode
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext
instanceKlass org/eclipse/aether/graph/Dependency
instanceKlass org/eclipse/aether/collection/VersionFilter
instanceKlass org/eclipse/aether/collection/DependencyTraverser
instanceKlass org/eclipse/aether/collection/DependencyManager
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCollector$Results
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCollector$Args
instanceKlass org/eclipse/aether/collection/DependencyGraphTransformationContext
instanceKlass org/eclipse/aether/collection/DependencyCollectionContext
instanceKlass org/eclipse/aether/collection/VersionFilter$VersionFilterContext
instanceKlass org/eclipse/aether/transfer/TransferResource
instanceKlass org/eclipse/aether/spi/connector/checksum/ChecksumPolicy
instanceKlass org/eclipse/aether/resolution/ArtifactDescriptorResult
instanceKlass org/eclipse/aether/resolution/ArtifactDescriptorRequest
instanceKlass org/eclipse/aether/collection/CollectResult
instanceKlass org/eclipse/aether/collection/CollectRequest
instanceKlass org/eclipse/aether/resolution/DependencyResult
instanceKlass org/eclipse/aether/resolution/DependencyRequest
instanceKlass org/eclipse/aether/resolution/VersionRangeResult
instanceKlass org/eclipse/aether/resolution/VersionRangeRequest
instanceKlass org/eclipse/aether/resolution/VersionResult
instanceKlass org/eclipse/aether/resolution/VersionRequest
instanceKlass org/eclipse/aether/resolution/ArtifactResult
instanceKlass org/eclipse/aether/resolution/ArtifactRequest
instanceKlass org/eclipse/aether/installation/InstallResult
instanceKlass org/eclipse/aether/installation/InstallRequest
instanceKlass org/eclipse/aether/impl/UpdateCheck
instanceKlass org/eclipse/aether/spi/connector/layout/RepositoryLayout
instanceKlass com/google/inject/util/Types
instanceKlass org/eclipse/aether/spi/io/FileProcessor$ProgressListener
instanceKlass org/eclipse/aether/spi/log/Logger
instanceKlass org/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult
instanceKlass org/eclipse/aether/SyncContext
instanceKlass org/eclipse/aether/deployment/DeployResult
instanceKlass org/eclipse/aether/deployment/DeployRequest
instanceKlass org/eclipse/aether/repository/RepositoryPolicy
instanceKlass org/eclipse/aether/repository/LocalRepository
instanceKlass org/eclipse/aether/repository/LocalRepositoryManager
instanceKlass org/eclipse/aether/spi/connector/transport/Transporter
instanceKlass org/eclipse/aether/spi/locator/ServiceLocator
instanceKlass org/eclipse/aether/repository/RemoteRepository
instanceKlass org/eclipse/aether/spi/connector/RepositoryConnector
instanceKlass org/apache/maven/model/Activation
instanceKlass org/apache/maven/model/ActivationOS
instanceKlass org/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue
instanceKlass org/apache/maven/model/InputLocation
instanceKlass org/apache/maven/model/InputSource
instanceKlass org/apache/maven/model/interpolation/StringVisitorModelInterpolator$InnerInterpolator
instanceKlass org/apache/maven/model/profile/DefaultProfileActivationContext
instanceKlass org/apache/maven/model/building/ModelData
instanceKlass org/apache/maven/model/building/DefaultModelProblemCollector
instanceKlass org/apache/maven/model/building/ModelBuildingEventCatapult
instanceKlass org/apache/maven/model/building/ModelCacheTag
instanceKlass org/apache/maven/model/building/ModelBuildingEvent
instanceKlass org/apache/maven/model/building/ModelProblemCollectorExt
instanceKlass org/apache/maven/model/profile/ProfileActivationContext
instanceKlass org/apache/maven/cli/internal/extension/model/CoreExtension
instanceKlass org/apache/maven/building/ProblemCollector
instanceKlass org/apache/maven/toolchain/merge/MavenToolchainMerger
instanceKlass org/codehaus/plexus/interpolation/InterpolationPostProcessor
instanceKlass org/apache/maven/toolchain/building/ToolchainsBuildingResult
instanceKlass org/eclipse/aether/repository/AuthenticationSelector
instanceKlass org/eclipse/aether/repository/ProxySelector
instanceKlass org/eclipse/aether/repository/MirrorSelector
instanceKlass org/eclipse/aether/resolution/ResolutionErrorPolicy
instanceKlass org/eclipse/sisu/Nullable
instanceKlass org/apache/maven/classrealm/ClassRealmManagerDelegate
instanceKlass org/apache/maven/classrealm/ClassRealmConstituent
instanceKlass org/apache/maven/classrealm/ClassRealmRequest
instanceKlass org/eclipse/aether/repository/WorkspaceRepository
instanceKlass org/apache/maven/ArtifactFilterManagerDelegate
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass sun/reflect/generics/tree/VoidDescriptor
instanceKlass org/eclipse/aether/RepositoryEvent
instanceKlass org/apache/maven/project/DefaultProjectBuildingHelper
instanceKlass org/apache/maven/plugin/internal/DefaultPluginManager
instanceKlass org/apache/maven/settings/RepositoryPolicy
instanceKlass org/apache/maven/settings/RepositoryBase
instanceKlass org/apache/maven/profiles/ProfilesRoot
instanceKlass org/apache/maven/toolchain/DefaultToolchain
instanceKlass org/apache/maven/toolchain/java/JavaToolchain
instanceKlass org/apache/maven/toolchain/java/JavaToolchainFactory
instanceKlass org/apache/maven/rtinfo/internal/DefaultRuntimeInformation
instanceKlass org/apache/maven/execution/ProjectDependencyGraph
instanceKlass org/apache/maven/graph/DefaultGraphBuilder
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/NewestConflictResolver
instanceKlass org/apache/maven/repository/metadata/MetadataGraphVertex
instanceKlass org/apache/maven/repository/metadata/DefaultGraphConflictResolver
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecyclePluginAnalyzer$GoalSpec
instanceKlass org/apache/maven/lifecycle/mapping/LifecyclePhase
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecyclePluginAnalyzer
instanceKlass org/apache/maven/artifact/repository/layout/DefaultRepositoryLayout
instanceKlass org/apache/maven/model/merge/ModelMerger
instanceKlass org/apache/maven/model/plugin/DefaultLifecycleBindingsInjector
instanceKlass org/apache/maven/DefaultProjectDependenciesResolver
instanceKlass org/apache/maven/repository/metadata/MetadataGraphEdge
instanceKlass org/apache/maven/repository/metadata/DefaultGraphConflictResolutionPolicy
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass org/apache/maven/exception/ExceptionSummary
instanceKlass org/apache/maven/exception/DefaultExceptionHandler
instanceKlass org/apache/maven/repository/legacy/resolver/transform/DefaultArtifactTransformationManager
instanceKlass org/apache/maven/configuration/BeanConfigurationRequest
instanceKlass org/apache/maven/configuration/internal/DefaultBeanConfigurator
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleMappingDelegate
instanceKlass org/apache/maven/toolchain/model/TrackableBase
instanceKlass org/apache/maven/toolchain/DefaultToolchainsBuilder
instanceKlass org/apache/maven/model/building/ModelBuildingResult
instanceKlass org/apache/maven/project/ReactorModelPool
instanceKlass org/apache/maven/project/DefaultProjectBuilder$InternalConfig
instanceKlass org/apache/maven/project/ReactorModelCache
instanceKlass org/apache/maven/model/building/ModelCache
instanceKlass org/apache/maven/model/resolution/ModelResolver
instanceKlass org/apache/maven/project/ProjectBuildingResult
instanceKlass org/apache/maven/model/building/ModelBuildingListener
instanceKlass org/apache/maven/project/DefaultProjectBuilder
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/FarthestConflictResolver
instanceKlass org/apache/maven/project/path/DefaultPathTranslator
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleTaskSegmentCalculator
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/OldestConflictResolver
instanceKlass org/apache/maven/wagon/observers/ChecksumObserver
instanceKlass org/apache/maven/repository/legacy/DefaultWagonManager
instanceKlass org/apache/maven/settings/building/SettingsBuildingRequest
instanceKlass org/apache/maven/artifact/repository/layout/FlatRepositoryLayout
instanceKlass org/codehaus/classworlds/ClassRealm
instanceKlass org/codehaus/plexus/component/configurator/converters/lookup/ConverterLookup
instanceKlass org/codehaus/plexus/component/configurator/AbstractComponentConfigurator
instanceKlass org/apache/maven/plugin/PluginDescriptorCache$Key
instanceKlass org/apache/maven/plugin/DefaultPluginDescriptorCache
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleExecutionPlanCalculator
instanceKlass org/apache/maven/plugin/PluginRealmCache$CacheRecord
instanceKlass org/apache/maven/plugin/PluginRealmCache$Key
instanceKlass org/apache/maven/plugin/DefaultPluginRealmCache
instanceKlass org/apache/maven/model/Reporting
instanceKlass org/apache/maven/model/PluginContainer
instanceKlass org/apache/maven/project/inheritance/DefaultModelInheritanceAssembler
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/DefaultConflictResolverFactory
instanceKlass org/apache/maven/profiles/ProfileManager
instanceKlass org/apache/maven/model/building/ModelSource
instanceKlass org/apache/maven/project/DefaultMavenProjectBuilder
instanceKlass org/apache/maven/toolchain/ToolchainPrivate
instanceKlass org/apache/maven/project/DefaultDependencyResolutionResult
instanceKlass org/apache/maven/project/DependencyResolutionRequest
instanceKlass org/apache/maven/project/DependencyResolutionResult
instanceKlass org/apache/maven/project/DefaultProjectDependenciesResolver
instanceKlass org/sonatype/plexus/components/sec/dispatcher/model/SettingsSecurity
instanceKlass org/apache/maven/plugin/PluginArtifactsCache$CacheRecord
instanceKlass org/apache/maven/plugin/PluginArtifactsCache$Key
instanceKlass org/apache/maven/plugin/DefaultPluginArtifactsCache
instanceKlass org/apache/maven/artifact/resolver/DefaultArtifactResolver
instanceKlass org/apache/maven/project/validation/ModelValidationResult
instanceKlass org/apache/maven/model/building/ModelBuildingRequest
instanceKlass org/apache/maven/model/building/ModelProblemCollector
instanceKlass org/apache/maven/project/validation/DefaultModelValidator
instanceKlass org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator
instanceKlass org/apache/maven/artifact/resolver/DefaultResolutionErrorHandler
instanceKlass org/apache/maven/project/artifact/MavenMetadataSource$ProjectRelocation
instanceKlass org/apache/maven/model/building/ModelProblem
instanceKlass org/apache/maven/project/artifact/MavenMetadataSource
instanceKlass org/eclipse/aether/collection/DependencySelector
instanceKlass org/eclipse/aether/collection/DependencyGraphTransformer
instanceKlass org/eclipse/aether/resolution/ArtifactDescriptorPolicy
instanceKlass org/apache/maven/plugin/internal/DefaultPluginDependenciesResolver
instanceKlass org/apache/maven/repository/Proxy
instanceKlass org/apache/maven/model/RepositoryPolicy
instanceKlass org/apache/maven/model/Dependency
instanceKlass org/apache/maven/model/RepositoryBase
instanceKlass org/apache/maven/artifact/repository/Authentication
instanceKlass org/apache/maven/repository/ArtifactTransferListener
instanceKlass org/apache/maven/settings/crypto/SettingsDecryptionRequest
instanceKlass org/apache/maven/repository/legacy/LegacyRepositorySystem
instanceKlass org/eclipse/aether/RepositoryListener
instanceKlass org/apache/maven/artifact/repository/DefaultArtifactRepositoryFactory
instanceKlass org/apache/maven/settings/TrackableBase
instanceKlass org/apache/maven/repository/DefaultMirrorSelector
instanceKlass org/apache/maven/lifecycle/internal/builder/multithreaded/ConcurrencyDependencyGraph
instanceKlass org/apache/maven/lifecycle/internal/builder/multithreaded/ThreadOutputMuxer
instanceKlass org/apache/maven/lifecycle/internal/ProjectSegment
instanceKlass java/util/concurrent/CompletionService
instanceKlass java/util/concurrent/Executor
instanceKlass org/apache/maven/lifecycle/internal/builder/multithreaded/MultiThreadedBuilder
instanceKlass org/apache/maven/plugin/version/internal/DefaultPluginVersionResult
instanceKlass org/apache/maven/plugin/version/internal/DefaultPluginVersionResolver$Versions
instanceKlass org/eclipse/aether/version/VersionScheme
instanceKlass org/apache/maven/plugin/version/PluginVersionResult
instanceKlass org/apache/maven/plugin/version/internal/DefaultPluginVersionResolver
instanceKlass org/apache/maven/toolchain/Toolchain
instanceKlass org/apache/maven/toolchain/DefaultToolchainManager
instanceKlass org/apache/maven/project/artifact/ProjectArtifactsCache$CacheRecord
instanceKlass org/apache/maven/project/artifact/ProjectArtifactsCache$Key
instanceKlass org/apache/maven/project/artifact/DefaultProjectArtifactsCache
instanceKlass org/apache/maven/artifact/factory/DefaultArtifactFactory
instanceKlass org/eclipse/aether/artifact/Artifact
instanceKlass org/apache/maven/artifact/versioning/VersionRange
instanceKlass org/apache/maven/artifact/resolver/ArtifactResolutionResult
instanceKlass org/apache/maven/artifact/resolver/ArtifactResolutionRequest
instanceKlass org/apache/maven/repository/legacy/metadata/MetadataResolutionRequest
instanceKlass org/apache/maven/repository/legacy/resolver/DefaultLegacyArtifactCollector
instanceKlass org/apache/maven/artifact/handler/DefaultArtifactHandler
instanceKlass org/apache/maven/artifact/versioning/ArtifactVersion
instanceKlass org/apache/maven/execution/DefaultRuntimeInformation
instanceKlass org/apache/maven/repository/legacy/repository/DefaultArtifactRepositoryFactory
instanceKlass org/apache/maven/lifecycle/internal/PhaseRecorder
instanceKlass org/apache/maven/lifecycle/internal/DependencyContext
instanceKlass org/apache/maven/lifecycle/internal/ProjectIndex
instanceKlass org/apache/maven/artifact/resolver/filter/ArtifactFilter
instanceKlass org/apache/maven/plugin/ExtensionRealmCache$Key
instanceKlass org/apache/maven/plugin/DefaultExtensionRealmCache
instanceKlass org/apache/maven/lifecycle/MavenExecutionPlan
instanceKlass org/apache/maven/lifecycle/DefaultLifecycleExecutor
instanceKlass org/apache/maven/artifact/repository/ArtifactRepositoryPolicy
instanceKlass org/apache/maven/project/artifact/DefaultMavenMetadataCache$CacheKey
instanceKlass org/apache/maven/repository/legacy/metadata/ResolutionGroup
instanceKlass org/apache/maven/project/artifact/DefaultMavenMetadataCache
instanceKlass org/eclipse/aether/RequestTrace
instanceKlass org/apache/maven/plugin/prefix/PluginPrefixRequest
instanceKlass org/eclipse/aether/repository/ArtifactRepository
instanceKlass org/apache/maven/plugin/prefix/PluginPrefixResult
instanceKlass org/eclipse/aether/metadata/Metadata
instanceKlass org/apache/maven/plugin/prefix/internal/DefaultPluginPrefixResolver
instanceKlass org/apache/maven/repository/metadata/ClasspathContainer
instanceKlass org/apache/maven/repository/metadata/MetadataGraph
instanceKlass org/apache/maven/repository/metadata/DefaultClasspathTransformation
instanceKlass org/apache/maven/project/ProjectRealmCache$CacheRecord
instanceKlass org/apache/maven/project/ProjectRealmCache$Key
instanceKlass org/apache/maven/project/DefaultProjectRealmCache
instanceKlass org/apache/maven/lifecycle/mapping/DefaultLifecycleMapping
instanceKlass org/apache/maven/plugin/DefaultBuildPluginManager
instanceKlass org/apache/maven/wagon/providers/http/httpclient/impl/conn/PoolingHttpClientConnectionManager
instanceKlass org/apache/maven/wagon/providers/http/httpclient/pool/ConnPoolControl
instanceKlass org/apache/maven/wagon/providers/http/httpclient/config/Registry
instanceKlass org/apache/maven/wagon/providers/http/httpclient/client/methods/CloseableHttpResponse
instanceKlass org/apache/maven/wagon/providers/http/wagon/shared/BasicAuthScope
instanceKlass org/apache/maven/wagon/providers/http/wagon/shared/HttpConfiguration
instanceKlass org/apache/maven/wagon/providers/http/httpclient/impl/client/CloseableHttpClient
instanceKlass org/apache/maven/wagon/providers/http/httpclient/client/HttpClient
instanceKlass org/apache/maven/wagon/providers/http/httpclient/config/Lookup
instanceKlass org/apache/maven/wagon/providers/http/httpclient/client/HttpRequestRetryHandler
instanceKlass org/apache/maven/wagon/providers/http/httpclient/conn/ssl/TrustStrategy
instanceKlass org/apache/maven/wagon/providers/http/httpclient/ssl/TrustStrategy
instanceKlass org/apache/maven/wagon/providers/http/httpclient/Header
instanceKlass org/apache/maven/wagon/providers/http/httpclient/NameValuePair
instanceKlass org/apache/maven/wagon/providers/http/httpclient/client/ServiceUnavailableRetryStrategy
instanceKlass org/apache/maven/wagon/providers/http/httpclient/auth/Credentials
instanceKlass org/apache/maven/wagon/providers/http/httpclient/client/AuthCache
instanceKlass org/apache/maven/wagon/providers/http/httpclient/client/CredentialsProvider
instanceKlass org/apache/maven/wagon/providers/http/httpclient/protocol/HttpContext
instanceKlass org/apache/maven/wagon/providers/http/httpclient/HttpResponse
instanceKlass org/apache/maven/wagon/providers/http/httpclient/client/methods/HttpUriRequest
instanceKlass org/apache/maven/wagon/providers/http/httpclient/HttpRequest
instanceKlass org/apache/maven/wagon/providers/http/httpclient/HttpMessage
instanceKlass org/apache/maven/wagon/providers/http/httpclient/auth/AuthScheme
instanceKlass org/apache/maven/wagon/providers/http/httpclient/HttpEntity
instanceKlass org/apache/maven/wagon/providers/http/httpclient/conn/HttpClientConnectionManager
instanceKlass org/apache/maven/wagon/OutputData
instanceKlass org/apache/maven/wagon/InputData
instanceKlass java/util/EventObject
instanceKlass org/apache/maven/wagon/events/TransferListener
instanceKlass org/apache/maven/wagon/events/SessionListener
instanceKlass org/apache/maven/wagon/resource/Resource
instanceKlass org/apache/maven/wagon/repository/RepositoryPermissions
instanceKlass org/apache/maven/wagon/proxy/ProxyInfo
instanceKlass org/apache/maven/wagon/authentication/AuthenticationInfo
instanceKlass org/apache/maven/wagon/events/TransferEventSupport
instanceKlass org/apache/maven/wagon/events/SessionEventSupport
instanceKlass org/apache/maven/wagon/repository/Repository
instanceKlass org/apache/maven/wagon/proxy/ProxyInfoProvider
instanceKlass org/apache/maven/wagon/AbstractWagon
instanceKlass org/apache/maven/wagon/StreamingWagon
instanceKlass org/eclipse/sisu/space/asm/Item
instanceKlass org/eclipse/sisu/space/asm/ByteVector
instanceKlass org/eclipse/sisu/space/asm/FieldVisitor
instanceKlass org/eclipse/sisu/space/asm/MethodVisitor
instanceKlass org/apache/maven/artifact/resolver/ResolutionNode
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/NearestConflictResolver
instanceKlass org/apache/maven/artifact/repository/metadata/Metadata
instanceKlass org/apache/maven/artifact/repository/metadata/io/DefaultMetadataReader
instanceKlass org/apache/maven/model/building/Result
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession
instanceKlass org/apache/maven/execution/MavenExecutionResult
instanceKlass org/apache/maven/DefaultMaven
instanceKlass org/eclipse/aether/util/graph/visitor/AbstractDepthFirstNodeListGenerator
instanceKlass org/eclipse/aether/graph/DependencyNode
instanceKlass org/eclipse/aether/graph/DependencyFilter
instanceKlass org/apache/maven/plugin/ExtensionRealmCache$CacheRecord
instanceKlass org/apache/maven/model/ConfigurationContainer
instanceKlass org/codehaus/plexus/component/repository/ComponentSetDescriptor
instanceKlass org/apache/maven/plugin/descriptor/PluginDescriptorBuilder
instanceKlass org/eclipse/aether/graph/DependencyVisitor
instanceKlass org/codehaus/plexus/component/configurator/ConfigurationListener
instanceKlass org/codehaus/plexus/component/configurator/expression/ExpressionEvaluator
instanceKlass org/codehaus/plexus/configuration/PlexusConfiguration
instanceKlass org/apache/maven/plugin/logging/Log
instanceKlass org/apache/maven/plugin/version/PluginVersionRequest
instanceKlass org/apache/maven/plugin/internal/DefaultMavenPluginManager
instanceKlass org/apache/maven/lifecycle/internal/ReactorBuildStatus
instanceKlass org/apache/maven/lifecycle/internal/ProjectBuildList
instanceKlass org/apache/maven/lifecycle/internal/builder/singlethreaded/SingleThreadedBuilder
instanceKlass org/apache/maven/artifact/repository/metadata/Versioning
instanceKlass org/apache/maven/artifact/repository/ArtifactRepository
instanceKlass org/apache/maven/artifact/Artifact
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadata
instanceKlass org/apache/maven/artifact/metadata/ArtifactMetadata
instanceKlass org/apache/maven/repository/legacy/metadata/ArtifactMetadata
instanceKlass org/apache/maven/artifact/repository/RepositoryRequest
instanceKlass org/eclipse/aether/RepositorySystemSession
instanceKlass org/apache/maven/plugin/internal/DefaultLegacySupport
instanceKlass org/apache/maven/project/ProjectBuilderConfiguration
instanceKlass org/apache/maven/model/ModelBase
instanceKlass org/apache/maven/model/InputLocationTracker
instanceKlass org/codehaus/plexus/interpolation/Interpolator
instanceKlass org/codehaus/plexus/interpolation/BasicInterpolator
instanceKlass org/codehaus/plexus/interpolation/ValueSource
instanceKlass org/codehaus/plexus/interpolation/RecursionInterceptor
instanceKlass org/codehaus/plexus/logging/AbstractLogEnabled
instanceKlass org/apache/maven/lifecycle/internal/TaskSegment
instanceKlass org/apache/maven/lifecycle/internal/ReactorContext
instanceKlass org/apache/maven/execution/ProjectExecutionListener
instanceKlass org/apache/maven/execution/BuildSummary
instanceKlass org/apache/maven/execution/ExecutionEvent
instanceKlass org/apache/maven/lifecycle/internal/DefaultExecutionEventCatapult
instanceKlass java/lang/Deprecated
instanceKlass org/apache/maven/artifact/handler/manager/DefaultArtifactHandlerManager
instanceKlass org/sonatype/plexus/components/cipher/PBECipher
instanceKlass org/eclipse/sisu/inject/Guice4
instanceKlass com/google/inject/spi/ProviderWithExtensionVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusBean
instanceKlass org/codehaus/plexus/component/repository/ComponentDescriptor
instanceKlass org/sonatype/inject/Parameters
instanceKlass org/eclipse/sisu/plexus/PlexusXmlBeanConverter
instanceKlass org/eclipse/sisu/plexus/PlexusBeanConverter
instanceKlass com/google/inject/spi/TypeConverterBinding
instanceKlass com/google/inject/spi/ProvisionListenerBinding
instanceKlass com/google/inject/spi/TypeListenerBinding
instanceKlass org/eclipse/sisu/bean/BeanListener
instanceKlass com/google/inject/matcher/Matchers
instanceKlass org/eclipse/sisu/bean/PropertyBinder
instanceKlass org/eclipse/sisu/plexus/PlexusBeanBinder
instanceKlass com/google/inject/spi/InjectionListener
instanceKlass org/sonatype/plexus/components/cipher/DefaultPlexusCipher
instanceKlass org/apache/maven/settings/validation/DefaultSettingsValidator
instanceKlass org/apache/maven/settings/validation/SettingsValidator
instanceKlass org/apache/maven/settings/io/DefaultSettingsWriter
instanceKlass org/apache/maven/settings/io/SettingsWriter
instanceKlass org/apache/maven/settings/io/DefaultSettingsReader
instanceKlass org/apache/maven/settings/io/SettingsReader
instanceKlass org/apache/maven/settings/crypto/DefaultSettingsDecrypter
instanceKlass org/apache/maven/settings/crypto/SettingsDecrypter
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuilder
instanceKlass org/apache/maven/settings/building/SettingsBuilder
instanceKlass org/eclipse/aether/transport/wagon/WagonTransporterFactory
instanceKlass org/eclipse/aether/spi/connector/transport/TransporterFactory
instanceKlass org/eclipse/aether/internal/transport/wagon/PlexusWagonProvider
instanceKlass org/eclipse/aether/transport/wagon/WagonProvider
instanceKlass org/eclipse/aether/internal/transport/wagon/PlexusWagonConfigurator
instanceKlass org/eclipse/aether/transport/wagon/WagonConfigurator
instanceKlass org/apache/maven/repository/internal/VersionsMetadataGeneratorFactory
instanceKlass org/apache/maven/repository/internal/SnapshotMetadataGeneratorFactory
instanceKlass org/eclipse/aether/impl/MetadataGeneratorFactory
instanceKlass org/apache/maven/repository/internal/DefaultVersionResolver
instanceKlass org/eclipse/aether/impl/VersionResolver
instanceKlass org/apache/maven/repository/internal/DefaultVersionRangeResolver
instanceKlass org/eclipse/aether/impl/VersionRangeResolver
instanceKlass org/apache/maven/repository/internal/DefaultArtifactDescriptorReader
instanceKlass org/eclipse/aether/impl/ArtifactDescriptorReader
instanceKlass org/eclipse/aether/internal/impl/DefaultArtifactResolver
instanceKlass org/eclipse/aether/impl/ArtifactResolver
instanceKlass org/eclipse/aether/internal/impl/DefaultInstaller
instanceKlass org/eclipse/aether/impl/Installer
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCollector
instanceKlass org/eclipse/aether/impl/DependencyCollector
instanceKlass org/eclipse/aether/internal/impl/DefaultLocalRepositoryProvider
instanceKlass org/eclipse/aether/impl/LocalRepositoryProvider
instanceKlass org/eclipse/aether/internal/impl/DefaultChecksumPolicyProvider
instanceKlass org/eclipse/aether/spi/connector/checksum/ChecksumPolicyProvider
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositorySystem
instanceKlass org/eclipse/aether/RepositorySystem
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher
instanceKlass org/eclipse/aether/impl/RepositoryEventDispatcher
instanceKlass org/eclipse/aether/internal/impl/DefaultUpdateCheckManager
instanceKlass org/eclipse/aether/impl/UpdateCheckManager
instanceKlass org/eclipse/aether/internal/impl/DefaultMetadataResolver
instanceKlass org/eclipse/aether/impl/MetadataResolver
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider
instanceKlass org/eclipse/aether/impl/RepositoryConnectorProvider
instanceKlass org/eclipse/aether/internal/impl/DefaultSyncContextFactory
instanceKlass org/eclipse/aether/impl/SyncContextFactory
instanceKlass org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory
instanceKlass org/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryLayoutProvider
instanceKlass org/eclipse/aether/spi/connector/layout/RepositoryLayoutProvider
instanceKlass sun/util/resources/provider/NonBaseLocaleDataMetaInfo
instanceKlass org/eclipse/aether/internal/impl/LoggerFactoryProvider
instanceKlass sun/util/locale/provider/BaseLocaleDataMetaInfo
instanceKlass org/eclipse/aether/internal/impl/DefaultUpdatePolicyAnalyzer
instanceKlass org/eclipse/aether/impl/UpdatePolicyAnalyzer
instanceKlass org/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager
instanceKlass org/eclipse/aether/impl/RemoteRepositoryManager
instanceKlass sun/util/locale/provider/TimeZoneNameUtility$TimeZoneNameGetter
instanceKlass sun/util/locale/provider/TimeZoneNameUtility
instanceKlass org/eclipse/aether/internal/impl/DefaultOfflineController
instanceKlass org/eclipse/aether/impl/OfflineController
instanceKlass sun/nio/cs/Surrogate
instanceKlass sun/nio/cs/Surrogate$Parser
instanceKlass org/eclipse/aether/internal/impl/DefaultFileProcessor
instanceKlass org/eclipse/aether/spi/io/FileProcessor
instanceKlass jdk/internal/vm/VMSupport
instanceKlass org/eclipse/aether/internal/impl/EnhancedLocalRepositoryManagerFactory
instanceKlass org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory
instanceKlass org/eclipse/aether/spi/log/LoggerFactory
instanceKlass org/eclipse/aether/internal/impl/DefaultDeployer
instanceKlass org/eclipse/aether/impl/Deployer
instanceKlass org/eclipse/aether/internal/impl/SimpleLocalRepositoryManagerFactory
instanceKlass org/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory
instanceKlass org/eclipse/aether/internal/impl/DefaultTransporterProvider
instanceKlass org/eclipse/aether/spi/connector/transport/TransporterProvider
instanceKlass org/eclipse/aether/connector/basic/BasicRepositoryConnectorFactory
instanceKlass org/eclipse/aether/spi/locator/Service
instanceKlass org/eclipse/aether/spi/connector/RepositoryConnectorFactory
instanceKlass org/apache/maven/model/validation/DefaultModelValidator
instanceKlass org/apache/maven/model/validation/ModelValidator
instanceKlass org/apache/maven/model/superpom/DefaultSuperPomProvider
instanceKlass org/apache/maven/model/superpom/SuperPomProvider
instanceKlass org/apache/maven/model/profile/activation/PropertyProfileActivator
instanceKlass org/apache/maven/model/profile/activation/OperatingSystemProfileActivator
instanceKlass org/apache/maven/model/profile/activation/JdkVersionProfileActivator
instanceKlass org/apache/maven/model/profile/activation/FileProfileActivator
instanceKlass org/apache/maven/model/profile/activation/ProfileActivator
instanceKlass org/apache/maven/model/profile/DefaultProfileSelector
instanceKlass org/apache/maven/model/profile/ProfileSelector
instanceKlass org/apache/maven/model/profile/DefaultProfileInjector
instanceKlass org/apache/maven/model/profile/ProfileInjector
instanceKlass org/apache/maven/model/plugin/DefaultReportingConverter
instanceKlass org/apache/maven/model/plugin/ReportingConverter
instanceKlass org/apache/maven/model/plugin/DefaultReportConfigurationExpander
instanceKlass org/apache/maven/model/plugin/ReportConfigurationExpander
instanceKlass org/apache/maven/model/plugin/DefaultPluginConfigurationExpander
instanceKlass org/apache/maven/model/plugin/PluginConfigurationExpander
instanceKlass org/apache/maven/model/path/DefaultUrlNormalizer
instanceKlass org/apache/maven/model/path/UrlNormalizer
instanceKlass org/apache/maven/model/path/DefaultPathTranslator
instanceKlass org/apache/maven/model/path/PathTranslator
instanceKlass org/apache/maven/model/path/DefaultModelUrlNormalizer
instanceKlass org/apache/maven/model/path/ModelUrlNormalizer
instanceKlass org/apache/maven/model/path/DefaultModelPathTranslator
instanceKlass org/apache/maven/model/path/ModelPathTranslator
instanceKlass org/apache/maven/model/normalization/DefaultModelNormalizer
instanceKlass org/apache/maven/model/normalization/ModelNormalizer
instanceKlass org/apache/maven/model/management/DefaultPluginManagementInjector
instanceKlass org/apache/maven/model/management/PluginManagementInjector
instanceKlass org/apache/maven/model/management/DefaultDependencyManagementInjector
instanceKlass org/apache/maven/model/management/DependencyManagementInjector
instanceKlass org/apache/maven/model/locator/DefaultModelLocator
instanceKlass org/apache/maven/model/io/DefaultModelWriter
instanceKlass org/apache/maven/model/io/ModelWriter
instanceKlass org/apache/maven/model/io/DefaultModelReader
instanceKlass org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator
instanceKlass org/apache/maven/model/interpolation/ModelInterpolator
instanceKlass org/apache/maven/model/inheritance/DefaultInheritanceAssembler
instanceKlass org/apache/maven/model/inheritance/InheritanceAssembler
instanceKlass org/apache/maven/model/composition/DefaultDependencyManagementImporter
instanceKlass org/apache/maven/model/composition/DependencyManagementImporter
instanceKlass jdk/internal/reflect/ClassDefiner$1
instanceKlass jdk/internal/reflect/ClassDefiner
instanceKlass jdk/internal/reflect/MethodAccessorGenerator$1
instanceKlass jdk/internal/reflect/Label$PatchInfo
instanceKlass jdk/internal/reflect/Label
instanceKlass jdk/internal/reflect/UTF8
instanceKlass jdk/internal/reflect/ClassFileAssembler
instanceKlass jdk/internal/reflect/ByteVectorImpl
instanceKlass jdk/internal/reflect/ByteVector
instanceKlass jdk/internal/reflect/ByteVectorFactory
instanceKlass jdk/internal/reflect/AccessorGenerator
instanceKlass jdk/internal/reflect/ClassFileConstants
instanceKlass org/apache/maven/model/building/DefaultModelProcessor
instanceKlass org/apache/maven/model/building/ModelProcessor
instanceKlass org/apache/maven/model/io/ModelReader
instanceKlass org/apache/maven/model/locator/ModelLocator
instanceKlass org/apache/maven/model/building/DefaultModelBuilder
instanceKlass org/apache/maven/model/building/ModelBuilder
instanceKlass org/apache/maven/cli/internal/BootstrapCoreExtensionManager
instanceKlass org/apache/maven/cli/configuration/SettingsXmlConfigurationProcessor
instanceKlass org/apache/maven/cli/configuration/ConfigurationProcessor
instanceKlass org/apache/maven/toolchain/io/DefaultToolchainsWriter
instanceKlass org/apache/maven/toolchain/io/ToolchainsWriter
instanceKlass org/apache/maven/toolchain/io/DefaultToolchainsReader
instanceKlass org/apache/maven/toolchain/io/ToolchainsReader
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuilder
instanceKlass org/apache/maven/toolchain/building/ToolchainsBuilder
instanceKlass org/apache/maven/execution/MavenSession
instanceKlass org/apache/maven/session/scope/internal/SessionScope$ScopeState
instanceKlass org/apache/maven/session/scope/internal/SessionScope$Memento
instanceKlass org/apache/maven/session/scope/internal/SessionScope$1
instanceKlass org/apache/maven/session/scope/internal/SessionScope
instanceKlass org/apache/maven/lifecycle/internal/LifecycleDependencyResolver
instanceKlass org/apache/maven/lifecycle/internal/DefaultProjectArtifactFactory
instanceKlass org/apache/maven/lifecycle/internal/ProjectArtifactFactory
instanceKlass org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory
instanceKlass org/apache/maven/extension/internal/CoreExportsProvider
instanceKlass org/apache/maven/plugin/MojoExecution
instanceKlass org/apache/maven/project/MavenProject
instanceKlass org/apache/maven/execution/scope/internal/MojoExecutionScope$ScopeState
instanceKlass org/apache/maven/execution/MojoExecutionEvent
instanceKlass org/apache/maven/execution/scope/MojoExecutionScoped
instanceKlass org/apache/maven/execution/scope/internal/MojoExecutionScope$1
instanceKlass org/apache/maven/execution/scope/internal/MojoExecutionScope
instanceKlass org/apache/maven/execution/MojoExecutionListener
instanceKlass org/eclipse/sisu/space/QualifiedTypeBinder$1
instanceKlass org/apache/maven/execution/DefaultMavenExecutionRequestPopulator
instanceKlass org/apache/maven/execution/MavenExecutionRequestPopulator
instanceKlass org/apache/maven/classrealm/DefaultClassRealmManager
instanceKlass org/apache/maven/classrealm/ClassRealmManager
instanceKlass org/apache/maven/SessionScoped
instanceKlass org/apache/maven/ReactorReader
instanceKlass org/apache/maven/repository/internal/MavenWorkspaceReader
instanceKlass org/eclipse/aether/repository/WorkspaceReader
instanceKlass org/apache/maven/DefaultArtifactFilterManager
instanceKlass org/apache/maven/ArtifactFilterManager
instanceKlass org/eclipse/sisu/space/WildcardKey$QualifiedImpl
instanceKlass org/eclipse/sisu/space/WildcardKey$Qualified
instanceKlass org/eclipse/sisu/space/WildcardKey
instanceKlass org/eclipse/sisu/Typed
instanceKlass org/sonatype/inject/EagerSingleton
instanceKlass org/eclipse/sisu/EagerSingleton
instanceKlass org/sonatype/inject/Mediator
instanceKlass org/eclipse/sisu/inject/TypeArguments
instanceKlass org/apache/maven/eventspy/AbstractEventSpy
instanceKlass org/apache/maven/eventspy/EventSpy
instanceKlass org/eclipse/sisu/space/asm/Context
instanceKlass org/eclipse/sisu/space/asm/Attribute
instanceKlass org/eclipse/sisu/space/asm/AnnotationVisitor
instanceKlass org/eclipse/sisu/space/asm/ClassReader
instanceKlass org/eclipse/sisu/space/IndexedClassFinder$1
instanceKlass org/eclipse/sisu/inject/Logs$SLF4JSink
instanceKlass org/eclipse/sisu/inject/Logs$Sink
instanceKlass org/eclipse/sisu/inject/Logs
instanceKlass org/eclipse/sisu/space/QualifierCache
instanceKlass org/eclipse/sisu/space/QualifiedTypeVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusTypeVisitor$ComponentAnnotationVisitor
instanceKlass org/eclipse/sisu/space/AnnotationVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusTypeVisitor
instanceKlass org/eclipse/sisu/space/ClassVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusXmlBeanModule$PlexusXmlBeanSource
instanceKlass org/eclipse/sisu/inject/DescriptionSource
instanceKlass org/eclipse/sisu/inject/AnnotatedSource
instanceKlass org/eclipse/sisu/Priority
instanceKlass org/eclipse/sisu/Hidden
instanceKlass org/eclipse/sisu/Description
instanceKlass org/eclipse/sisu/inject/Sources
instanceKlass com/google/inject/Key$AnnotationInstanceStrategy
instanceKlass com/google/inject/name/NamedImpl
instanceKlass com/google/inject/name/Named
instanceKlass com/google/inject/name/Names
instanceKlass com/google/inject/internal/MoreTypes$ParameterizedTypeImpl
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass org/apache/maven/wagon/Wagon
instanceKlass org/sonatype/plexus/components/cipher/PlexusCipher
instanceKlass org/codehaus/plexus/component/configurator/ComponentConfigurator
instanceKlass org/apache/maven/toolchain/ToolchainsBuilder
instanceKlass org/apache/maven/toolchain/ToolchainManagerPrivate
instanceKlass org/apache/maven/toolchain/ToolchainManager
instanceKlass org/apache/maven/toolchain/ToolchainFactory
instanceKlass org/apache/maven/settings/MavenSettingsBuilder
instanceKlass org/apache/maven/rtinfo/RuntimeInformation
instanceKlass org/apache/maven/project/artifact/ProjectArtifactsCache
instanceKlass org/apache/maven/project/artifact/MavenMetadataCache
instanceKlass org/apache/maven/project/ProjectRealmCache
instanceKlass org/apache/maven/project/ProjectDependenciesResolver
instanceKlass org/apache/maven/project/ProjectBuildingHelper
instanceKlass org/apache/maven/project/ProjectBuilder
instanceKlass org/apache/maven/project/MavenProjectHelper
instanceKlass org/apache/maven/plugin/version/PluginVersionResolver
instanceKlass org/apache/maven/plugin/prefix/PluginPrefixResolver
instanceKlass org/apache/maven/plugin/internal/PluginDependenciesResolver
instanceKlass org/apache/maven/plugin/PluginRealmCache
instanceKlass org/apache/maven/plugin/PluginManager
instanceKlass org/apache/maven/plugin/PluginDescriptorCache
instanceKlass org/apache/maven/plugin/PluginArtifactsCache
instanceKlass org/apache/maven/plugin/MavenPluginManager
instanceKlass org/apache/maven/plugin/LegacySupport
instanceKlass org/apache/maven/plugin/ExtensionRealmCache
instanceKlass org/apache/maven/plugin/BuildPluginManager
instanceKlass org/apache/maven/model/plugin/LifecycleBindingsInjector
instanceKlass org/apache/maven/lifecycle/internal/builder/BuilderCommon
instanceKlass org/apache/maven/lifecycle/internal/builder/Builder
instanceKlass org/apache/maven/lifecycle/internal/MojoExecutor
instanceKlass org/apache/maven/lifecycle/internal/MojoDescriptorCreator
instanceKlass org/apache/maven/lifecycle/internal/LifecycleTaskSegmentCalculator
instanceKlass org/apache/maven/lifecycle/internal/LifecycleStarter
instanceKlass org/apache/maven/lifecycle/internal/LifecyclePluginResolver
instanceKlass org/apache/maven/lifecycle/internal/LifecycleModuleBuilder
instanceKlass org/apache/maven/lifecycle/internal/LifecycleExecutionPlanCalculator
instanceKlass org/apache/maven/lifecycle/internal/LifecycleDebugLogger
instanceKlass org/apache/maven/lifecycle/internal/ExecutionEventCatapult
instanceKlass org/apache/maven/lifecycle/internal/BuildListCalculator
instanceKlass org/apache/maven/lifecycle/MojoExecutionConfigurator
instanceKlass org/apache/maven/lifecycle/LifecycleMappingDelegate
instanceKlass org/apache/maven/lifecycle/LifecycleExecutor
instanceKlass org/apache/maven/lifecycle/LifeCyclePluginAnalyzer
instanceKlass org/apache/maven/lifecycle/DefaultLifecycles
instanceKlass org/apache/maven/graph/GraphBuilder
instanceKlass org/apache/maven/eventspy/internal/EventSpyDispatcher
instanceKlass org/apache/maven/configuration/BeanConfigurator
instanceKlass org/apache/maven/bridge/MavenRepositorySystem
instanceKlass org/apache/maven/artifact/resolver/ResolutionErrorHandler
instanceKlass org/apache/maven/artifact/repository/metadata/io/MetadataReader
instanceKlass org/apache/maven/artifact/metadata/ArtifactMetadataSource
instanceKlass org/apache/maven/repository/legacy/metadata/ArtifactMetadataSource
instanceKlass org/apache/maven/artifact/handler/manager/ArtifactHandlerManager
instanceKlass org/apache/maven/artifact/factory/ArtifactFactory
instanceKlass org/apache/maven/ProjectDependenciesResolver
instanceKlass org/apache/maven/Maven
instanceKlass org/apache/maven/artifact/handler/ArtifactHandler
instanceKlass org/sonatype/plexus/components/sec/dispatcher/SecDispatcher
instanceKlass org/apache/maven/lifecycle/Lifecycle
instanceKlass org/eclipse/sisu/space/CloningClassSpace$1
instanceKlass org/apache/maven/lifecycle/mapping/LifecycleMapping
instanceKlass org/apache/maven/repository/metadata/GraphConflictResolver
instanceKlass org/apache/maven/repository/metadata/GraphConflictResolutionPolicy
instanceKlass org/eclipse/sisu/plexus/ConfigurationImpl
instanceKlass org/apache/maven/repository/metadata/ClasspathTransformation
instanceKlass org/apache/maven/repository/legacy/resolver/transform/ArtifactTransformationManager
instanceKlass org/apache/maven/repository/legacy/resolver/transform/ArtifactTransformation
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/ConflictResolverFactory
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/ConflictResolver
instanceKlass org/apache/maven/repository/legacy/repository/ArtifactRepositoryFactory
instanceKlass org/apache/maven/repository/legacy/UpdateCheckManager
instanceKlass org/apache/maven/repository/RepositorySystem
instanceKlass org/apache/maven/repository/MirrorSelector
instanceKlass org/apache/maven/project/validation/ModelValidator
instanceKlass org/apache/maven/project/path/PathTranslator
instanceKlass org/apache/maven/project/interpolation/ModelInterpolator
instanceKlass org/apache/maven/project/inheritance/ModelInheritanceAssembler
instanceKlass org/apache/maven/project/MavenProjectBuilder
instanceKlass org/apache/maven/profiles/MavenProfilesBuilder
instanceKlass org/apache/maven/execution/RuntimeInformation
instanceKlass jdk/internal/access/foreign/MemorySegmentProxy
instanceKlass org/apache/maven/artifact/resolver/ArtifactResolver
instanceKlass org/apache/maven/artifact/resolver/ArtifactCollector
instanceKlass org/apache/maven/repository/legacy/resolver/LegacyArtifactCollector
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataManager
instanceKlass org/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout
instanceKlass org/apache/maven/artifact/repository/ArtifactRepositoryFactory
instanceKlass org/apache/maven/artifact/manager/WagonManager
instanceKlass org/apache/maven/repository/legacy/WagonManager
instanceKlass org/apache/maven/artifact/installer/ArtifactInstaller
instanceKlass org/eclipse/sisu/plexus/PlexusXmlMetadata
instanceKlass org/eclipse/sisu/plexus/Roles
instanceKlass org/apache/maven/artifact/deployer/ArtifactDeployer
instanceKlass org/eclipse/sisu/plexus/Hints
instanceKlass org/eclipse/sisu/space/AbstractDeferredClass
instanceKlass org/eclipse/sisu/plexus/RequirementImpl
instanceKlass org/codehaus/plexus/component/annotations/Requirement
instanceKlass org/eclipse/sisu/space/Streams
instanceKlass org/eclipse/sisu/plexus/ComponentImpl
instanceKlass org/codehaus/plexus/component/annotations/Component
instanceKlass org/eclipse/sisu/plexus/PlexusTypeRegistry
instanceKlass org/eclipse/sisu/plexus/PlexusXmlScanner
instanceKlass javax/enterprise/inject/Typed
instanceKlass org/eclipse/sisu/space/QualifiedTypeBinder
instanceKlass org/eclipse/sisu/plexus/PlexusTypeBinder
instanceKlass com/google/inject/spi/InjectionRequest
instanceKlass org/eclipse/sisu/bean/BeanProperty
instanceKlass com/google/inject/internal/Nullability
instanceKlass com/google/inject/spi/InjectionPoint$OverrideIndex
instanceKlass org/eclipse/sisu/Mediator
instanceKlass org/eclipse/sisu/inject/RankedBindings
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass com/google/common/collect/ComparisonChain
instanceKlass com/google/inject/Inject
instanceKlass javax/inject/Inject
instanceKlass java/lang/reflect/WildcardType
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass com/google/inject/spi/InjectionPoint$InjectableMembers
instanceKlass com/google/inject/spi/InjectionPoint$InjectableMember
instanceKlass com/google/common/collect/Ordering
instanceKlass com/google/inject/spi/InjectionPoint
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass com/google/inject/internal/MoreTypes$GenericArrayTypeImpl
instanceKlass com/google/inject/internal/MoreTypes$CompositeType
instanceKlass com/google/inject/Key$AnnotationTypeStrategy
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/util/concurrent/TimeUnit$1
instanceKlass com/google/common/util/concurrent/AbstractFuture$Failure
instanceKlass com/google/common/util/concurrent/AbstractFuture$Cancellation
instanceKlass com/google/common/util/concurrent/AbstractFuture$SetFuture
instanceKlass com/google/common/util/concurrent/Uninterruptibles
instanceKlass com/google/common/base/CommonPattern
instanceKlass com/google/common/base/Platform$JdkPatternCompiler
instanceKlass com/google/common/base/PatternCompiler
instanceKlass com/google/common/base/Platform
instanceKlass com/google/common/base/Stopwatch
instanceKlass com/google/common/util/concurrent/AbstractFuture$Waiter
instanceKlass com/google/common/util/concurrent/AbstractFuture$Listener
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorFactory
instanceKlass com/google/common/util/concurrent/AbstractFuture$UnsafeAtomicHelper$1
instanceKlass sun/misc/Unsafe
instanceKlass com/google/common/util/concurrent/AbstractFuture$AtomicHelper
instanceKlass com/google/common/util/concurrent/GwtFluentFutureCatchingSpecialization
instanceKlass com/google/common/util/concurrent/ListenableFuture
instanceKlass com/google/common/cache/LocalCache$LoadingValueReference
instanceKlass java/lang/Class$AnnotationData
instanceKlass java/lang/annotation/Documented
instanceKlass java/lang/annotation/Target
instanceKlass javax/inject/Named
instanceKlass javax/inject/Qualifier
instanceKlass com/google/inject/BindingAnnotation
instanceKlass javax/inject/Scope
instanceKlass com/google/inject/ScopeAnnotation
instanceKlass com/google/inject/internal/Annotations$AnnotationChecker
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/util/StringJoiner
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass java/lang/PublicMethods
instanceKlass java/util/Collections$1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass com/google/inject/internal/Annotations$TestAnnotation
instanceKlass com/google/inject/internal/Annotations$3
instanceKlass com/google/common/base/Joiner$MapJoiner
instanceKlass com/google/common/base/Joiner
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass com/google/inject/internal/Annotations
instanceKlass org/eclipse/sisu/Parameters
instanceKlass org/eclipse/sisu/wire/ParameterKeys
instanceKlass org/eclipse/sisu/wire/TypeConverterCache
instanceKlass org/eclipse/sisu/inject/DefaultRankingFunction
instanceKlass com/google/inject/internal/Scoping
instanceKlass com/google/inject/internal/InternalFactory
instanceKlass java/lang/StackTraceElement$HashedModules
instanceKlass com/google/inject/spi/ConstructorBinding
instanceKlass com/google/inject/spi/InstanceBinding
instanceKlass com/google/inject/internal/DelayedInitialize
instanceKlass com/google/inject/spi/ProviderKeyBinding
instanceKlass com/google/inject/spi/ProviderInstanceBinding
instanceKlass com/google/inject/spi/HasDependencies
instanceKlass com/google/inject/spi/LinkedKeyBinding
instanceKlass com/google/inject/spi/UntargettedBinding
instanceKlass com/google/inject/internal/BindingImpl
instanceKlass com/google/inject/Key$AnnotationStrategy
instanceKlass org/eclipse/sisu/wire/ElementAnalyzer$1
instanceKlass com/google/inject/util/Modules$EmptyModule
instanceKlass com/google/inject/util/Modules$OverriddenModuleBuilder
instanceKlass com/google/inject/util/Modules
instanceKlass com/google/common/collect/ImmutableMap$Builder
instanceKlass com/google/inject/internal/MoreTypes
instanceKlass com/google/inject/multibindings/ProvidesIntoOptional
instanceKlass com/google/inject/multibindings/ProvidesIntoMap
instanceKlass com/google/inject/multibindings/ProvidesIntoSet
instanceKlass com/google/inject/Provides
instanceKlass javax/inject/Singleton
instanceKlass com/google/inject/spi/ElementSource
instanceKlass com/google/inject/spi/ScopeBinding
instanceKlass com/google/inject/Scopes$2
instanceKlass com/google/inject/Scopes$1
instanceKlass com/google/inject/internal/SingletonScope
instanceKlass com/google/inject/Scopes
instanceKlass com/google/inject/Singleton
instanceKlass com/google/inject/spi/Elements$ModuleInfo
instanceKlass com/google/inject/PrivateModule
instanceKlass com/google/inject/internal/util/StackTraceElements$InMemoryStackTraceElement
instanceKlass com/google/inject/internal/util/StackTraceElements
instanceKlass com/google/inject/spi/ModuleSource
instanceKlass com/google/inject/internal/InternalFlags$1
instanceKlass com/google/inject/internal/InternalFlags
instanceKlass com/google/inject/internal/ProviderMethodsModule
instanceKlass com/google/inject/internal/AbstractBindingBuilder
instanceKlass com/google/inject/binder/ConstantBindingBuilder
instanceKlass com/google/common/collect/Sets
instanceKlass com/google/inject/binder/AnnotatedElementBuilder
instanceKlass com/google/inject/spi/Elements$RecordingBinder
instanceKlass com/google/inject/Binding
instanceKlass com/google/inject/spi/DefaultBindingTargetVisitor
instanceKlass com/google/inject/spi/BindingTargetVisitor
instanceKlass com/google/inject/spi/Elements
instanceKlass com/google/inject/internal/InjectorShell$RootModule
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass java/util/concurrent/Future
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass com/google/common/cache/Weigher
instanceKlass com/google/common/base/Predicate
instanceKlass com/google/common/base/Equivalence
instanceKlass com/google/common/base/MoreObjects
instanceKlass com/google/common/cache/LocalCache$1
instanceKlass com/google/common/cache/ReferenceEntry
instanceKlass com/google/common/cache/CacheLoader
instanceKlass com/google/common/cache/LocalCache$LocalManualCache
instanceKlass com/google/inject/internal/WeakKeySet$1
instanceKlass com/google/common/cache/LocalCache$StrongValueReference
instanceKlass com/google/common/cache/LocalCache$ValueReference
instanceKlass com/google/common/cache/CacheBuilder$2
instanceKlass com/google/common/cache/CacheStats
instanceKlass com/google/common/base/Suppliers$SupplierOfInstance
instanceKlass com/google/common/base/Suppliers
instanceKlass com/google/common/cache/CacheBuilder$1
instanceKlass com/google/common/cache/AbstractCache$StatsCounter
instanceKlass com/google/common/cache/LoadingCache
instanceKlass com/google/common/cache/Cache
instanceKlass com/google/common/base/Ticker
instanceKlass com/google/common/base/Supplier
instanceKlass com/google/common/cache/CacheBuilder
instanceKlass com/google/common/cache/RemovalListener
instanceKlass com/google/inject/internal/WeakKeySet
instanceKlass com/google/inject/internal/State$1
instanceKlass com/google/inject/internal/InheritingState
instanceKlass com/google/inject/internal/ProcessedBindingData
instanceKlass com/google/inject/spi/DefaultElementVisitor
instanceKlass com/google/inject/internal/State
instanceKlass com/google/inject/internal/InjectorShell$Builder
instanceKlass com/google/common/collect/Lists
instanceKlass com/google/common/collect/AbstractMapEntry
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSetLink
instanceKlass com/google/common/collect/Platform
instanceKlass com/google/common/collect/Multiset
instanceKlass com/google/common/collect/AbstractMultimap
instanceKlass com/google/common/collect/SetMultimap
instanceKlass com/google/common/collect/Maps$EntryTransformer
instanceKlass com/google/common/collect/BiMap
instanceKlass com/google/common/base/Converter
instanceKlass com/google/common/collect/ImmutableMap
instanceKlass com/google/common/base/Function
instanceKlass com/google/common/collect/SortedMapDifference
instanceKlass com/google/common/collect/MapDifference
instanceKlass com/google/common/collect/Maps
instanceKlass com/google/inject/internal/CycleDetectingLock
instanceKlass com/google/common/collect/Multimap
instanceKlass com/google/inject/internal/CycleDetectingLock$CycleDetectingLockFactory
instanceKlass com/google/inject/internal/Initializable
instanceKlass com/google/inject/internal/Initializer
instanceKlass com/google/common/collect/PeekingIterator
instanceKlass com/google/common/collect/UnmodifiableIterator
instanceKlass com/google/common/collect/Iterators
instanceKlass com/google/inject/internal/util/SourceProvider
instanceKlass com/google/common/collect/Hashing
instanceKlass com/google/common/collect/ObjectArrays
instanceKlass com/google/common/primitives/Primitives
instanceKlass com/google/common/base/Preconditions
instanceKlass com/google/common/collect/CollectPreconditions
instanceKlass com/google/common/collect/ImmutableCollection$Builder
instanceKlass com/google/inject/internal/Errors
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass jdk/internal/misc/ScopedMemoryAccess$Scope
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/lang/System$Logger
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass com/google/inject/internal/util/Stopwatch
instanceKlass com/google/inject/Injector
instanceKlass com/google/inject/internal/InternalInjectorCreator
instanceKlass com/google/inject/Guice
instanceKlass org/eclipse/sisu/wire/Wiring
instanceKlass org/eclipse/sisu/wire/WireModule$Strategy$1
instanceKlass org/eclipse/sisu/wire/WireModule$Strategy
instanceKlass org/eclipse/sisu/wire/AbstractTypeConverter
instanceKlass com/google/inject/spi/ElementVisitor
instanceKlass org/eclipse/sisu/wire/WireModule
instanceKlass org/eclipse/sisu/bean/BeanBinder
instanceKlass org/eclipse/sisu/plexus/PlexusBindingModule
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$BootModule
instanceKlass org/codehaus/plexus/component/annotations/Configuration
instanceKlass org/eclipse/sisu/plexus/PlexusAnnotatedMetadata
instanceKlass org/eclipse/sisu/plexus/PlexusBeanMetadata
instanceKlass org/eclipse/sisu/plexus/PlexusAnnotatedBeanModule$PlexusAnnotatedBeanSource
instanceKlass org/eclipse/sisu/space/SpaceModule$Strategy$1
instanceKlass org/eclipse/sisu/space/DefaultClassFinder
instanceKlass org/eclipse/sisu/space/asm/ClassVisitor
instanceKlass org/eclipse/sisu/space/SpaceScanner
instanceKlass org/eclipse/sisu/space/IndexedClassFinder
instanceKlass org/eclipse/sisu/space/ClassFinder
instanceKlass org/eclipse/sisu/space/SpaceModule
instanceKlass org/eclipse/sisu/space/SpaceVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusTypeListener
instanceKlass org/eclipse/sisu/space/QualifiedTypeListener
instanceKlass org/eclipse/sisu/plexus/PlexusAnnotatedBeanModule$1
instanceKlass org/eclipse/sisu/space/SpaceModule$Strategy
instanceKlass org/eclipse/sisu/plexus/PlexusAnnotatedBeanModule
instanceKlass org/eclipse/sisu/plexus/PlexusBeanSource
instanceKlass org/eclipse/sisu/plexus/PlexusXmlBeanModule
instanceKlass org/eclipse/sisu/plexus/PlexusBeanModule
instanceKlass org/eclipse/sisu/space/URLClassSpace
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$SLF4JLoggerFactoryProvider
instanceKlass com/google/inject/util/Providers$ConstantProvider
instanceKlass com/google/inject/util/Providers
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/Disposable
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/Startable
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/Initializable
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/Contextualizable
instanceKlass org/codehaus/plexus/logging/LogEnabled
instanceKlass org/eclipse/sisu/bean/PropertyBinding
instanceKlass org/eclipse/sisu/bean/LifecycleBuilder
instanceKlass org/eclipse/sisu/bean/BeanScheduler$1
instanceKlass com/google/inject/spi/DefaultBindingScopingVisitor
instanceKlass com/google/inject/spi/BindingScopingVisitor
instanceKlass org/eclipse/sisu/bean/BeanScheduler$CycleActivator
instanceKlass com/google/inject/PrivateBinder
instanceKlass com/google/inject/spi/ModuleAnnotatedMethodScanner
instanceKlass com/google/inject/spi/Message
instanceKlass com/google/inject/spi/Element
instanceKlass com/google/inject/binder/AnnotatedConstantBindingBuilder
instanceKlass com/google/inject/spi/TypeListener
instanceKlass com/google/inject/MembersInjector
instanceKlass com/google/inject/Scope
instanceKlass com/google/inject/spi/Dependency
instanceKlass com/google/inject/Key
instanceKlass com/google/inject/binder/AnnotatedBindingBuilder
instanceKlass com/google/inject/binder/LinkedBindingBuilder
instanceKlass com/google/inject/binder/ScopedBindingBuilder
instanceKlass com/google/inject/TypeLiteral
instanceKlass com/google/inject/spi/ProvisionListener
instanceKlass com/google/inject/Binder
instanceKlass org/eclipse/sisu/bean/BeanScheduler
instanceKlass org/eclipse/sisu/plexus/DefaultPlexusBeanLocator
instanceKlass org/eclipse/sisu/inject/MildKeys
instanceKlass org/eclipse/sisu/plexus/ClassRealmManager
instanceKlass org/codehaus/plexus/context/ContextMapAdapter
instanceKlass org/codehaus/plexus/context/DefaultContext
instanceKlass org/codehaus/plexus/logging/AbstractLogger
instanceKlass org/codehaus/plexus/logging/AbstractLoggerManager
instanceKlass java/util/Date
instanceKlass java/text/DigitList
instanceKlass java/text/FieldPosition
instanceKlass java/lang/StringUTF16$CharsSpliterator
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass java/util/OptionalInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass java/util/function/IntPredicate
instanceKlass java/util/stream/IntStream
instanceKlass java/lang/StringLatin1$CharsSpliterator
instanceKlass java/text/DecimalFormatSymbols
instanceKlass java/text/DateFormatSymbols
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass java/util/ResourceBundle
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass java/util/Calendar$Builder
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$1
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass java/util/Calendar
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass com/google/inject/matcher/AbstractMatcher
instanceKlass com/google/inject/matcher/Matcher
instanceKlass com/google/inject/spi/TypeConverter
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$LoggerProvider
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$DefaultsModule
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$ContainerModule
instanceKlass org/eclipse/sisu/inject/ImplicitBindings
instanceKlass org/eclipse/sisu/inject/MildValues$InverseMapping
instanceKlass org/eclipse/sisu/inject/MildValues
instanceKlass org/eclipse/sisu/inject/Weak
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/eclipse/sisu/inject/BindingPublisher
instanceKlass org/eclipse/sisu/inject/RankingFunction
instanceKlass org/eclipse/sisu/inject/BindingSubscriber
instanceKlass org/eclipse/sisu/inject/DefaultBeanLocator
instanceKlass org/eclipse/sisu/inject/DeferredClass
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$LoggerManagerProvider
instanceKlass org/eclipse/sisu/inject/DeferredProvider
instanceKlass com/google/inject/Provider
instanceKlass com/google/inject/AbstractModule
instanceKlass org/codehaus/plexus/context/Context
instanceKlass org/eclipse/sisu/space/ClassSpace
instanceKlass javax/inject/Provider
instanceKlass org/eclipse/sisu/bean/BeanManager
instanceKlass org/eclipse/sisu/plexus/PlexusBeanLocator
instanceKlass org/codehaus/plexus/classworlds/ClassWorldListener
instanceKlass com/google/inject/Module
instanceKlass org/eclipse/sisu/inject/MutableBeanLocator
instanceKlass org/eclipse/sisu/inject/BeanLocator
instanceKlass org/codehaus/plexus/DefaultPlexusContainer
instanceKlass org/codehaus/plexus/MutablePlexusContainer
instanceKlass org/apache/maven/extension/internal/CoreExports
instanceKlass org/codehaus/plexus/DefaultContainerConfiguration
instanceKlass org/codehaus/plexus/ContainerConfiguration
instanceKlass org/codehaus/plexus/util/xml/XMLWriter
instanceKlass org/codehaus/plexus/util/xml/Xpp3Dom
instanceKlass org/codehaus/plexus/util/xml/pull/MXParser
instanceKlass org/codehaus/plexus/util/xml/pull/XmlPullParser
instanceKlass org/codehaus/plexus/util/xml/Xpp3DomBuilder
instanceKlass java/util/regex/ASCII
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass org/codehaus/plexus/util/ReaderFactory
instanceKlass org/apache/maven/project/ExtensionDescriptor
instanceKlass org/apache/maven/project/ExtensionDescriptorBuilder
instanceKlass org/apache/maven/extension/internal/CoreExtensionEntry
instanceKlass org/apache/maven/cli/ResolveFile
instanceKlass java/util/StringTokenizer
instanceKlass org/codehaus/plexus/util/StringUtils
instanceKlass org/codehaus/plexus/logging/Logger
instanceKlass org/apache/maven/cli/logging/Slf4jLoggerManager
instanceKlass org/slf4j/impl/MavenSlf4jSimpleFriend
instanceKlass org/slf4j/MavenSlf4jFriend
instanceKlass org/apache/maven/cli/logging/BaseSlf4jConfiguration
instanceKlass org/codehaus/plexus/util/IOUtil
instanceKlass org/codehaus/plexus/util/PropertyUtils
instanceKlass org/apache/maven/cli/logging/Slf4jConfiguration
instanceKlass org/apache/maven/cli/logging/Slf4jConfigurationFactory
instanceKlass org/slf4j/impl/OutputChoice
instanceKlass sun/net/DefaultProgressMeteringPolicy
instanceKlass sun/net/ProgressMeteringPolicy
instanceKlass sun/net/ProgressMonitor
instanceKlass org/slf4j/impl/SimpleLoggerConfiguration$1
instanceKlass java/text/Format
instanceKlass org/slf4j/impl/SimpleLoggerConfiguration
instanceKlass org/slf4j/helpers/NamedLoggerBase
instanceKlass org/slf4j/impl/SimpleLoggerFactory
instanceKlass org/slf4j/impl/StaticLoggerBinder
instanceKlass org/slf4j/spi/LoggerFactoryBinder
instanceKlass java/util/Collections$3
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass org/slf4j/helpers/Util
instanceKlass org/slf4j/helpers/NOPLoggerFactory
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass org/slf4j/helpers/SubstituteLoggerFactory
instanceKlass org/slf4j/ILoggerFactory
instanceKlass org/slf4j/event/LoggingEvent
instanceKlass org/slf4j/LoggerFactory
instanceKlass org/apache/commons/lang3/StringUtils
instanceKlass org/apache/maven/cli/CLIReportingUtils
instanceKlass org/apache/maven/properties/internal/SystemProperties
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/codehaus/plexus/util/Os
instanceKlass org/apache/maven/properties/internal/EnvironmentUtils
instanceKlass java/util/LinkedList$Node
instanceKlass org/apache/commons/cli/Util
instanceKlass org/apache/commons/cli/CommandLine
instanceKlass org/apache/commons/cli/Parser
instanceKlass org/apache/maven/cli/CleanArgument
instanceKlass org/apache/commons/cli/OptionValidator
instanceKlass org/apache/commons/cli/Option$Builder
instanceKlass org/apache/commons/cli/Option
instanceKlass org/apache/commons/cli/Options
instanceKlass org/apache/commons/cli/CommandLineParser
instanceKlass org/apache/maven/cli/CLIManager
instanceKlass org/apache/maven/cli/logging/Slf4jStdoutLogger
instanceKlass org/eclipse/aether/DefaultRepositoryCache
instanceKlass org/apache/maven/project/ProjectBuildingRequest
instanceKlass org/eclipse/aether/RepositoryCache
instanceKlass org/apache/maven/execution/DefaultMavenExecutionRequest
instanceKlass org/apache/maven/execution/MavenExecutionRequest
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass jdk/internal/loader/NativeLibraries$Unloader
instanceKlass java/io/FileOutputStream$1
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/io/DeleteOnExitHook$1
instanceKlass java/io/DeleteOnExitHook
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass sun/security/provider/AbstractDrbg$NonceProvider
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/net/NetworkInterface$1
instanceKlass java/net/DefaultInterface
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface
instanceKlass java/lang/constant/DynamicConstantDesc
instanceKlass java/lang/constant/DirectMethodHandleDesc$1
instanceKlass java/lang/constant/DirectMethodHandleDescImpl$1
instanceKlass java/lang/constant/DirectMethodHandleDescImpl
instanceKlass java/lang/constant/DirectMethodHandleDesc
instanceKlass java/lang/constant/MethodHandleDesc$1
instanceKlass java/lang/constant/MethodHandleDesc
instanceKlass java/lang/constant/MethodTypeDescImpl
instanceKlass java/lang/constant/MethodTypeDesc
instanceKlass java/lang/constant/ReferenceClassDescImpl
instanceKlass java/lang/constant/ConstantUtils
instanceKlass java/lang/constant/ClassDesc
instanceKlass java/lang/constant/ConstantDescs
instanceKlass java/lang/invoke/VarHandle$2
instanceKlass java/lang/invoke/VarHandle$TypesAndInvokers
instanceKlass java/lang/invoke/VarHandleByteArrayBase
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder
instanceKlass java/security/DrbgParameters$NextBytes
instanceKlass sun/security/provider/EntropySource
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass java/security/SecureRandomSpi
instanceKlass java/security/SecureRandomParameters
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/util/AbstractList$Itr
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass java/io/File$TempDirectory
instanceKlass java/net/URLClassLoader$2
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass org/fusesource/hawtjni/runtime/Library
instanceKlass org/fusesource/jansi/internal/CLibrary
instanceKlass java/util/TreeMap$Entry
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/fusesource/jansi/AnsiConsole
instanceKlass org/fusesource/jansi/Ansi$1
instanceKlass java/util/concurrent/Callable
instanceKlass org/fusesource/jansi/Ansi
instanceKlass org/apache/maven/shared/utils/logging/LoggerLevelRenderer
instanceKlass org/apache/maven/shared/utils/logging/MessageUtils
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass java/util/regex/CharPredicates
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass org/apache/maven/cli/CliRequest
instanceKlass org/apache/maven/execution/ExecutionListener
instanceKlass org/apache/maven/toolchain/building/ToolchainsBuildingRequest
instanceKlass org/apache/maven/building/Source
instanceKlass org/slf4j/Logger
instanceKlass org/codehaus/plexus/logging/LoggerManager
instanceKlass org/eclipse/aether/transfer/TransferListener
instanceKlass org/apache/maven/eventspy/EventSpy$Context
instanceKlass org/codehaus/plexus/PlexusContainer
instanceKlass org/apache/maven/exception/ExceptionHandler
instanceKlass org/apache/maven/cli/MavenCli
instanceKlass java/io/FilePermissionCollection$1
instanceKlass java/security/Security$1
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass sun/net/www/MessageHeader
instanceKlass java/net/URLConnection
instanceKlass java/net/URLClassLoader$1
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass org/codehaus/plexus/classworlds/launcher/Configurator$1
instanceKlass org/codehaus/plexus/classworlds/launcher/ConfigurationParser$1
instanceKlass org/codehaus/plexus/classworlds/strategy/AbstractStrategy
instanceKlass org/codehaus/plexus/classworlds/strategy/Strategy
instanceKlass org/codehaus/plexus/classworlds/strategy/StrategyFactory
instanceKlass java/io/FilenameFilter
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass org/codehaus/plexus/classworlds/launcher/ConfigurationParser
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/Util
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass org/codehaus/plexus/classworlds/ClassWorld
instanceKlass org/codehaus/plexus/classworlds/launcher/Configurator
instanceKlass org/codehaus/plexus/classworlds/launcher/ConfigurationHandler
instanceKlass sun/nio/ch/NativeThread
instanceKlass sun/net/NetHooks
instanceKlass sun/net/spi/DefaultProxySelector$3
instanceKlass sun/net/spi/DefaultProxySelector$NonProxyInfo
instanceKlass sun/net/spi/DefaultProxySelector$1
instanceKlass java/net/Proxy
instanceKlass java/net/ProxySelector
instanceKlass java/net/SocksSocketImpl$3
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions$1
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions
instanceKlass jdk/net/ExtendedSocketOptions$ExtSocketOption
instanceKlass java/net/SocketOption
instanceKlass jdk/net/ExtendedSocketOptions
instanceKlass sun/net/ext/ExtendedSocketOptions
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass sun/nio/ch/Net$1
instanceKlass java/net/ProtocolFamily
instanceKlass sun/nio/ch/Net
instanceKlass java/net/SocksConsts
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass sun/net/PlatformSocketImpl
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass sun/net/NetProperties$1
instanceKlass sun/net/NetProperties
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass java/net/SocketImpl
instanceKlass java/net/SocketOptions
instanceKlass java/net/InetSocketAddress$InetSocketAddressHolder
instanceKlass java/net/InetAddress$PlatformNameService
instanceKlass java/net/InetAddress$NameService
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/net/InetAddressImplFactory
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass jdk/internal/access/JavaNetInetAddressAccess
instanceKlass org/codehaus/plexus/classworlds/launcher/Launcher
instanceKlass java/net/InetAddress
instanceKlass java/net/SocketAddress
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/lang/invoke/VarHandle$1
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/VarHandles
instanceKlass sun/launcher/LauncherHelper
instanceKlass java/net/Socket
instanceKlass jdk/internal/vm/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass jdk/internal/vm/PostVMInitHook$2
instanceKlass jdk/internal/vm/PostVMInitHook
instanceKlass java/io/Reader
instanceKlass com/intellij/rt/execution/application/AppMainV2
instanceKlass com/intellij/rt/execution/application/AppMainV2$Agent
instanceKlass sun/security/util/Debug
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass java/io/RandomAccessFile$1
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass jdk/internal/util/jar/JarIndex
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/net/URI$Parser
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/util/Arrays$ArrayItr
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass sun/net/util/URLUtil
instanceKlass sun/instrument/TransformerManager$TransformerInfo
instanceKlass sun/instrument/TransformerManager
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryImpl
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/Readable
instanceKlass sun/nio/cs/GBK$EncodeHolder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/instrument/InstrumentationImpl
instanceKlass java/lang/instrument/Instrumentation
instanceKlass java/lang/invoke/StringConcatFactory$3
instanceKlass java/lang/invoke/StringConcatFactory$2
instanceKlass java/lang/invoke/StringConcatFactory$1
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass jdk/internal/module/ModuleBootstrap$SafeModuleFinder
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/lang/ModuleLayer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/module/ModuleFinder$1
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/lang/module/Resolver
instanceKlass java/lang/module/Configuration
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass java/util/function/BinaryOperator
instanceKlass java/util/function/BiFunction
instanceKlass java/util/function/BiConsumer
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass sun/invoke/util/Wrapper$1
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/util/function/Function
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InnerClassLambdaMetafactory$1
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/util/function/Predicate
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$2
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/Void
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterator
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass jdk/internal/module/DefaultRoots
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass jdk/internal/util/Preconditions
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass java/util/HexFormat
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/lang/Enum
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$all
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass java/util/Collections
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/io/Writer
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/lang/Integer$IntegerCache
instanceKlass java/lang/CharacterData
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/lang/StringCoding
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/DoubleByte
instanceKlass sun/nio/cs/GBK$DecodeHolder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass sun/nio/cs/DelegatableDecoder
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/util/Arrays
instanceKlass java/lang/ThreadLocal
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass jdk/internal/misc/VM
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass java/lang/ref/ReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/Runtime
instanceKlass java/util/HashMap$Node
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/Map$Entry
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/Math
instanceKlass jdk/internal/reflect/Reflection
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/StringLatin1
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/invoke/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 92 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 3 8 1 100 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciMethod java/lang/Object equals (Ljava/lang/Object;)Z 1496 0 7041 0 96
ciMethod java/lang/Object hashCode ()I 512 0 256 0 -1
ciInstanceKlass java/lang/Class 1 1 1611 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 7 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 1 100 1 10 12 1 1 9 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 7 1 10 10 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 12 1 1 100 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 100 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 7 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 7 1 10 12 1 1 7 11 7 1 9 12 1 1 9 12 1 100 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 100 1 8 1 10 7 1 4 10 10 12 11 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 7 1 11 12 1 10 7 12 1 1 1 10 12 1 7 1 11 12 1 10 100 12 1 1 1 10 12 1 10 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 100 1 100 1 100 1 100 1 100 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 1 1 1 1 1 1 1 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/Type 1 1 17 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciMethod java/lang/Class isArray ()Z 512 0 256 0 -1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 487 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 1 100 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/sun/tools/javac/tree/JCTree$JCMemberReference$OverloadKind
instanceKlass com/sun/tools/javac/tree/JCTree$JCMemberReference$ReferenceKind
instanceKlass com/sun/source/tree/LambdaExpressionTree$BodyKind
instanceKlass lombok/core/AST$Kind
instanceKlass com/sun/tools/javac/util/Log$PrefixKind
instanceKlass javax/lang/model/element/ModuleElement$DirectiveKind
instanceKlass lombok/core/configuration/CapitalizationStrategy
instanceKlass lombok/core/configuration/NullCheckExceptionType
instanceKlass lombok/core/configuration/CallSuperType
instanceKlass lombok/core/configuration/FlagUsageType
instanceKlass com/sun/tools/javac/code/TypeAnnotations$AnnotationType
instanceKlass com/sun/source/tree/Tree$Kind
instanceKlass javax/lang/model/element/ElementKind
instanceKlass com/sun/tools/javac/comp/Resolve$InterfaceLookupPhase
instanceKlass com/sun/tools/javac/code/TypeAnnotationPosition$TypePathEntryKind
instanceKlass com/sun/tools/javac/code/Types$ProjectionKind
instanceKlass com/sun/tools/javac/code/TargetType
instanceKlass com/sun/tools/javac/code/Attribute$RetentionPolicy
instanceKlass javax/lang/model/element/NestingKind
instanceKlass com/sun/tools/javac/comp/DeferredAttr$AttributionMode
instanceKlass com/sun/tools/javac/code/Scope$LookupKind
instanceKlass java/nio/file/FileVisitResult
instanceKlass java/nio/file/FileTreeWalker$EventType
instanceKlass java/time/temporal/ChronoField
instanceKlass java/nio/file/attribute/PosixFilePermission
instanceKlass com/sun/tools/javac/code/Directive$OpensFlag
instanceKlass java/util/stream/MatchOps$MatchKind
instanceKlass com/sun/tools/javac/code/Directive$ExportsFlag
instanceKlass com/sun/tools/javac/parser/JavacParser$EnumeratorEstimate
instanceKlass com/sun/source/tree/MemberReferenceTree$ReferenceMode
instanceKlass com/sun/source/tree/CaseTree$CaseKind
instanceKlass com/sun/tools/javac/parser/JavacParser$PatternResult
instanceKlass com/sun/tools/javac/code/BoundKind
instanceKlass com/sun/tools/javac/parser/UnicodeReader$UnicodeEscapeResult
instanceKlass com/sun/tools/javac/tree/JCTree$JCLambda$ParameterKind
instanceKlass com/sun/tools/javac/tree/JCTree$JCPolyExpression$PolyKind
instanceKlass com/sun/tools/javac/parser/JavacParser$ParensResult
instanceKlass com/sun/tools/javac/parser/Tokens$Comment$CommentStyle
instanceKlass javax/lang/model/type/TypeKind
instanceKlass com/sun/tools/javac/util/RichDiagnosticFormatter$RichConfiguration$RichFormatterFeature
instanceKlass com/sun/tools/javac/util/RichDiagnosticFormatter$WhereClauseKind
instanceKlass com/sun/tools/javac/comp/CompileStates$CompileState
instanceKlass com/sun/tools/javac/main/JavaCompiler$ImplicitSourcePolicy
instanceKlass com/sun/tools/javac/parser/Tokens$Token$Tag
instanceKlass com/sun/tools/javac/parser/Tokens$TokenKind
instanceKlass com/sun/tools/javac/jvm/ClassFile$Version
instanceKlass com/sun/tools/javac/comp/Attr$CheckMode
instanceKlass com/sun/tools/javac/comp/Analyzer$AnalyzerMode
instanceKlass com/sun/tools/javac/jvm/Code$StackMapFormat
instanceKlass com/sun/tools/javac/comp/Operators$OperatorType
instanceKlass com/sun/tools/javac/tree/JCTree$Tag
instanceKlass com/sun/tools/javac/jvm/Profile
instanceKlass com/sun/tools/javac/comp/Resolve$VerboseResolutionMode
instanceKlass com/sun/tools/javac/comp/DeferredAttr$AttrMode
instanceKlass com/sun/tools/javac/main/Option$PkgInfo
instanceKlass com/sun/tools/javac/util/Dependencies$CompletionCause
instanceKlass com/sun/tools/javac/comp/Resolve$ReferenceLookupResult$StaticKind
instanceKlass com/sun/tools/javac/comp/Resolve$MethodResolutionPhase
instanceKlass com/sun/tools/javac/code/Source$Feature$DiagKind
instanceKlass com/sun/tools/javac/util/JCDiagnostic$DiagnosticType
instanceKlass com/sun/tools/javac/code/Source$Feature
instanceKlass com/sun/tools/javac/code/Symbol$ModuleResolutionFlags
instanceKlass com/sun/tools/javac/code/Symbol$ModuleFlags
instanceKlass com/sun/tools/javac/code/Directive$RequiresFlag
instanceKlass com/sun/tools/javac/code/Kinds$KindName
instanceKlass com/sun/tools/javac/code/Kinds$Kind$Category
instanceKlass com/sun/tools/javac/code/Kinds$Kind
instanceKlass com/sun/tools/javac/code/TypeMetadata$Entry$Kind
instanceKlass com/sun/tools/javac/code/TypeTag
instanceKlass com/sun/tools/javac/jvm/ClassReader$AttributeKind
instanceKlass com/sun/tools/javac/main/JavaCompiler$CompilePolicy
instanceKlass com/sun/tools/javac/main/Main$Result
instanceKlass java/nio/file/AccessMode
instanceKlass com/sun/tools/javac/main/Arguments$ErrorMode
instanceKlass com/sun/tools/javac/jvm/Target
instanceKlass com/sun/tools/javac/util/BasicDiagnosticFormatter$BasicConfiguration$SourcePosition
instanceKlass com/sun/tools/javac/util/BasicDiagnosticFormatter$BasicConfiguration$BasicFormatKind
instanceKlass com/sun/tools/javac/api/DiagnosticFormatter$Configuration$MultilineLimit
instanceKlass com/sun/tools/javac/api/DiagnosticFormatter$Configuration$DiagnosticPart
instanceKlass com/sun/tools/javac/util/JCDiagnostic$DiagnosticFlag
instanceKlass com/sun/tools/javac/util/Log$WriterKind
instanceKlass javax/tools/StandardLocation
instanceKlass java/nio/file/FileVisitOption
instanceKlass javax/tools/JavaFileObject$Kind
instanceKlass com/sun/tools/javac/code/Source
instanceKlass com/sun/tools/javac/code/Lint$LintCategory
instanceKlass com/sun/tools/javac/main/Option$ChoiceKind
instanceKlass com/sun/tools/javac/main/Option$ArgKind
instanceKlass com/sun/tools/javac/main/Option$OptionGroup
instanceKlass com/sun/tools/javac/main/Option$OptionKind
instanceKlass com/sun/tools/javac/main/Option
instanceKlass javax/tools/Diagnostic$Kind
instanceKlass org/apache/maven/shared/utils/io/ScanConductor$ScanAction
instanceKlass org/codehaus/plexus/compiler/CompilerConfiguration$CompilerReuseStrategy
instanceKlass javax/lang/model/SourceVersion
instanceKlass org/codehaus/plexus/compiler/CompilerMessage$Kind
instanceKlass javax/annotation/meta/When
instanceKlass org/eclipse/sisu/space/GlobberStrategy
instanceKlass org/apache/maven/plugin/MojoExecution$Source
instanceKlass org/eclipse/aether/RepositoryEvent$EventType
instanceKlass org/apache/maven/project/ProjectBuildingRequest$RepositoryMerging
instanceKlass org/fusesource/jansi/Ansi$Attribute
instanceKlass org/fusesource/jansi/Ansi$Color
instanceKlass org/apache/maven/shared/utils/logging/Style
instanceKlass org/eclipse/sisu/inject/QualifyingStrategy
instanceKlass com/google/inject/internal/InjectorImpl$JitLimitation
instanceKlass org/eclipse/sisu/bean/DeclaredMembers$View
instanceKlass com/google/inject/internal/Initializer$InjectableReferenceState
instanceKlass org/apache/maven/settings/building/SettingsProblem$Severity
instanceKlass org/eclipse/aether/metadata/Metadata$Nature
instanceKlass org/apache/maven/model/building/ModelProblem$Version
instanceKlass org/apache/maven/building/Problem$Severity
instanceKlass org/apache/maven/classrealm/ClassRealmRequest$RealmType
instanceKlass org/apache/maven/model/building/ModelProblem$Severity
instanceKlass org/apache/maven/artifact/ArtifactScopeEnum
instanceKlass org/apache/maven/execution/ExecutionEvent$Type
instanceKlass com/google/inject/spi/InjectionPoint$Position
instanceKlass java/time/temporal/ChronoUnit
instanceKlass java/lang/annotation/ElementType
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass com/google/inject/Key$NullAnnotationStrategy
instanceKlass com/google/inject/internal/InternalFlags$IncludeStackTraceOption
instanceKlass com/google/inject/internal/InternalFlags$CustomClassLoadingOption
instanceKlass com/google/inject/internal/InternalFlags$NullableProvidesOption
instanceKlass com/google/common/cache/LocalCache$EntryFactory
instanceKlass com/google/common/cache/CacheBuilder$NullListener
instanceKlass com/google/common/cache/CacheBuilder$OneWeigher
instanceKlass com/google/common/cache/LocalCache$Strength
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass com/google/inject/Stage
instanceKlass org/eclipse/sisu/space/BeanScanning
instanceKlass java/math/RoundingMode
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass java/util/Locale$Category
instanceKlass org/slf4j/impl/OutputChoice$OutputChoiceType
instanceKlass org/fusesource/jansi/AnsiConsole$JansiOutputType
instanceKlass java/lang/constant/DirectMethodHandleDesc$Kind
instanceKlass java/lang/invoke/VarHandle$VarHandleDesc$Kind
instanceKlass java/security/DrbgParameters$Capability
instanceKlass sun/security/util/KnownOIDs
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/net/Proxy$Type
instanceKlass java/net/StandardProtocolFamily
instanceKlass java/util/concurrent/TimeUnit
instanceKlass java/nio/file/LinkOption
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/stream/StreamShape
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/module/ModuleDescriptor$Modifier
ciInstanceKlass java/lang/Enum 1 1 188 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 100 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 1 1 100 1 100 1 1
ciInstanceKlass java/lang/System 1 1 803 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass lombok/javac/apt/LombokProcessor$1
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass com/google/inject/internal/BytecodeGen$BridgeClassLoader
instanceKlass org/eclipse/sisu/space/CloningClassSpace$CloningClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1098 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 10 12 1 1 10 12 1 1 100 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 100 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 100 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 1 1 18 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/loader/Loader
instanceKlass java/net/URLClassLoader
instanceKlass jdk/internal/loader/BuiltinClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 100 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 7 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/security/AccessController 1 1 295 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 100 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor18
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor17
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor16
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor15
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor14
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor13
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor12
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor11
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor10
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor9
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor8
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor7
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor6
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor5
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor4
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor3
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor2
instanceKlass jdk/internal/reflect/BootstrapConstructorAccessorImpl
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor1
instanceKlass jdk/internal/reflect/DelegatingConstructorAccessorImpl
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor17
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor16
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor15
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor14
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor13
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor12
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor11
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor10
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor9
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor8
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor7
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor6
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor5
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor4
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor3
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor2
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor1
instanceKlass jdk/internal/reflect/DelegatingMethodAccessorImpl
instanceKlass jdk/internal/reflect/NativeMethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 25 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1
ciInstanceKlass java/lang/Module 1 1 959 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 7 1 11 12 1 1 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 4 7 1 11 12 1 7 1 7 1 10 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 100 1 10 10 12 1 1 11 100 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 100 1 8 1 100 1 10 100 1 100 1 3 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 100 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion$ListItem
instanceKlass org/eclipse/sisu/bean/BeanScheduler$Pending
ciInstanceKlass java/util/ArrayList 1 1 492 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 10 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 100 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 100 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 100 12 1 1 1 10 12 3 10 100 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 100 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 100 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 100 1 100 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 16
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/lang/String 1 1 1396 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 3 10 7 12 1 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 100 1 10 12 1 1 10 12 1 1 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 11 10 12 1 10 12 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 100 1 100 1 8 1 10 10 10 12 1 8 1 10 12 1 3 3 7 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 7 1 100 1 10 12 1 7 1 10 10 7 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 8 1 8 1 10 8 1 8 1 8 1 8 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 100 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/security/ProtectionDomain 1 1 324 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 1 10 100 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 100 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 395 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 100 1 10 12 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 10 12 10 12 1 1 11 100 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 11 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 409 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/Reflection 1 1 376 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 11 7 12 1 1 100 1 8 1 10 7 1 10 12 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 7 1 7 1 10 12 1 8 1 11 12 1 1 10 100 12 1 1 1 100 1 11 100 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 11 12 1 1 9 12 1 1 7 1 8 1 8 1 11 12 1 7 1 7 1 7 1 7 1 8 1 11 12 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
staticfield jdk/internal/reflect/Reflection ALL_MEMBERS Ljava/util/Set; java/util/ImmutableCollections$Set12
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1285 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 100 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 100 1 9 7 1 9 100 1 9 9 100 1 9 100 1 9 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
ciInstanceKlass java/lang/reflect/ReflectAccess 1 1 179 10 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1
ciInstanceKlass java/util/Map 1 1 259 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 100 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 293 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 100 10 12 1 1 10 7 12 1 1 1 10 100 12 1 9 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 100 1 10 10 12 1 10 12 1 10 12 1 7 10 12 1 9 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 1 100 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 1 8 1 10 8 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 651 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 3 10 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 7 1 9 7 12 1 1 1 10 12 1 10 12 1 1 7 1 10 10 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 100 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 8 1 10 100 1 11 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 100 1 10 11 100 12 1 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 512 100 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 100 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 100 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 10 12 4 10 12 1 8 1 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/nio/zipfs/ZipFileSystem$EntryInputStream
instanceKlass sun/nio/ch/ChannelInputStream
instanceKlass sun/nio/ch/NioSocketImpl$1
instanceKlass java/net/Socket$SocketInputStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 184 100 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 3 100 1 8 1 10 10 7 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 96 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/sun/tools/javac/file/BaseFileManager$1
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass org/apache/maven/shared/utils/logging/MessageUtils$1
instanceKlass com/intellij/rt/execution/application/AppMainV2$1
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
ciInstanceKlass java/lang/Thread 1 1 610 9 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 1 3 8 1 100 1 5 0 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 100 1 8 1 10 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 100 1 10 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 100 1 11 7 12 1 1 9 100 12 1 1 1 10 12 1 10 12 1 10 12 9 12 1 1 10 9 12 1 10 12 1 100 1 10 10 12 1 1 9 12 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 10 12 1 10 12 8 1 10 12 1 8 1 10 8 1 8 1 10 100 12 1 1 10 100 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 1 9 12 1 10 12 1 1 100 1 10 12 11 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 11 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 9 12 1 10 12 1 1 11 100 12 1 1 1 10 100 12 1 1 1 11 12 1 10 12 1 7 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciMethod java/lang/System identityHashCode (Ljava/lang/Object;)I 512 0 256 0 -1
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 512 0 256 0 -1
ciInstanceKlass java/lang/StringLatin1 1 1 380 7 1 10 100 12 1 1 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 8 1 10 12 100 1 10 10 10 7 12 1 1 1 8 1 8 1 8 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 395 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 100 1 5 0 5 0 100 1 3 5 0 3 5 0 10 12 1 10 12 1 8 1 10 12 1 8 1 9 12 1 1 9 12 1 10 12 1 1 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 1 1 10 12 10 12 1 4 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 1 9 12 1 1 10 12 1 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass java/util/Arrays 1 1 988 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 100 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 100 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 10 12 1 10 12 1 10 12 10 12 1 11 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 7 1 10 12 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 10 12 1 100 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 3 10 100 1 10 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 12 1 8 1 10 11 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 18 12 1 1 11 12 1 1 11 100 12 1 1 1 18 12 1 11 100 12 1 1 1 18 12 1 11 100 12 1 1 1 18 12 1 100 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 15 10 12 15 10 12 15 10 12 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 100 1 100 1 1
staticfield java/util/Arrays $assertionsDisabled Z 1
ciMethod java/lang/StringLatin1 equals ([B[B)Z 554 536 6707 0 -1
instanceKlass lombok/javac/handlers/HandleDelegate$DelegateRecursion
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataDeploymentException
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataInstallationException
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 393 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 100 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 7 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass lombok/javac/JavacResolution$TypeNotConvertibleException
instanceKlass lombok/javac/handlers/HandleDelegate$CantMakeDelegates
instanceKlass com/sun/tools/javac/parser/ReferenceParser$ParseException
instanceKlass com/sun/tools/javac/jvm/JNIWriter$TypeSignature$SignatureException
instanceKlass com/sun/tools/javac/jvm/ModuleNameReader$BadClassFile
instanceKlass sun/nio/fs/WindowsException
instanceKlass jdk/javadoc/internal/doclint/DocLint$BadArgs
instanceKlass com/sun/tools/javac/main/Option$InvalidValueException
instanceKlass org/codehaus/plexus/util/cli/CommandLineException
instanceKlass org/codehaus/plexus/compiler/util/scan/InclusionScanException
instanceKlass org/codehaus/plexus/compiler/CompilerException
instanceKlass org/codehaus/plexus/compiler/manager/NoSuchCompilerException
instanceKlass org/codehaus/plexus/interpolation/InterpolationException
instanceKlass org/sonatype/plexus/components/cipher/PlexusCipherException
instanceKlass org/sonatype/plexus/components/sec/dispatcher/SecDispatcherException
instanceKlass org/apache/maven/shared/filtering/MavenFilteringException
instanceKlass org/apache/maven/artifact/DependencyResolutionRequiredException
instanceKlass org/codehaus/plexus/util/introspection/MethodMap$AmbiguousException
instanceKlass java/net/URISyntaxException
instanceKlass javax/xml/parsers/ParserConfigurationException
instanceKlass org/xml/sax/SAXException
instanceKlass org/codehaus/plexus/interpolation/reflection/MethodMap$AmbiguousException
instanceKlass org/apache/maven/model/resolution/UnresolvableModelException
instanceKlass org/apache/maven/model/resolution/InvalidRepositoryException
instanceKlass org/apache/maven/toolchain/building/ToolchainsBuildingException
instanceKlass org/apache/maven/execution/MavenExecutionRequestPopulationException
instanceKlass org/apache/maven/plugin/version/PluginVersionNotFoundException
instanceKlass org/apache/maven/plugin/InvalidPluginException
instanceKlass org/apache/maven/BuildFailureException
instanceKlass org/codehaus/plexus/util/dag/CycleDetectedException
instanceKlass org/apache/maven/project/DuplicateProjectException
instanceKlass org/apache/maven/repository/metadata/MetadataResolutionException
instanceKlass org/apache/maven/configuration/BeanConfigurationException
instanceKlass org/apache/maven/model/building/ModelBuildingException
instanceKlass java/security/GeneralSecurityException
instanceKlass org/apache/maven/settings/building/SettingsBuildingException
instanceKlass org/apache/maven/lifecycle/internal/builder/BuilderNotFoundException
instanceKlass org/apache/maven/lifecycle/NoGoalSpecifiedException
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/ConflictResolverNotFoundException
instanceKlass org/apache/maven/project/DependencyResolutionException
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataReadException
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataStoreException
instanceKlass org/sonatype/plexus/components/sec/dispatcher/SecDispatcherException
instanceKlass org/apache/maven/project/ProjectBuildingException
instanceKlass org/apache/maven/artifact/versioning/InvalidVersionSpecificationException
instanceKlass org/apache/maven/repository/ArtifactDoesNotExistException
instanceKlass org/apache/maven/repository/ArtifactTransferFailedException
instanceKlass org/apache/maven/toolchain/MisconfiguredToolchainException
instanceKlass org/apache/maven/artifact/installer/ArtifactInstallationException
instanceKlass org/eclipse/aether/RepositoryException
instanceKlass org/apache/maven/repository/legacy/metadata/ArtifactMetadataRetrievalException
instanceKlass org/apache/maven/artifact/InvalidRepositoryException
instanceKlass org/apache/maven/lifecycle/MissingProjectException
instanceKlass org/apache/maven/lifecycle/LifecycleExecutionException
instanceKlass org/apache/maven/lifecycle/LifecycleNotFoundException
instanceKlass org/apache/maven/lifecycle/LifecyclePhaseNotFoundException
instanceKlass org/apache/maven/plugin/prefix/NoPluginFoundForPrefixException
instanceKlass org/apache/maven/repository/metadata/MetadataGraphTransformationException
instanceKlass org/apache/maven/repository/metadata/GraphConflictResolutionException
instanceKlass org/apache/maven/wagon/providers/http/httpclient/HttpException
instanceKlass org/apache/maven/wagon/WagonException
instanceKlass org/apache/maven/MavenExecutionException
instanceKlass org/codehaus/plexus/component/configurator/expression/ExpressionEvaluationException
instanceKlass org/apache/maven/plugin/MojoNotFoundException
instanceKlass org/codehaus/plexus/component/composition/CycleDetectedInComponentGraphException
instanceKlass org/apache/maven/plugin/PluginConfigurationException
instanceKlass org/codehaus/plexus/component/configurator/ComponentConfigurationException
instanceKlass org/codehaus/plexus/configuration/PlexusConfigurationException
instanceKlass org/codehaus/plexus/component/repository/exception/ComponentLifecycleException
instanceKlass org/apache/maven/plugin/InvalidPluginDescriptorException
instanceKlass org/apache/maven/plugin/PluginDescriptorParsingException
instanceKlass org/apache/maven/plugin/PluginResolutionException
instanceKlass org/apache/maven/plugin/version/PluginVersionResolutionException
instanceKlass org/apache/maven/plugin/PluginManagerException
instanceKlass org/apache/maven/artifact/deployer/ArtifactDeploymentException
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataResolutionException
instanceKlass org/apache/maven/artifact/resolver/AbstractArtifactResolutionException
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/InitializationException
instanceKlass org/codehaus/plexus/interpolation/InterpolationException
instanceKlass org/apache/maven/project/interpolation/ModelInterpolationException
instanceKlass org/sonatype/plexus/components/cipher/PlexusCipherException
instanceKlass org/apache/maven/plugin/AbstractMojoExecutionException
instanceKlass java/util/concurrent/TimeoutException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/lang/InterruptedException
instanceKlass com/google/inject/internal/ErrorsException
instanceKlass com/google/inject/internal/InternalProvisionException
instanceKlass org/codehaus/plexus/context/ContextException
instanceKlass java/text/ParseException
instanceKlass org/codehaus/plexus/PlexusContainerException
instanceKlass org/codehaus/plexus/component/repository/exception/ComponentLookupException
instanceKlass org/codehaus/plexus/util/xml/pull/XmlPullParserException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass org/apache/commons/cli/ParseException
instanceKlass org/apache/maven/cli/MavenCli$ExitException
instanceKlass org/codehaus/plexus/classworlds/launcher/ConfigurationException
instanceKlass org/codehaus/plexus/classworlds/ClassWorldException
instanceKlass java/security/PrivilegedActionException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/Throwable getMessage ()Ljava/lang/String; 8 0 4 0 0
instanceKlass lombok/core/AnnotationValues$AnnotationValueDecodeFail
instanceKlass java/lang/LayerInstantiationException
instanceKlass javax/lang/model/UnknownEntityException
instanceKlass java/time/DateTimeException
instanceKlass com/sun/tools/javac/jvm/Gen$CodeSizeOverflow
instanceKlass com/sun/tools/javac/code/Types$SignatureGenerator$InvalidSignatureException
instanceKlass com/sun/tools/javac/comp/Infer$GraphStrategy$NodeNotFoundException
instanceKlass com/sun/tools/javac/code/Types$AdaptFailure
instanceKlass com/sun/tools/javac/code/Types$FunctionDescriptorLookupError
instanceKlass com/sun/tools/javac/comp/Attr$BreakAttr
instanceKlass com/sun/tools/javac/comp/Resolve$InapplicableMethodException
instanceKlass com/sun/tools/javac/jvm/ClassWriter$StringOverflow
instanceKlass com/sun/tools/javac/jvm/ClassWriter$PoolOverflow
instanceKlass com/sun/tools/javac/code/Symbol$CompletionFailure
instanceKlass java/nio/file/FileSystemAlreadyExistsException
instanceKlass java/util/MissingResourceException
instanceKlass java/nio/file/FileSystemNotFoundException
instanceKlass java/io/UncheckedIOException
instanceKlass java/nio/file/ProviderNotFoundException
instanceKlass com/sun/tools/javac/util/ClientCodeException
instanceKlass com/sun/tools/javac/util/PropagatedException
instanceKlass org/apache/maven/project/DuplicateArtifactAttachmentException
instanceKlass java/util/ConcurrentModificationException
instanceKlass com/google/inject/OutOfScopeException
instanceKlass org/apache/maven/artifact/InvalidArtifactRTException
instanceKlass java/lang/annotation/IncompleteAnnotationException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass com/google/common/util/concurrent/UncheckedExecutionException
instanceKlass com/google/common/cache/CacheLoader$InvalidCacheLoadException
instanceKlass java/util/NoSuchElementException
instanceKlass com/google/inject/CreationException
instanceKlass com/google/inject/ConfigurationException
instanceKlass com/google/inject/ProvisionException
instanceKlass java/lang/TypeNotPresentException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/sun/tools/javac/tree/Pretty$UncheckedIOException
instanceKlass com/sun/source/util/TreePath$1Result
instanceKlass com/sun/tools/javac/file/PathFileObject$CannotCreateUriError
instanceKlass com/sun/tools/javac/tree/TreeInfo$1Result
instanceKlass com/sun/tools/javac/processing/ServiceProxy$ServiceConfigurationError
instanceKlass com/sun/tools/javac/util/Abort
instanceKlass com/sun/tools/javac/processing/AnnotationProcessingError
instanceKlass com/sun/tools/javac/util/FatalError
instanceKlass java/util/ServiceConfigurationError
instanceKlass com/google/common/util/concurrent/ExecutionError
instanceKlass java/lang/AssertionError
instanceKlass org/apache/maven/BuildAbort
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
instanceKlass java/lang/ThreadDeath
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadDeath 0 0 21 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collections 1 1 851 10 7 12 1 1 1 11 7 12 1 1 1 7 1 11 12 1 1 7 1 10 12 1 1 10 12 1 11 12 1 1 7 1 11 12 1 1 11 12 1 1 10 12 1 11 100 12 1 1 11 12 1 1 11 12 1 10 12 1 10 12 1 10 12 11 100 12 1 1 1 10 12 1 1 11 12 1 11 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 100 1 8 1 10 12 1 11 7 12 1 1 1 11 100 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 10 12 1 7 1 10 12 1 100 1 10 12 1 7 1 7 1 10 12 10 7 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 10 12 1 7 1 10 10 12 1 100 1 10 100 1 10 7 1 10 100 1 10 10 12 1 10 7 1 10 100 1 10 100 1 10 100 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 9 7 12 1 1 1 9 100 12 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 7 1 10 100 1 10 7 1 10 7 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 100 1 10 12 1 9 100 12 1 1 1 9 100 12 1 1 1 100 1 9 12 1 1 10 12 7 1 10 7 1 10 11 7 12 1 1 11 12 1 10 12 1 100 1 11 11 12 1 11 7 1 10 100 1 10 100 12 1 1 1 100 1 10 12 1 7 1 10 7 1 10 7 1 10 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/Collections EMPTY_SET Ljava/util/Set; java/util/Collections$EmptySet
staticfield java/util/Collections EMPTY_LIST Ljava/util/List; java/util/Collections$EmptyList
staticfield java/util/Collections EMPTY_MAP Ljava/util/Map; java/util/Collections$EmptyMap
ciInstanceKlass java/lang/StackTraceElement 1 1 224 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 8 1 10 12 1 1 10 12 1 7 1 10 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 12 1 1 100 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass com/sun/tools/javac/util/List
instanceKlass java/util/TreeMap$Values
instanceKlass org/eclipse/sisu/inject/MildElements
instanceKlass org/eclipse/sisu/inject/MildValues$1
instanceKlass com/google/common/collect/Maps$Values
instanceKlass com/google/common/collect/AbstractMultimap$Values
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection
instanceKlass com/google/common/collect/ImmutableCollection
instanceKlass java/util/IdentityHashMap$Values
instanceKlass java/util/AbstractQueue
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 7 12 1 1 1 11 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 151 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 132 10 100 12 1 1 1 9 100 12 1 1 1 9 100 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/Character 1 1 576 7 1 100 1 100 1 9 12 1 1 8 1 9 12 1 1 100 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/Float 1 1 223 7 1 100 1 10 7 12 1 1 1 10 100 12 1 1 1 4 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 4 4 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/math/BigDecimal
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Double 1 1 285 7 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 100 12 1 1 1 10 100 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 6 0 6 0 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 215 7 1 100 1 10 100 12 1 1 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 7 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 224 7 1 100 1 100 1 10 100 12 1 1 1 10 12 1 1 7 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer 1 1 445 7 1 100 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 7 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 3 3 3 3 3 3 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
staticfield java/lang/Integer sizeTable [I 10
ciInstanceKlass java/lang/Long 1 1 506 7 1 100 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 7 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 195 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 100 1 10 12 1 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass java/util/ResourceBundle$BundleReference
instanceKlass org/eclipse/sisu/inject/MildElements$Soft
instanceKlass com/google/common/cache/LocalCache$SoftValueReference
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass org/eclipse/sisu/inject/MildKeys$Soft
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass java/util/ResourceBundle$KeyElementReference
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass org/eclipse/sisu/inject/MildElements$Weak
instanceKlass com/google/common/cache/LocalCache$WeakEntry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass com/google/common/cache/LocalCache$WeakValueReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass org/eclipse/sisu/inject/MildKeys$Weak
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 47 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 152 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 12 1 100 1 11 100 12 1 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 398 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 7 12 1 1 1 11 12 1 100 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 7 1 10 10 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 11 100 1 100 1 8 1 10 10 12 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 9 12 1 100 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 548 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 8 1 10 12 1 100 1 8 1 10 12 1 8 1 11 100 12 1 1 1 100 1 10 12 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 10 12 1 100 1 8 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 15 10 100 12 1 1 1 16 15 16 1 16 1 15 10 12 16 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 429 10 7 12 1 1 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 100 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 446 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 100 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 437 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 100 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1
ciMethod java/lang/reflect/Field getName ()Ljava/lang/String; 292 0 146 0 -1
ciMethod java/lang/reflect/Field get (Ljava/lang/Object;)Ljava/lang/Object; 516 0 19144 0 -1
ciMethod java/lang/reflect/Field getModifiers ()I 424 0 212 0 -1
ciMethod java/lang/reflect/Field getDeclaringClass ()Ljava/lang/Class; 256 0 128 0 0
ciMethod java/lang/reflect/Field checkAccess (Ljava/lang/Class;Ljava/lang/Object;)V 0 0 1 0 -1
ciMethod java/lang/reflect/Field getGenericType ()Ljava/lang/reflect/Type; 512 0 778 0 -1
ciMethod java/lang/reflect/Field getType ()Ljava/lang/Class; 256 0 128 0 -1
ciMethod java/lang/reflect/Field getFieldAccessor (Ljava/lang/Object;)Ljdk/internal/reflect/FieldAccessor; 520 0 6978 0 1120
ciMethod java/lang/reflect/Field getFieldAccessor (Z)Ljdk/internal/reflect/FieldAccessor; 516 0 353 0 0
ciMethod java/lang/reflect/Field acquireFieldAccessor (Z)Ljdk/internal/reflect/FieldAccessor; 296 0 353 0 0
ciMethod java/lang/reflect/Field setFieldAccessor (Ljdk/internal/reflect/FieldAccessor;Z)V 568 0 706 0 0
ciInstanceKlass java/lang/reflect/Parameter 0 0 226 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 470 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 100 1 10 10 100 12 1 1 1 10 10 12 1 10 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 547 7 1 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 100 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 10 12 1 10 8 1 8 1 8 1 10 10 100 1 10 12 1 100 1 10 100 1 10 100 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 15 10 12 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 1 10 100 1 10 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 100 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 100 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 100 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 373 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 10 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/net/URL 1 1 743 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 100 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 10 12 1 100 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 10 9 12 1 1 10 7 12 1 1 8 1 10 12 1 1 100 1 10 10 100 12 1 1 1 8 9 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 100 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/util/jar/Manifest 1 1 336 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 100 12 1 1 1 8 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 10 12 1 11 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/apache/maven/project/DefaultProjectBuilder$1
instanceKlass java/util/Collections$SingletonMap
instanceKlass org/eclipse/sisu/wire/EntryMapAdapter
instanceKlass com/google/common/collect/Maps$ViewCachingAbstractMap
instanceKlass org/eclipse/sisu/wire/MergedProperties
instanceKlass com/google/common/cache/LocalCache
instanceKlass com/google/common/collect/CompactHashMap
instanceKlass java/util/TreeMap
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 192 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 100 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 100 1 100 1 11 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 100 12 1 1 1 100 1 11 7 12 1 1 1 10 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/ImmutableCollections$List12 1 1 125 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/List 1 1 217 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 7 1 100 1 10 12 1 1 100 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
instanceKlass org/eclipse/aether/util/graph/visitor/Stack
instanceKlass org/apache/maven/model/merge/ModelMerger$MergingList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$SingletonList
instanceKlass com/google/common/collect/Lists$Partition
instanceKlass com/google/common/collect/Lists$TransformingRandomAccessList
instanceKlass java/util/AbstractSequentialList
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/Vector
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 1 11 7 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 100 1 10 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 100 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/invoke/NativeEntryPoint 0 0 92 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 11 100 12 1 1 11 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 302 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 10 12 10 12 1 1 100 1 100 1 100 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 771 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 7 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 100 1 100 1 100 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 16 15 10 12 16 1 1 1 1 100 1 1 100 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass jdk/net/UnixDomainPrincipal
ciInstanceKlass java/lang/Record 0 0 22 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 7 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/InternalError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 1 1 26 10 7 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/WeakPairMap$Pair$Lookup 1 1 62 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 11 7 12 1 1 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 224 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 100 1 10 10 100 12 1 1 1 10 11 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/util/ArraysSupport 1 1 271 7 1 100 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 9 12 1 10 12 9 12 1 10 12 1 1 10 12 9 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 1 10 100 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 100 12 1 1 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 3 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 10 12 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/util/ArraysSupport U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/util/ArraysSupport BIG_ENDIAN Z 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BOOLEAN_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BYTE_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_CHAR_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_SHORT_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_INT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_LONG_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_FLOAT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_DOUBLE_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_BYTE_BIT_SIZE I 3
ciMethod jdk/internal/util/ArraysSupport newLength (III)I 512 0 1860 0 0
ciMethod jdk/internal/util/ArraysSupport hugeLength (II)I 0 0 1 0 -1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 644 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 100 12 1 1 1 9 12 1 1 100 1 10 9 100 12 1 1 1 9 100 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 11 12 1 10 100 1 11 12 1 100 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 100 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 1 1 126 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl GENERATED_OFFSET J 16
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 940 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 100 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 8 1 9 12 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1052 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 100 1 10 9 12 1 10 12 1 1 9 12 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 7 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 1 8 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 10 12 1 1 8 1 8 1 100 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 9 100 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 8 1 100 1 8 1 100 1 8 1 100 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 100 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 684 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 100 12 1 1 1 8 1 10 100 12 1 1 1 100 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 100 1 100 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeQualifiedStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod jdk/internal/reflect/FieldAccessor get (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 59 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeQualifiedFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeIntegerFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeBooleanFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeObjectFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 1 1 254 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 8 1 8 1 8 1 10 12 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/UnsafeFieldAccessorImpl unsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciMethod jdk/internal/reflect/UnsafeFieldAccessorImpl throwSetIllegalArgumentException (Ljava/lang/Object;)V 0 0 1 0 -1
ciMethod jdk/internal/reflect/UnsafeFieldAccessorImpl ensureObj (Ljava/lang/Object;)V 522 0 6315 0 0
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$Array
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 390 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 100 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 10 7 12 1 1 1 9 12 1 1 8 10 12 1 1 7 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/VarHandle AIOOBE_SUPPLIER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 757 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 10 12 1 100 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 100 1 8 9 100 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 8 1 8 1 100 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 3 100 1 10 12 1 10 7 12 1 1 1 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 100 1 10 12 1 10 100 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 100 1 100 1 100 1 10 100 1 10 100 1 10 100 12 1 1 1 9 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackWalker 0 0 235 9 100 12 1 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 10 10 100 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 306 100 1 100 1 3 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 9 100 12 1 1 1 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 100 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 7 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 100 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 100 1 10 18 12 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Modifier 1 1 133 100 1 10 100 12 1 1 1 100 1 10 100 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 3 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/reflect/Modifier isStatic (I)Z 516 0 16734 0 -1
ciMethod java/lang/reflect/Modifier isFinal (I)Z 512 0 2090 0 -1
ciInstanceKlass jdk/internal/reflect/ReflectionFactory 1 1 581 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 11 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 10 12 1 10 12 1 10 10 12 1 1 7 1 10 12 1 7 1 10 12 1 10 12 1 1 10 7 1 10 10 12 1 100 1 10 12 1 8 1 7 1 10 12 1 1 7 1 10 12 1 10 10 10 10 12 1 1 7 1 10 7 1 10 12 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 100 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 10 10 8 1 100 1 10 12 1 1 8 1 8 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 1 100 1 8 1 8 1 10 12 1 10 12 1 1 100 1 10 12 1 100 1 8 1 100 1 9 8 1 9 12 1 1 9 12 1 10 7 12 1 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 100 1 100 1 8 1 10 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield jdk/internal/reflect/ReflectionFactory soleInstance Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
staticfield jdk/internal/reflect/ReflectionFactory $assertionsDisabled Z 1
ciMethod jdk/internal/reflect/ReflectionFactory newFieldAccessor (Ljava/lang/reflect/Field;Z)Ljdk/internal/reflect/FieldAccessor; 516 0 355 0 0
ciMethod jdk/internal/reflect/ReflectionFactory checkInitted ()V 512 0 861 0 -1
ciInstanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry 1 1 57 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciInstanceKlass java/util/IdentityHashMap 1 1 397 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 3 3 10 7 12 1 1 7 1 9 12 1 1 11 100 12 1 1 1 6 0 10 12 10 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 3 3 100 1 8 1 10 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 11 12 1 1 10 12 1 10 12 1 9 12 1 10 7 100 1 100 1 10 12 1 9 12 1 7 1 10 12 1 9 12 1 1 7 1 10 7 1 10 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 100 1 8 1 10 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 10 10 100 12 1 1 11 100 12 1 1 100 1 10 11 100 12 1 1 10 100 1 100 1 1 1 3 1 3 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1
staticfield java/util/IdentityHashMap NULL_KEY Ljava/lang/Object; java/lang/Object
ciMethod java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 512 634 68314 0 1696
ciMethod java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 524 0 206635 0 160
ciMethod java/util/IdentityHashMap resize (I)Z 10 1240 496 0 0
ciMethod java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 828 0 152217 0 96
ciMethod java/util/IdentityHashMap nextKeyIndex (II)I 554 0 84090 0 96
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 100 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 11 12 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
ciMethod java/util/concurrent/ConcurrentMap get (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/concurrent/ConcurrentMap putIfAbsent (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
instanceKlass java/util/concurrent/ConcurrentHashMap$ForwardingNode
instanceKlass java/util/concurrent/ConcurrentHashMap$ReservationNode
ciInstanceKlass java/util/concurrent/ConcurrentHashMap$Node 1 1 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 100 1 11 12 1 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap$ForwardingNode 1 1 71 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap$ReservationNode 1 1 34 100 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/concurrent/ConcurrentHashMap$Node find (ILjava/lang/Object;)Ljava/util/concurrent/ConcurrentHashMap$Node; 0 0 1 0 -1
ciMethod java/util/concurrent/ConcurrentHashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;)V 524 0 10421 0 -1
ciInstanceKlass jdk/internal/access/JavaLangReflectAccess 1 0 59 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1
ciMethod jdk/internal/access/JavaLangReflectAccess getRoot (Ljava/lang/reflect/AccessibleObject;)Ljava/lang/reflect/AccessibleObject; 0 0 1 0 -1
ciMethod jdk/internal/access/JavaLangReflectAccess isTrustedFinalField (Ljava/lang/reflect/Field;)Z 0 0 1 0 -1
ciMethod java/lang/OutOfMemoryError <init> (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod java/util/AbstractList <init> ()V 512 0 201032 0 0
ciMethod java/util/List toArray ([Ljava/lang/Object;)[Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/List iterator ()Ljava/util/Iterator; 0 0 1 0 -1
ciMethod java/util/List addAll (Ljava/util/Collection;)Z 0 0 1 0 -1
ciMethod java/util/Collection iterator ()Ljava/util/Iterator; 0 0 1 0 -1
ciMethod java/util/Collection toArray ()[Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Collection add (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod java/util/AbstractCollection <init> ()V 626 0 437502 0 0
ciMethod java/util/Collections emptyList ()Ljava/util/List; 564 0 39277 0 -1
ciMethod java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 10 0 6600 0 0
ciMethod java/util/Arrays copyOf ([Ljava/lang/Object;ILjava/lang/Class;)[Ljava/lang/Object; 100 0 6431 0 -1
ciMethod java/lang/Math max (II)I 736 0 151031 0 -1
ciMethod java/util/Map put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod jdk/internal/misc/Unsafe getReference (Ljava/lang/Object;J)Ljava/lang/Object; 512 0 256 0 -1
ciMethod jdk/internal/misc/Unsafe getReferenceAcquire (Ljava/lang/Object;J)Ljava/lang/Object; 554 0 5617 0 -1
ciMethod jdk/internal/reflect/Reflection getCallerClass ()Ljava/lang/Class; 512 0 256 0 -1
ciMethod java/lang/StringBuilder <init> (Ljava/lang/String;)V 4 0 4476 0 -1
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 130 0 130173 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 200 0 369647 0 -1
ciMethod java/lang/StringBuilder append (I)Ljava/lang/StringBuilder; 38 0 4736 0 -1
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 870 0 15078 0 416
ciMethod java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 102 0 20484 0 -1
ciMethod java/util/concurrent/ConcurrentHashMap putIfAbsent (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 526 0 14823 0 96
ciMethod java/util/concurrent/ConcurrentHashMap get (Ljava/lang/Object;)Ljava/lang/Object; 534 2 6437 0 1184
ciMethod java/util/concurrent/ConcurrentHashMap spread (I)I 572 0 19057 0 0
ciMethod java/util/concurrent/ConcurrentHashMap tabAt ([Ljava/util/concurrent/ConcurrentHashMap$Node;I)Ljava/util/concurrent/ConcurrentHashMap$Node; 512 0 5617 0 96
ciMethod java/util/concurrent/ConcurrentHashMap putVal (Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; 524 16 5659 0 -1
ciMethod java/util/concurrent/ConcurrentHashMap initTable ()[Ljava/util/concurrent/ConcurrentHashMap$Node; 14 0 2621 0 -1
ciMethod java/util/concurrent/ConcurrentHashMap casTabAt ([Ljava/util/concurrent/ConcurrentHashMap$Node;ILjava/util/concurrent/ConcurrentHashMap$Node;Ljava/util/concurrent/ConcurrentHashMap$Node;)Z 500 0 1287 0 -1
ciMethod java/util/concurrent/ConcurrentHashMap helpTransfer ([Ljava/util/concurrent/ConcurrentHashMap$Node;Ljava/util/concurrent/ConcurrentHashMap$Node;)[Ljava/util/concurrent/ConcurrentHashMap$Node; 0 0 1 0 -1
ciMethod java/util/concurrent/ConcurrentHashMap treeifyBin ([Ljava/util/concurrent/ConcurrentHashMap$Node;I)V 0 0 1 0 -1
ciMethod java/util/concurrent/ConcurrentHashMap addCount (JI)V 512 0 6401 0 -1
ciMethod java/util/ArrayList addAll (Ljava/util/Collection;)Z 4 0 6156 0 2176
ciMethod java/util/ArrayList toArray ([Ljava/lang/Object;)[Ljava/lang/Object; 54 0 5407 0 1568
ciMethod java/util/ArrayList add (Ljava/lang/Object;)Z 446 0 69779 0 480
ciMethod java/util/ArrayList <init> ()V 498 0 164358 0 256
ciMethod java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 446 0 69779 0 0
ciMethod java/util/ArrayList grow (I)[Ljava/lang/Object; 80 0 5197 0 1536
ciMethod java/util/ArrayList grow ()[Ljava/lang/Object; 80 0 12721 0 0
ciMethod java/lang/Enum ordinal ()I 356 0 178 0 0
ciMethod java/util/Iterator hasNext ()Z 0 0 1 0 -1
ciMethod java/util/Iterator next ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/Class getDeclaredFields ()[Ljava/lang/reflect/Field; 512 0 1636 0 -1
ciMethod java/lang/Class initClassName ()Ljava/lang/String; 516 0 258 0 -1
ciMethod java/lang/Class getComponentType ()Ljava/lang/Class; 112 0 12043 0 -1
ciMethod java/lang/Class cast (Ljava/lang/Object;)Ljava/lang/Object; 512 0 43845 0 -1
ciMethod java/lang/Class getSuperclass ()Ljava/lang/Class; 512 0 256 0 -1
ciMethod java/lang/Class isInstance (Ljava/lang/Object;)Z 512 0 256 0 -1
ciMethod java/lang/Class isAssignableFrom (Ljava/lang/Class;)Z 512 0 256 0 -1
ciMethod java/lang/Class getName ()Ljava/lang/String; 512 0 37137 0 96
ciMethod java/lang/Object getClass ()Ljava/lang/Class; 512 0 256 0 -1
ciMethod java/lang/Object <init> ()V 2032 0 2220400 0 128
instanceKlass java/nio/file/ClosedDirectoryStreamException
instanceKlass java/nio/file/ClosedFileSystemException
instanceKlass java/nio/channels/OverlappingFileLockException
instanceKlass org/apache/maven/wagon/providers/http/httpclient/conn/ssl/SSLInitializationException
instanceKlass java/util/concurrent/CancellationException
ciInstanceKlass java/lang/IllegalStateException 0 0 35 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NoSuchFieldError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/IllegalAccessException 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionError 0 0 79 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 100 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/NoSuchElementException 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorFactory 1 1 205 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 7 1 100 1 10 9 7 1 100 1 10 9 7 1 100 1 10 9 7 1 100 1 10 9 7 1 100 1 10 9 7 1 100 1 10 9 7 1 100 1 10 100 1 10 100 1 10 12 1 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 7 1 10 7 1 10 100 1 10 100 1 10 100 1 10 7 1 10 100 1 10 100 1 10 100 1 10 7 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 7 1 10 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass com/google/common/collect/RegularImmutableList 1 1 68 10 9 9 10 10 7 7 10 9 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 12 12 12 7 12 7 12 1 1 12 12 1 1 1 1 1 1 1
staticfield com/google/common/collect/RegularImmutableList EMPTY Lcom/google/common/collect/ImmutableList; com/google/common/collect/RegularImmutableList
ciInstanceKlass jdk/internal/reflect/UnsafeObjectFieldAccessorImpl 1 1 177 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass com/google/inject/internal/ConstructorInjector 1 1 225 10 10 10 9 9 9 9 10 10 10 10 10 10 10 10 9 10 10 10 7 10 10 10 11 10 10 10 10 10 100 10 10 11 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 1 100 1 1 1 1 1 1 1 1 1 1 100 100 100 1 1 1 1 1 1 1 1 1 12 12 7 12 12 12 12 12 7 12 7 12 12 100 12 100 12 100 12 12 12 100 12 12 12 12 1 12 7 12 7 12 7 12 12 12 7 12 12 12 1 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass com/google/inject/internal/ConstructionContext 1 1 132 10 9 9 9 9 10 10 10 7 10 7 10 11 10 7 7 10 10 11 11 11 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 12 12 12 12 12 12 12 12 1 1 7 12 7 12 1 1 7 12 12 12 7 12 12 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass com/google/inject/internal/InternalFactoryToInitializableAdapter 1 1 98 10 9 8 10 7 9 11 7 10 10 100 10 9 10 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 12 12 7 12 1 12 12 1 12 12 1 12 12 12 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/eclipse/sisu/inject/LazyBeanEntry 1 1 160 7 1 7 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 7 1 11 7 1 12 1 1 7 1 10 12 1 9 12 9 12 9 12 10 7 1 12 1 1 11 7 1 12 1 1 9 12 10 7 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 11 7 1 12 1 1 1 1 100 1 10 1 1 1 1 1 1 1 100 1 10 100 1 12 1 1 11 12 1 1 100 1 1 1 10 100 1 12 1 1 1 10 12 1 1 1 1 1 100 1 10 10 12 10 12 1 1 10 12 1 10 12 10 12 10 12 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
instanceKlass com/sun/tools/javac/tree/JCTree$TypeBoundKind
instanceKlass com/sun/tools/javac/tree/JCTree$JCCompilationUnit
instanceKlass com/sun/tools/javac/tree/JCTree$JCMethodDecl
instanceKlass com/sun/tools/javac/tree/JCTree$JCModuleDecl
instanceKlass com/sun/tools/javac/tree/JCTree$JCPackageDecl
instanceKlass com/sun/tools/javac/tree/JCTree$JCDirective
instanceKlass com/sun/tools/javac/tree/JCTree$JCTypeParameter
instanceKlass com/sun/tools/javac/tree/JCTree$JCModifiers
instanceKlass com/sun/tools/javac/tree/JCTree$JCCatch
instanceKlass com/sun/tools/javac/tree/JCTree$JCImport
instanceKlass com/sun/tools/javac/tree/JCTree$JCStatement
instanceKlass com/sun/tools/javac/tree/JCTree$JCCaseLabel
ciInstanceKlass com/sun/tools/javac/tree/JCTree 1 1 363 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 7 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/sun/tools/javac/util/List$1
ciInstanceKlass com/sun/tools/javac/util/List 1 1 448 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 11 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 100 1 10 12 1 1 8 1 100 1 10 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 100 1 11 11 7 12 1 1 10 12 1 9 12 1 1 10 12 1 7 1 10 12 1 100 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 8 1 11 100 1 100 1 10 100 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 12 1 100 1 10 10 12 1 10 12 1 18 12 1 1 18 12 1 1 18 12 1 18 12 1 100 1 11 100 12 1 1 10 12 1 1 7 1 10 7 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 16 1 16 1 15 10 16 1 16 1 15 10 12 16 16 15 16 1 1 100 1 100 1 1
staticfield com/sun/tools/javac/util/List EMPTY_LIST Lcom/sun/tools/javac/util/List; com/sun/tools/javac/util/List$1
staticfield com/sun/tools/javac/util/List EMPTYITERATOR Ljava/util/Iterator; com/sun/tools/javac/util/List$2
ciInstanceKlass com/sun/tools/javac/util/List$1 1 1 36 10 7 12 1 1 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass com/sun/tools/javac/util/List$2 1 1 36 10 7 12 1 1 1 100 1 10 100 1 10 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/util/List$3 1 1 60 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 7 12 1 1 100 1 10 9 12 1 1 100 1 10 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1 1 1
instanceKlass com/sun/tools/javac/tree/JCTree$JCFieldAccess
instanceKlass com/sun/tools/javac/tree/JCTree$JCTypeApply
instanceKlass com/sun/tools/javac/tree/JCTree$JCArrayTypeTree
instanceKlass com/sun/tools/javac/tree/JCTree$LetExpr
instanceKlass com/sun/tools/javac/tree/JCTree$JCTypeUnion
instanceKlass com/sun/tools/javac/tree/JCTree$JCIdent
instanceKlass com/sun/tools/javac/tree/JCTree$JCPrimitiveTypeTree
instanceKlass com/sun/tools/javac/tree/JCTree$JCArrayAccess
instanceKlass com/sun/tools/javac/tree/JCTree$JCAssign
instanceKlass com/sun/tools/javac/tree/JCTree$JCParens
instanceKlass com/sun/tools/javac/tree/JCTree$JCInstanceOf
instanceKlass com/sun/tools/javac/tree/JCTree$JCTypeIntersection
instanceKlass com/sun/tools/javac/tree/JCTree$JCAnnotatedType
instanceKlass com/sun/tools/javac/tree/JCTree$JCErroneous
instanceKlass com/sun/tools/javac/tree/JCTree$JCLiteral
instanceKlass com/sun/tools/javac/tree/JCTree$JCTypeCast
instanceKlass com/sun/tools/javac/tree/JCTree$JCPolyExpression
instanceKlass com/sun/tools/javac/tree/JCTree$JCOperatorExpression
instanceKlass com/sun/tools/javac/tree/JCTree$JCNewArray
instanceKlass com/sun/tools/javac/tree/JCTree$JCWildcard
instanceKlass com/sun/tools/javac/tree/JCTree$JCAnnotation
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCExpression 1 1 47 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCAnnotation 1 1 102 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCWildcard 1 1 135 10 7 12 1 1 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 11 100 12 1 1 9 7 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1
instanceKlass com/sun/tools/javac/tree/JCTree$JCExpressionStatement
instanceKlass com/sun/tools/javac/tree/JCTree$JCSkip
instanceKlass com/sun/tools/javac/tree/JCTree$JCVariableDecl
instanceKlass com/sun/tools/javac/tree/JCTree$JCClassDecl
instanceKlass com/sun/tools/javac/tree/JCTree$JCLabeledStatement
instanceKlass com/sun/tools/javac/tree/JCTree$JCEnhancedForLoop
instanceKlass com/sun/tools/javac/tree/JCTree$JCDoWhileLoop
instanceKlass com/sun/tools/javac/tree/JCTree$JCYield
instanceKlass com/sun/tools/javac/tree/JCTree$JCBreak
instanceKlass com/sun/tools/javac/tree/JCTree$JCSynchronized
instanceKlass com/sun/tools/javac/tree/JCTree$JCCase
instanceKlass com/sun/tools/javac/tree/JCTree$JCTry
instanceKlass com/sun/tools/javac/tree/JCTree$JCIf
instanceKlass com/sun/tools/javac/tree/JCTree$JCReturn
instanceKlass com/sun/tools/javac/tree/JCTree$JCAssert
instanceKlass com/sun/tools/javac/tree/JCTree$JCThrow
instanceKlass com/sun/tools/javac/tree/JCTree$JCContinue
instanceKlass com/sun/tools/javac/tree/JCTree$JCForLoop
instanceKlass com/sun/tools/javac/tree/JCTree$JCSwitch
instanceKlass com/sun/tools/javac/tree/JCTree$JCWhileLoop
instanceKlass com/sun/tools/javac/tree/JCTree$JCBlock
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCStatement 1 1 39 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCBlock 1 1 98 10 7 12 1 1 1 100 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 100 1 5 0 11 100 12 1 1 9 7 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCIf 1 1 96 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 100 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCTry 1 1 112 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 9 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCCatch 1 1 90 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 9 100 12 1 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCModifiers 1 1 94 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 9 100 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCEnhancedForLoop 1 1 104 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 100 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCClassDecl 1 1 177 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 5 0 9 7 12 1 1 1 5 0 9 12 1 5 0 9 12 1 5 0 9 12 1 9 12 1 11 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 100 1 100 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCMethodDecl 1 1 173 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 100 1 1 100 1 100 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCVariableDecl 1 1 159 10 7 12 1 1 1 10 7 12 1 1 100 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 7 12 1 1 1 10 100 12 1 1 1 100 1 9 100 1 9 10 7 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 100 1 1
instanceKlass com/sun/tools/javac/api/JavacTrees$6
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCCompilationUnit 1 1 253 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 9 12 1 11 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 100 1 1 1 1 1 100 1 1 1 100 1 100 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1 100 1 1 100 1 1 100 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCMethodInvocation 1 1 118 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCParens 1 1 80 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCPrimitiveTypeTree 1 1 81 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCIdent 1 1 84 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCArrayTypeTree 1 1 80 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1
ciInstanceKlass com/sun/tools/javac/tree/JCTree$JCFieldAccess 1 1 93 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1
ciInstanceKlass lombok/permit/Permit 1 1 319 7 1 100 1 1 1 1 1 1 1 1 1 1 7 1 8 1 10 12 1 1 9 12 10 12 1 1 5 0 9 12 9 12 100 1 8 1 10 12 1 1 10 12 1 1 7 1 1 1 1 1 1 1 10 12 1 1 1 1 1 1 10 12 1 1 10 7 1 12 1 1 1 1 1 1 8 1 10 7 1 12 1 1 10 12 1 1 7 1 1 1 1 1 1 7 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 7 1 12 1 1 10 8 1 10 12 1 1 8 1 10 12 1 10 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 10 12 7 1 1 1 7 1 1 10 7 1 1 1 10 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 10 12 1 1 100 1 1 10 1 1 8 1 8 1 10 100 1 12 1 1 10 12 1 1 1 1 10 12 9 12 1 1 8 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 100 1 12 1 10 12 1 1 8 1 1 1 1 100 1 10 12 1 1 1 1 10 12 1 10 12 100 1 100 1 1 1 1 1 10 12 10 12 1 1 10 12 1 1 100 1 100 1 1 1 1 1 1 100 1 1 10 12 1 1 1 1 10 12 1 1 1 10 12 1 1 1 1 10 12 1 1 1 1 8 1 1 8 10 10 12 1 1 1 1 1 1
staticfield lombok/permit/Permit ACCESSIBLE_OVERRIDE_FIELD_OFFSET J 12
staticfield lombok/permit/Permit INIT_ERROR Ljava/lang/IllegalAccessException; null
staticfield lombok/permit/Permit UNSAFE Lsun/misc/Unsafe; sun/misc/Unsafe
instanceKlass lombok/javac/JavacNode
ciInstanceKlass lombok/core/LombokNode 1 1 263 7 1 7 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 9 12 9 12 10 7 1 12 1 1 10 12 1 1 9 12 10 12 1 1 11 7 1 12 1 1 9 12 9 12 10 12 1 1 11 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 8 1 8 1 10 12 1 1 8 1 10 100 1 12 1 1 100 1 100 1 1 10 12 10 7 1 12 1 1 10 12 1 1 10 12 1 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 9 12 1 10 100 1 12 1 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 12 11 12 1 1 1 1 1 1 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 9 12 1 11 100 1 12 1 1 11 12 1 1 10 12 1 1 1 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass lombok/javac/JavacNode 1 1 453 7 1 7 1 1 1 1 1 1 1 1 1 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 12 1 7 1 9 12 1 1 7 1 9 12 1 7 1 9 12 1 1 1 1 10 12 1 1 10 12 1 1 7 1 10 100 1 12 1 1 1 1 1 1 100 1 10 12 1 1 10 12 1 10 12 1 1 10 7 1 12 1 11 7 1 12 1 1 10 7 1 12 1 1 11 12 1 11 12 1 1 11 12 1 11 12 1 1 11 12 1 11 12 1 1 11 12 1 7 1 11 12 1 1 11 12 1 10 12 1 11 12 1 1 11 12 1 11 12 1 11 12 1 7 1 11 12 1 1 11 12 1 7 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 11 12 1 11 12 1 8 1 1 1 1 1 1 1 1 1 9 12 1 1 9 9 10 100 1 1 100 1 1 1 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 1 12 1 1 1 10 12 1 1 1 10 12 1 1 9 100 1 12 1 1 10 12 1 1 1 1 1 9 12 1 1 1 1 10 100 1 12 1 1 1 1 1 1 1 1 10 12 1 10 12 1 1 1 1 1 1 10 12 10 10 1 9 12 1 1 9 100 1 12 1 1 5 0 10 12 1 9 12 1 5 0 10 5 0 1 1 1 1 1 5 0 1 9 12 1 5 0 1 9 12 1 1 10 12 9 12 1 1 10 12 1 9 12 1 1 10 100 1 1 5 0 1 9 12 1 9 12 1 1 10 100 1 12 1 1 1 10 12 1 1 10 12 1 10 12 9 12 10 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1
instanceKlass lombok/javac/JavacAST
ciInstanceKlass lombok/core/AST 1 1 500 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 10 7 1 12 1 1 100 1 8 1 10 12 1 1 9 12 7 1 10 12 9 12 1 1 1 1 1 10 7 1 10 9 12 9 12 9 12 8 1 9 12 9 12 9 12 9 12 1 1 1 1 100 1 100 1 7 1 1 1 1 1 1 1 1 1 1 9 12 1 1 1 1 1 1 9 12 100 1 10 12 10 12 1 1 1 1 10 7 1 12 1 1 11 7 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 11 12 1 1 1 1 1 1 1 7 1 10 9 12 1 1 10 100 1 12 1 1 11 7 1 12 1 10 12 11 7 1 12 1 9 12 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 11 7 1 7 1 10 12 1 1 7 1 11 12 1 1 11 12 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 7 1 12 1 10 7 1 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 1 12 1 1 10 12 1 11 10 12 1 1 1 1 1 1 1 100 1 1 7 1 11 12 1 1 1 1 1 100 1 1 11 1 1 1 1 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 10 12 10 12 1 1 1 1 1 9 12 1 10 10 12 1 1 9 12 10 12 1 10 12 1 10 12 1 1 10 100 1 12 1 1 100 1 1 1 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 11 12 1 1 10 100 1 12 1 1 10 12 1 10 12 1 1 1 1 1 9 7 1 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 1 100 1 1 1 1 1 10 12 1 1 10 7 1 12 1 1 10 12 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 10 12 100 1 10 100 1 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield lombok/core/AST configTracker Llombok/core/debug/HistogramTracker; null
staticfield lombok/core/AST fieldsOfASTClasses Ljava/util/concurrent/ConcurrentMap; java/util/concurrent/ConcurrentHashMap
ciInstanceKlass lombok/javac/JavacAST 1 1 1008 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 10 7 1 12 1 1 9 12 9 12 9 12 7 1 8 1 10 7 1 12 1 1 9 12 7 1 10 12 1 9 12 1 1 1 10 12 1 1 10 7 1 12 1 7 1 10 12 1 10 12 1 1 10 12 1 9 12 10 12 1 1 10 12 1 1 9 12 9 12 10 7 1 12 1 1 9 12 7 1 10 7 1 12 1 10 12 1 9 12 10 7 1 12 1 9 12 10 7 1 12 1 9 12 9 12 10 12 1 1 1 1 1 1 1 1 10 12 1 7 1 10 12 1 1 7 1 10 12 1 1 9 12 1 1 11 7 1 12 1 10 12 1 1 8 1 10 7 1 12 1 1 10 12 1 1 100 1 1 1 1 1 1 10 12 1 1 10 7 1 12 1 1 9 12 10 7 1 12 1 8 1 10 12 1 1 9 12 10 12 1 8 1 10 7 1 12 1 9 12 8 1 8 1 10 12 1 9 12 8 1 9 12 8 1 9 12 8 1 9 12 8 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 8 10 12 1 1 10 7 1 12 1 1 9 12 8 1 9 12 8 1 100 1 10 10 12 1 1 10 10 12 1 10 12 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 1 1 1 10 12 1 1 10 7 1 12 1 1 11 7 1 12 1 11 12 1 1 1 1 100 1 1 1 10 100 1 12 1 10 12 1 10 100 1 12 1 1 1 1 1 10 100 1 12 1 1 1 10 100 1 12 1 1 1 1 1 1 1 1 1 10 12 1 1 1 10 12 1 1 1 1 1 1 1 1 10 12 1 10 7 1 12 1 7 1 10 12 1 1 7 1 10 12 1 1 7 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 1 7 1 10 9 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 10 12 1 1 1 1 1 7 1 10 12 1 9 12 1 1 9 7 1 12 1 9 9 12 1 10 12 1 1 1 1 1 1 1 9 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 1 1 8 1 10 12 1 1 7 1 9 12 10 12 1 1 7 1 9 12 11 9 12 1 7 1 9 12 1 10 7 1 12 1 1 7 1 9 12 1 7 1 9 12 1 7 1 1 1 1 1 1 1 1 7 1 8 1 9 12 1 1 1 1 8 8 1 1 1 9 12 1 1 1 1 1 10 12 11 9 12 1 9 12 1 1 10 12 1 1 9 12 1 7 1 9 12 1 10 12 9 12 1 1 1 1 9 12 1 7 1 9 12 1 1 1 1 9 9 12 1 9 12 1 9 9 12 1 1 1 1 9 12 1 1 1 10 12 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 10 12 1 1 10 12 1 1 1 11 7 1 8 10 12 1 1 11 12 1 1 100 1 1 1 1 10 12 9 12 1 9 12 10 12 1 1 10 12 1 1 11 12 1 1 10 100 1 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 1 1 1 1 1 1 100 1 1 10 12 1 11 12 1 1 1 1 1 1 1 1 10 12 1 1 11 100 1 12 1 10 12 1 1 10 12 1 1 1 1 1 10 100 1 12 1 1 10 12 1 10 12 10 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 100 1 100 1 10 12 10 12 1 10 12 1 1 1 1 1 1 1 100 1 1 11 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 10 12 1 1 11 12 1 10 12 1 11 12 1 11 12 1 1 1 1 1 1 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 100 1 10 12 1 9 12 10 12 1 1 100 1 100 9 12 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 100 1 1 100 1 1
staticfield lombok/javac/JavacAST NOT_CALCULATED_MARKER Ljava/net/URI; java/net/URI
staticfield lombok/javac/JavacAST getBodyMethods Ljava/util/concurrent/ConcurrentMap; java/util/concurrent/ConcurrentHashMap
ciInstanceKlass lombok/core/LombokImmutableList 1 1 177 7 1 7 1 100 1 1 1 1 1 1 1 1 1 1 10 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 7 1 12 1 1 1 1 1 1 1 1 1 11 7 1 12 1 1 1 1 1 1 1 100 1 10 12 11 12 1 1 11 100 1 12 1 1 11 100 1 12 1 1 11 12 1 1 10 12 1 1 1 1 1 1 1 1 1 10 9 12 1 1 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 1 1 10 12 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 10 12 1 1 1 10 100 1 12 1 10 12 1 1 1 10 12 1 1 1 1 1 1 1
staticfield lombok/core/LombokImmutableList EMPTY Llombok/core/LombokImmutableList; lombok/core/LombokImmutableList
ciInstanceKlass lombok/core/AST$Kind 1 1 82 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 10 12 1 1 9 12 8 9 12 8 9 12 8 9 12 8 9 12 8 9 12 8 9 12 8 9 12 8 9 12 8 9 12 9 12 1 1 10 1 1 1 10 7 1 12 1 1 1 1 10 12 1 1 1 1 1 100 1 1
staticfield lombok/core/AST$Kind COMPILATION_UNIT Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind TYPE Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind FIELD Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind INITIALIZER Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind METHOD Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind ANNOTATION Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind ARGUMENT Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind LOCAL Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind STATEMENT Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind TYPE_USE Llombok/core/AST$Kind; lombok/core/AST$Kind
staticfield lombok/core/AST$Kind ENUM$VALUES [Llombok/core/AST$Kind; 10 [Llombok/core/AST$Kind;
ciInstanceKlass lombok/core/LombokImmutableList$1 1 1 56 7 1 7 1 100 1 1 1 1 1 1 1 1 9 12 10 12 1 9 12 1 1 1 1 1 1 10 7 1 12 1 1 1 1 1 1 100 1 10 1 100 1 8 1 10 12 1 1 1 1 1 12 1 1 1
ciInstanceKlass lombok/core/AST$FieldAccess 1 1 29 7 1 7 1 1 1 1 1 1 1 1 10 12 1 9 12 9 12 1 1 1 1 1 1 1 100 1 1
ciMethodData java/lang/Object <init> ()V 2 2220400 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String equals (Ljava/lang/Object;)Z 2 15078 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x20007 0x362b 0x20 0x308 0x80104 0x0 0x0 0x1f6e4c580c0 0x361b 0x0 0x0 0xb0007 0x10 0xe0 0x361b 0xf0004 0x0 0x0 0x1f6e4c580c0 0x361b 0x0 0x0 0x160007 0x0 0x40 0x361b 0x8000000600210007 0x2c 0x68 0x35f0 0x2c0002 0x35f0 0x2f0007 0x3217 0x38 0x3d9 0x330003 0x3d9 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/lang/String 18 java/lang/String methods 0
ciMethodData java/util/AbstractCollection <init> ()V 2 437502 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x6abc5 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/ConcurrentHashMap tabAt ([Ljava/util/concurrent/ConcurrentHashMap$Node;I)Ljava/util/concurrent/ConcurrentHashMap$Node; 2 5617 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0xf000b 0x14f1 0x0 0x0 0x0 0x0 0x0 0x2 0x1 0x1f6e854c9c0 0x120104 0x0 0x0 0x1f6e854c910 0x953 0x1f6e7030e40 0x250 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 3 9 [Ljava/util/concurrent/ConcurrentHashMap$Node; 13 java/util/concurrent/ConcurrentHashMap$Node 15 java/util/concurrent/ConcurrentHashMap$ForwardingNode methods 0
ciMethodData java/util/concurrent/ConcurrentHashMap spread (I)I 2 19057 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/ConcurrentHashMap putVal (Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; 2 5659 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 250 0x10007 0x0 0x40 0x1515 0x50007 0x1515 0x30 0x0 0xc0002 0x0 0x110005 0x869 0x0 0x1f6e98e51e0 0xdd 0x1f6e4c580c0 0xbcf 0x140002 0x1515 0x240007 0x51 0x40 0x1515 0x2d0007 0x1515 0x70 0x0 0x310005 0x51 0x0 0x0 0x0 0x0 0x0 0x360003 0x51 0x5f8 0x450002 0x1515 0x4b0007 0x81b 0x78 0xcfa 0x5b0002 0xcfa 0x5e0002 0xcfa 0x610007 0x0 0x590 0xcfa 0x640003 0xcfa 0x588 0x700007 0x81b 0x70 0x0 0x780005 0x0 0x0 0x0 0x0 0x0 0x0 0x7d0003 0x0 0x500 0x810007 0x1a1 0xf8 0x67a 0x880007 0x58c 0xd8 0xee 0x940007 0x5f 0x98 0x8f 0x990007 0x0 0x98 0x8f 0x9f0005 0x0 0x0 0x1f6e4c580c0 0x8f 0x0 0x0 0xa20007 0x0 0x40 0x8f 0xad0007 0x0 0x20 0xee 0xc00002 0x72d 0xc50007 0x0 0x330 0x72d 0xca0007 0x0 0x188 0x72d 0xdb0007 0x904 0xf0 0x67 0xe70007 0x40 0x98 0x27 0xec0007 0x0 0xb0 0x27 0xf20005 0x0 0x0 0x1f6e4c580c0 0x27 0x0 0x0 0xf50007 0x0 0x58 0x27 0x1000007 0x66 0x98 0x1 0x1090003 0x1 0x78 0x1180007 0x23e 0x48 0x6c6 0x1250002 0x6c6 0x12b0003 0x6c6 0x30 0x1310003 0x23e 0xfffffffffffffec8 0x1340003 0x72d 0x1a0 0x1390004 0x0 0x0 0x0 0x0 0x0 0x0 0x13c0007 0x0 0xe8 0x0 0x1440004 0x0 0x0 0x0 0x0 0x0 0x0 0x14b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1510007 0x0 0x40 0x0 0x15c0007 0x0 0x20 0x0 0x1650003 0x0 0x80 0x16a0004 0x0 0x0 0x0 0x0 0x0 0x0 0x16d0007 0x0 0x30 0x0 0x1760002 0x0 0x17d0003 0x72d 0x18 0x18a0007 0x0 0x98 0x72d 0x1910007 0x72d 0x58 0x0 0x1990005 0x0 0x0 0x0 0x0 0x0 0x0 0x19e0007 0x6c6 0x38 0x67 0x1a40003 0x51 0xfffffffffffff990 0x1ab0005 0x13c0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 4 13 java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry 15 java/lang/String 87 java/lang/String 124 java/lang/String methods 0
ciMethodData java/util/concurrent/ConcurrentHashMap get (Ljava/lang/Object;)Ljava/lang/Object; 2 6437 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 101 0x10005 0x1603 0x0 0x1f6e702d4d0 0x3 0x1f6e4c5a4d0 0x214 0x40002 0x181a 0xf0007 0x7c 0x290 0x179e 0x170007 0x0 0x270 0x179e 0x220002 0x179e 0x270007 0x909 0x240 0xe95 0x330007 0x6e4 0xb8 0x7b1 0x3e0007 0x398 0x98 0x419 0x430007 0x0 0x108 0x419 0x490005 0x26 0x0 0x1f6e4c5a4d0 0x143 0x1f6e4c580c0 0x2b0 0x4c0007 0x0 0xb0 0x419 0x560007 0x6e4 0x90 0x0 0x5d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x630007 0x0 0x38 0x0 0x6b0003 0x0 0x18 0x760007 0x4bd 0xd8 0x3bd 0x7f0007 0x196 0xffffffffffffffe0 0x227 0x8a0007 0x11c 0x98 0x10b 0x8f0007 0x0 0xffffffffffffffa0 0x10b 0x950005 0x3 0x0 0x1f6e4c5a4d0 0x13 0x1f6e4c580c0 0xf5 0x980007 0x0 0xffffffffffffff48 0x10b 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 6 3 java/lang/WeakPairMap$Pair$Lookup 5 java/lang/invoke/MethodType 38 java/lang/invoke/MethodType 40 java/lang/String 83 java/lang/invoke/MethodType 85 java/lang/String methods 0
ciMethodData java/util/concurrent/ConcurrentHashMap putIfAbsent (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 14823 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x40005 0x38e0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 2 6600 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x30005 0x19c3 0x0 0x0 0x0 0x0 0x0 0x60002 0x19c3 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/Object equals (Ljava/lang/Object;)Z 2 7041 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20007 0x1426 0x38 0x46f 0x60003 0x46f 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/util/ArraysSupport newLength (III)I 2 1860 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x30002 0x644 0xa0007 0x0 0x40 0x644 0x100007 0x0 0x20 0x644 0x170002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/Class getName ()Ljava/lang/String; 2 38023 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x60007 0xea9 0x38 0x84de 0xa0003 0x84de 0x50 0xe0005 0xea9 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethodData java/util/ArrayList add (Ljava/lang/Object;)Z 2 69779 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x140005 0x10fb4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 2 69779 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x30007 0xde68 0x58 0x314c 0x70005 0x314c 0x0 0x0 0x0 0x0 0x0 0xe0104 0x0 0x0 0x1f6e4c58150 0xed8 0x1f6e4c5bda0 0x6c2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0xc 0x0 0xffffffffffffffff 0x0 oops 2 14 java/lang/Class 16 java/lang/Integer methods 0
ciMethodData java/util/ArrayList grow ()[Ljava/lang/Object; 2 12721 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x70005 0x3189 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 0 methods 0
ciMethodData java/util/ArrayList grow (I)[Ljava/lang/Object; 2 5197 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x70007 0x27b 0x40 0x11aa 0x110007 0x119b 0x40 0xf 0x1b0002 0x28a 0x250002 0x28a 0x310002 0x119b 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/util/AbstractList <init> ()V 2 201032 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x31048 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData java/util/ArrayList <init> ()V 2 164358 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x2810d 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList addAll (Ljava/util/Collection;)Z 2 6156 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 34 0x10005 0x169b 0x0 0x1f6e6173e70 0x1 0x1f6e6173f20 0x16e 0x150007 0x13da 0x20 0x430 0x2b0007 0x100 0x58 0x12da 0x330005 0x12da 0x0 0x0 0x0 0x0 0x0 0x3f0002 0x13da 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0xffffffffffffffff oops 2 3 java/util/ImmutableCollections$List12 5 com/google/common/collect/RegularImmutableList methods 0
ciMethodData java/util/ArrayList toArray ([Ljava/lang/Object;)[Ljava/lang/Object; 2 5407 orig 80 2 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x60007 0xefa 0x68 0x60a 0x120005 0x60a 0x0 0x0 0x0 0x0 0x0 0x8000000400150002 0x60b 0x8000000600240002 0xefb 0x2d0007 0xefb 0x58 0x0 0x360004 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 0 methods 0
ciMethod jdk/internal/reflect/UnsafeFieldAccessorFactory newFieldAccessor (Ljava/lang/reflect/Field;Z)Ljdk/internal/reflect/FieldAccessor; 518 0 355 0 -1
ciMethodData java/lang/Class cast (Ljava/lang/Object;)Ljava/lang/Object; 2 56324 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 34 0x10007 0x369d 0xc0 0xa467 0x60005 0xa467 0x0 0x0 0x0 0x0 0x0 0x90007 0xa467 0x68 0x0 0x120005 0x0 0x0 0x0 0x0 0x0 0x0 0x150002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData java/util/IdentityHashMap nextKeyIndex (II)I 2 84319 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x40007 0x279 0x38 0x145d1 0xa0003 0x145d1 0x18 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 2 206863 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x10002 0x32709 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 2 152217 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10007 0x250fb 0x38 0x0 0x70003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod jdk/internal/reflect/UnsafeObjectFieldAccessorImpl get (Ljava/lang/Object;)Ljava/lang/Object; 636 0 5907 0 352
ciMethodData java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 68314 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0x10002 0x109da 0x130002 0x109e1 0x200007 0x10709 0xa0 0x4c52 0x260007 0x497a 0x58 0x2d8 0x390004 0x0 0x0 0x1f6e4c5b990 0x2d8 0x0 0x0 0x410002 0x497a 0x460003 0x497a 0xffffffffffffff78 0x5a0007 0x10702 0x90 0x7 0x600005 0x7 0x0 0x0 0x0 0x0 0x0 0x630007 0x0 0x38 0x7 0x660003 0x7 0xfffffffffffffed8 0x780004 0x0 0x0 0x1f6e98bf6f0 0x80 0x1f6e98bf7a0 0x86 0x800104 0x0 0x0 0x1f6e98bf850 0x106 0x1f6e4c5b990 0x842c 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0x0 oops 5 15 java/lang/Boolean 45 com/google/inject/internal/InternalFactoryToInitializableAdapter 47 com/google/inject/internal/ConstructorInjector 52 com/google/inject/internal/ConstructionContext 54 java/lang/Boolean methods 0
ciMethodData java/util/IdentityHashMap resize (I)Z 2 138740 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 74 0x110007 0x279 0x50 0x0 0x1a0007 0x0 0x30 0x0 0x230002 0x0 0x2c0007 0x279 0x20 0x0 0x3e0007 0x279 0x190 0x1cf30 0x490007 0x9b8e 0x158 0x133a2 0x580104 0x0 0x0 0x0 0x0 0x0 0x0 0x5f0104 0x0 0x0 0x0 0x0 0x0 0x0 0x630002 0x133a2 0x6d0007 0x133a2 0x48 0x4c53 0x730002 0x4c53 0x780003 0x4c53 0xffffffffffffffd0 0x810004 0x0 0x0 0x1f6e98bf6f0 0xa0 0x1f6e98bf7a0 0x9e 0x8a0004 0x0 0x0 0x1f6e98bf850 0x13e 0x1f6e98e58c0 0xa 0x8e0003 0x1cf30 0xfffffffffffffe88 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 4 50 com/google/inject/internal/InternalFactoryToInitializableAdapter 52 com/google/inject/internal/ConstructorInjector 57 com/google/inject/internal/ConstructionContext 59 org/eclipse/sisu/inject/LazyBeanEntry methods 0
ciMethodData java/lang/reflect/Field setFieldAccessor (Ljdk/internal/reflect/FieldAccessor;Z)V 1 706 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0x10007 0x0 0x38 0x1a6 0x90003 0x1a6 0x18 0x150007 0xd3 0x58 0xd3 0x1e0005 0xd3 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xc0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/reflect/Field getFieldAccessor (Ljava/lang/Object;)Ljdk/internal/reflect/FieldAccessor; 2 6978 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x60007 0x0 0x38 0x1a3e 0xd0003 0x1a3e 0x18 0x160007 0xcd 0x38 0x1971 0x1a0003 0x1971 0x50 0x1f0005 0xcd 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData java/lang/reflect/Field acquireFieldAccessor (Z)Ljdk/internal/reflect/FieldAccessor; 1 353 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x60007 0x0 0x58 0xcd 0xe0005 0xcd 0x0 0x0 0x0 0x0 0x0 0x130007 0xcd 0x70 0x0 0x170007 0x0 0x38 0x0 0x1f0003 0x0 0xa0 0x270003 0x0 0x88 0x2f0005 0x0 0x0 0x1f6e9341f00 0xcd 0x0 0x0 0x360005 0xcd 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 28 jdk/internal/reflect/ReflectionFactory methods 0
ciMethodData jdk/internal/reflect/UnsafeFieldAccessorImpl ensureObj (Ljava/lang/Object;)V 2 6315 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x40005 0x17a6 0x0 0x0 0x0 0x0 0x0 0x80005 0x17a6 0x0 0x0 0x0 0x0 0x0 0xb0005 0x17a6 0x0 0x0 0x0 0x0 0x0 0xe0007 0x17a6 0x58 0x0 0x130005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/reflect/Field getFieldAccessor (Z)Ljdk/internal/reflect/FieldAccessor; 1 353 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x10007 0x0 0x38 0x5f 0x80003 0x5f 0x18 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/reflect/ReflectionFactory newFieldAccessor (Ljava/lang/reflect/Field;Z)Ljdk/internal/reflect/FieldAccessor; 1 361 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 86 0x2 0x67 0x80005 0x0 0x0 0x1f6e98dfee0 0x67 0x0 0x0 0xd0004 0x0 0x0 0x1f6e4c59810 0x2 0x0 0x0 0x120007 0x0 0xd0 0x67 0x160005 0x67 0x0 0x0 0x0 0x0 0x0 0x1a0005 0x67 0x0 0x0 0x0 0x0 0x0 0x1d0007 0x67 0x40 0x0 0x210007 0x0 0x20 0x0 0x270005 0x67 0x0 0x0 0x0 0x0 0x0 0x2a0002 0x67 0x310007 0x42 0xb0 0x25 0x350007 0x0 0x78 0x25 0x3d0005 0x0 0x0 0x1f6e98dfee0 0x25 0x0 0x0 0x420007 0x2 0x38 0x23 0x460003 0x23 0x18 0x4f0002 0x67 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0xffffffffffffffff 0x0 oops 3 5 java/lang/reflect/ReflectAccess 12 java/lang/reflect/Field 62 java/lang/reflect/ReflectAccess methods 0
ciMethod com/sun/tools/javac/util/List emptyIterator ()Ljava/util/Iterator; 190 0 59017 0 0
ciMethod com/sun/tools/javac/util/List iterator ()Ljava/util/Iterator; 546 0 98000 0 192
ciMethod com/sun/tools/javac/util/List$2 hasNext ()Z 260 0 130 0 0
ciMethod com/sun/tools/javac/util/List$3 <init> (Lcom/sun/tools/javac/util/List;)V 358 0 39077 0 0
ciMethod com/sun/tools/javac/util/List$3 hasNext ()Z 514 0 6503 0 96
ciMethod com/sun/tools/javac/util/List$3 next ()Ljava/lang/Object; 512 0 7501 0 320
ciMethod com/sun/tools/javac/tree/JCTree getPreferredPosition ()I 0 0 1 0 -1
ciMethodData com/sun/tools/javac/util/List$3 hasNext ()Z 2 6503 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x70007 0xb16 0x38 0xd50 0xb0003 0xd50 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData com/sun/tools/javac/util/List$3 next ()Ljava/lang/Object; 2 7501 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x70007 0x1c4d 0x30 0x0 0xe0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData com/sun/tools/javac/util/List iterator ()Ljava/util/Iterator; 2 98094 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x40007 0x97f3 0x30 0xe62a 0x70002 0xe62a 0x100002 0x97f3 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData com/sun/tools/javac/util/List emptyIterator ()Ljava/util/Iterator; 2 59017 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData com/sun/tools/javac/util/List$3 <init> (Lcom/sun/tools/javac/util/List;)V 2 39077 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x60002 0x97f2 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData java/lang/reflect/Field get (Ljava/lang/Object;)Ljava/lang/Object; 2 19144 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 37 0x40007 0x49c6 0x68 0x0 0x70002 0x0 0xe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x130005 0x49c6 0x0 0x0 0x0 0x0 0x0 0x170005 0x0 0x0 0x1f6e6028370 0x49c6 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 1 23 jdk/internal/reflect/UnsafeObjectFieldAccessorImpl methods 0
ciMethodData jdk/internal/reflect/UnsafeObjectFieldAccessorImpl get (Ljava/lang/Object;)Ljava/lang/Object; 2 5907 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x20005 0x0 0x0 0x1f6e6028370 0x15d5 0x0 0x0 0xd000b 0x31 0x0 0x0 0x0 0x0 0x0 0x2 0x1 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 jdk/internal/reflect/UnsafeObjectFieldAccessorImpl methods 0
ciMethod lombok/permit/Permit setAccessible (Ljava/lang/reflect/AccessibleObject;)Ljava/lang/reflect/AccessibleObject; 336 0 123 0 -1
ciMethod lombok/permit/Permit permissiveReadField (Ljava/lang/Class;Ljava/lang/reflect/Field;Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod lombok/core/LombokNode <init> (Ljava/lang/Object;Ljava/util/List;Llombok/core/AST$Kind;)V 516 466 14423 0 3008
ciMethod lombok/core/LombokNode calculateIsStructurallySignificant (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod lombok/core/LombokNode get ()Ljava/lang/Object; 342 0 171 0 0
ciMethod lombok/javac/JavacNode <init> (Llombok/javac/JavacAST;Lcom/sun/tools/javac/tree/JCTree;Ljava/util/List;Llombok/core/AST$Kind;)V 518 0 23611 0 0
ciMethod lombok/core/AST putInMap (Llombok/core/LombokNode;)Llombok/core/LombokNode; 648 0 23528 0 0
ciMethod lombok/core/AST setAndGetAsHandled (Ljava/lang/Object;)Z 526 0 23685 0 0
ciMethod lombok/core/AST getFileName ()Ljava/lang/String; 0 0 1 0 -1
ciMethod lombok/core/AST buildTree (Ljava/lang/Object;Llombok/core/AST$Kind;)Llombok/core/LombokNode; 0 0 1 0 -1
ciMethod lombok/core/AST fieldsOf (Ljava/lang/Class;)[Llombok/core/AST$FieldAccess; 616 0 19338 0 0
ciMethod lombok/core/AST getFields (Ljava/lang/Class;Ljava/util/Collection;)V *********** 0 0
ciMethod lombok/core/AST getComponentType (Ljava/lang/reflect/Type;)Ljava/lang/Class; 32 0 15 0 -1
ciMethod lombok/core/AST shouldDrill (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)Z *********** 0 -1
ciMethod lombok/core/AST buildWithField (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;)Ljava/util/Collection; 658 0 18933 0 0
ciMethod lombok/core/AST buildWithField0 (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;Ljava/util/Collection;)V 696 0 18933 0 0
ciMethod lombok/core/AST buildWithArray (Ljava/lang/Class;Ljava/lang/Object;Ljava/util/Collection;I)V 0 0 1 0 -1
ciMethod lombok/core/AST buildWithCollection (Ljava/lang/Class;Ljava/lang/Object;Ljava/util/Collection;I)V 364 240 6608 0 0
ciMethod lombok/javac/JavacAST buildTree (Lcom/sun/tools/javac/tree/JCTree;Llombok/core/AST$Kind;)Llombok/javac/JavacNode; 770 0 15944 0 0
ciMethod lombok/javac/JavacAST buildCompilationUnit (Lcom/sun/tools/javac/tree/JCTree$JCCompilationUnit;)Llombok/javac/JavacNode; 2 28 86 0 -1
ciMethod lombok/javac/JavacAST buildType (Lcom/sun/tools/javac/tree/JCTree$JCClassDecl;)Llombok/javac/JavacNode; 2 12 92 0 0
ciMethod lombok/javac/JavacAST buildField (Lcom/sun/tools/javac/tree/JCTree$JCVariableDecl;)Llombok/javac/JavacNode; 0 0 523 0 -1
ciMethod lombok/javac/JavacAST buildLocalVar (Lcom/sun/tools/javac/tree/JCTree$JCVariableDecl;Llombok/core/AST$Kind;)Llombok/javac/JavacNode; 88 0 1544 0 0
ciMethod lombok/javac/JavacAST buildTypeUse (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 94 0 2172 0 -1
ciMethod lombok/javac/JavacAST getResourcesForTryNode (Lcom/sun/tools/javac/tree/JCTree$JCTry;)Ljava/util/List; 514 0 67 0 -1
ciMethod lombok/javac/JavacAST initJcAnnotatedType (Ljava/lang/Class;)V 0 0 1 0 -1
ciMethod lombok/javac/JavacAST getVarOrRecordPattern (Lcom/sun/tools/javac/tree/JCTree$JCEnhancedForLoop;)Lcom/sun/tools/javac/tree/JCTree; 512 0 84 0 -1
ciMethod lombok/javac/JavacAST buildTry (Lcom/sun/tools/javac/tree/JCTree$JCTry;)Llombok/javac/JavacNode; 10 22 54 0 0
ciMethod lombok/javac/JavacAST buildInitializer (Lcom/sun/tools/javac/tree/JCTree$JCBlock;)Llombok/javac/JavacNode; 0 0 6 0 -1
ciMethod lombok/javac/JavacAST buildMethod (Lcom/sun/tools/javac/tree/JCTree$JCMethodDecl;)Llombok/javac/JavacNode; 14 84 679 0 -1
ciMethod lombok/javac/JavacAST buildAnnotation (Lcom/sun/tools/javac/tree/JCTree$JCAnnotation;Z)Llombok/javac/JavacNode; 2 0 275 0 0
ciMethod lombok/javac/JavacAST buildExpression (Lcom/sun/tools/javac/tree/JCTree$JCExpression;)Llombok/javac/JavacNode; 516 0 2108 0 0
ciMethod lombok/javac/JavacAST buildStatement (Lcom/sun/tools/javac/tree/JCTree$JCStatement;)Llombok/javac/JavacNode; 512 0 2150 0 -1
ciMethod lombok/javac/JavacAST buildStatementOrExpression (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 770 0 20186 0 0
ciMethod lombok/javac/JavacAST buildLambda (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 0 0 11 0 0
ciMethod lombok/javac/JavacAST getBody (Lcom/sun/tools/javac/tree/JCTree;)Lcom/sun/tools/javac/tree/JCTree; 0 0 12 0 -1
ciMethod lombok/javac/JavacAST buildEnhancedForLoop (Lcom/sun/tools/javac/tree/JCTree$JCEnhancedForLoop;)Llombok/javac/JavacNode; 2 0 59 0 0
ciMethod lombok/javac/JavacAST drill (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 564 648 18253 0 0
ciMethod lombok/javac/JavacAST addIfNotNull (Ljava/util/Collection;Llombok/javac/JavacNode;)V 560 0 8765 0 0
ciMethod lombok/javac/JavacAST buildTree (Ljava/lang/Object;Llombok/core/AST$Kind;)Llombok/core/LombokNode; 756 0 15755 0 0
ciMethod lombok/javac/JavacAST $SWITCH_TABLE$lombok$core$AST$Kind ()[I 770 0 16228 0 0
ciMethod lombok/core/LombokImmutableList of ()Llombok/core/LombokImmutableList; 2 0 101 0 -1
ciMethod lombok/core/LombokImmutableList copyOf (Ljava/util/Collection;)Llombok/core/LombokImmutableList; 520 0 14322 0 -1
ciMethod lombok/core/LombokImmutableList iterator ()Ljava/util/Iterator; 516 0 23799 0 -1
ciMethod lombok/core/AST$Kind values ()[Llombok/core/AST$Kind; 2 0 2 0 -1
ciMethod lombok/core/AST$FieldAccess <init> (Ljava/lang/reflect/Field;I)V 114 0 52 0 -1
ciMethodData lombok/core/AST setAndGetAsHandled (Ljava/lang/Object;)Z 2 24121 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 21 0x60005 0x0 0x0 0x1f6ec391f80 0x5d2d 0x0 0x0 0xb0007 0x5d27 0x20 0x6 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 1 3 java/util/IdentityHashMap methods 0
ciMethodData lombok/core/LombokNode <init> (Ljava/lang/Object;Ljava/util/List;Llombok/core/AST$Kind;)V 2 14423 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 78 0x10002 0x3755 0x100007 0x64 0x48 0x36f1 0x140002 0x36f1 0x170003 0x36f1 0x28 0x1a0002 0x64 0x240005 0x3755 0x0 0x0 0x0 0x0 0x0 0x290003 0x3755 0xe0 0x2e0005 0x0 0x0 0x1f6e98e0a40 0x3747 0x0 0x0 0x330004 0x0 0x0 0x1f6e6025630 0x3747 0x0 0x0 0x430007 0x5d1 0x58 0x3176 0x4a0005 0x0 0x0 0x1f6e6025630 0x3176 0x0 0x0 0x520005 0x0 0x0 0x1f6e98e0a40 0x6e9c 0x0 0x0 0x570007 0x3747 0xffffffffffffff00 0x3755 0x5d0005 0x0 0x0 0x1f6e6025630 0x3755 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0xe 0x0 0xffffffffffffffff 0x0 oops 5 26 lombok/core/LombokImmutableList$1 33 lombok/javac/JavacNode 44 lombok/javac/JavacNode 51 lombok/core/LombokImmutableList$1 62 lombok/javac/JavacNode methods 0
ciMethodData lombok/javac/JavacNode <init> (Llombok/javac/JavacAST;Lcom/sun/tools/javac/tree/JCTree;Ljava/util/List;Llombok/core/AST$Kind;)V 2 26341 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x50002 0x65e2 0x0 0x0 0x0 0x0 0x9 0x5 0x1e 0x0 0x0 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData lombok/core/AST putInMap (Llombok/core/LombokNode;)Llombok/core/LombokNode; 2 26241 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 45 0x50005 0x0 0x0 0x1f6e6025630 0x653d 0x0 0x0 0x90005 0x0 0x0 0x1f6ec391f80 0x653d 0x0 0x0 0x140005 0x0 0x0 0x1f6e6025630 0x653d 0x0 0x0 0x180005 0x0 0x0 0x1f6e6025630 0x653d 0x0 0x0 0x1b0005 0x0 0x0 0x1f6ec391f80 0x653d 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 5 3 lombok/javac/JavacNode 10 java/util/IdentityHashMap 17 lombok/javac/JavacNode 24 lombok/javac/JavacNode 31 java/util/IdentityHashMap methods 0
ciMethodData lombok/javac/JavacAST drill (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 2 20835 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 129 0x40002 0x4a70 0xa0005 0x4a70 0x0 0x0 0x0 0x0 0x0 0xd0005 0x0 0x0 0x1f6e66313f0 0x4a70 0x0 0x0 0x190003 0x4a70 0x88 0x280005 0x0 0x0 0x1f6e66313f0 0x501b 0x0 0x0 0x2b0005 0x0 0x0 0x1f6e4c5b3d0 0x501b 0x0 0x0 0x380007 0x501b 0xffffffffffffff90 0x4a70 0x460002 0x4a70 0x490005 0x0 0x0 0x1f6e66313f0 0x4a70 0x0 0x0 0x4c0004 0x0 0x0 0x1f6e6025630 0x84 0x0 0x0 0x520005 0x0 0x0 0x0 0x0 0x0 0x0 0x570007 0x0 0x20 0x0 0x670005 0x0 0x0 0x0 0x0 0x0 0x0 0x6a0002 0x0 0x6d0002 0x0 0x730005 0x0 0x0 0x0 0x0 0x0 0x0 0x770005 0x0 0x0 0x0 0x0 0x0 0x0 0x7a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x800005 0x0 0x0 0x0 0x0 0x0 0x0 0x840005 0x0 0x0 0x0 0x0 0x0 0x0 0x870005 0x0 0x0 0x0 0x0 0x0 0x0 0x8a0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 5 12 lombok/javac/JavacAST 22 lombok/javac/JavacAST 29 java/util/ArrayList 42 lombok/javac/JavacAST 49 lombok/javac/JavacNode methods 0
ciMethodData lombok/core/AST buildWithField (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;)Ljava/util/Collection; 2 18933 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x40002 0x48ac 0xf0002 0x48ac 0x0 0x0 0x0 0x9 0x4 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData lombok/core/AST buildWithField0 (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;Ljava/util/Collection;)V 2 18933 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 104 0x50005 0x4899 0x0 0x0 0x0 0x0 0x0 0xc0007 0x44cc 0x20 0x3cd 0x140007 0x194a 0x138 0x2b82 0x1d0005 0x0 0x0 0x1f6e66313f0 0x2b82 0x0 0x0 0x240007 0x0 0x228 0x2b82 0x2c0005 0x2b82 0x0 0x0 0x0 0x0 0x0 0x2f0004 0x0 0x0 0x1f6e6025630 0x4d 0x0 0x0 0x320005 0x0 0x0 0x1f6e4c5b3d0 0x2b82 0x0 0x0 0x380003 0x2b82 0x160 0x3d0005 0x194a 0x0 0x0 0x0 0x0 0x0 0x400005 0x194a 0x0 0x0 0x0 0x0 0x0 0x430007 0x194a 0x48 0x0 0x500002 0x0 0x530003 0x0 0xa8 0x5a0005 0x194a 0x0 0x0 0x0 0x0 0x0 0x5d0007 0x0 0x58 0x194a 0x6a0002 0x194a 0x6d0003 0x194a 0x28 0x740002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff oops 3 18 lombok/javac/JavacAST 36 lombok/javac/JavacNode 43 java/util/ArrayList methods 0
ciMethodData lombok/core/AST fieldsOf (Ljava/lang/Class;)[Llombok/core/AST$FieldAccess; 2 19459 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0x40005 0x0 0x0 0x1f6e4c5b290 0x4acf 0x0 0x0 0x90104 0x0 0x0 0x1f6eada9820 0x4ac2 0x0 0x0 0xe0007 0xd 0x20 0x4ac2 0x170002 0xd 0x1e0002 0xd 0x2a0005 0x0 0x0 0x1f6e4c5b3d0 0xc 0x0 0x0 0x2f0004 0x0 0x0 0x1f6eada9820 0xc 0x0 0x0 0x320005 0x0 0x0 0x1f6e4c5b290 0xc 0x0 0x0 0x3c0005 0x0 0x0 0x1f6e4c5b290 0xc 0x0 0x0 0x410004 0x0 0x0 0x1f6eada9820 0xc 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 7 3 java/util/concurrent/ConcurrentHashMap 10 [Llombok/core/AST$FieldAccess; 25 java/util/ArrayList 32 [Llombok/core/AST$FieldAccess; 39 java/util/concurrent/ConcurrentHashMap 46 java/util/concurrent/ConcurrentHashMap 53 [Llombok/core/AST$FieldAccess; methods 0
ciMethodData lombok/javac/JavacAST buildStatementOrExpression (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 2 20313 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 145 0x10007 0x496d 0x20 0x465 0x70004 0xffffffffffffb693 0x0 0x1f6ec2ae9e0 0x2 0x1f6ec2aea90 0x3 0xa0007 0x496d 0x20 0x0 0x100004 0xffffffffffffb696 0x0 0x1f6ec2ae9e0 0x2 0x1f6ec2aea90 0x3 0x130007 0x496a 0x68 0x3 0x180004 0x0 0x0 0x1f6e6c10d70 0x3 0x0 0x0 0x1b0002 0x3 0x200004 0xffffffffffffb967 0x0 0x1f6ec2ae9e0 0x2 0x1f6ec2aea90 0x3 0x230007 0x4699 0x68 0x2d1 0x280004 0x0 0x0 0x1f6e6c11070 0x2d1 0x0 0x0 0x2e0002 0x2d1 0x330004 0xffffffffffffb998 0x0 0x1f6ec2ae9e0 0x2 0x1f6ec2aea90 0x3 0x360007 0x4668 0x68 0x31 0x3b0004 0x0 0x0 0x1f6e5b65f70 0x31 0x0 0x0 0x3e0002 0x31 0x430005 0x4668 0x0 0x0 0x0 0x0 0x0 0x460005 0x4668 0x0 0x0 0x0 0x0 0x0 0x4c0005 0x4668 0x0 0x0 0x0 0x0 0x0 0x4f0007 0x465d 0x30 0xb 0x540002 0xb 0x590004 0xffffffffffffb9dd 0x0 0x1f6ec2ae9e0 0x2 0x1f6ec2aea90 0x3 0x5c0007 0x4623 0x68 0x3a 0x610004 0x0 0x0 0x1f6e5b668d0 0x3a 0x0 0x0 0x640002 0x3a 0x6a0005 0x0 0x0 0x1f6e66313f0 0x4623 0x0 0x0 0x6d0007 0x4623 0x20 0x0 0x740002 0x4623 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 15 7 com/sun/tools/javac/tree/JCTree$JCIf 9 com/sun/tools/javac/tree/JCTree$JCParens 18 com/sun/tools/javac/tree/JCTree$JCIf 20 com/sun/tools/javac/tree/JCTree$JCParens 29 com/sun/tools/javac/tree/JCTree$JCClassDecl 38 com/sun/tools/javac/tree/JCTree$JCIf 40 com/sun/tools/javac/tree/JCTree$JCParens 49 com/sun/tools/javac/tree/JCTree$JCVariableDecl 58 com/sun/tools/javac/tree/JCTree$JCIf 60 com/sun/tools/javac/tree/JCTree$JCParens 69 com/sun/tools/javac/tree/JCTree$JCTry 105 com/sun/tools/javac/tree/JCTree$JCIf 107 com/sun/tools/javac/tree/JCTree$JCParens 116 com/sun/tools/javac/tree/JCTree$JCEnhancedForLoop 125 lombok/javac/JavacAST methods 0
ciMethodData lombok/javac/JavacAST buildType (Lcom/sun/tools/javac/tree/JCTree$JCClassDecl;)Llombok/javac/JavacNode; 1 2404 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 210 0x20005 0x0 0x0 0x1f6e66313f0 0xc9 0x0 0x0 0x50007 0xc9 0x20 0x0 0xe0002 0xc9 0x190005 0x0 0x0 0x1f6ec2b21a0 0x6e 0x1f6ec38ffc0 0x5b 0x1e0003 0xc9 0xa8 0x230005 0x0 0x0 0x1f6ec390070 0xb8 0x0 0x0 0x280004 0x0 0x0 0x1f6e6c11970 0xb8 0x0 0x0 0x300002 0xb8 0x330002 0xb8 0x380005 0x0 0x0 0x1f6ec390070 0x126 0x1f6ec390120 0x5b 0x3d0007 0xb8 0xffffffffffffff38 0xc9 0x440005 0x0 0x0 0x1f6ec2b21a0 0xc7 0x1f6ec38ffc0 0x2 0x490003 0xc9 0x390 0x4e0005 0x0 0x0 0x1f6ec390070 0x8a4 0x0 0x0 0x530004 0x0 0x0 0x1f6e6c11670 0x4a4 0x1f6e6c11070 0x3ef 0x580004 0xfffffffffffffc00 0x0 0x1f6e6c11670 0x4a4 0x1f6e6c11070 0x33f 0x5b0007 0x400 0x90 0x4a4 0x610004 0x0 0x0 0x1f6e6c11670 0x4a4 0x0 0x0 0x640002 0x4a4 0x670002 0x4a3 0x6a0003 0x4a3 0x258 0x6e0004 0xfffffffffffffc06 0x0 0x1f6e6c11070 0x33f 0x1f6e6c10d70 0x6 0x710007 0x3fa 0x90 0x6 0x770004 0x0 0x0 0x1f6e6c10d70 0x6 0x0 0x0 0x7a0002 0x6 0x7d0002 0x6 0x800003 0x6 0x190 0x840004 0xfffffffffffffff5 0x0 0x1f6e6c11070 0x3ef 0x1f6e6c11370 0x9 0x870007 0xb 0x90 0x3ef 0x8d0004 0x0 0x0 0x1f6e6c11070 0x3ef 0x0 0x0 0x900002 0x3ef 0x930002 0x3ef 0x960003 0x3ef 0xc8 0x9a0004 0x0 0x0 0x1f6e6c11370 0xb 0x0 0x0 0x9d0007 0x0 0x78 0xb 0xa30004 0x0 0x0 0x1f6e6c11370 0xb 0x0 0x0 0xa60002 0xb 0xa90002 0xb 0xae0005 0x0 0x0 0x1f6ec390070 0x96a 0x1f6ec390120 0x2 0xb30007 0x8a4 0xfffffffffffffc50 0xc8 0xc10002 0xc8 0xc40005 0x0 0x0 0x1f6e66313f0 0xc8 0x0 0x0 0xc70004 0x0 0x0 0x1f6e6025630 0xb1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 27 3 lombok/javac/JavacAST 16 com/sun/tools/javac/util/List 18 com/sun/tools/javac/util/List$1 26 com/sun/tools/javac/util/List$3 33 com/sun/tools/javac/tree/JCTree$JCAnnotation 44 com/sun/tools/javac/util/List$3 46 com/sun/tools/javac/util/List$2 55 com/sun/tools/javac/util/List 57 com/sun/tools/javac/util/List$1 65 com/sun/tools/javac/util/List$3 72 com/sun/tools/javac/tree/JCTree$JCMethodDecl 74 com/sun/tools/javac/tree/JCTree$JCVariableDecl 79 com/sun/tools/javac/tree/JCTree$JCMethodDecl 81 com/sun/tools/javac/tree/JCTree$JCVariableDecl 90 com/sun/tools/javac/tree/JCTree$JCMethodDecl 104 com/sun/tools/javac/tree/JCTree$JCVariableDecl 106 com/sun/tools/javac/tree/JCTree$JCClassDecl 115 com/sun/tools/javac/tree/JCTree$JCClassDecl 129 com/sun/tools/javac/tree/JCTree$JCVariableDecl 131 com/sun/tools/javac/tree/JCTree$JCBlock 140 com/sun/tools/javac/tree/JCTree$JCVariableDecl 154 com/sun/tools/javac/tree/JCTree$JCBlock 165 com/sun/tools/javac/tree/JCTree$JCBlock 176 com/sun/tools/javac/util/List$3 178 com/sun/tools/javac/util/List$2 189 lombok/javac/JavacAST 196 lombok/javac/JavacNode methods 0
ciMethodData lombok/core/AST getFields (Ljava/lang/Class;Ljava/util/Collection;)V 1 187 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 144 0x30007 0xe 0x40 0x35 0x70007 0x35 0x20 0x0 0xc0005 0x35 0x0 0x0 0x0 0x0 0x0 0x180003 0x35 0x348 0x220005 0x51 0x0 0x0 0x0 0x0 0x0 0x250002 0x51 0x280007 0x4f 0x38 0x2 0x2b0003 0x2 0x2c8 0x2f0005 0x4f 0x0 0x0 0x0 0x0 0x0 0x390005 0x4f 0x0 0x0 0x0 0x0 0x0 0x3c0007 0x4f 0x128 0x0 0x3f0003 0x0 0x50 0x470005 0x0 0x0 0x0 0x0 0x0 0x0 0x4e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x510007 0x0 0xffffffffffffff90 0x0 0x540003 0x0 0xb8 0x5c0005 0x7 0x0 0x0 0x0 0x0 0x0 0x5f0002 0x7 0x680005 0x56 0x0 0x0 0x0 0x0 0x0 0x6b0007 0x7 0xffffffffffffff80 0x4f 0x730005 0x4f 0x0 0x0 0x0 0x0 0x0 0x760002 0x4f 0x790007 0x37 0x78 0x18 0x7d0002 0x18 0x890002 0x18 0x8c0005 0x0 0x0 0x1f6e4c5b3d0 0x18 0x0 0x0 0x990007 0x51 0xfffffffffffffcd0 0x35 0x9e0005 0x35 0x0 0x0 0x0 0x0 0x0 0xa20002 0x35 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 1 116 java/util/ArrayList methods 0
ciMethodData lombok/javac/JavacAST buildLocalVar (Lcom/sun/tools/javac/tree/JCTree$JCVariableDecl;Llombok/core/AST$Kind;)Llombok/javac/JavacNode; 2 1586 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 87 0x20005 0x0 0x0 0x1f6e66313f0 0x603 0x0 0x0 0x50007 0x603 0x20 0x0 0xe0002 0x603 0x190005 0x0 0x0 0x1f6ec38ffc0 0x601 0x1f6ec2b21a0 0x2 0x1e0003 0x603 0xa8 0x230005 0x0 0x0 0x1f6ec390070 0x2 0x0 0x0 0x280004 0x0 0x0 0x1f6e6c11970 0x2 0x0 0x0 0x320002 0x2 0x350002 0x2 0x3a0005 0x0 0x0 0x1f6ec390120 0x601 0x1f6ec390070 0x4 0x3f0007 0x2 0xffffffffffffff38 0x603 0x480002 0x603 0x4b0002 0x603 0x540002 0x603 0x570002 0x603 0x630002 0x603 0x660005 0x0 0x0 0x1f6e66313f0 0x603 0x0 0x0 0x690004 0x0 0x0 0x1f6e6025630 0x134 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 9 3 lombok/javac/JavacAST 16 com/sun/tools/javac/util/List$1 18 com/sun/tools/javac/util/List 26 com/sun/tools/javac/util/List$3 33 com/sun/tools/javac/tree/JCTree$JCAnnotation 44 com/sun/tools/javac/util/List$2 46 com/sun/tools/javac/util/List$3 65 lombok/javac/JavacAST 72 lombok/javac/JavacNode methods 0
ciMethodData lombok/javac/JavacAST buildTree (Ljava/lang/Object;Llombok/core/AST$Kind;)Llombok/core/LombokNode; 2 16042 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x20004 0x0 0x0 0x1f6e6c0e320 0x880 0x1f6e6c0e3d0 0x1460 0x60005 0x0 0x0 0x1f6e66313f0 0x3d29 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff oops 3 3 com/sun/tools/javac/tree/JCTree$JCMethodInvocation 5 com/sun/tools/javac/tree/JCTree$JCIdent 10 lombok/javac/JavacAST methods 0
ciMethodData lombok/javac/JavacAST buildTree (Lcom/sun/tools/javac/tree/JCTree;Llombok/core/AST$Kind;)Llombok/javac/JavacNode; 2 16228 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 139 0x2 0x3de3 0x40005 0x3de3 0x0 0x0 0x0 0x0 0x0 0x80008 0x16 0x0 0x320 0x0 0xc0 0x0 0x108 0x0 0x150 0x0 0x198 0x0 0x1e0 0x0 0x2c8 0x0 0x228 0x0 0x270 0x3de3 0x2b8 0x0 0x310 0x420004 0x0 0x0 0x0 0x0 0x0 0x0 0x450002 0x0 0x4b0004 0x0 0x0 0x0 0x0 0x0 0x0 0x4e0002 0x0 0x540004 0x0 0x0 0x0 0x0 0x0 0x0 0x570002 0x0 0x5d0004 0x0 0x0 0x0 0x0 0x0 0x0 0x600002 0x0 0x660004 0x0 0x0 0x0 0x0 0x0 0x0 0x690002 0x0 0x6f0004 0x0 0x0 0x0 0x0 0x0 0x0 0x730002 0x0 0x790004 0x0 0x0 0x0 0x0 0x0 0x0 0x7d0002 0x0 0x830002 0x3de3 0x890004 0x0 0x0 0x0 0x0 0x0 0x0 0x8d0002 0x0 0x930002 0x0 0xa20002 0x0 0xa60005 0x0 0x0 0x0 0x0 0x0 0x0 0xa90005 0x0 0x0 0x0 0x0 0x0 0x0 0xac0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData lombok/javac/JavacAST buildTry (Lcom/sun/tools/javac/tree/JCTree$JCTry;)Llombok/javac/JavacNode; 1 138 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 150 0x20005 0x0 0x0 0x1f6e66313f0 0x77 0x0 0x0 0x50007 0x77 0x20 0x0 0xe0002 0x77 0x130002 0x77 0x160005 0x0 0x0 0x1f6ec38ffc0 0x71 0x1f6ec2b21a0 0x6 0x1d0003 0x77 0x138 0x220005 0x0 0x0 0x1f6ec390070 0xa 0x0 0x0 0x270004 0x0 0x0 0x1f6e6c11070 0xa 0x0 0x0 0x2c0004 0x0 0x0 0x1f6e6c11070 0xa 0x0 0x0 0x2f0007 0x0 0x78 0xa 0x350004 0x0 0x0 0x1f6e6c11070 0xa 0x0 0x0 0x3b0002 0xa 0x3e0002 0xa 0x430005 0x0 0x0 0x1f6ec390120 0x71 0x1f6ec390070 0x10 0x480007 0xa 0xfffffffffffffea8 0x77 0x510002 0x77 0x540002 0x77 0x5b0005 0x0 0x0 0x1f6ec38ffc0 0x5 0x1f6ec2b21a0 0x72 0x600003 0x77 0xd0 0x650005 0x0 0x0 0x1f6ec390070 0x75 0x0 0x0 0x6a0004 0x0 0x0 0x1f6e5f026f0 0x75 0x0 0x0 0x740005 0x0 0x0 0x1f6e66313f0 0x75 0x0 0x0 0x770002 0x75 0x7c0005 0x0 0x0 0x1f6ec390120 0x5 0x1f6ec390070 0xe7 0x810007 0x75 0xffffffffffffff10 0x77 0x8a0002 0x77 0x8d0002 0x77 0x9b0002 0x77 0x9e0005 0x0 0x0 0x1f6e66313f0 0x77 0x0 0x0 0xa10004 0x0 0x0 0x1f6e6025630 0x77 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 18 3 lombok/javac/JavacAST 18 com/sun/tools/javac/util/List$1 20 com/sun/tools/javac/util/List 28 com/sun/tools/javac/util/List$3 35 com/sun/tools/javac/tree/JCTree$JCVariableDecl 42 com/sun/tools/javac/tree/JCTree$JCVariableDecl 53 com/sun/tools/javac/tree/JCTree$JCVariableDecl 64 com/sun/tools/javac/util/List$2 66 com/sun/tools/javac/util/List$3 79 com/sun/tools/javac/util/List$1 81 com/sun/tools/javac/util/List 89 com/sun/tools/javac/util/List$3 96 com/sun/tools/javac/tree/JCTree$JCCatch 103 lombok/javac/JavacAST 112 com/sun/tools/javac/util/List$2 114 com/sun/tools/javac/util/List$3 129 lombok/javac/JavacAST 136 lombok/javac/JavacNode methods 0
ciMethodData lombok/javac/JavacAST buildLambda (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 1 23 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x30002 0x17 0x60002 0x17 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData lombok/core/AST buildWithCollection (Ljava/lang/Class;Ljava/lang/Object;Ljava/util/Collection;I)V 2 7569 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 131 0x30007 0x0 0x288 0x1cd9 0x70004 0x0 0x0 0x1f6ec38ffc0 0x10fb 0x1f6ec2b21a0 0xbde 0xa0005 0x0 0x0 0x1f6ec38ffc0 0x10fb 0x1f6ec2b21a0 0xbde 0x110003 0x1cd9 0x188 0x160005 0x0 0x0 0x1f6ec390070 0x125d 0x0 0x0 0x1f0007 0x125d 0x38 0x0 0x220003 0x0 0x118 0x2b0005 0x0 0x0 0x1f6e66313f0 0x125d 0x0 0x0 0x320007 0x0 0xc8 0x125c 0x390005 0x125c 0x0 0x0 0x0 0x0 0x0 0x3c0004 0x0 0x0 0x1f6e6025630 0x116 0x0 0x0 0x3f0005 0x0 0x0 0x1f6e4c5b3d0 0x125c 0x0 0x0 0x470005 0x0 0x0 0x1f6ec390120 0x10fb 0x1f6ec390070 0x1e3a 0x4c0007 0x125d 0xfffffffffffffe58 0x1cd7 0x4f0003 0x1cd7 0x140 0x530004 0x0 0x0 0x0 0x0 0x0 0x0 0x560005 0x0 0x0 0x0 0x0 0x0 0x0 0x5d0003 0x0 0x60 0x620005 0x0 0x0 0x0 0x0 0x0 0x0 0x720002 0x0 0x770005 0x0 0x0 0x0 0x0 0x0 0x0 0x7c0007 0x0 0xffffffffffffff80 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff 0x0 oops 10 7 com/sun/tools/javac/util/List$1 9 com/sun/tools/javac/util/List 14 com/sun/tools/javac/util/List$1 16 com/sun/tools/javac/util/List 24 com/sun/tools/javac/util/List$3 38 lombok/javac/JavacAST 56 lombok/javac/JavacNode 63 java/util/ArrayList 70 com/sun/tools/javac/util/List$2 72 com/sun/tools/javac/util/List$3 methods 0
ciMethodData lombok/javac/JavacAST buildEnhancedForLoop (Lcom/sun/tools/javac/tree/JCTree$JCEnhancedForLoop;)Llombok/javac/JavacNode; 1 123 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 63 0x20005 0x0 0x0 0x1f6e66313f0 0x7a 0x0 0x0 0x50007 0x7a 0x20 0x0 0xe0002 0x7a 0x150002 0x7a 0x1b0005 0x0 0x0 0x1f6e66313f0 0x7a 0x0 0x0 0x1e0002 0x7a 0x2a0005 0x0 0x0 0x1f6e66313f0 0x7a 0x0 0x0 0x2d0002 0x7a 0x360002 0x7a 0x390002 0x7a 0x470002 0x7a 0x4a0005 0x0 0x0 0x1f6e66313f0 0x7a 0x0 0x0 0x4d0004 0x0 0x0 0x1f6e6025630 0x7a 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 5 3 lombok/javac/JavacAST 18 lombok/javac/JavacAST 27 lombok/javac/JavacAST 42 lombok/javac/JavacAST 49 lombok/javac/JavacNode methods 0
ciMethodData lombok/javac/JavacAST $SWITCH_TABLE$lombok$core$AST$Kind ()[I 2 16228 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 119 0x40007 0x0 0x20 0x3de3 0x90002 0x0 0x140005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a0003 0x0 0x18 0x220005 0x0 0x0 0x0 0x0 0x0 0x0 0x280003 0x0 0x18 0x300005 0x0 0x0 0x0 0x0 0x0 0x0 0x350003 0x0 0x18 0x3d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x420003 0x0 0x18 0x4a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4f0003 0x0 0x18 0x570005 0x0 0x0 0x0 0x0 0x0 0x0 0x5d0003 0x0 0x18 0x650005 0x0 0x0 0x0 0x0 0x0 0x0 0x6a0003 0x0 0x18 0x720005 0x0 0x0 0x0 0x0 0x0 0x0 0x780003 0x0 0x18 0x800005 0x0 0x0 0x0 0x0 0x0 0x0 0x850003 0x0 0x18 0x8d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x930003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData lombok/javac/JavacAST buildAnnotation (Lcom/sun/tools/javac/tree/JCTree$JCAnnotation;Z)Llombok/javac/JavacNode; 1 504 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x20005 0x0 0x0 0x1f6e66313f0 0x1f7 0x0 0x0 0x70007 0x52 0x40 0x1a5 0xb0007 0x1a5 0x20 0x0 0x1b0002 0x1f7 0x1e0005 0x0 0x0 0x1f6e66313f0 0x1f7 0x0 0x0 0x210004 0x0 0x0 0x1f6e6025630 0x105 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 3 3 lombok/javac/JavacAST 20 lombok/javac/JavacAST 27 lombok/javac/JavacNode methods 0
ciMethodData lombok/javac/JavacAST buildTypeUse (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 2 3921 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 348 0x20005 0x0 0x0 0x1f6e66313f0 0xf22 0x0 0x0 0x50007 0xf1a 0x20 0x8 0xb0007 0xf1a 0x20 0x0 0x110005 0xf1a 0x0 0x0 0x0 0x0 0x0 0x140005 0xf1a 0x0 0x0 0x0 0x0 0x0 0x1a0005 0xf1a 0x0 0x0 0x0 0x0 0x0 0x1d0007 0xf1a 0x358 0x0 0x210005 0x0 0x0 0x0 0x0 0x0 0x0 0x240002 0x0 0x2e0002 0x0 0x310004 0x0 0x0 0x0 0x0 0x0 0x0 0x3c0002 0x0 0x3f0004 0x0 0x0 0x0 0x0 0x0 0x0 0x470002 0x0 0x4d0007 0x0 0x1b0 0x0 0x510005 0x0 0x0 0x0 0x0 0x0 0x0 0x580003 0x0 0x100 0x5d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x660004 0x0 0x0 0x0 0x0 0x0 0x0 0x690007 0x0 0x78 0x0 0x710004 0x0 0x0 0x0 0x0 0x0 0x0 0x750002 0x0 0x780002 0x0 0x7d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x820007 0x0 0xfffffffffffffee0 0x0 0x890002 0x0 0x8c0002 0x0 0x9b0002 0x0 0x9e0005 0x0 0x0 0x0 0x0 0x0 0x0 0xa10004 0x0 0x0 0x0 0x0 0x0 0x0 0xa60004 0xfffffffffffff0e6 0x0 0x1f6e6c0e3d0 0x105 0x1f6e98e20d0 0x26 0xa90007 0xf1a 0x170 0x0 0xad0004 0x0 0x0 0x0 0x0 0x0 0x0 0xb50007 0x0 0x48 0x0 0xb80002 0x0 0xbb0003 0x0 0x28 0xc20002 0x0 0xc70007 0x0 0x40 0x0 0xcd0002 0x0 0xd00002 0x0 0xde0002 0x0 0xe10005 0x0 0x0 0x0 0x0 0x0 0x0 0xe40004 0x0 0x0 0x0 0x0 0x0 0x0 0xe90004 0xfffffffffffff174 0x0 0x1f6e6c0e3d0 0x105 0x1f6e98e20d0 0x26 0xec0007 0xe8c 0x170 0x8e 0xf00004 0x0 0x0 0x1f6e7a398d0 0x8e 0x0 0x0 0xf80007 0x8e 0x48 0x0 0xfb0002 0x0 0xfe0003 0x0 0x28 0x1050002 0x8e 0x10a0007 0x0 0x40 0x8e 0x1100002 0x8e 0x1130002 0x8e 0x1210002 0x8e 0x1240005 0x0 0x0 0x1f6e66313f0 0x8e 0x0 0x0 0x1270004 0x0 0x0 0x1f6e6025630 0x15 0x0 0x0 0x12c0004 0xfffffffffffff1ab 0x0 0x1f6e6c0e3d0 0x105 0x1f6e98e20d0 0x26 0x12f0007 0xe55 0x170 0x37 0x1330004 0x0 0x0 0x1f6e7a39d50 0x37 0x0 0x0 0x13b0007 0x37 0x48 0x0 0x13e0002 0x0 0x1410003 0x0 0x28 0x1480002 0x37 0x14d0007 0x0 0x40 0x37 0x1530002 0x37 0x1560002 0x37 0x1640002 0x37 0x1670005 0x0 0x0 0x1f6e66313f0 0x37 0x0 0x0 0x16a0004 0x0 0x0 0x0 0x0 0x0 0x0 0x16f0004 0xfffffffffffffdc9 0x0 0x1f6e6c0e3d0 0xc1e 0x1f6e98e20d0 0x26 0x1720007 0x237 0xb0 0xc1e 0x17c0002 0xc1e 0x1820002 0xc1e 0x1850005 0x0 0x0 0x1f6e66313f0 0xc1e 0x0 0x0 0x1880004 0x0 0x0 0x1f6e6025630 0x105 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 16 3 lombok/javac/JavacAST 146 com/sun/tools/javac/tree/JCTree$JCIdent 148 com/sun/tools/javac/tree/JCTree$JCPrimitiveTypeTree 199 com/sun/tools/javac/tree/JCTree$JCIdent 201 com/sun/tools/javac/tree/JCTree$JCPrimitiveTypeTree 210 com/sun/tools/javac/tree/JCTree$JCArrayTypeTree 238 lombok/javac/JavacAST 245 lombok/javac/JavacNode 252 com/sun/tools/javac/tree/JCTree$JCIdent 254 com/sun/tools/javac/tree/JCTree$JCPrimitiveTypeTree 263 com/sun/tools/javac/tree/JCTree$JCFieldAccess 291 lombok/javac/JavacAST 305 com/sun/tools/javac/tree/JCTree$JCIdent 307 com/sun/tools/javac/tree/JCTree$JCPrimitiveTypeTree 320 lombok/javac/JavacAST 327 lombok/javac/JavacNode methods 0
ciMethodData lombok/javac/JavacAST addIfNotNull (Ljava/util/Collection;Llombok/javac/JavacNode;)V 2 9031 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 21 0x10007 0x5ee 0x58 0x1c41 0x60005 0x0 0x0 0x1f6e4c5b3d0 0x1c41 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 7 java/util/ArrayList methods 0
ciMethodData lombok/javac/JavacAST buildExpression (Lcom/sun/tools/javac/tree/JCTree$JCExpression;)Llombok/javac/JavacNode; 2 2173 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x20002 0x77b 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
compile lombok/core/AST buildWithField (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;)Ljava/util/Collection; -1 4 inline 151 0 -1 lombok/core/AST buildWithField (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;)Ljava/util/Collection; 1 4 java/util/ArrayList <init> ()V 2 1 java/util/AbstractList <init> ()V 3 1 java/util/AbstractCollection <init> ()V 4 1 java/lang/Object <init> ()V 1 15 lombok/core/AST buildWithField0 (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;Ljava/util/Collection;)V 2 5 java/lang/reflect/Field get (Ljava/lang/Object;)Ljava/lang/Object; 3 19 java/lang/reflect/Field getFieldAccessor (Ljava/lang/Object;)Ljdk/internal/reflect/FieldAccessor; 4 31 java/lang/reflect/Field acquireFieldAccessor (Z)Ljdk/internal/reflect/FieldAccessor; 5 14 java/lang/reflect/Field getFieldAccessor (Z)Ljdk/internal/reflect/FieldAccessor; 5 54 java/lang/reflect/Field setFieldAccessor (Ljdk/internal/reflect/FieldAccessor;Z)V 6 30 java/lang/reflect/Field setFieldAccessor (Ljdk/internal/reflect/FieldAccessor;Z)V 3 23 jdk/internal/reflect/UnsafeObjectFieldAccessorImpl get (Ljava/lang/Object;)Ljava/lang/Object; 4 2 jdk/internal/reflect/UnsafeFieldAccessorImpl ensureObj (Ljava/lang/Object;)V 5 4 java/lang/reflect/Field getDeclaringClass ()Ljava/lang/Class; 2 29 lombok/javac/JavacAST buildTree (Ljava/lang/Object;Llombok/core/AST$Kind;)Llombok/core/LombokNode; 3 6 lombok/javac/JavacAST buildTree (Lcom/sun/tools/javac/tree/JCTree;Llombok/core/AST$Kind;)Llombok/javac/JavacNode; 4 0 lombok/javac/JavacAST $SWITCH_TABLE$lombok$core$AST$Kind ()[I 4 4 java/lang/Enum ordinal ()I 4 131 lombok/javac/JavacAST buildStatementOrExpression (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 5 46 lombok/javac/JavacAST buildLocalVar (Lcom/sun/tools/javac/tree/JCTree$JCVariableDecl;Llombok/core/AST$Kind;)Llombok/javac/JavacNode; 6 2 lombok/core/AST setAndGetAsHandled (Ljava/lang/Object;)Z 7 6 java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 8 1 java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 8 19 java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 8 65 java/util/IdentityHashMap nextKeyIndex (II)I 6 14 java/util/ArrayList <init> ()V 7 1 java/util/AbstractList <init> ()V 8 1 java/util/AbstractCollection <init> ()V 9 1 java/lang/Object <init> ()V 6 25 com/sun/tools/javac/util/List iterator ()Ljava/util/Iterator; 7 7 com/sun/tools/javac/util/List emptyIterator ()Ljava/util/Iterator; 7 16 com/sun/tools/javac/util/List$3 <init> (Lcom/sun/tools/javac/util/List;)V 8 6 java/lang/Object <init> ()V 6 58 com/sun/tools/javac/util/List$2 hasNext ()Z 6 58 com/sun/tools/javac/util/List$3 hasNext ()Z 6 53 lombok/javac/JavacAST addIfNotNull (Ljava/util/Collection;Llombok/javac/JavacNode;)V 7 6 java/util/ArrayList add (Ljava/lang/Object;)Z 8 20 java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 9 7 java/util/ArrayList grow ()[Ljava/lang/Object; 10 7 java/util/ArrayList grow (I)[Ljava/lang/Object; 11 27 jdk/internal/util/ArraysSupport newLength (III)I 11 37 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 6 75 lombok/javac/JavacAST addIfNotNull (Ljava/util/Collection;Llombok/javac/JavacNode;)V 7 6 java/util/ArrayList add (Ljava/lang/Object;)Z 8 20 java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 9 7 java/util/ArrayList grow ()[Ljava/lang/Object; 10 7 java/util/ArrayList grow (I)[Ljava/lang/Object; 11 27 jdk/internal/util/ArraysSupport newLength (III)I 11 37 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 6 84 lombok/javac/JavacAST buildExpression (Lcom/sun/tools/javac/tree/JCTree$JCExpression;)Llombok/javac/JavacNode; 7 2 lombok/javac/JavacAST buildStatementOrExpression (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 8 70 java/lang/Class getName ()Ljava/lang/String; 8 76 java/lang/String equals (Ljava/lang/Object;)Z 8 106 lombok/core/AST setAndGetAsHandled (Ljava/lang/Object;)Z 9 6 java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 10 1 java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 10 19 java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 10 65 java/util/IdentityHashMap nextKeyIndex (II)I 8 116 lombok/javac/JavacAST drill (Lcom/sun/tools/javac/tree/JCTree;)Llombok/javac/JavacNode; 9 4 java/util/ArrayList <init> ()V 10 1 java/util/AbstractList <init> ()V 11 1 java/util/AbstractCollection <init> ()V 12 1 java/lang/Object <init> ()V 9 13 lombok/core/AST fieldsOf (Ljava/lang/Class;)[Llombok/core/AST$FieldAccess; 10 4 java/util/concurrent/ConcurrentHashMap get (Ljava/lang/Object;)Ljava/lang/Object; 11 4 java/util/concurrent/ConcurrentHashMap spread (I)I 11 34 java/util/concurrent/ConcurrentHashMap tabAt ([Ljava/util/concurrent/ConcurrentHashMap$Node;I)Ljava/util/concurrent/ConcurrentHashMap$Node; 11 73 java/lang/Object equals (Ljava/lang/Object;)Z 11 149 java/lang/Object equals (Ljava/lang/Object;)Z 10 23 java/util/ArrayList <init> ()V 11 1 java/util/AbstractList <init> ()V 12 1 java/util/AbstractCollection <init> ()V 13 1 java/lang/Object <init> ()V 10 50 java/util/concurrent/ConcurrentHashMap putIfAbsent (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 9 40 lombok/core/AST buildWithField (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;)Ljava/util/Collection; 10 4 java/util/ArrayList <init> ()V 11 1 java/util/AbstractList <init> ()V 12 1 java/util/AbstractCollection <init> ()V 13 1 java/lang/Object <init> ()V 10 15 lombok/core/AST buildWithField0 (Ljava/lang/Class;Ljava/lang/Object;Llombok/core/AST$FieldAccess;Ljava/util/Collection;)V 11 5 java/lang/reflect/Field get (Ljava/lang/Object;)Ljava/lang/Object; 12 19 java/lang/reflect/Field getFieldAccessor (Ljava/lang/Object;)Ljdk/internal/reflect/FieldAccessor; 13 31 java/lang/reflect/Field acquireFieldAccessor (Z)Ljdk/internal/reflect/FieldAccessor; 14 14 java/lang/reflect/Field getFieldAccessor (Z)Ljdk/internal/reflect/FieldAccessor; 14 54 java/lang/reflect/Field setFieldAccessor (Ljdk/internal/reflect/FieldAccessor;Z)V 15 30 java/lang/reflect/Field setFieldAccessor (Ljdk/internal/reflect/FieldAccessor;Z)V 12 23 jdk/internal/reflect/UnsafeObjectFieldAccessorImpl get (Ljava/lang/Object;)Ljava/lang/Object; 13 2 jdk/internal/reflect/UnsafeFieldAccessorImpl ensureObj (Ljava/lang/Object;)V 14 4 java/lang/reflect/Field getDeclaringClass ()Ljava/lang/Class; 11 29 lombok/javac/JavacAST buildTree (Ljava/lang/Object;Llombok/core/AST$Kind;)Llombok/core/LombokNode; 12 6 lombok/javac/JavacAST buildTree (Lcom/sun/tools/javac/tree/JCTree;Llombok/core/AST$Kind;)Llombok/javac/JavacNode; 13 0 lombok/javac/JavacAST $SWITCH_TABLE$lombok$core$AST$Kind ()[I 13 4 java/lang/Enum ordinal ()I 11 50 java/util/ArrayList add (Ljava/lang/Object;)Z 12 20 java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 13 7 java/util/ArrayList grow ()[Ljava/lang/Object; 14 7 java/util/ArrayList grow (I)[Ljava/lang/Object; 15 27 jdk/internal/util/ArraysSupport newLength (III)I 15 37 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 11 106 lombok/core/AST buildWithCollection (Ljava/lang/Class;Ljava/lang/Object;Ljava/util/Collection;I)V 12 10 com/sun/tools/javac/util/List iterator ()Ljava/util/Iterator; 13 7 com/sun/tools/javac/util/List emptyIterator ()Ljava/util/Iterator; 13 16 com/sun/tools/javac/util/List$3 <init> (Lcom/sun/tools/javac/util/List;)V 14 6 java/lang/Object <init> ()V 12 71 com/sun/tools/javac/util/List$3 hasNext ()Z 12 71 com/sun/tools/javac/util/List$2 hasNext ()Z 12 22 com/sun/tools/javac/util/List$3 next ()Ljava/lang/Object; 12 43 lombok/javac/JavacAST buildTree (Ljava/lang/Object;Llombok/core/AST$Kind;)Llombok/core/LombokNode; 13 6 lombok/javac/JavacAST buildTree (Lcom/sun/tools/javac/tree/JCTree;Llombok/core/AST$Kind;)Llombok/javac/JavacNode; 14 0 lombok/javac/JavacAST $SWITCH_TABLE$lombok$core$AST$Kind ()[I 14 4 java/lang/Enum ordinal ()I 12 63 java/util/ArrayList add (Ljava/lang/Object;)Z 13 20 java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 14 7 java/util/ArrayList grow ()[Ljava/lang/Object; 15 7 java/util/ArrayList grow (I)[Ljava/lang/Object; 16 27 jdk/internal/util/ArraysSupport newLength (III)I 16 37 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 9 43 java/util/ArrayList addAll (Ljava/util/Collection;)Z 10 51 java/util/ArrayList grow (I)[Ljava/lang/Object; 11 27 jdk/internal/util/ArraysSupport newLength (III)I 11 37 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 9 70 lombok/javac/JavacNode <init> (Llombok/javac/JavacAST;Lcom/sun/tools/javac/tree/JCTree;Ljava/util/List;Llombok/core/AST$Kind;)V 9 73 lombok/core/AST putInMap (Llombok/core/LombokNode;)Llombok/core/LombokNode; 10 5 lombok/core/LombokNode get ()Ljava/lang/Object; 10 9 java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 11 1 java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 11 19 java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 11 65 java/util/IdentityHashMap nextKeyIndex (II)I 10 20 lombok/core/LombokNode get ()Ljava/lang/Object; 10 24 lombok/core/LombokNode get ()Ljava/lang/Object; 10 27 java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 11 1 java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 11 19 java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 11 65 java/util/IdentityHashMap nextKeyIndex (II)I 6 87 lombok/javac/JavacAST addIfNotNull (Ljava/util/Collection;Llombok/javac/JavacNode;)V 7 6 java/util/ArrayList add (Ljava/lang/Object;)Z 8 20 java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 9 7 java/util/ArrayList grow ()[Ljava/lang/Object; 10 7 java/util/ArrayList grow (I)[Ljava/lang/Object; 11 27 jdk/internal/util/ArraysSupport newLength (III)I 11 37 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 6 99 lombok/javac/JavacNode <init> (Llombok/javac/JavacAST;Lcom/sun/tools/javac/tree/JCTree;Ljava/util/List;Llombok/core/AST$Kind;)V 6 102 lombok/core/AST putInMap (Llombok/core/LombokNode;)Llombok/core/LombokNode; 7 5 lombok/core/LombokNode get ()Ljava/lang/Object; 7 9 java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 8 1 java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 8 19 java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 8 65 java/util/IdentityHashMap nextKeyIndex (II)I 7 20 lombok/core/LombokNode get ()Ljava/lang/Object; 7 24 lombok/core/LombokNode get ()Ljava/lang/Object;
