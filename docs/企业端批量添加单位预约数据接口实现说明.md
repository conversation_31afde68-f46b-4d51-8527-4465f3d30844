# 企业端批量添加单位预约数据接口实现说明

## 概述

根据企业端提供的接口数据格式，调整了 `createCompanyReg` 接口逻辑，新增了专门处理企业端批量数据的接口方法。

## 接口信息

### 新增接口
- **接口路径**: `POST /api/company-reg/registration/batch`
- **接口描述**: 企业端批量添加单位预约数据
- **请求体**: `CompanyRegBatchCreateDTO`

## 数据处理流程

### 1. 数据结构映射

根据接口文档，数据结构映射关系如下：

- `planInfo` → `CompanyReg` 实体（已存在于数据库）
- `teamsInfo` → `CompanyTeam` 实体（已存在于数据库）
- `examinedPersonnelInfo.basicInfo` → `Customer` 实体
- `examinedPersonnelInfo.questionnaireInfo` → 职业病问卷相关实体

### 2. 处理逻辑

#### 2.1 人员基本信息处理
- 根据 `idCard` 查找现有 `Customer` 记录
- 如果存在则更新，不存在则创建新记录
- 从身份证号解析性别、年龄、出生日期等信息

#### 2.2 客户登记处理
- 根据 `idCard` 和 `companyRegId` 查找现有 `CustomerReg` 记录
- 如果存在且状态为"未登记"，则更新记录
- 如果已登记，则抛出异常
- 如果不存在，则创建新记录
- 参考 `CustomerRegServiceImpl.importExcel` 中的逻辑

#### 2.3 职业病问卷处理
根据 `questionnaireInfo` 创建以下问卷记录：
- `occupationalHistory` → `ZyInquiryOccuHistory`
- `radiationHistory` → `ZyInquiryRadiationHistory`
- `diseaseHistory` → `ZyInquiryDiseaseHistory`
- `familyHistory` → `ZyInquiryFamilyHistory`
- `maritalHistory` → `ZyInquiryMaritalStatus`
- `symptoms` → `ZyInquirySymptom`

每个问卷记录都需要设置：
- `inquiryId` = `customer_reg_id`
- 相关人员信息字段
- 创建时间等基础字段

### 3. 错误处理

#### 3.1 数据验证
- 验证必填字段（姓名、身份证号等）
- 验证身份证号格式
- 验证手机号格式

#### 3.2 失败记录
不符合要求的数据写入 `CompanyImportRecord` 表，包含：
- 团检预约信息
- 分组信息
- 人员基本信息
- 错误信息

### 4. 进度通知

处理完成后调用企业端提供的接口通知进度（待实现具体接口调用逻辑）。

## 新增的类和方法

### 1. DTO 类
- `CompanyRegBatchCreateDTO`: 企业端批量数据主DTO
- `PlanInfoDTO`: 团检登记信息
- `TeamsInfoDTO`: 分组信息
- `ExaminedPersonnelInfoDTO`: 体检人员信息
- `BasicInfoDTO`: 基本信息
- `QuestionnaireInfoDTO`: 问卷信息
- `SignatureDTO`: 签名信息

### 2. 服务方法
- `ICompanyRegApiService.batchCreateCompanyReg()`: 批量处理企业端数据
- `CompanyRegApiServiceImpl.batchCreateCompanyReg()`: 具体实现

### 3. 辅助方法
- `processTeamPersonnel()`: 处理分组人员数据
- `processPersonnelInfo()`: 处理单个人员信息
- `validateBasicInfo()`: 验证基本信息
- `createOrUpdateCustomer()`: 创建或更新Customer
- `createOrUpdateCustomerReg()`: 创建或更新CustomerReg
- `processQuestionnaire()`: 处理职业病问卷
- `processOccupationalHistory()`: 处理职业史
- `processRadiationHistory()`: 处理放射史
- `processDiseaseHistory()`: 处理疾病史
- `processFamilyHistory()`: 处理家族史
- `processMaritalHistory()`: 处理婚姻史
- `processSymptoms()`: 处理症状
- `recordFailure()`: 记录失败信息
- `notifyEnterpriseProgress()`: 通知企业端进度

### 4. 控制器方法
- `CompanyRegApiController.batchCreateCompanyReg()`: 新增接口端点

## 依赖注入

新增了以下服务的依赖注入：
- `ICompanyImportRecordService`: 失败记录服务
- `IZyInquiryOccuHistoryService`: 职业史服务
- `IZyInquiryRadiationHistoryService`: 放射史服务
- `IZyInquiryDiseaseHistoryService`: 疾病史服务
- `IZyInquiryFamilyHistoryService`: 家族史服务
- `IZyInquiryMaritalStatusService`: 婚姻状况服务
- `IZyInquirySymptomService`: 症状服务
- `ObjectMapper`: JSON转换工具

## 事务处理

整个批量处理过程使用 `@Transactional` 注解确保数据一致性，如果处理过程中出现异常，会回滚所有操作。

## 返回结果

返回 `BatchResultVO<CustomerReg>` 包含：
- `successItems`: 成功处理的记录列表
- `failureItems`: 失败处理的记录列表
- `successCount`: 成功数量
- `failureCount`: 失败数量
- `totalCount`: 总数量

## 注意事项

1. 职业病问卷处理采用容错机制，即使问卷处理失败也不会影响主流程
2. 失败记录会同时写入 `CompanyImportRecord` 表和返回结果中
3. 企业端进度通知接口需要根据实际情况实现具体调用逻辑
4. 所有时间字段都使用当前时间
5. 身份证号验证使用现有的 `IdCardUtils` 工具类
6. 手机号验证使用 `PhoneUtil` 工具类

## 测试建议

1. 准备符合接口文档格式的测试数据
2. 测试正常流程：所有数据都符合要求
3. 测试异常流程：包含不符合要求的数据
4. 测试边界情况：空数据、重复数据等
5. 验证职业病问卷数据是否正确保存
6. 验证失败记录是否正确写入 `CompanyImportRecord` 表
