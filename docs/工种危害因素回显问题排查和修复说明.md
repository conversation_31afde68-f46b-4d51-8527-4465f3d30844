# 工种危害因素回显问题排查和修复说明

## 问题描述

工种编辑页面没有调接口来获取关联的危害因素，导致危害因素没有回显。

## 问题分析

经过代码检查，发现以下问题：

### 1. 时序问题
在编辑工种时，`loadWorktypeRiskFactors` 方法被调用，但此时 `riskFactorList` 可能还没有加载完成，导致 `updateSelectedRiskFactorDetails` 方法无法正确匹配到危害因素的详细信息。

### 2. 异步加载顺序
- `edit` 方法中调用 `loadWorktypeRiskFactors`
- 但 `riskFactorList` 是在 `onMounted` 时异步加载的
- 如果编辑操作在危害因素列表加载完成前执行，就会导致回显失败

## 修复方案

### 1. 确保危害因素列表先加载
在编辑时，先确保危害因素列表已加载完成，然后再加载关联的危害因素：

```javascript
// 修改前
function edit(record) {
  nextTick(() => {
    // ...
    if (record.id) {
      loadWorktypeRiskFactors(record.id);
    }
  });
}

// 修改后
function edit(record) {
  nextTick(async () => {
    // ...
    if (record.id) {
      await ensureRiskFactorListLoaded();
      await loadWorktypeRiskFactors(record.id);
    }
  });
}
```

### 2. 添加确保加载方法
```javascript
// 确保危害因素列表已加载
const ensureRiskFactorListLoaded = async () => {
  if (riskFactorList.value.length === 0) {
    await loadRiskFactorList();
  }
};
```

### 3. 增强调试信息
在关键方法中添加调试日志，便于排查问题：

```javascript
const loadWorktypeRiskFactors = async (worktypeId) => {
  try {
    console.log('开始加载工种关联的危害因素, worktypeId:', worktypeId);
    const res = await queryZyWorktypeRiskFactor({ worktypeId });
    console.log('查询工种关联危害因素结果:', res);
    
    if (res.success) {
      const riskFactorIds = res.result.map(item => item.riskFactorId);
      selectedRiskFactors.value = riskFactorIds;
      console.log('设置选中的危害因素ID:', riskFactorIds);
      
      updateSelectedRiskFactorDetails();
      console.log('更新后的危害因素详细信息:', selectedRiskFactorDetails.value);
    }
  } catch (error) {
    console.error('加载工种关联危害因素失败:', error);
  }
};
```

## 后端接口验证

### 1. 控制器接口
```java
@GetMapping(value = "/queryZyWorktypeRiskFactorByMainId")
public Result<List<ZyWorktypeRiskFactor>> queryZyWorktypeRiskFactorByMainId(
    @RequestParam(name="worktypeId",required=true) String worktypeId) {
    List<ZyWorktypeRiskFactor> zyWorktypeRiskFactorList = 
        zyWorktypeRiskFactorService.selectByWorktypeId(worktypeId);
    return Result.OK(zyWorktypeRiskFactorList);
}
```

### 2. 服务层实现
```java
@Override
public List<ZyWorktypeRiskFactor> selectByWorktypeId(String worktypeId) {
    return baseMapper.selectByWorktypeId(worktypeId);
}
```

### 3. Mapper SQL
```xml
<select id="selectByWorktypeId" resultType="org.jeecg.modules.occu.entity.ZyWorktypeRiskFactor">
    SELECT * FROM zy_worktype_risk_factor WHERE worktype_id = #{worktypeId} ORDER BY create_time
</select>
```

## 前端API调用

### 1. API定义
```typescript
// ZyWorktype.api.ts
export const queryZyWorktypeRiskFactor = (params) => 
  defHttp.get({ url: Api.zyWorktypeRiskFactorList, params });
```

### 2. 调用方式
```javascript
const res = await queryZyWorktypeRiskFactor({ worktypeId });
```

## 测试验证

### 1. 控制台调试
打开浏览器开发者工具，查看控制台输出：
- 检查是否有API调用错误
- 查看返回的数据结构
- 确认危害因素ID匹配情况

### 2. 网络请求
在Network标签中检查：
- API请求是否发送成功
- 请求参数是否正确
- 响应数据是否符合预期

### 3. 数据库验证
直接查询数据库确认数据：
```sql
SELECT * FROM zy_worktype_risk_factor WHERE worktype_id = '工种ID';
```

## 预期效果

修复后，工种编辑页面应该能够：
1. 正确加载并显示已关联的危害因素
2. 在危害因素选择区域显示已选择的项目
3. 支持添加、删除危害因素
4. 保存时正确提交关联关系

## 注意事项

1. **异步操作顺序**：确保依赖数据先加载完成
2. **错误处理**：添加适当的错误处理和用户提示
3. **性能考虑**：避免重复加载相同数据
4. **调试信息**：在生产环境中移除调试日志

## 后续优化建议

1. **缓存机制**：对危害因素列表进行缓存，避免重复请求
2. **加载状态**：添加加载状态指示器，提升用户体验
3. **错误重试**：在网络请求失败时提供重试机制
4. **数据预加载**：在页面初始化时预加载常用数据
