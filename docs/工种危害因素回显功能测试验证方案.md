# 工种危害因素回显功能测试验证方案

## 修复内容总结

### 1. 主要问题
工种编辑页面没有正确回显关联的危害因素，原因是异步加载时序问题。

### 2. 修复方案
- 在编辑时确保危害因素列表先加载完成
- 添加 `ensureRiskFactorListLoaded` 方法确保数据加载顺序
- 优化异步操作的执行顺序

### 3. 修改的文件
- `src/views/occu/components/ZyWorktypeForm.vue`

## 测试验证步骤

### 步骤1：准备测试数据

1. **创建工种数据**
   ```sql
   INSERT INTO zy_worktype (id, name, code, help_char, enable_flag, sort, company_id, create_time) 
   VALUES ('test_worktype_001', '测试工种', 'TEST001', 'CSGZ', 1, 1, 'company_001', NOW());
   ```

2. **创建危害因素数据**
   ```sql
   INSERT INTO zy_risk_factor (id, name, code, help_char, enable_flag, sort, company_id, create_time) 
   VALUES 
   ('risk_factor_001', '噪声', 'RF001', 'ZS', 1, 1, 'company_001', NOW()),
   ('risk_factor_002', '粉尘', 'RF002', 'FC', 1, 2, 'company_001', NOW()),
   ('risk_factor_003', '高温', 'RF003', 'GW', 1, 3, 'company_001', NOW());
   ```

3. **创建关联关系数据**
   ```sql
   INSERT INTO zy_worktype_risk_factor (id, worktype_id, risk_factor_id, risk_factor_name, risk_factor_code, create_time) 
   VALUES 
   ('relation_001', 'test_worktype_001', 'risk_factor_001', '噪声', 'RF001', NOW()),
   ('relation_002', 'test_worktype_001', 'risk_factor_002', '粉尘', 'RF002', NOW());
   ```

### 步骤2：功能测试

#### 2.1 新增工种测试
1. 打开工种管理页面
2. 点击"新增"按钮
3. 填写工种基本信息
4. 点击"选择关联的危害因素"按钮
5. 在弹窗中选择多个危害因素
6. 确认选择，检查是否正确显示已选择的危害因素
7. 保存工种，检查是否保存成功

#### 2.2 编辑工种测试（重点）
1. 在工种列表中找到已有关联危害因素的工种
2. 点击"编辑"按钮
3. **检查关键点**：
   - 危害因素选择区域是否显示已关联的危害因素
   - 已选择的危害因素标签是否正确显示
   - 危害因素数量统计是否正确
4. 点击"选择关联的危害因素"按钮
5. **检查弹窗中的状态**：
   - 已关联的危害因素是否在表格中被选中
   - 选中状态是否正确高亮显示
6. 修改选择（添加或删除危害因素）
7. 保存修改，检查是否保存成功

#### 2.3 查看工种详情测试
1. 点击工种的"详情"按钮
2. 检查危害因素信息是否正确显示
3. 确认所有关联的危害因素都能正确展示

### 步骤3：API接口测试

#### 3.1 直接API测试
使用浏览器开发者工具或Postman测试：

```
GET /occu/zyWorktype/queryZyWorktypeRiskFactorByMainId?worktypeId=test_worktype_001
```

**预期响应**：
```json
{
  "success": true,
  "result": [
    {
      "id": "relation_001",
      "worktypeId": "test_worktype_001",
      "riskFactorId": "risk_factor_001",
      "riskFactorName": "噪声",
      "riskFactorCode": "RF001",
      "createTime": "2025-01-08 10:00:00"
    },
    {
      "id": "relation_002",
      "worktypeId": "test_worktype_001",
      "riskFactorId": "risk_factor_002",
      "riskFactorName": "粉尘",
      "riskFactorCode": "RF002",
      "createTime": "2025-01-08 10:00:00"
    }
  ],
  "message": "操作成功",
  "code": 200
}
```

#### 3.2 前端调试测试
1. 打开浏览器开发者工具
2. 在Console中执行：
   ```javascript
   // 如果启用了测试方法
   window.testLoadRiskFactors('test_worktype_001');
   ```
3. 查看控制台输出，确认API调用和数据处理是否正确

### 步骤4：边界情况测试

#### 4.1 无关联危害因素的工种
1. 编辑一个没有关联危害因素的工种
2. 检查是否显示"暂未选择危害因素"
3. 添加危害因素后保存，再次编辑检查回显

#### 4.2 大量危害因素测试
1. 创建一个关联了多个危害因素的工种（如10个以上）
2. 编辑该工种，检查所有危害因素是否正确回显
3. 检查界面性能和用户体验

#### 4.3 网络异常测试
1. 在网络较慢的环境下测试
2. 模拟网络中断，检查错误处理
3. 检查重试机制是否正常工作

### 步骤5：性能测试

#### 5.1 加载时间测试
1. 记录编辑工种时的加载时间
2. 检查是否有不必要的重复请求
3. 确认数据加载顺序是否合理

#### 5.2 内存使用测试
1. 多次打开和关闭编辑弹窗
2. 检查是否有内存泄漏
3. 确认组件销毁时资源是否正确释放

## 验证检查点

### ✅ 功能正确性
- [ ] 新增工种时危害因素选择正常
- [ ] 编辑工种时危害因素正确回显
- [ ] 危害因素的增删改操作正常
- [ ] 保存后数据持久化正确

### ✅ 用户体验
- [ ] 界面响应速度合理
- [ ] 加载状态提示清晰
- [ ] 错误信息提示友好
- [ ] 操作流程顺畅

### ✅ 数据一致性
- [ ] 前端显示与数据库数据一致
- [ ] 并发操作时数据不冲突
- [ ] 事务处理正确

### ✅ 兼容性
- [ ] 不同浏览器下功能正常
- [ ] 移动端适配良好
- [ ] 不同屏幕尺寸显示正常

## 问题排查指南

### 如果回显仍然不正常

1. **检查控制台错误**
   - 查看Network标签中的API请求
   - 检查Console中的错误信息
   - 确认API响应数据格式

2. **检查数据库数据**
   ```sql
   SELECT * FROM zy_worktype_risk_factor WHERE worktype_id = '工种ID';
   ```

3. **检查前端数据流**
   - 在`loadWorktypeRiskFactors`方法中添加断点
   - 检查`selectedRiskFactors`和`selectedRiskFactorDetails`的值
   - 确认`updateSelectedRiskFactorDetails`方法执行情况

4. **检查组件状态**
   - 确认`riskFactorList`是否已加载
   - 检查异步操作的执行顺序
   - 验证组件生命周期是否正确

## 后续优化建议

1. **缓存优化**：对危害因素列表进行缓存
2. **预加载**：在页面初始化时预加载常用数据
3. **错误重试**：添加自动重试机制
4. **用户反馈**：增加更详细的加载和错误状态提示
