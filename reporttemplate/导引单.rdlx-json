{"Name": "报表1", "Width": "21cm", "DocumentMap": {"Source": "All"}, "Layers": [{"Name": "default"}], "CustomProperties": [{"Name": "PaperOrientation", "Value": "Portrait"}, {"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}], "Page": {"PageWidth": "21cm", "PageHeight": "29.7cm", "RightMargin": "0in", "LeftMargin": "0in", "TopMargin": "0in", "BottomMargin": "0in", "Columns": 1, "ColumnSpacing": "0.5in"}, "DataSources": [{"Name": "DataSource", "ConnectionProperties": {"DataProvider": "JSONEMBED", "ConnectString": "jsondata={\\n  \"reg\": {\\n    \"id\": \"1771724324357672962\",\\n    \"examNo\": \"**********\",\\n    \"status\": \"已登记\",\\n    \"customerAvatar\": \"上传失败！\",\\n    \"customerId\": null,\\n    \"customerRegNum\": null,\\n    \"hisPid\": null,\\n    \"hisInpatientId\": null,\\n    \"name\": \"高艳鹏\",\\n    \"gender\": \"男\",\\n    \"age\": 36,\\n    \"ageUnit\": \"岁\",\\n    \"birthday\": \"1988-01-09\",\\n    \"phone\": \"15248108086\",\\n    \"marriageStatus\": \"已婚\",\\n    \"pregnancyFlag\": null,\\n    \"nation\": \"汉\",\\n    \"bloodType\": \"不明\",\\n    \"cardType\": \"居民身份证\",\\n    \"idCard\": \"150426198801092393\",\\n    \"examCardNo\": null,\\n    \"medicalCardNo\": null,\\n    \"healthNo\": null,\\n    \"address\": \"上院小区\",\\n    \"postCode\": \"010010\",\\n    \"email\": \"<EMAIL>\",\\n    \"customerCategory\": \"企业客户\",\\n    \"examCategory\": \"健康体检\",\\n    \"teamId\": \"1759843409162178562\",\\n    \"teamName\": \"上岗前\",\\n    \"bookingDate\": null,\\n    \"companyDeptId\": null,\\n    \"companyDeptName\": null,\\n    \"country\": \"中国\",\\n    \"industry\": null,\\n    \"workNo\": null,\\n    \"career\": \"餐饮食品业\",\\n    \"jobStatus\": null,\\n    \"workType\": \"6489215e-cdda-4115-8396-5844e9143bd0\",\\n    \"workShop\": null,\\n    \"riskFactor\": null,\\n    \"workYears\": null,\\n    \"workMonths\": null,\\n    \"riskYears\": null,\\n    \"riskMonths\": null,\\n    \"reExamStatus\": 0,\\n    \"reExamRemark\": null,\\n    \"monitoringType\": null,\\n    \"occIrradiation\": null,\\n    \"recipeTitle\": null,\\n    \"eduLevel\": \"研究生\",\\n    \"introducer\": null,\\n    \"remark\": null,\\n    \"companyRegId\": \"1759843305634172929\",\\n    \"companyRegName\": \"2024测试\",\\n    \"checkDate\": null,\\n    \"creatorBy\": \"admin\",\\n    \"checkState\": \"未检\",\\n    \"infoSource\": null,\\n    \"paymentState\": \"待支付\",\\n    \"operId\": null,\\n    \"operName\": null,\\n    \"operTime\": null,\\n    \"auditDate\": null,\\n    \"originCustomer\": null,\\n    \"supplyFlag\": 0,\\n    \"prePayFlag\": 0,\\n    \"webQueryCode\": null,\\n    \"companyName\": \"呼和浩特市邦健信息技术有限公司\",\\n    \"companyId\": null,\\n    \"createTime\": \"2024-03-24 10:22:51\",\\n    \"regTime\": \"2024-04-19 17:19:22\",\\n    \"guidancePrintTimes\": 20,\\n    \"serialNo\": 1,\\n    \"itemGroupList\": null,\\n    \"companyTeam\": null,\\n    \"companyReg\": null,\\n    \"statusStatList\": null,\\n    \"delFlag\": \"0\",\\n    \"summaryStatus\": null,\\n    \"summaryStatusColor\": null,\\n    \"riskFactorList\": null\\n  },\\n  \"itemGroups\": [\\n    {\\n      \"id\": \"1783451883311075329\",\\n      \"customerRegId\": \"1771724324357672962\",\\n      \"itemGroupId\": \"JCJC001\",\\n      \"itemGroupName\": \"基础检查项\",\\n      \"departmentId\": \"1752144113801428993\",\\n      \"departmentName\": \"一般项目\",\\n      \"type\": \"健康项目\",\\n      \"itemSuitId\": null,\\n      \"itemSuitName\": null,\\n      \"addMinusFlag\": 1,\\n      \"price\": 100,\\n      \"disRate\": 1,\\n      \"priceAfterDis\": 100,\\n      \"payerType\": \"个人支付\",\\n      \"payStatus\": \"待支付\",\\n      \"checkTime\": null,\\n      \"checkStatus\": \"已小结\",\\n      \"checkStatusColor\": null,\\n      \"regBy\": null,\\n      \"departReportStatus\": null,\\n      \"receiptId\": null,\\n      \"createTime\": \"2024-04-25 19:03:59\",\\n      \"updateTime\": null,\\n      \"department\": {\\n        \"id\": \"1752144113801428993\",\\n        \"parentId\": null,\\n        \"departName\": \"一般项目\",\\n        \"helpChar\": null,\\n        \"departNameEn\": null,\\n        \"departNameAbbr\": null,\\n        \"departOrder\": null,\\n        \"description\": null,\\n        \"orgCategory\": null,\\n        \"orgType\": null,\\n        \"orgCode\": null,\\n        \"mobile\": null,\\n        \"fax\": null,\\n        \"address\": null,\\n        \"memo\": null,\\n        \"status\": null,\\n        \"delFlag\": null,\\n        \"qywxIdentifier\": null,\\n        \"createBy\": null,\\n        \"createTime\": null,\\n        \"updateBy\": null,\\n        \"updateTime\": null,\\n        \"tenantId\": null,\\n        \"izLeaf\": null,\\n        \"wubiChar\": null,\\n        \"sexLimit\": null,\\n        \"haveSummary\": null,\\n        \"summarySort\": null,\\n        \"guideSort\": 2,\\n        \"reportSort\": null,\\n        \"maxPerDay\": null,\\n        \"vipAddress\": null,\\n        \"womenAddress\": null,\\n        \"menAddress\": null,\\n        \"initialId\": null,\\n        \"checkDoctorIntro\": null,\\n        \"auditDoctorIntro\": null,\\n        \"sumFormat\": null,\\n        \"intro\": null,\\n        \"departFunCategory\": \"检查\",\\n        \"directorUserIds\": null,\\n        \"oldDirectorUserIds\": null\\n      },\\n      \"itemList\": null,\\n      \"abandonFlag\": null,\\n      \"departFunction\": \"检查\",\\n      \"guideSort\": null,\\n      \"reportSort\": null\\n    },\\n    {\\n      \"id\": \"1792817489009315841\",\\n      \"customerRegId\": \"1771724324357672962\",\\n      \"itemGroupId\": \"NKJX001\",\\n      \"itemGroupName\": \"内科检查\",\\n      \"departmentId\": \"1752144293560909825\",\\n      \"departmentName\": \"内科检查室\",\\n      \"type\": \"健康项目\",\\n      \"itemSuitId\": null,\\n      \"itemSuitName\": null,\\n      \"addMinusFlag\": 1,\\n      \"price\": 0,\\n      \"disRate\": 1,\\n      \"priceAfterDis\": 0,\\n      \"payerType\": \"个人支付\",\\n      \"payStatus\": \"待支付\",\\n      \"checkTime\": null,\\n      \"checkStatus\": \"待查\",\\n      \"checkStatusColor\": null,\\n      \"regBy\": null,\\n      \"departReportStatus\": null,\\n      \"receiptId\": null,\\n      \"createTime\": \"2024-05-21 15:19:33\",\\n      \"updateTime\": null,\\n      \"department\": {\\n        \"id\": \"1752144293560909825\",\\n        \"parentId\": null,\\n        \"departName\": \"内科检查室\",\\n        \"helpChar\": null,\\n        \"departNameEn\": null,\\n        \"departNameAbbr\": null,\\n        \"departOrder\": null,\\n        \"description\": null,\\n        \"orgCategory\": null,\\n        \"orgType\": null,\\n        \"orgCode\": null,\\n        \"mobile\": null,\\n        \"fax\": null,\\n        \"address\": null,\\n        \"memo\": null,\\n        \"status\": null,\\n        \"delFlag\": null,\\n        \"qywxIdentifier\": null,\\n        \"createBy\": null,\\n        \"createTime\": null,\\n        \"updateBy\": null,\\n        \"updateTime\": null,\\n        \"tenantId\": null,\\n        \"izLeaf\": null,\\n        \"wubiChar\": null,\\n        \"sexLimit\": null,\\n        \"haveSummary\": null,\\n        \"summarySort\": null,\\n        \"guideSort\": 3,\\n        \"reportSort\": null,\\n        \"maxPerDay\": null,\\n        \"vipAddress\": null,\\n        \"womenAddress\": null,\\n        \"menAddress\": null,\\n        \"initialId\": null,\\n        \"checkDoctorIntro\": null,\\n        \"auditDoctorIntro\": null,\\n        \"sumFormat\": null,\\n        \"intro\": null,\\n        \"departFunCategory\": \"检查\",\\n        \"directorUserIds\": null,\\n        \"oldDirectorUserIds\": null\\n      },\\n      \"itemList\": null,\\n      \"abandonFlag\": null,\\n      \"departFunction\": \"检查\",\\n      \"guideSort\": null,\\n      \"reportSort\": null\\n    }\\n  ],\\n  \"itemGroupByDepartment\": [\\n    {\\n      \"seq\": null,\\n      \"departFunction\": null,\\n      \"depart\": {\\n        \"id\": \"1752144113801428993\",\\n        \"parentId\": null,\\n        \"departName\": \"一般项目\",\\n        \"helpChar\": null,\\n        \"departNameEn\": null,\\n        \"departNameAbbr\": null,\\n        \"departOrder\": null,\\n        \"description\": null,\\n        \"orgCategory\": null,\\n        \"orgType\": null,\\n        \"orgCode\": null,\\n        \"mobile\": null,\\n        \"fax\": null,\\n        \"address\": null,\\n        \"memo\": null,\\n        \"status\": null,\\n        \"delFlag\": null,\\n        \"qywxIdentifier\": null,\\n        \"createBy\": null,\\n        \"createTime\": null,\\n        \"updateBy\": null,\\n        \"updateTime\": null,\\n        \"tenantId\": null,\\n        \"izLeaf\": null,\\n        \"wubiChar\": null,\\n        \"sexLimit\": null,\\n        \"haveSummary\": null,\\n        \"summarySort\": null,\\n        \"guideSort\": 2,\\n        \"reportSort\": null,\\n        \"maxPerDay\": null,\\n        \"vipAddress\": null,\\n        \"womenAddress\": null,\\n        \"menAddress\": null,\\n        \"initialId\": null,\\n        \"checkDoctorIntro\": null,\\n        \"auditDoctorIntro\": null,\\n        \"sumFormat\": null,\\n        \"intro\": null,\\n        \"departFunCategory\": \"检查\",\\n        \"directorUserIds\": null,\\n        \"oldDirectorUserIds\": null\\n      },\\n      \"groupList\": [\\n        {\\n          \"id\": \"1783451883311075329\",\\n          \"customerRegId\": \"1771724324357672962\",\\n          \"itemGroupId\": \"JCJC001\",\\n          \"itemGroupName\": \"基础检查项\",\\n          \"departmentId\": \"1752144113801428993\",\\n          \"departmentName\": \"一般项目\",\\n          \"type\": \"健康项目\",\\n          \"itemSuitId\": null,\\n          \"itemSuitName\": null,\\n          \"addMinusFlag\": 1,\\n          \"price\": 100,\\n          \"disRate\": 1,\\n          \"priceAfterDis\": 100,\\n          \"payerType\": \"个人支付\",\\n          \"payStatus\": \"待支付\",\\n          \"checkTime\": null,\\n          \"checkStatus\": \"已小结\",\\n          \"checkStatusColor\": null,\\n          \"regBy\": null,\\n          \"departReportStatus\": null,\\n          \"receiptId\": null,\\n          \"createTime\": \"2024-04-25 19:03:59\",\\n          \"updateTime\": null,\\n          \"department\": {\\n            \"id\": \"1752144113801428993\",\\n            \"parentId\": null,\\n            \"departName\": \"一般项目\",\\n            \"helpChar\": null,\\n            \"departNameEn\": null,\\n            \"departNameAbbr\": null,\\n            \"departOrder\": null,\\n            \"description\": null,\\n            \"orgCategory\": null,\\n            \"orgType\": null,\\n            \"orgCode\": null,\\n            \"mobile\": null,\\n            \"fax\": null,\\n            \"address\": null,\\n            \"memo\": null,\\n            \"status\": null,\\n            \"delFlag\": null,\\n            \"qywxIdentifier\": null,\\n            \"createBy\": null,\\n            \"createTime\": null,\\n            \"updateBy\": null,\\n            \"updateTime\": null,\\n            \"tenantId\": null,\\n            \"izLeaf\": null,\\n            \"wubiChar\": null,\\n            \"sexLimit\": null,\\n            \"haveSummary\": null,\\n            \"summarySort\": null,\\n            \"guideSort\": 2,\\n            \"reportSort\": null,\\n            \"maxPerDay\": null,\\n            \"vipAddress\": null,\\n            \"womenAddress\": null,\\n            \"menAddress\": null,\\n            \"initialId\": null,\\n            \"checkDoctorIntro\": null,\\n            \"auditDoctorIntro\": null,\\n            \"sumFormat\": null,\\n            \"intro\": null,\\n            \"departFunCategory\": \"检查\",\\n            \"directorUserIds\": null,\\n            \"oldDirectorUserIds\": null\\n          },\\n          \"itemList\": null,\\n          \"abandonFlag\": null,\\n          \"departFunction\": \"检查\",\\n          \"guideSort\": null,\\n          \"reportSort\": null\\n        }\\n      ]\\n    },\\n    {\\n      \"seq\": null,\\n      \"departFunction\": null,\\n      \"depart\": {\\n        \"id\": \"1752144293560909825\",\\n        \"parentId\": null,\\n        \"departName\": \"内科检查室\",\\n        \"helpChar\": null,\\n        \"departNameEn\": null,\\n        \"departNameAbbr\": null,\\n        \"departOrder\": null,\\n        \"description\": null,\\n        \"orgCategory\": null,\\n        \"orgType\": null,\\n        \"orgCode\": null,\\n        \"mobile\": null,\\n        \"fax\": null,\\n        \"address\": null,\\n        \"memo\": null,\\n        \"status\": null,\\n        \"delFlag\": null,\\n        \"qywxIdentifier\": null,\\n        \"createBy\": null,\\n        \"createTime\": null,\\n        \"updateBy\": null,\\n        \"updateTime\": null,\\n        \"tenantId\": null,\\n        \"izLeaf\": null,\\n        \"wubiChar\": null,\\n        \"sexLimit\": null,\\n        \"haveSummary\": null,\\n        \"summarySort\": null,\\n        \"guideSort\": 3,\\n        \"reportSort\": null,\\n        \"maxPerDay\": null,\\n        \"vipAddress\": null,\\n        \"womenAddress\": null,\\n        \"menAddress\": null,\\n        \"initialId\": null,\\n        \"checkDoctorIntro\": null,\\n        \"auditDoctorIntro\": null,\\n        \"sumFormat\": null,\\n        \"intro\": null,\\n        \"departFunCategory\": \"检查\",\\n        \"directorUserIds\": null,\\n        \"oldDirectorUserIds\": null\\n      },\\n      \"groupList\": [\\n        {\\n          \"id\": \"1792817489009315841\",\\n          \"customerRegId\": \"1771724324357672962\",\\n          \"itemGroupId\": \"NKJX001\",\\n          \"itemGroupName\": \"内科检查\",\\n          \"departmentId\": \"1752144293560909825\",\\n          \"departmentName\": \"内科检查室\",\\n          \"type\": \"健康项目\",\\n          \"itemSuitId\": null,\\n          \"itemSuitName\": null,\\n          \"addMinusFlag\": 1,\\n          \"price\": 0,\\n          \"disRate\": 1,\\n          \"priceAfterDis\": 0,\\n          \"payerType\": \"个人支付\",\\n          \"payStatus\": \"待支付\",\\n          \"checkTime\": null,\\n          \"checkStatus\": \"待查\",\\n          \"checkStatusColor\": null,\\n          \"regBy\": null,\\n          \"departReportStatus\": null,\\n          \"receiptId\": null,\\n          \"createTime\": \"2024-05-21 15:19:33\",\\n          \"updateTime\": null,\\n          \"department\": {\\n            \"id\": \"1752144293560909825\",\\n            \"parentId\": null,\\n            \"departName\": \"内科检查室\",\\n            \"helpChar\": null,\\n            \"departNameEn\": null,\\n            \"departNameAbbr\": null,\\n            \"departOrder\": null,\\n            \"description\": null,\\n            \"orgCategory\": null,\\n            \"orgType\": null,\\n            \"orgCode\": null,\\n            \"mobile\": null,\\n            \"fax\": null,\\n            \"address\": null,\\n            \"memo\": null,\\n            \"status\": null,\\n            \"delFlag\": null,\\n            \"qywxIdentifier\": null,\\n            \"createBy\": null,\\n            \"createTime\": null,\\n            \"updateBy\": null,\\n            \"updateTime\": null,\\n            \"tenantId\": null,\\n            \"izLeaf\": null,\\n            \"wubiChar\": null,\\n            \"sexLimit\": null,\\n            \"haveSummary\": null,\\n            \"summarySort\": null,\\n            \"guideSort\": 3,\\n            \"reportSort\": null,\\n            \"maxPerDay\": null,\\n            \"vipAddress\": null,\\n            \"womenAddress\": null,\\n            \"menAddress\": null,\\n            \"initialId\": null,\\n            \"checkDoctorIntro\": null,\\n            \"auditDoctorIntro\": null,\\n            \"sumFormat\": null,\\n            \"intro\": null,\\n            \"departFunCategory\": \"检查\",\\n            \"directorUserIds\": null,\\n            \"oldDirectorUserIds\": null\\n          },\\n          \"itemList\": null,\\n          \"abandonFlag\": null,\\n          \"departFunction\": \"检查\",\\n          \"guideSort\": null,\\n          \"reportSort\": null\\n        }\\n      ]\\n    }\\n  ],\\n  \"itemGroupByFunction\": [\\n    {\\n      \"seq\": null,\\n      \"departFunction\": \"检查\",\\n      \"depart\": null,\\n      \"groupList\": [\\n        {\\n          \"id\": \"1783451883311075329\",\\n          \"customerRegId\": \"1771724324357672962\",\\n          \"itemGroupId\": \"JCJC001\",\\n          \"itemGroupName\": \"基础检查项\",\\n          \"departmentId\": \"1752144113801428993\",\\n          \"departmentName\": \"一般项目\",\\n          \"type\": \"健康项目\",\\n          \"itemSuitId\": null,\\n          \"itemSuitName\": null,\\n          \"addMinusFlag\": 1,\\n          \"price\": 100,\\n          \"disRate\": 1,\\n          \"priceAfterDis\": 100,\\n          \"payerType\": \"个人支付\",\\n          \"payStatus\": \"待支付\",\\n          \"checkTime\": null,\\n          \"checkStatus\": \"已小结\",\\n          \"checkStatusColor\": null,\\n          \"regBy\": null,\\n          \"departReportStatus\": null,\\n          \"receiptId\": null,\\n          \"createTime\": \"2024-04-25 19:03:59\",\\n          \"updateTime\": null,\\n          \"department\": {\\n            \"id\": \"1752144113801428993\",\\n            \"parentId\": null,\\n            \"departName\": \"一般项目\",\\n            \"helpChar\": null,\\n            \"departNameEn\": null,\\n            \"departNameAbbr\": null,\\n            \"departOrder\": null,\\n            \"description\": null,\\n            \"orgCategory\": null,\\n            \"orgType\": null,\\n            \"orgCode\": null,\\n            \"mobile\": null,\\n            \"fax\": null,\\n            \"address\": null,\\n            \"memo\": null,\\n            \"status\": null,\\n            \"delFlag\": null,\\n            \"qywxIdentifier\": null,\\n            \"createBy\": null,\\n            \"createTime\": null,\\n            \"updateBy\": null,\\n            \"updateTime\": null,\\n            \"tenantId\": null,\\n            \"izLeaf\": null,\\n            \"wubiChar\": null,\\n            \"sexLimit\": null,\\n            \"haveSummary\": null,\\n            \"summarySort\": null,\\n            \"guideSort\": 2,\\n            \"reportSort\": null,\\n            \"maxPerDay\": null,\\n            \"vipAddress\": null,\\n            \"womenAddress\": null,\\n            \"menAddress\": null,\\n            \"initialId\": null,\\n            \"checkDoctorIntro\": null,\\n            \"auditDoctorIntro\": null,\\n            \"sumFormat\": null,\\n            \"intro\": null,\\n            \"departFunCategory\": \"检查\",\\n            \"directorUserIds\": null,\\n            \"oldDirectorUserIds\": null\\n          },\\n          \"itemList\": null,\\n          \"abandonFlag\": null,\\n          \"departFunction\": \"检查\",\\n          \"guideSort\": null,\\n          \"reportSort\": null\\n        },\\n        {\\n          \"id\": \"1792817489009315841\",\\n          \"customerRegId\": \"1771724324357672962\",\\n          \"itemGroupId\": \"NKJX001\",\\n          \"itemGroupName\": \"内科检查\",\\n          \"departmentId\": \"1752144293560909825\",\\n          \"departmentName\": \"内科检查室\",\\n          \"type\": \"健康项目\",\\n          \"itemSuitId\": null,\\n          \"itemSuitName\": null,\\n          \"addMinusFlag\": 1,\\n          \"price\": 0,\\n          \"disRate\": 1,\\n          \"priceAfterDis\": 0,\\n          \"payerType\": \"个人支付\",\\n          \"payStatus\": \"待支付\",\\n          \"checkTime\": null,\\n          \"checkStatus\": \"待查\",\\n          \"checkStatusColor\": null,\\n          \"regBy\": null,\\n          \"departReportStatus\": null,\\n          \"receiptId\": null,\\n          \"createTime\": \"2024-05-21 15:19:33\",\\n          \"updateTime\": null,\\n          \"department\": {\\n            \"id\": \"1752144293560909825\",\\n            \"parentId\": null,\\n            \"departName\": \"内科检查室\",\\n            \"helpChar\": null,\\n            \"departNameEn\": null,\\n            \"departNameAbbr\": null,\\n            \"departOrder\": null,\\n            \"description\": null,\\n            \"orgCategory\": null,\\n            \"orgType\": null,\\n            \"orgCode\": null,\\n            \"mobile\": null,\\n            \"fax\": null,\\n            \"address\": null,\\n            \"memo\": null,\\n            \"status\": null,\\n            \"delFlag\": null,\\n            \"qywxIdentifier\": null,\\n            \"createBy\": null,\\n            \"createTime\": null,\\n            \"updateBy\": null,\\n            \"updateTime\": null,\\n            \"tenantId\": null,\\n            \"izLeaf\": null,\\n            \"wubiChar\": null,\\n            \"sexLimit\": null,\\n            \"haveSummary\": null,\\n            \"summarySort\": null,\\n            \"guideSort\": 3,\\n            \"reportSort\": null,\\n            \"maxPerDay\": null,\\n            \"vipAddress\": null,\\n            \"womenAddress\": null,\\n            \"menAddress\": null,\\n            \"initialId\": null,\\n            \"checkDoctorIntro\": null,\\n            \"auditDoctorIntro\": null,\\n            \"sumFormat\": null,\\n            \"intro\": null,\\n            \"departFunCategory\": \"检查\",\\n            \"directorUserIds\": null,\\n            \"oldDirectorUserIds\": null\\n          },\\n          \"itemList\": null,\\n          \"abandonFlag\": null,\\n          \"departFunction\": \"检查\",\\n          \"guideSort\": null,\\n          \"reportSort\": null\\n        }\\n      ]\\n    }\\n  ]\\n}"}}], "PageHeader": {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Height": "2.5cm", "ReportItems": [{"Type": "textbox", "Name": "标题", "DataElementName": "文本框39", "CanGrow": true, "KeepTogether": true, "Value": "内蒙古杭锦后旗医院导引单", "Style": {"FontFamily": "黑体", "FontSize": "18pt", "PaddingLeft": "1pt", "PaddingRight": "1pt", "PaddingTop": "1pt", "PaddingBottom": "1pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "0cm", "Top": "0.2cm", "Width": "21cm", "Height": "1.5cm"}, {"Type": "barcode", "Name": "体检号二维码", "ZIndex": 4, "Value": "=Fields!examNo.Value", "Symbology": "Code_93", "CaptionLocation": "Below", "QuietZone": {"Left": "0cm", "Right": "0cm", "Top": "0cm", "Bottom": "0cm"}, "SupplementOptions": {"Spacing": "0cm"}, "Style": {"FontFamily": "黑体", "FontWeight": "Medium", "TextAlign": "Center"}, "Left": "16cm", "Top": "0.5cm", "Width": "4.5cm", "Height": "1.5cm"}, {"Type": "image", "Name": "logo", "ZIndex": 4, "Sizing": "Clip", "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "2.5cm"}, {"Type": "textbox", "Name": "文本框2", "ZIndex": 1, "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "体检后将导引单交回体检中心", "Style": {"FontFamily": "黑体", "FontSize": "11pt", "TextAlign": "Center"}, "Left": "0cm", "Top": "1.497cm", "Width": "21cm", "Height": "0.55cm"}, {"Type": "line", "Name": "页眉线", "ZIndex": 5, "StartPoint": {"X": "0.503cm", "Y": "2.047cm"}, "EndPoint": {"X": "20.503cm", "Y": "2.047cm"}, "LineWidth": "2pt"}]}, "Body": {"Height": "5.85cm", "ReportItems": [{"Type": "rectangle", "Name": "体检信息容器", "ZIndex": 1, "ReportItems": [{"Type": "table", "Name": "基本信息", "DataSetName": "reg", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Style": "Solid"}, "BottomBorder": {"Style": "Double"}, "LeftBorder": {"Style": "Solid"}, "RightBorder": {"Style": "Solid"}}, "TableColumns": [{"Width": "6.501cm"}, {"Width": "4.499cm"}, {"Width": "4.5cm"}, {"Width": "4.502cm"}], "Header": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "体检号", "DataElementName": "examNo", "CanGrow": true, "KeepTogether": true, "Value": "=\"体检号：\" & Fields!examNo.Value", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "6.501cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "姓名", "DataElementName": "name", "CanGrow": true, "KeepTogether": true, "Value": "=\"姓名：\" & Fields!name.Value", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "4.499cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "性别", "DataElementName": "gender", "CanGrow": true, "KeepTogether": true, "Value": "=\"性别：\" & Fields!gender.Value", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "4.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "年龄", "DataElementName": "age", "CanGrow": true, "KeepTogether": true, "Value": "=\"年龄：\" & Fields!age.Value", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "4.502cm", "Height": "0.75cm"}}]}], "RepeatOnNewPage": true}, "Details": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "证件号", "DataElementName": "idCard", "CanGrow": true, "KeepTogether": true, "Value": "=\"证件号：\" & Fields!idCard.Value", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "6.501cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "电话", "DataElementName": "phone", "CanGrow": true, "KeepTogether": true, "Value": "=\"电话：\" & Fields!phone.Value", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "4.499cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "登记日期", "DataElementName": "regTime", "CanGrow": true, "KeepTogether": true, "Value": "=\"登记日期：\" & Fields!regTime.Value", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "9.002cm", "Height": "0.75cm"}, "ColSpan": 2}, null]}], "Group": {"PageBreakDisabled": "false"}}, "Footer": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "工作单位", "DataElementName": "companyName", "CanGrow": true, "KeepTogether": true, "Value": "=\"工作单位：\" & Fields!companyName.Value", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "11cm", "Height": "0.75cm"}, "ColSpan": 2}, null, {"Item": {"Type": "textbox", "Name": "套餐", "DataElementName": "TextBox10", "CanGrow": true, "KeepTogether": true, "Value": "套餐：", "Style": {"Border": {"Style": "Solid", "Width": "0.5pt"}, "FontFamily": "黑体", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "9.002cm", "Height": "0.75cm"}, "ColSpan": 2}, null]}]}, "Left": "0.5cm", "Top": "0.5cm", "Width": "20.002cm", "Height": "2.25cm"}], "Left": "0cm", "Top": "0cm", "Width": "21cm", "Height": "3cm"}, {"Type": "table", "Name": "体检科室表格", "ZIndex": 3, "DataSetName": "itemGroupByDepartment", "Style": {"Border": {"Style": "Solid"}}, "TableColumns": [{"Width": "5.002cm"}, {"Width": "9.497cm"}, {"Width": "2.999cm"}, {"Width": "2.634cm"}], "Header": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框5", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "检查科室", "Style": {"Border": {"Width": "0.5pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "黑体", "FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Right"}, "Left": "0cm", "Top": "0cm", "Width": "5.002cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框8", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "检查项目", "Style": {"Border": {"Width": "0.5pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "黑体", "FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "9.497cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框6", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "放弃签字", "Style": {"Border": {"Width": "0.5pt"}, "BottomBorder": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.999cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框7", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "操作员签字", "Style": {"Border": {"Width": "0.5pt"}, "BottomBorder": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.634cm", "Height": "0.75cm"}}]}], "RepeatOnNewPage": true}, "Details": {"TableRows": [{"Height": "1.6cm", "TableCells": [{"Item": {"Type": "table", "Name": "体检项目表格", "ZIndex": 8, "DataSetName": "groupList", "Style": {"Border": {"Style": "Solid"}}, "TableColumns": [{"Width": "5.002cm"}, {"Width": "9.499cm"}, {"Width": "3cm"}, {"Width": "2.631cm"}], "Header": {"TableRows": [{"Height": "0.8cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框1", "DataElementName": "department.departName", "CanGrow": true, "KeepTogether": true, "Value": "=Fields.Item(\"department.departName\").Value & \"：\"", "Style": {"BottomBorder": {"Style": "Solid"}, "FontFamily": "黑体", "FontSize": "12pt", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Right"}, "Left": "0cm", "Top": "0cm", "Width": "5.002cm", "Height": "0.8cm"}}, {"Item": {"Type": "textbox", "Name": "科室导引介绍", "DataElementName": "department.intro", "CanGrow": true, "KeepTogether": true, "Value": "=Fields.Item(\"department.intro\").Value", "Style": {"BottomBorder": {"Style": "Solid"}, "FontFamily": "黑体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "9.499cm", "Height": "0.8cm"}}, {"Item": {"Type": "textbox", "Name": "文本框4", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"BottomBorder": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "3cm", "Height": "0.8cm"}}, {"Item": {"Type": "textbox", "Name": "文本框9", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"BottomBorder": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.631cm", "Height": "0.8cm"}}]}], "RepeatOnNewPage": true}, "Details": {"TableRows": [{"Height": "0.8cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框11", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Width": "0.5pt"}, "TopBorder": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "5.002cm", "Height": "0.8cm"}}, {"Item": {"Type": "textbox", "Name": "体检组合名称", "DataElementName": "itemGroupName", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!itemGroupName.Value", "Style": {"Border": {"Width": "0.5pt"}, "TopBorder": {"Style": "Solid"}, "FontFamily": "黑体", "FontSize": "11pt", "PaddingLeft": "1pt", "PaddingRight": "1pt", "PaddingTop": "1pt", "PaddingBottom": "1pt"}, "Width": "9.499cm", "Height": "0.8cm"}}, {"Item": {"Type": "rectangle", "Name": "放弃签字容器", "ZIndex": 2, "Style": {"Border": {"Style": "Groove", "Width": "0.5pt"}}, "Left": "0cm", "Top": "0cm", "Width": "3cm", "Height": "0.8cm"}}, {"Item": {"Type": "rectangle", "Name": "操作员签字容器", "ZIndex": 2, "Style": {"Border": {"Style": "Groove", "Width": "0.5pt"}}, "Left": "0cm", "Top": "0cm", "Width": "2.631cm", "Height": "0.8cm"}}]}], "Group": {"PageBreakDisabled": "false"}, "SortExpressions": [{"Value": "=Fields!guideSort.Value"}]}, "Left": "0cm", "Top": "0cm", "Width": "20.132cm", "Height": "1.6cm"}, "ColSpan": 4}, null, null, null]}], "Group": {"PageBreakDisabled": "false"}}, "Left": "0.5cm", "Top": "3.5cm", "Width": "20.132cm", "Height": "2.35cm"}]}, "PageFooter": {"Name": "<PERSON>Footer", "Height": "2.258cm", "ReportItems": [{"Type": "textbox", "Name": "地址", "ZIndex": 2, "DataElementName": "文本框32", "CanGrow": true, "KeepTogether": true, "Value": "地址：杭锦后旗陕坝镇健康路", "Style": {"FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.501cm", "Top": "0.5cm", "Width": "4.999cm", "Height": "0.75cm"}, {"Type": "textbox", "Name": "页数", "ZIndex": 7, "DataElementName": "文本框38", "CanGrow": true, "KeepTogether": true, "Value": "=\"第 \" & Globals!PageNumber & \" 页、共 \" & Globals!TotalPages & \" 页\"", "Style": {"FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "15.999cm", "Top": "0.5cm", "Width": "3.503cm", "Height": "0.75cm"}, {"Type": "line", "Name": "页脚线", "ZIndex": 8, "StartPoint": {"X": "0.5cm", "Y": "0.5cm"}, "EndPoint": {"X": "20.5cm", "Y": "0.5cm"}}, {"Type": "textbox", "Name": "联系电话", "ZIndex": 9, "DataElementName": "文本框32", "CanGrow": true, "KeepTogether": true, "Value": "地址：0478-6622283", "Style": {"FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "9.001cm", "Top": "0.5cm", "Width": "4.999cm", "Height": "0.75cm"}]}, "DataSets": [{"Name": "reg", "Fields": [{"Name": "id", "DataField": "id"}, {"Name": "examNo", "DataField": "examNo"}, {"Name": "status", "DataField": "status"}, {"Name": "customerAvatar", "DataField": "customerAvatar"}, {"Name": "customerId", "DataField": "customerId"}, {"Name": "customerRegNum", "DataField": "customerRegNum"}, {"Name": "hisPid", "DataField": "hisPid"}, {"Name": "hisInpatientId", "DataField": "hisInpatientId"}, {"Name": "name", "DataField": "name"}, {"Name": "gender", "DataField": "gender"}, {"Name": "age", "DataField": "age"}, {"Name": "ageUnit", "DataField": "ageUnit"}, {"Name": "birthday", "DataField": "birthday"}, {"Name": "phone", "DataField": "phone"}, {"Name": "marriage<PERSON>tatus", "DataField": "marriage<PERSON>tatus"}, {"Name": "pregnancyFlag", "DataField": "pregnancyFlag"}, {"Name": "nation", "DataField": "nation"}, {"Name": "bloodType", "DataField": "bloodType"}, {"Name": "cardType", "DataField": "cardType"}, {"Name": "idCard", "DataField": "idCard"}, {"Name": "examCardNo", "DataField": "examCardNo"}, {"Name": "medicalCardNo", "DataField": "medicalCardNo"}, {"Name": "healthNo", "DataField": "healthNo"}, {"Name": "address", "DataField": "address"}, {"Name": "postCode", "DataField": "postCode"}, {"Name": "email", "DataField": "email"}, {"Name": "customerCategory", "DataField": "customerCategory"}, {"Name": "examCategory", "DataField": "examCategory"}, {"Name": "teamId", "DataField": "teamId"}, {"Name": "teamName", "DataField": "teamName"}, {"Name": "bookingDate", "DataField": "bookingDate"}, {"Name": "companyDeptId", "DataField": "companyDeptId"}, {"Name": "companyDeptName", "DataField": "companyDeptName"}, {"Name": "country", "DataField": "country"}, {"Name": "industry", "DataField": "industry"}, {"Name": "workNo", "DataField": "workNo"}, {"Name": "career", "DataField": "career"}, {"Name": "jobStatus", "DataField": "jobStatus"}, {"Name": "workType", "DataField": "workType"}, {"Name": "workShop", "DataField": "workShop"}, {"Name": "riskFactor", "DataField": "riskFactor"}, {"Name": "workYears", "DataField": "workYears"}, {"Name": "workMonths", "DataField": "workMonths"}, {"Name": "riskYears", "DataField": "riskYears"}, {"Name": "riskMonths", "DataField": "riskMonths"}, {"Name": "reExamStatus", "DataField": "reExamStatus"}, {"Name": "reExamRemark", "DataField": "reExamRemark"}, {"Name": "monitoringType", "DataField": "monitoringType"}, {"Name": "occIrradiation", "DataField": "occIrradiation"}, {"Name": "recipeTitle", "DataField": "recipeTitle"}, {"Name": "eduLevel", "DataField": "eduLevel"}, {"Name": "introducer", "DataField": "introducer"}, {"Name": "remark", "DataField": "remark"}, {"Name": "companyRegId", "DataField": "companyRegId"}, {"Name": "companyRegName", "DataField": "companyRegName"}, {"Name": "checkDate", "DataField": "checkDate"}, {"Name": "<PERSON><PERSON><PERSON>", "DataField": "<PERSON><PERSON><PERSON>"}, {"Name": "checkState", "DataField": "checkState"}, {"Name": "infoSource", "DataField": "infoSource"}, {"Name": "paymentState", "DataField": "paymentState"}, {"Name": "operId", "DataField": "operId"}, {"Name": "operName", "DataField": "operName"}, {"Name": "operTime", "DataField": "operTime"}, {"Name": "auditDate", "DataField": "auditDate"}, {"Name": "originCustomer", "DataField": "originCustomer"}, {"Name": "supplyFlag", "DataField": "supplyFlag"}, {"Name": "prePayFlag", "DataField": "prePayFlag"}, {"Name": "webQueryCode", "DataField": "webQueryCode"}, {"Name": "companyName", "DataField": "companyName"}, {"Name": "companyId", "DataField": "companyId"}, {"Name": "createTime", "DataField": "createTime"}, {"Name": "regTime", "DataField": "regTime"}, {"Name": "guidancePrintTimes", "DataField": "guidancePrintTimes"}, {"Name": "serialNo", "DataField": "serialNo"}, {"Name": "itemGroupList", "DataField": "itemGroupList"}, {"Name": "companyTeam", "DataField": "companyTeam"}, {"Name": "companyReg", "DataField": "companyReg"}, {"Name": "statusStatList", "DataField": "statusStatList"}, {"Name": "delFlag", "DataField": "delFlag"}, {"Name": "summaryStatus", "DataField": "summaryStatus"}, {"Name": "summaryStatusColor", "DataField": "summaryStatusColor"}, {"Name": "riskFactorList", "DataField": "riskFactorList"}, {"Name": "查询字段"}], "Query": {"DataSourceName": "DataSource", "CommandText": "jpath=$.reg"}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}, {"Name": "itemGroupByDepartment", "Fields": [{"Name": "seq", "DataField": "seq"}, {"Name": "departFunction", "DataField": "departFunction"}, {"Name": "depart.id", "DataField": "depart.id"}, {"Name": "depart.parentId", "DataField": "depart.parentId"}, {"Name": "depart.departName", "DataField": "depart.departName"}, {"Name": "depart.helpChar", "DataField": "depart.helpChar"}, {"Name": "depart.departNameEn", "DataField": "depart.departNameEn"}, {"Name": "depart.departNameAbbr", "DataField": "depart.departNameAbbr"}, {"Name": "depart.departOrder", "DataField": "depart.departOrder"}, {"Name": "depart.description", "DataField": "depart.description"}, {"Name": "depart.orgCategory", "DataField": "depart.orgCategory"}, {"Name": "depart.orgType", "DataField": "depart.orgType"}, {"Name": "depart.orgCode", "DataField": "depart.orgCode"}, {"Name": "depart.mobile", "DataField": "depart.mobile"}, {"Name": "depart.fax", "DataField": "depart.fax"}, {"Name": "depart.address", "DataField": "depart.address"}, {"Name": "depart.memo", "DataField": "depart.memo"}, {"Name": "depart.status", "DataField": "depart.status"}, {"Name": "depart.delFlag", "DataField": "depart.delFlag"}, {"Name": "depart.qywxIdentifier", "DataField": "depart.qywxIdentifier"}, {"Name": "depart.createBy", "DataField": "depart.createBy"}, {"Name": "depart.createTime", "DataField": "depart.createTime"}, {"Name": "depart.updateBy", "DataField": "depart.updateBy"}, {"Name": "depart.updateTime", "DataField": "depart.updateTime"}, {"Name": "depart.tenantId", "DataField": "depart.tenantId"}, {"Name": "depart.izLeaf", "DataField": "depart.izLeaf"}, {"Name": "depart.wubiChar", "DataField": "depart.wubiChar"}, {"Name": "depart.sexLimit", "DataField": "depart.sexLimit"}, {"Name": "depart.haveSummary", "DataField": "depart.haveSummary"}, {"Name": "depart.summarySort", "DataField": "depart.summarySort"}, {"Name": "depart.guideSort", "DataField": "depart.guideSort"}, {"Name": "depart.reportSort", "DataField": "depart.reportSort"}, {"Name": "depart.maxPerDay", "DataField": "depart.maxPerDay"}, {"Name": "depart.vipAddress", "DataField": "depart.vipAddress"}, {"Name": "depart.womenAddress", "DataField": "depart.womenAddress"}, {"Name": "depart.men<PERSON>ddress", "DataField": "depart.men<PERSON>ddress"}, {"Name": "depart.initialId", "DataField": "depart.initialId"}, {"Name": "depart.checkDoctorIntro", "DataField": "depart.checkDoctorIntro"}, {"Name": "depart.auditDoctorIntro", "DataField": "depart.auditDoctorIntro"}, {"Name": "depart.sumFormat", "DataField": "depart.sumFormat"}, {"Name": "depart.intro", "DataField": "depart.intro"}, {"Name": "depart.departFunCategory", "DataField": "depart.departFunCategory"}, {"Name": "depart.directorUser<PERSON>ds", "DataField": "depart.directorUser<PERSON>ds"}, {"Name": "depart.oldDirectorUserIds", "DataField": "depart.oldDirectorUserIds"}, {"Name": "groupList", "DataField": "groupList"}], "Query": {"DataSourceName": "DataSource", "CommandText": "jpath=$.itemGroupByDepartment[*]"}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}, {"Name": "groupList", "Fields": [{"Name": "id", "DataField": "id"}, {"Name": "customerRegId", "DataField": "customerRegId"}, {"Name": "itemGroupId", "DataField": "itemGroupId"}, {"Name": "itemGroupName", "DataField": "itemGroupName"}, {"Name": "departmentId", "DataField": "departmentId"}, {"Name": "departmentName", "DataField": "departmentName"}, {"Name": "type", "DataField": "type"}, {"Name": "itemSuitId", "DataField": "itemSuitId"}, {"Name": "itemSuitName", "DataField": "itemSuitName"}, {"Name": "addMinusFlag", "DataField": "addMinusFlag"}, {"Name": "price", "DataField": "price"}, {"Name": "disRate", "DataField": "disRate"}, {"Name": "priceAfterDis", "DataField": "priceAfterDis"}, {"Name": "payerType", "DataField": "payerType"}, {"Name": "payStatus", "DataField": "payStatus"}, {"Name": "checkTime", "DataField": "checkTime"}, {"Name": "checkStatus", "DataField": "checkStatus"}, {"Name": "checkStatusColor", "DataField": "checkStatusColor"}, {"Name": "reg<PERSON>y", "DataField": "reg<PERSON>y"}, {"Name": "departReportStatus", "DataField": "departReportStatus"}, {"Name": "receiptId", "DataField": "receiptId"}, {"Name": "createTime", "DataField": "createTime"}, {"Name": "updateTime", "DataField": "updateTime"}, {"Name": "department.id", "DataField": "department.id"}, {"Name": "department.parentId", "DataField": "department.parentId"}, {"Name": "department.departName", "DataField": "department.departName"}, {"Name": "department.helpChar", "DataField": "department.helpChar"}, {"Name": "department.departNameEn", "DataField": "department.departNameEn"}, {"Name": "department.departNameAbbr", "DataField": "department.departNameAbbr"}, {"Name": "department.departOrder", "DataField": "department.departOrder"}, {"Name": "department.description", "DataField": "department.description"}, {"Name": "department.orgCategory", "DataField": "department.orgCategory"}, {"Name": "department.orgType", "DataField": "department.orgType"}, {"Name": "department.orgCode", "DataField": "department.orgCode"}, {"Name": "department.mobile", "DataField": "department.mobile"}, {"Name": "department.fax", "DataField": "department.fax"}, {"Name": "department.address", "DataField": "department.address"}, {"Name": "department.memo", "DataField": "department.memo"}, {"Name": "department.status", "DataField": "department.status"}, {"Name": "department.delFlag", "DataField": "department.delFlag"}, {"Name": "department.qywxIdentifier", "DataField": "department.qywxIdentifier"}, {"Name": "department.createBy", "DataField": "department.createBy"}, {"Name": "department.createTime", "DataField": "department.createTime"}, {"Name": "department.updateBy", "DataField": "department.updateBy"}, {"Name": "department.updateTime", "DataField": "department.updateTime"}, {"Name": "department.tenantId", "DataField": "department.tenantId"}, {"Name": "department.izLeaf", "DataField": "department.izLeaf"}, {"Name": "department.wubiChar", "DataField": "department.wubiChar"}, {"Name": "department.sexLimit", "DataField": "department.sexLimit"}, {"Name": "department.haveSummary", "DataField": "department.haveSummary"}, {"Name": "department.summarySort", "DataField": "department.summarySort"}, {"Name": "department.guideSort", "DataField": "department.guideSort"}, {"Name": "department.reportSort", "DataField": "department.reportSort"}, {"Name": "department.maxPerDay", "DataField": "department.maxPerDay"}, {"Name": "department.vipAddress", "DataField": "department.vipAddress"}, {"Name": "department.womenAddress", "DataField": "department.womenAddress"}, {"Name": "department.menAddress", "DataField": "department.menAddress"}, {"Name": "department.initialId", "DataField": "department.initialId"}, {"Name": "department.checkDoctorIntro", "DataField": "department.checkDoctorIntro"}, {"Name": "department.auditDoctorIntro", "DataField": "department.auditDoctorIntro"}, {"Name": "department.sumFormat", "DataField": "department.sumFormat"}, {"Name": "department.intro", "DataField": "department.intro"}, {"Name": "department.departFunCategory", "DataField": "department.departFunCategory"}, {"Name": "department.directorUserIds", "DataField": "department.directorUserIds"}, {"Name": "department.oldDirectorUserIds", "DataField": "department.oldDirectorUserIds"}, {"Name": "itemList", "DataField": "itemList"}, {"Name": "abandonFlag", "DataField": "abandonFlag"}, {"Name": "departFunction", "DataField": "departFunction"}, {"Name": "guideSort", "DataField": "guideSort"}, {"Name": "reportSort", "DataField": "reportSort"}], "Query": {"DataSourceName": "$dataset:itemGroupByDepartment/groupList"}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}]}