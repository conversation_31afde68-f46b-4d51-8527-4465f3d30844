#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=28016, tid=29660
#
# JRE version:  (21.0.6+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: AMD Ryzen 7 7735HS with Radeon Graphics, 16 cores, 13G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Sun Jun  8 15:47:38 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.3912) elapsed time: 1.296603 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000001e1f8946e00):  JavaThread "Unknown thread" [_thread_in_vm, id=29660, stack(0x000000b3f3400000,0x000000b3f3500000) (1024K)]

Stack: [0x000000b3f3400000,0x000000b3f3500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e5cb9]
V  [jvm.dll+0x8c4113]
V  [jvm.dll+0x8c666e]
V  [jvm.dll+0x8c6d53]
V  [jvm.dll+0x288f76]
V  [jvm.dll+0xc0e57]
V  [jvm.dll+0x33e405]
V  [jvm.dll+0x3369f1]
V  [jvm.dll+0x88b569]
V  [jvm.dll+0x3ca6c8]
V  [jvm.dll+0x8745b8]
V  [jvm.dll+0x45f0de]
V  [jvm.dll+0x460dc1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffb3cf6a148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x000001e1facabc00 WorkerThread "GC Thread#0"                     [id=3092, stack(0x000000b3f3500000,0x000000b3f3600000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffb3c652ff0]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffb3cfdeb30] Heap_lock - owner thread: 0x000001e1f8946e00

Heap address: 0x0000000724e00000, size: 3506 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000724e00000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000001e18f000000,0x000001e18f6e0000] _byte_map_base: 0x000001e18b6d9000

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.107 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6c4470000 - 0x00007ff6c447a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\java.exe
0x00007ffb6edc0000 - 0x00007ffb6f026000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb6e510000 - 0x00007ffb6e5d9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb6c1c0000 - 0x00007ffb6c58c000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb6ca30000 - 0x00007ffb6cb7b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb66c90000 - 0x00007ffb66cab000 	D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\VCRUNTIME140.dll
0x00007ffb65950000 - 0x00007ffb65968000 	D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\jli.dll
0x00007ffb6dc10000 - 0x00007ffb6ddda000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb55e90000 - 0x00007ffb5612a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffb6bf50000 - 0x00007ffb6bf77000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb6cd00000 - 0x00007ffb6cda9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb6cb80000 - 0x00007ffb6cbab000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb6c8f0000 - 0x00007ffb6ca22000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb6c6c0000 - 0x00007ffb6c763000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb6d230000 - 0x00007ffb6d260000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb67e50000 - 0x00007ffb67e5c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\vcruntime140_1.dll
0x00007ffb17030000 - 0x00007ffb170bd000 	D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\msvcp140.dll
0x00007ffb3c310000 - 0x00007ffb3d0d1000 	D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\server\jvm.dll
0x00007ffb6d9a0000 - 0x00007ffb6da52000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb6db60000 - 0x00007ffb6dc06000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb6cbd0000 - 0x00007ffb6cce6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb6d820000 - 0x00007ffb6d894000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb6aba0000 - 0x00007ffb6abfe000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb63100000 - 0x00007ffb63136000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb63140000 - 0x00007ffb6314b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb6ab80000 - 0x00007ffb6ab94000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb6ae30000 - 0x00007ffb6ae4a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb66c80000 - 0x00007ffb66c8a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\jimage.dll
0x00007ffb68cb0000 - 0x00007ffb68ef1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb6ce10000 - 0x00007ffb6d194000 	C:\WINDOWS\System32\combase.dll
0x00007ffb6d270000 - 0x00007ffb6d350000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb5e680000 - 0x00007ffb5e6b9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb6c620000 - 0x00007ffb6c6b9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb65930000 - 0x00007ffb65950000 	D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;D:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2023.3.8/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2023.3.8/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 230686720                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 3676307456                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 3676307456                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Java\open-jdk-17
PATH=D:/Program Files/Git/mingw64/libexec/git-core;D:/Program Files/Git/mingw64/libexec/git-core;D:\Program Files\Git\mingw64\bin;D:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files (x86)\VMware\VMware Workstation\bin\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Python312\Scripts\;C:\Python312\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\TortoiseGit\bin;C:\ProgramData\chocolatey\bin;d:\Program Files\Git\cmd;D:\Program Files\Java\open-jdk-17\bin;d:\Program Files (x86)\Tencent\΢��web�����߹���\dll;node_global;D:\Program Files (x86)\Microsoft SQL Server\130\Tools\Binn\;D:\Program Files\Microsoft SQL Server\130\Tools\Binn\;D:\Program Files (x86)\Microsoft SQL Server\130\DTS\Binn\;D:\Program Files\Microsoft SQL Server\130\DTS\Binn\;D:\Program Files\Microsoft SQL Server\Client SDK\ODBC\130\Tools\Binn\;D:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files\IDM Computer Solutions\UltraEdit;D:\Program Files\nvm;D:\Program Files\nodejs\node_global;C:\Program Files\Redis\;C:\Program Files\Docker\Docker\re;D:\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\nvm;C:\nvm4w\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;d:\Program Files\JetBrains\IntelliJ IDEA 2023.3.8\bin;d:\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\gitkraken\bin;D:\Program Files\nvm;d:\nvm4w\nodejs;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\nvm;C:\nvm4w\nodejs
USERNAME=lenovo
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 68 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 12980K (0% of 14357448K total physical memory with 948140K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 2 days 19:18 hours

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 68 stepping 1 microcode 0xa404105, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, rdpid, fsrm, f16c, pku, cet_ss
Processor Information for the first 16 processors :
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201

Memory: 4k page, system-wide physical 14020M (925M free)
TotalPageFile size 39516M (AvailPageFile size 32M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 65M, peak: 66M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109) for windows-amd64 JRE (21.0.6+9-b895.109), built on 2025-03-26 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
