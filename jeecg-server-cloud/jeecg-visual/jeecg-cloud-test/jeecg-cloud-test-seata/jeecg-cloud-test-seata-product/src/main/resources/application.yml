server:
  port: 5003
spring:
  application:
    name: seata-product
  main:
    allow-bean-definition-overriding: true
  datasource:
    dynamic:
      primary: product
      seata: true    # 开启对 seata的支持
      seata-mode: AT #支持XA及AT模式,默认AT
      datasource:
        # 设置 账号数据源配置
        product:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***************************************************************************************************************************
          username: root
          password: root
          schema: classpath:sql/schema-product.sql
seata:
  enable-auto-data-source-proxy: false
  service:
    grouplist:
      default: 127.0.0.1:8091
    vgroup-mapping:
      springboot-seata-group: default
  # seata 事务组编号 用于TC集群名
  tx-service-group: springboot-seata-group