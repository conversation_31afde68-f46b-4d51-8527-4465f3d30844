<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.jeecgframework.boot</groupId>
        <artifactId>jeecg-cloud-test</artifactId>
        <version>3.7.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>消息队列测试模块</description>
    <artifactId>jeecg-cloud-test-rocketmq</artifactId>

    <dependencies>
        <!-- rocketmq消息队列-->
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-starter-rocketmq</artifactId>
        </dependency>
    </dependencies>

</project>