server:
  port: 7002
  
spring:
  application:
    name: jeecg-demo
  cloud:
    nacos:
      config:
        server-addr: @config.server-addr@
        group: @config.group@
        namespace: @config.namespace@
        username: @config.username@
        password: @config.password@
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        group: @config.group@
        namespace: @config.namespace@
        username: @config.username@
        password: @config.password@
  config:
    import:
      - optional:nacos:jeecg.yaml
      - optional:nacos:<EMAIL>@.yaml